
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ScriptGenius - Professional Screenwriting Tools</title>
    <meta name="description" content="Transform your ideas into professional screenplays with AI-powered writing tools. Collaborate, create, and bring your stories to life with ScriptGenius." />
    <meta name="author" content="ScriptGenius" />
    <meta name="keywords" content="screenwriting, screenplay, AI writing, script editor, film production, storytelling, creative writing, collaboration" />

    <!-- Brand Colors -->
    <meta name="theme-color" content="#1a1a1a" />
    <meta name="msapplication-TileColor" content="#1a1a1a" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/placeholder.svg" />
    <link rel="icon" type="image/png" href="/favicon-32x32.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Optimized font loading with font-display: swap -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Critical CSS and resource preloading -->
    <link rel="preload" href="/src/index.css" as="style" />
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//images.unsplash.com" />
    
    <!-- Critical resource hints -->
    <link rel="modulepreload" href="/src/main.tsx" />
    <link rel="modulepreload" href="/src/App.tsx" />
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="ScriptGenius" />

    <!-- Performance optimizations -->
    <meta http-equiv="X-DNS-Prefetch-Control" content="on" />
    <meta name="format-detection" content="telephone=no" />

    <!-- Open Graph meta tags -->
    <meta property="og:site_name" content="ScriptGenius" />
    <meta property="og:title" content="ScriptGenius - Professional Screenwriting Tools" />
    <meta property="og:description" content="Transform your ideas into professional screenplays with AI-powered writing tools. Collaborate, create, and bring your stories to life with ScriptGenius." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://scriptgenius.com" />
    <meta property="og:image" content="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1200&h=630&fit=crop" />
    <meta property="og:image:alt" content="ScriptGenius - Professional screenwriting platform interface" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@scriptgenius" />
    <meta name="twitter:creator" content="@scriptgenius" />
    <meta name="twitter:title" content="ScriptGenius - Professional Screenwriting Tools" />
    <meta name="twitter:description" content="Transform your ideas into professional screenplays with AI-powered writing tools. Collaborate, create, and bring your stories to life." />
    <meta name="twitter:image" content="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1200&h=630&fit=crop" />
    <meta name="twitter:image:alt" content="ScriptGenius - Professional screenwriting platform" />

    <!-- Additional SEO -->
    <link rel="canonical" href="https://scriptgenius.com" />
    <meta name="robots" content="index, follow" />

    <!-- Script to prevent FOUC and optimize initial render -->
    <script>
      // Prevent flash of unstyled content
      document.documentElement.style.visibility = 'hidden';
      window.addEventListener('DOMContentLoaded', function() {
        document.documentElement.style.visibility = 'visible';
      });
      
      // Early performance monitoring
      if ('performance' in window && 'mark' in performance) {
        performance.mark('page-start');
      }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Mark end of page load for performance monitoring -->
    <script>
      if ('performance' in window && 'mark' in performance) {
        performance.mark('page-end');
        performance.measure('page-load', 'page-start', 'page-end');
      }
    </script>
  </body>
</html>
