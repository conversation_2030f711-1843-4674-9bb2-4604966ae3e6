
# ===========================================
# ScriptGenius Environment Configuration
# ===========================================

# REQUIRED: Supabase Configuration
# Get these from your Supabase project settings
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# REQUIRED: Application Configuration
VITE_APP_VERSION=2.1.0
VITE_API_URL=http://localhost:8080

# OPTIONAL: Monitoring and Analytics
VITE_ERROR_ENDPOINT=https://your-error-service.com/api/errors
VITE_METRICS_ENDPOINT=https://your-metrics-service.com/api/metrics
VITE_ANALYTICS_ID=your_analytics_id_here

# OPTIONAL: Feature Flags
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_ANALYTICS=false

# OPTIONAL: Rate Limiting Configuration
VITE_RATE_LIMIT_REQUESTS_PER_MINUTE=60
VITE_RATE_LIMIT_BURST_SIZE=10

# PRODUCTION: Security Configuration
# Uncomment and configure for production deployment
# VITE_ENFORCE_HTTPS=true
# VITE_CSRF_PROTECTION=true
# VITE_CONTENT_SECURITY_POLICY=true

# PRODUCTION: Performance Configuration
# VITE_CDN_URL=https://your-cdn.com
# VITE_ASSET_OPTIMIZATION=true
# VITE_COMPRESSION_ENABLED=true

# ===========================================
# Development vs Production Notes:
# ===========================================
# - NODE_ENV is automatically set by Vite
# - In production, ensure all URLs use HTTPS
# - Never commit actual API keys to version control
# - Use your deployment platform's environment variable system
# ===========================================
