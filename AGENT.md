# Agent Guidelines for ScriptGenius

## Commands
- `bun dev` - Start development server
- `bun build` - Production build (runs TypeScript check + Vite build)
- `bun test` - Run Vitest unit tests
- `bun test:coverage` - Run tests with coverage
- `bun test:ui` - Run tests with UI interface
- `bun test -- <pattern>` - Run specific test files/patterns
- `bun lint` - Lint with ESLint
- `bun lint:fix` - Auto-fix linting issues
- `bun type-check` - TypeScript type checking only
- `supabase start` - Start local Supabase instance
- `supabase db reset` - Reset local database

## Architecture
- React + Vite + TypeScript frontend
- Supabase backend (auth, database, edge functions)
- Shadcn/UI components with Tailwind CSS
- Feature-based organization in `/src/features/`
- Shared components in `/src/components/`
- Custom hooks in `/src/hooks/`
- Utilities in `/src/lib/` and `/src/utils/`

## Code Style
- Use `@/` imports for src paths (configured in tsconfig)
- Follow Shadcn/UI patterns for components
- Use `cn()` utility for conditional Tailwind classes
- TypeScript with relaxed settings (no strict null checks)
- PascalCase for components, camelCase for functions
- Use React Hook Form with Zod validation
- Prefer `const` arrow functions for components
- Use Tanstack Query for server state
