
#!/bin/bash

# Performance Audit Script
set -e

echo "🔍 Running Performance Audit"

# Build the project
echo "Building project..."
npm run build

# Start preview server
echo "Starting preview server..."
npm run preview &
PREVIEW_PID=$!

# Wait for server
npx wait-on http://localhost:4173 --timeout 30000

# Run Lighthouse audit
echo "Running Lighthouse audit..."
npx lighthouse http://localhost:4173 \
  --chrome-flags="--headless" \
  --output=html \
  --output-path=./performance-report.html \
  --budget-path=./lighthouse-budget.json

# Cleanup
kill $PREVIEW_PID 2>/dev/null || true

echo "✅ Performance audit completed. Report saved to performance-report.html"
