
#!/bin/bash

# E2E Testing Script
set -e

echo "🚀 Starting E2E Test Suite"

# Install dependencies if needed
if ! command -v playwright &> /dev/null; then
    echo "Installing Playwright..."
    npx playwright install
fi

# Start development server in background
echo "Starting development server..."
npm run dev &
SERVER_PID=$!

# Wait for server to be ready
echo "Waiting for server to be ready..."
npx wait-on http://localhost:8080 --timeout 60000

# Run E2E tests
echo "Running E2E tests..."
npx playwright test

# Cleanup
echo "Cleaning up..."
kill $SERVER_PID 2>/dev/null || true

echo "✅ E2E tests completed"
