
import { test, expect } from '@playwright/test';

test.describe('Landing Page', () => {
  test('should load successfully', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/Idea to Screen Flow/);
  });

  test('should display hero section', async ({ page }) => {
    await page.goto('/');
    
    // Check for main heading
    const heading = page.locator('h1').first();
    await expect(heading).toBeVisible();
    
    // Check for CTA buttons
    const ctaButtons = page.locator('button, a').filter({ hasText: /get started|sign up|try now/i });
    await expect(ctaButtons.first()).toBeVisible();
  });

  test('should navigate to dashboard when authenticated', async ({ page }) => {
    await page.goto('/');
    
    // Mock authentication state
    await page.addInitScript(() => {
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        expires_at: Date.now() + 3600000,
        user: { id: 'mock-user-id', email: '<EMAIL>' }
      }));
    });
    
    await page.reload();
    
    // Should redirect to dashboard or show authenticated state
    await expect(page.locator('[data-testid="dashboard"], [data-testid="user-menu"]').first()).toBeVisible({ timeout: 10000 });
  });

  test('should be responsive on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    
    // Check that content is visible and properly laid out
    const heading = page.locator('h1').first();
    await expect(heading).toBeVisible();
    
    // Check mobile navigation if present
    const mobileNav = page.locator('[data-testid="mobile-nav"], button[aria-label*="menu"]');
    if (await mobileNav.count() > 0) {
      await expect(mobileNav.first()).toBeVisible();
    }
  });
});
