
import { test, expect } from '@playwright/test';

test.describe('Navigation', () => {
  test('should navigate between main sections', async ({ page }) => {
    await page.goto('/');
    
    // Test navigation links
    const navLinks = page.locator('nav a, header a').filter({ hasText: /features|pricing|about|dashboard/i });
    
    for (let i = 0; i < Math.min(await navLinks.count(), 3); i++) {
      const link = navLinks.nth(i);
      const linkText = await link.textContent();
      
      if (linkText && !linkText.toLowerCase().includes('dashboard')) {
        await link.click();
        await page.waitForLoadState('networkidle');
        
        // Should navigate successfully (URL change or content change)
        const url = page.url();
        expect(url).toBeTruthy();
      }
    }
  });

  test('should handle browser back/forward navigation', async ({ page }) => {
    await page.goto('/');
    const initialUrl = page.url();
    
    // Navigate to another page if possible
    const navLink = page.locator('nav a, header a').filter({ hasText: /features|pricing|about/i }).first();
    
    if (await navLink.isVisible()) {
      await navLink.click();
      await page.waitForLoadState('networkidle');
      
      const newUrl = page.url();
      expect(newUrl).not.toBe(initialUrl);
      
      // Test back navigation
      await page.goBack();
      await page.waitForLoadState('networkidle');
      expect(page.url()).toBe(initialUrl);
      
      // Test forward navigation
      await page.goForward();
      await page.waitForLoadState('networkidle');
      expect(page.url()).toBe(newUrl);
    }
  });

  test('should maintain scroll position', async ({ page }) => {
    await page.goto('/');
    
    // Scroll down
    await page.evaluate(() => window.scrollTo(0, 500));
    const scrollPosition = await page.evaluate(() => window.pageYOffset);
    
    // Navigate and come back
    const navLink = page.locator('nav a, header a').filter({ hasText: /features|pricing|about/i }).first();
    
    if (await navLink.isVisible()) {
      await navLink.click();
      await page.waitForLoadState('networkidle');
      
      await page.goBack();
      await page.waitForLoadState('networkidle');
      
      // Check if scroll position is maintained (within reasonable range)
      const newScrollPosition = await page.evaluate(() => window.pageYOffset);
      expect(Math.abs(newScrollPosition - scrollPosition)).toBeLessThan(100);
    }
  });
});
