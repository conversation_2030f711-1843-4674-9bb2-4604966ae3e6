
import { test, expect } from '@playwright/test';

test.describe('Authentication Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing auth state
    await page.context().clearCookies();
    await page.evaluate(() => localStorage.clear());
  });

  test('should display login/signup options', async ({ page }) => {
    await page.goto('/');
    
    // Look for auth buttons/links
    const authElements = page.locator('button, a').filter({ hasText: /sign in|log in|sign up|register/i });
    await expect(authElements.first()).toBeVisible();
  });

  test('should handle login form validation', async ({ page }) => {
    await page.goto('/');
    
    // Try to find and interact with login form
    const loginButton = page.locator('button, a').filter({ hasText: /sign in|log in/i }).first();
    
    if (await loginButton.isVisible()) {
      await loginButton.click();
      
      // Look for email and password fields
      const emailField = page.locator('input[type="email"], input[name*="email"]').first();
      const passwordField = page.locator('input[type="password"], input[name*="password"]').first();
      
      if (await emailField.isVisible() && await passwordField.isVisible()) {
        // Test empty form submission
        const submitButton = page.locator('button[type="submit"], button').filter({ hasText: /sign in|log in|submit/i }).first();
        await submitButton.click();
        
        // Should show validation errors
        await expect(page.locator('text=/required|invalid|error/i').first()).toBeVisible({ timeout: 5000 });
      }
    }
  });

  test('should persist auth state across page reloads', async ({ page }) => {
    await page.goto('/');
    
    // Mock successful authentication
    await page.addInitScript(() => {
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: 'mock-token',
        refresh_token: 'mock-refresh',
        expires_at: Date.now() + 3600000,
        user: { id: 'mock-user-id', email: '<EMAIL>' }
      }));
    });
    
    await page.reload();
    
    // Should maintain authenticated state
    await expect(page.locator('[data-testid="user-menu"], [data-testid="dashboard"]').first()).toBeVisible({ timeout: 10000 });
    
    await page.reload();
    
    // Should still be authenticated after reload
    await expect(page.locator('[data-testid="user-menu"], [data-testid="dashboard"]').first()).toBeVisible({ timeout: 10000 });
  });
});
