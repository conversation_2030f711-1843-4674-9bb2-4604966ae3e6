# Contributing to ScriptGenius

Thank you for your interest in contributing to ScriptGenius! This guide will help you understand our development process, coding standards, and performance optimization practices.

## Table of Contents

- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Performance Guidelines](#performance-guidelines)
- [Code Standards](#code-standards)
- [Testing Requirements](#testing-requirements)
- [Pull Request Process](#pull-request-process)

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Bun package manager
- Supabase CLI
- Docker (for local Supabase)

### Setup

1. Fork the repository
2. Clone your fork: `git clone https://github.com/your-username/script-genius.git`
3. Install dependencies: `bun install`
4. Copy environment file: `cp .env.example .env.local`
5. Start Supabase: `supabase start`
6. Start development server: `bun dev`

## Development Workflow

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `perf/description` - Performance improvements
- `docs/description` - Documentation updates

### Commit Messages

Follow conventional commits format:

```
type(scope): description

feat(ai-panel): add debounced content analysis
fix(admin): resolve pagination memory leak
perf(storyboard): optimize search with memoization
docs(performance): update optimization guide
```

## Performance Guidelines

ScriptGenius prioritizes performance. All contributions must maintain or improve performance metrics.

### Component Performance Standards

#### 1. Use React.memo for Expensive Components

```typescript
// ✅ Good: Memoized component
const ExpensiveComponent = memo(({ data, onUpdate }) => {
  const processedData = useMemo(() =>
    heavyProcessing(data), [data]
  );

  const handleUpdate = useCallback((newData) => {
    onUpdate(newData);
  }, [onUpdate]);

  return <div>{/* Component JSX */}</div>;
});

// ❌ Bad: No memoization
const ExpensiveComponent = ({ data, onUpdate }) => {
  const processedData = heavyProcessing(data); // Runs every render

  return <div>{/* Component JSX */}</div>;
};
```

#### 2. Optimize State Management

```typescript
// ✅ Good: Consolidated state with useReducer
const [state, dispatch] = useReducer(reducer, initialState);

// ❌ Bad: Multiple useState for related state
const [loading, setLoading] = useState(false);
const [error, setError] = useState(null);
const [data, setData] = useState([]);
// ... more useState calls
```

#### 3. Implement Proper Debouncing

```typescript
// ✅ Good: Debounced search
const debouncedQuery = useDebounce(searchQuery, 300);

useEffect(() => {
  if (debouncedQuery) {
    performSearch(debouncedQuery);
  }
}, [debouncedQuery]);

// ❌ Bad: Search on every keystroke
useEffect(() => {
  performSearch(searchQuery);
}, [searchQuery]);
```

### Data Fetching Standards

#### 1. Use React Query for Server State

```typescript
// ✅ Good: Optimized query with caching
const { data, isLoading } = useOptimizedQuery(
  ['resource', id],
  () => fetchResource(id),
  {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  }
);

// ❌ Bad: Direct API calls without caching
useEffect(() => {
  fetchResource(id).then(setData);
}, [id]);
```

#### 2. Implement Pagination for Large Datasets

```typescript
// ✅ Good: Paginated data loading
const { data } = usePaginatedQuery(
  ['users'],
  (page, limit) => fetchUsers(page, limit),
  { page: currentPage, limit: 50 }
);

// ❌ Bad: Loading all data at once
const { data } = useQuery(['users'], () => fetchAllUsers());
```

### Performance Budgets

All components must meet these performance budgets:

| Component Type | Render Time | Memory Usage | Bundle Impact |
|----------------|-------------|--------------|---------------|
| Small Components | <20ms | <1MB | <5KB |
| Medium Components | <50ms | <5MB | <20KB |
| Large Components | <100ms | <10MB | <50KB |
| Page Components | <200ms | <20MB | <100KB |

## Code Standards

### TypeScript

- Use strict TypeScript configuration
- Define proper interfaces for all props and state
- Avoid `any` type - use proper typing
- Use generic types for reusable components

### React Patterns

```typescript
// ✅ Good: Proper component structure
interface ComponentProps {
  data: DataType[];
  onUpdate: (data: DataType) => void;
  className?: string;
}

const Component: React.FC<ComponentProps> = memo(({
  data,
  onUpdate,
  className
}) => {
  // Memoized calculations
  const processedData = useMemo(() =>
    processData(data), [data]
  );

  // Stable event handlers
  const handleUpdate = useCallback((newData: DataType) => {
    onUpdate(newData);
  }, [onUpdate]);

  return (
    <div className={className}>
      {/* Component JSX */}
    </div>
  );
});

Component.displayName = 'Component';
```

### Styling

- Use Tailwind CSS for styling
- Follow mobile-first responsive design
- Use CSS variables for theme consistency
- Optimize for dark/light mode support

## Testing Requirements

### Required Tests

1. **Unit Tests**: All new functions and hooks
2. **Component Tests**: All new components
3. **Performance Tests**: Components with performance impact
4. **Integration Tests**: Feature workflows

### Performance Testing

```typescript
// Example: Component performance test
describe('ComponentName Performance', () => {
  it('should render within performance budget', () => {
    const startTime = performance.now();
    render(<ComponentName {...props} />);
    const renderTime = performance.now() - startTime;

    expect(renderTime).toBeLessThan(50); // 50ms budget
  });

  it('should not cause memory leaks', () => {
    const { unmount } = render(<ComponentName {...props} />);
    const initialMemory = performance.memory?.usedJSHeapSize || 0;

    unmount();

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    const finalMemory = performance.memory?.usedJSHeapSize || 0;
    const memoryDiff = finalMemory - initialMemory;

    expect(memoryDiff).toBeLessThan(1024 * 1024); // 1MB threshold
  });
});
```

### Test Commands

```bash
# Run all tests
bun test

# Run performance tests
bun test:performance

# Run with coverage
bun test:coverage

# Run E2E tests
bun test:e2e
```

## Pull Request Process

### Before Submitting

1. **Run Performance Tests**: `bun test:performance`
2. **Check Bundle Size**: `bun analyze`
3. **Run Full Test Suite**: `bun test`
4. **Lint Code**: `bun lint:fix`
5. **Type Check**: `bun type-check`

### PR Requirements

1. **Performance Impact**: Document any performance changes
2. **Test Coverage**: Maintain or improve test coverage
3. **Documentation**: Update relevant documentation
4. **Breaking Changes**: Clearly document any breaking changes

### PR Template

```markdown
## Description
Brief description of changes

## Performance Impact
- [ ] No performance impact
- [ ] Performance improvement (provide metrics)
- [ ] Performance regression (justify and provide mitigation)

## Testing
- [ ] Unit tests added/updated
- [ ] Performance tests added/updated
- [ ] E2E tests added/updated
- [ ] Manual testing completed

## Documentation
- [ ] README updated
- [ ] API documentation updated
- [ ] Performance documentation updated

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Performance tests pass
- [ ] All tests pass
- [ ] No console errors/warnings
```

## Performance Monitoring

### Development Tools

Use these tools during development:

1. **PerformanceMonitor**: Real-time component tracking
2. **PerformanceTestSuite**: Comprehensive benchmarking
3. **React DevTools Profiler**: Component analysis
4. **Chrome DevTools**: Memory and performance profiling

### Continuous Monitoring

- Performance tests run on every PR
- Bundle size analysis on build
- Performance regression detection
- Real user monitoring in production

## Getting Help

- **Documentation**: Check `/docs` directory
- **Performance Issues**: Review `docs/performance.md`
- **API Questions**: See `docs/api-optimization.md`
- **Architecture**: Read `docs/01-architecture.md`

## Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Prioritize user experience and performance
- Follow established patterns and conventions
- Document performance-critical changes

Thank you for contributing to ScriptGenius! Your efforts help make the platform faster and better for all users.