# Contributing to ScriptGenius

Thank you for your interest in contributing! We welcome all contributions that help improve the platform. This document outlines the process for contributing to the project.

## How to Report Bugs

If you find a bug, please create an issue in the GitHub repository. Provide a clear title, a detailed description of the bug, steps to reproduce it, and what you expected to happen.

## Feature Requests

We love new ideas! Please create an issue and use the "Feature Request" template to outline your idea. Explain the problem you're trying to solve and how your proposed feature would solve it.

## Pull Request Process

1.  **Fork the repository** and create your branch from `main`. Use a clear branch name (e.g., `feat/add-new-export-format`, `fix/editor-rendering-bug`).
2.  **Make your changes.** Ensure your code adheres to the project's coding standards (`bun lint`).
3.  **Add tests** for any new features or bug fixes.
4.  **Update documentation** if you are changing how a feature works or adding a new one.
5.  **Create a Pull Request** against the `main` branch. Provide a clear description of the changes you've made and why. Link to any relevant issues.
6.  The PR will be reviewed by the core team. Address any feedback or requested changes.
7.  Once approved, your PR will be merged. Thank you for your contribution!

## Code of Conduct

All contributors are expected to adhere to our [Code of Conduct](./CODE_OF_CONDUCT.md). Please be respectful and constructive in all your interactions. 