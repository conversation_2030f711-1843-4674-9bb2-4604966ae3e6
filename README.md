# ScriptGenius: The AI-Powered Screenwriting Studio

![CI/CD](https://img.shields.io/github/actions/workflow/status/your-org/your-repo/ci.yml?branch=main&style=for-the-badge)
![Code Coverage](https://img.shields.io/codecov/c/github/your-org/your-repo?style=for-the-badge)
![License](https://img.shields.io/github/license/your-org/your-repo?style=for-the-badge)

ScriptGenius is a modern, web-based screenwriting application designed to help storytellers go from idea to production-ready script with the help of powerful AI tools, collaboration features, and integrated industry workflows.

**For detailed documentation, please see the `/docs` directory:**
*   [**Architecture Overview**](./docs/01-architecture.md)
*   [**Codebase Guide**](./docs/02-codebase-guide.md)
*   [**Backend & Supabase Guide**](./docs/03-backend-guide.md)
*   [**Testing Strategy**](./docs/04-testing-strategy.md)
*   [**Deployment Guide**](./docs/05-deployment.md)
*   [**Architectural Decision Records (ADRs)**](./docs/06-ADRs.md)

---

## 🚀 Quick Start: Local Development

Follow these steps to get the development environment running on your local machine.

### Prerequisites

*   **Node.js** (v18 or later)
*   **Bun** (`npm install -g bun`)
*   **Supabase CLI** (`npm install -g supabase`)
*   **Docker** (for running Supabase locally)

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/your-repo.git
cd your-repo
```

### 2. Install Dependencies

This project uses `bun` for package management.

```bash
bun install
```

### 3. Set Up Environment Variables

Copy the example environment file. You will need to populate this file with your local Supabase keys and any other required service keys (e.g., Stripe, OpenAI).

```bash
cp .env.example .env.local
```

### 4. Run Supabase Locally

Start the Supabase services (database, auth, storage) using Docker. This command also applies any new database migrations.

```bash
supabase start
```

After startup, the CLI will output your local Supabase URL and `anon` key. Add these to your `.env.local` file.

### 5. Start the Development Server

You can now start the Vite development server.

```bash
bun dev
```

The application should now be running at `http://localhost:5173`.

---

## 🛠️ Key Commands

*   `bun dev`: Starts the local development server.
*   `bun build`: Creates a production build of the application.
*   `bun test`: Runs the test suite using Vitest.
*   `bun test:e2e`: Runs the end-to-end tests using Playwright.
*   `bun lint`: Lints the codebase using ESLint and Prettier.
*   `bun lint:fix`: Automatically fixes linting issues.
*   `supabase db reset`: Resets the local database and re-applies all migrations.
*   `supabase functions deploy <function-name>`: Deploys a specific edge function to the Supabase cloud.
