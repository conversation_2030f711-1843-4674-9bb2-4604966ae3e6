
import { advancedCache } from './advancedCache';

interface QueryOptions {
  ttl?: number;
  staleTime?: number;
  tags?: string[];
  retry?: number;
  retryDelay?: number;
}

export class QueryCache {
  private pendingQueries = new Map<string, Promise<any>>();

  async query<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: QueryOptions = {}
  ): Promise<T> {
    const { retry = 3, retryDelay = 1000 } = options;

    // Return pending query if exists
    if (this.pendingQueries.has(key)) {
      return this.pendingQueries.get(key)!;
    }

    // Try SWR pattern first
    try {
      const promise = advancedCache.swr(key, async () => {
        return this.executeWithRetry(fetchFn, retry, retryDelay);
      }, options);

      this.pendingQueries.set(key, promise);
      const result = await promise;
      this.pendingQueries.delete(key);
      
      return result;
    } catch (error) {
      this.pendingQueries.delete(key);
      throw error;
    }
  }

  private async executeWithRetry<T>(
    fetchFn: () => Promise<T>, 
    maxRetries: number, 
    delay: number
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fetchFn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)));
        }
      }
    }

    throw lastError!;
  }

  invalidate(keyOrPattern: string | RegExp): void {
    if (typeof keyOrPattern === 'string') {
      advancedCache.delete(keyOrPattern);
      this.pendingQueries.delete(keyOrPattern);
    } else {
      advancedCache.invalidateByPattern(keyOrPattern);
      // Clear matching pending queries
      for (const key of this.pendingQueries.keys()) {
        if (keyOrPattern.test(key)) {
          this.pendingQueries.delete(key);
        }
      }
    }
  }

  invalidateByTag(tag: string): void {
    advancedCache.invalidateByTag(tag);
  }

  prefetch<T>(key: string, fetchFn: () => Promise<T>, options: QueryOptions = {}): void {
    if (!advancedCache.has(key)) {
      this.query(key, fetchFn, options).catch(() => {
        // Silently fail prefetch
      });
    }
  }

  getStats() {
    return {
      cache: advancedCache.getStats(),
      pendingQueries: this.pendingQueries.size
    };
  }
}

export const queryCache = new QueryCache();
