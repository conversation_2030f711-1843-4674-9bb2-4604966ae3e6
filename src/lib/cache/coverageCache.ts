
import { CoverageReport } from '@/lib/api/coverage';

export interface CacheEntry {
  data: CoverageReport;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheConfig {
  maxSize: number;
  ttlMs: number;
  cleanupIntervalMs: number;
}

export class CoverageCache {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 100, // Maximum number of cached entries
      ttlMs: 24 * 60 * 60 * 1000, // 24 hours
      cleanupIntervalMs: 60 * 60 * 1000, // 1 hour cleanup interval
      ...config,
    };

    this.startCleanupTimer();
  }

  private generateKey(sceneId: string, fidelityLevel: string, contentHash: string): string {
    return `${sceneId}-${fidelityLevel}-${contentHash}`;
  }

  private hashContent(content: string): string {
    // Simple hash function for content
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  get(sceneId: string, fidelityLevel: string, content: string): CoverageReport | null {
    const contentHash = this.hashContent(content);
    const key = this.generateKey(sceneId, fidelityLevel, contentHash);
    const entry = this.cache.get(key);

    if (!entry) return null;

    // Check if entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    console.log(`Cache hit for key: ${key}`);
    return entry.data;
  }

  set(sceneId: string, fidelityLevel: string, content: string, report: CoverageReport): void {
    const contentHash = this.hashContent(content);
    const key = this.generateKey(sceneId, fidelityLevel, contentHash);
    const now = Date.now();

    // Check if cache is full and needs eviction
    if (this.cache.size >= this.config.maxSize) {
      this.evictLeastRecentlyUsed();
    }

    const entry: CacheEntry = {
      data: report,
      timestamp: now,
      expiresAt: now + this.config.ttlMs,
      accessCount: 1,
      lastAccessed: now,
    };

    this.cache.set(key, entry);
    console.log(`Cached coverage report for key: ${key}`);
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      console.log(`Evicted cache entry: ${oldestKey}`);
    }
  }

  clear(): void {
    this.cache.clear();
    console.log('Cache cleared');
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      this.cache.delete(key);
    });

    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`);
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupIntervalMs);
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }

  getStats() {
    const entries = Array.from(this.cache.values());
    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      totalAccesses: entries.reduce((sum, entry) => sum + entry.accessCount, 0),
      averageAge: entries.length > 0 
        ? entries.reduce((sum, entry) => sum + (Date.now() - entry.timestamp), 0) / entries.length
        : 0,
    };
  }
}

// Global cache instance
export const coverageCache = new CoverageCache();
