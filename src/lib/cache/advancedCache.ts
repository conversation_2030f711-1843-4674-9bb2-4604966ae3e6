
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
  hits: number;
  lastAccessed: number;
  tags: string[];
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
}

export class AdvancedCache {
  private cache = new Map<string, CacheEntry<any>>();
  private stats = { hits: 0, misses: 0 };
  private maxSize: number;
  private defaultTTL: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxSize = 1000, defaultTTL = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    
    // Auto-cleanup expired entries every minute
    this.cleanupInterval = setInterval(() => this.cleanup(), 60000);
  }

  set<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      tags?: string[];
      priority?: 'low' | 'normal' | 'high';
    } = {}
  ): void {
    const { ttl = this.defaultTTL, tags = [], priority = 'normal' } = options;
    const now = Date.now();
    
    // Evict if cache is full
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + ttl,
      hits: 0,
      lastAccessed: now,
      tags
    };

    this.cache.set(key, entry);
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    const now = Date.now();
    
    // Check if expired
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access stats
    entry.hits++;
    entry.lastAccessed = now;
    this.stats.hits++;

    return entry.data;
  }

  // Get multiple keys at once
  getMany<T>(keys: string[]): Record<string, T | null> {
    const result: Record<string, T | null> = {};
    keys.forEach(key => {
      result[key] = this.get<T>(key);
    });
    return result;
  }

  // Set multiple keys at once
  setMany<T>(entries: Array<{ key: string; data: T; options?: any }>): void {
    entries.forEach(({ key, data, options }) => {
      this.set(key, data, options);
    });
  }

  // Invalidate by tags
  invalidateByTag(tag: string): void {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.includes(tag)) {
        this.cache.delete(key);
      }
    }
  }

  // Pattern-based invalidation
  invalidateByPattern(pattern: RegExp): void {
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
      }
    }
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0 };
  }

  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      ...this.stats,
      size: this.cache.size,
      hitRate: total > 0 ? this.stats.hits / total : 0
    };
  }

  // Preload data with background refresh
  async preload<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: { ttl?: number; tags?: string[] } = {}
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached) return cached;

    const data = await fetchFn();
    this.set(key, data, options);
    return data;
  }

  // Stale-while-revalidate pattern
  async swr<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: { ttl?: number; staleTime?: number; tags?: string[] } = {}
  ): Promise<T> {
    const { staleTime = this.defaultTTL / 2 } = options;
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    const now = Date.now();

    // Return cached data if fresh
    if (entry && now < entry.expiresAt) {
      // Background refresh if stale
      if (now > entry.timestamp + staleTime) {
        fetchFn().then(data => this.set(key, data, options)).catch(() => {});
      }
      
      entry.hits++;
      entry.lastAccessed = now;
      this.stats.hits++;
      return entry.data;
    }

    // Fetch fresh data
    this.stats.misses++;
    const data = await fetchFn();
    this.set(key, data, options);
    return data;
  }

  private evictLeastUsed(): void {
    let leastUsedKey = '';
    let leastUsed: CacheEntry<any> | null = null;

    for (const [key, entry] of this.cache.entries()) {
      if (!leastUsed || entry.hits < leastUsed.hits || 
          (entry.hits === leastUsed.hits && entry.lastAccessed < leastUsed.lastAccessed)) {
        leastUsed = entry;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Create singleton instance
export const advancedCache = new AdvancedCache();
