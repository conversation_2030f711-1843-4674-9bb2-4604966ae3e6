
import { toast } from '@/hooks/use-toast';

export interface AppError {
  code: string;
  message: string;
  userMessage: string;
  details?: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class ErrorHandler {
  private static errorMessages: Record<string, { message: string; userMessage: string; severity: AppError['severity'] }> = {
    // Validation errors
    'VALIDATION_FAILED': {
      message: 'Input validation failed',
      userMessage: 'Please check your input and try again',
      severity: 'low'
    },
    'REQUIRED_FIELD_MISSING': {
      message: 'Required field is missing',
      userMessage: 'Please fill in all required fields',
      severity: 'low'
    },
    
    // Authentication errors
    'AUTH_REQUIRED': {
      message: 'Authentication required',
      userMessage: 'Please sign in to continue',
      severity: 'medium'
    },
    'PERMISSION_DENIED': {
      message: 'Permission denied',
      userMessage: 'You don\'t have permission to perform this action',
      severity: 'medium'
    },
    
    // API errors
    'API_ERROR': {
      message: 'API request failed',
      userMessage: 'Something went wrong. Please try again',
      severity: 'medium'
    },
    'NETWORK_ERROR': {
      message: 'Network request failed',
      userMessage: 'Please check your internet connection and try again',
      severity: 'medium'
    },
    'RATE_LIMIT_EXCEEDED': {
      message: 'Rate limit exceeded',
      userMessage: 'Too many requests. Please wait a moment and try again',
      severity: 'high'
    },
    
    // Production-specific errors
    'SCHEDULE_CREATE_FAILED': {
      message: 'Failed to create schedule',
      userMessage: 'Unable to create schedule. Please try again',
      severity: 'medium'
    },
    'BUDGET_CREATE_FAILED': {
      message: 'Failed to create budget',
      userMessage: 'Unable to create budget. Please try again',
      severity: 'medium'
    },
    'RESOURCE_CREATE_FAILED': {
      message: 'Failed to create resource',
      userMessage: 'Unable to create resource. Please try again',
      severity: 'medium'
    },
    'REPORT_CREATE_FAILED': {
      message: 'Failed to create report',
      userMessage: 'Unable to create report. Please try again',
      severity: 'medium'
    },
    
    // General errors
    'UNKNOWN_ERROR': {
      message: 'Unknown error occurred',
      userMessage: 'An unexpected error occurred. Please try again',
      severity: 'high'
    }
  };

  static createError(
    code: string, 
    details?: Record<string, any>, 
    customUserMessage?: string
  ): AppError {
    const errorDef = this.errorMessages[code] || this.errorMessages['UNKNOWN_ERROR'];
    
    return {
      code,
      message: errorDef.message,
      userMessage: customUserMessage || errorDef.userMessage,
      details,
      severity: errorDef.severity
    };
  }

  static handleError(error: AppError | Error | string, showToast = true): AppError {
    let appError: AppError;

    if (typeof error === 'string') {
      appError = this.createError('UNKNOWN_ERROR', { originalMessage: error }, error);
    } else if (error instanceof Error) {
      // Try to map common error patterns to specific error codes
      const errorCode = this.mapErrorToCode(error.message);
      appError = this.createError(errorCode, { originalError: error.message }, error.message);
    } else {
      appError = error;
    }

    // Log the technical error for debugging
    console.error(`[${appError.code}] ${appError.message}`, appError.details);

    // Show user-friendly toast if requested
    if (showToast) {
      toast({
        title: 'Error',
        description: appError.userMessage,
        variant: 'destructive'
      });
    }

    return appError;
  }

  private static mapErrorToCode(errorMessage: string): string {
    const lowerMessage = errorMessage.toLowerCase();
    
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
      return 'VALIDATION_FAILED';
    }
    if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
      return 'NETWORK_ERROR';
    }
    if (lowerMessage.includes('auth') || lowerMessage.includes('unauthorized')) {
      return 'AUTH_REQUIRED';
    }
    if (lowerMessage.includes('permission') || lowerMessage.includes('forbidden')) {
      return 'PERMISSION_DENIED';
    }
    if (lowerMessage.includes('rate limit')) {
      return 'RATE_LIMIT_EXCEEDED';
    }
    
    return 'UNKNOWN_ERROR';
  }

  static formatValidationErrors(errors: Record<string, string>): string {
    const errorMessages = Object.entries(errors).map(([field, message]) => {
      const fieldName = field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      return `${fieldName}: ${message}`;
    });
    
    return errorMessages.join(', ');
  }

  static setUserContext(userId: string, email?: string, organizationId?: string) {
    // No-op - previously was Sentry integration
    console.log('User context set:', { userId, email, organizationId });
  }

  static clearUserContext() {
    // No-op - previously was Sentry integration
    console.log('User context cleared');
  }

  static addBreadcrumb(message: string, category: string = 'custom', level: 'info' | 'warning' | 'error' = 'info') {
    // Log breadcrumb to console for debugging
    console.log(`[${level.toUpperCase()}] ${category}: ${message}`);
  }
}
