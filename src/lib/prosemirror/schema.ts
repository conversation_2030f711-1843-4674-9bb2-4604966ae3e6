
import { Schema, DOMOutputSpec } from 'prosemirror-model';

// Define screenplay-specific node types
const nodes = {
  doc: {
    content: 'block+'
  },
  
  scene_heading: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'h1.scene-heading' }],
    toDOM(): DOMOutputSpec {
      return ['h1', { class: 'scene-heading' }, 0];
    }
  },
  
  action: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'p.action' }],
    toDOM(): DOMOutputSpec {
      return ['p', { class: 'action' }, 0];
    }
  },
  
  character: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'p.character' }],
    toDOM(): DOMOutputSpec {
      return ['p', { class: 'character' }, 0];
    }
  },
  
  dialogue: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'p.dialogue' }],
    toDOM(): DOMOutputSpec {
      return ['p', { class: 'dialogue' }, 0];
    }
  },
  
  parenthetical: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'p.parenthetical' }],
    toDOM(): DOMOutputSpec {
      return ['p', { class: 'parenthetical' }, 0];
    }
  },
  
  transition: {
    content: 'text*',
    group: 'block',
    parseDOM: [{ tag: 'p.transition' }],
    toDOM(): DOMOutputSpec {
      return ['p', { class: 'transition' }, 0];
    }
  },
  
  text: {
    group: 'inline'
  }
};

// Define marks for text formatting
const marks = {
  emphasis: {
    parseDOM: [{ tag: 'em' }, { tag: 'i' }],
    toDOM(): DOMOutputSpec {
      return ['em', 0];
    }
  },
  
  strong: {
    parseDOM: [{ tag: 'strong' }, { tag: 'b' }],
    toDOM(): DOMOutputSpec {
      return ['strong', 0];
    }
  },
  
  underline: {
    parseDOM: [{ tag: 'u' }],
    toDOM(): DOMOutputSpec {
      return ['u', 0];
    }
  }
};

export const screenplaySchema = new Schema({ nodes, marks });

// Helper functions for creating nodes
export const createSceneHeading = (text: string) => 
  screenplaySchema.nodes.scene_heading.create({}, screenplaySchema.text(text));

export const createAction = (text: string) => 
  screenplaySchema.nodes.action.create({}, screenplaySchema.text(text));

export const createCharacter = (text: string) => 
  screenplaySchema.nodes.character.create({}, screenplaySchema.text(text));

export const createDialogue = (text: string) => 
  screenplaySchema.nodes.dialogue.create({}, screenplaySchema.text(text));

export const createParenthetical = (text: string) => 
  screenplaySchema.nodes.parenthetical.create({}, screenplaySchema.text(text));

export const createTransition = (text: string) => 
  screenplaySchema.nodes.transition.create({}, screenplaySchema.text(text));
