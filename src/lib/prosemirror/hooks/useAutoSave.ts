
import { useRef, useCallback } from 'react';

interface UseAutoSaveProps {
  onSave?: () => Promise<void> | void;
  autoSave: boolean;
  autoSaveDelay: number;
  hasUnsavedChanges: boolean;
  isSaving: boolean;
  setIsSaving: (saving: boolean) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
}

export const useAutoSave = ({
  onSave,
  autoSave,
  autoSaveDelay,
  hasUnsavedChanges,
  isSaving,
  setIsSaving,
  setHasUnsavedChanges
}: UseAutoSaveProps) => {
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-save functionality
  const triggerAutoSave = useCallback(() => {
    if (!autoSave || !onSave) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(async () => {
      if (hasUnsavedChanges && !isSaving) {
        setIsSaving(true);
        try {
          await onSave();
          setHasUnsavedChanges(false);
        } catch (error) {
          console.error('Auto-save failed:', error);
        } finally {
          setIsSaving(false);
        }
      }
    }, autoSaveDelay);
  }, [autoSave, onSave, hasUnsavedChanges, isSaving, autoSaveDelay, setIsSaving, setHasUnsavedChanges]);

  const clearAutoSaveTimeout = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
  }, []);

  return {
    triggerAutoSave,
    clearAutoSaveTimeout
  };
};
