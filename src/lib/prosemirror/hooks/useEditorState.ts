
import { useRef, useState, useCallback } from 'react';
import { EditorState } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { history } from 'prosemirror-history';
import { D<PERSON><PERSON>arser, DOMSerializer } from 'prosemirror-model';
import { screenplaySchema } from '../schema';
import { screenplayKeymap } from '../keymap';

interface UseEditorStateProps {
  content: string;
  placeholder: string;
  onChange?: (content: string) => void;
  onSave?: () => void;
  className?: string;
  onWordCountChange?: (count: number) => void;
  onUnsavedChange?: (hasChanges: boolean) => void;
  triggerAutoSave?: () => void;
}

export const useEditorState = ({
  content,
  placeholder,
  onChange,
  onSave,
  className,
  onWordCountChange,
  onUnsavedChange,
  triggerAutoSave
}: UseEditorStateProps) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Convert HTML content to ProseMirror document
  const createDocFromContent = useCallback((htmlContent: string) => {
    if (!htmlContent.trim()) {
      const lines = placeholder.split('\n');
      const nodes = lines.map((line, index) => {
        if (index === 0 && line.includes('FADE IN')) {
          return screenplaySchema.nodes.action.create({}, screenplaySchema.text(line));
        } else if (line.includes('EXT.') || line.includes('INT.')) {
          return screenplaySchema.nodes.scene_heading.create({}, screenplaySchema.text(line));
        } else {
          return screenplaySchema.nodes.action.create({}, screenplaySchema.text(line));
        }
      });
      return screenplaySchema.nodes.doc.create({}, nodes);
    }

    try {
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = htmlContent;
      return DOMParser.fromSchema(screenplaySchema).parse(tempDiv);
    } catch (error) {
      console.error('Error parsing content:', error);
      return screenplaySchema.nodes.doc.create({}, [
        screenplaySchema.nodes.action.create({}, screenplaySchema.text('Start writing...'))
      ]);
    }
  }, [placeholder]);

  // Convert ProseMirror document to HTML
  const serializeDoc = useCallback((doc: any) => {
    try {
      const fragment = DOMSerializer.fromSchema(screenplaySchema).serializeFragment(doc.content);
      const tempDiv = document.createElement('div');
      tempDiv.appendChild(fragment);
      return tempDiv.innerHTML;
    } catch (error) {
      console.error('Error serializing document:', error);
      return '';
    }
  }, []);

  // Calculate word count
  const updateWordCount = useCallback((newContent: string) => {
    const text = newContent.replace(/<[^>]*>/g, '').trim();
    const words = text ? text.split(/\s+/).length : 0;
    onWordCountChange?.(words);
  }, [onWordCountChange]);

  const initializeEditor = useCallback(() => {
    if (!editorRef.current) return null;

    const doc = createDocFromContent(content);
    
    const state = EditorState.create({
      doc,
      plugins: [
        history(),
        screenplayKeymap
      ]
    });

    const view = new EditorView(editorRef.current, {
      state,
      dispatchTransaction: (transaction) => {
        const newState = view.state.apply(transaction);
        view.updateState(newState);
        
        // Call onChange when content changes
        if (transaction.docChanged) {
          const newContent = serializeDoc(newState.doc);
          onChange?.(newContent);
          updateWordCount(newContent);
          onUnsavedChange?.(true);
          triggerAutoSave?.();
        }
      },
      attributes: {
        class: `prose-editor ${className}`,
        'data-placeholder': placeholder,
        spellcheck: 'true'
      }
    });

    viewRef.current = view;
    setIsReady(true);

    return view;
  }, [content, placeholder, className, onChange, updateWordCount, onUnsavedChange, triggerAutoSave, createDocFromContent, serializeDoc]);

  const updateContent = useCallback((newContent: string) => {
    if (!viewRef.current || !isReady) return;

    const currentContent = serializeDoc(viewRef.current.state.doc);
    if (currentContent !== newContent) {
      const newDoc = createDocFromContent(newContent);
      const newState = EditorState.create({
        doc: newDoc,
        plugins: viewRef.current.state.plugins
      });
      viewRef.current.updateState(newState);
      updateWordCount(newContent);
    }
  }, [isReady, createDocFromContent, serializeDoc, updateWordCount]);

  const destroyEditor = useCallback(() => {
    if (viewRef.current) {
      viewRef.current.destroy();
      viewRef.current = null;
      setIsReady(false);
    }
  }, []);

  return {
    editorRef,
    viewRef,
    isReady,
    initializeEditor,
    updateContent,
    destroyEditor,
    serializeDoc
  };
};
