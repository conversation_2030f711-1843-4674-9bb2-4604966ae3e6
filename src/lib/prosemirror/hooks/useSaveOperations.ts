
import { useCallback } from 'react';
import { toast } from 'sonner';

interface UseSaveOperationsProps {
  onSave?: () => Promise<void> | void;
  isSaving: boolean;
  setIsSaving: (saving: boolean) => void;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
}

export const useSaveOperations = ({
  onSave,
  isSaving,
  setIsSaving,
  setHasUnsavedChanges
}: UseSaveOperationsProps) => {
  // Manual save function
  const handleManualSave = useCallback(async () => {
    if (!onSave || isSaving) return;

    setIsSaving(true);
    try {
      await onSave();
      setHasUnsavedChanges(false);
      toast.success('Script saved successfully');
    } catch (error) {
      console.error('Manual save failed:', error);
      toast.error('Failed to save script');
    } finally {
      setIsSaving(false);
    }
  }, [onSave, isSaving, setIsSaving, setHasUnsavedChanges]);

  // Setup keyboard shortcuts
  const setupKeyboardShortcuts = useCallback(() => {
    const handleSave = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        handleManualSave();
      }
    };

    document.addEventListener('keydown', handleSave);
    return () => document.removeEventListener('keydown', handleSave);
  }, [handleManualSave]);

  return {
    handleManualSave,
    setupKeyboardShortcuts
  };
};
