
/* Element Interactions and Highlighting */

/* Current element highlighting */
.ProseMirror p:focus-within,
.ProseMirror h1:focus-within {
  background: hsl(var(--primary) / 0.1);
  border-radius: 4px;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.2);
  transition: all 0.2s ease;
}

/* Element type indicators (only show on hover/focus) */
.ProseMirror p::before,
.ProseMirror h1::before {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.ProseMirror p:hover::before,
.ProseMirror h1:hover::before,
.ProseMirror p:focus-within::before,
.ProseMirror h1:focus-within::before {
  opacity: 1;
}

/* Placeholder text */
.ProseMirror p:empty::after {
  content: attr(data-placeholder);
  color: hsl(var(--muted-foreground));
  pointer-events: none;
  font-style: italic;
}

/* Smart suggestions overlay */
.element-suggestion {
  position: absolute;
  background: hsl(var(--popover));
  border: 1px solid hsl(var(--border));
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 11px;
  color: hsl(var(--popover-foreground));
  z-index: 1000;
  backdrop-filter: blur(8px);
  box-shadow: var(--shadow-lg);
}

/* Smooth animations for professional feel */
.ProseMirror * {
  transition: color 0.15s ease, background-color 0.15s ease, border-color 0.15s ease;
}
