
/* Responsive Design for Mobile and Tablet */

@media (max-width: 768px) {
  .ProseMirror {
    padding: 20px;
    font-size: 11pt;
  }
  
  .ProseMirror .character,
  .ProseMirror .dialogue,
  .ProseMirror .parenthetical {
    margin-left: 0;
    max-width: 100%;
    text-align: left;
  }
  
  .ProseMirror .transition {
    text-align: left;
  }
  
  .ProseMirror p::before,
  .ProseMirror h1::before {
    display: none;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .ProseMirror {
    padding: 30px 40px;
    max-width: 7.5in;
  }
  
  .ProseMirror .character {
    margin-left: 1.8in;
  }
  
  .ProseMirror .dialogue {
    margin-left: 1.3in;
    max-width: 3in;
  }
  
  .ProseMirror .parenthetical {
    margin-left: 1.6in;
    max-width: 2in;
  }
}

/* Large screen optimizations */
@media (min-width: 1400px) {
  .ProseMirror {
    max-width: 9in;
    padding: 50px 80px;
  }
}
