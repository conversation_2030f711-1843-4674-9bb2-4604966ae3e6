
/* Base ProseMirror Editor Styles */
.ProseMirror {
  outline: none;
  padding: 40px 60px;
  line-height: 1.5;
  font-family: 'Courier New', 'Courier', monospace;
  font-size: 12pt;
  color: hsl(var(--foreground));
  background: transparent;
  min-height: 600px;
  max-width: 8.5in;
  margin: 0 auto;
  position: relative;
}

.ProseMirror:focus {
  outline: none;
}

/* Page guidelines for 8.5x11 format */
.ProseMirror::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, 
    transparent 1in, 
    hsl(var(--primary)) 0.1 1in, 
    hsl(var(--primary)) 0.1 1.1in, 
    transparent 1.1in, 
    transparent 7.5in, 
    hsl(var(--primary)) 0.1 7.5in, 
    hsl(var(--primary)) 0.1 7.6in, 
    transparent 7.6in);
  pointer-events: none;
  z-index: -1;
}

/* Focus indicators for accessibility */
.ProseMirror:focus-visible {
  box-shadow: 0 0 0 3px hsl(var(--ring));
  border-radius: 8px;
}

/* Selection and cursor styles */
.ProseMirror-selectednode {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 4px;
}
