
/* Professional Screenplay Element Styles */

.ProseMirror .scene-heading {
  text-transform: uppercase;
  font-weight: bold;
  margin: 24px 0 12px 0;
  text-align: left;
  color: hsl(var(--foreground));
  border-bottom: 2px solid hsl(var(--primary));
  padding-bottom: 8px;
  font-size: 12pt;
  letter-spacing: 0.5px;
  position: relative;
}

.ProseMirror .scene-heading::before {
  content: 'SCENE';
  position: absolute;
  left: -45px;
  top: 0;
  font-size: 8pt;
  color: hsl(var(--muted-foreground));
  font-weight: normal;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.ProseMirror .action {
  margin: 12px 0;
  text-align: left;
  color: hsl(var(--foreground));
  line-height: 1.6;
  font-size: 12pt;
  max-width: 6in;
  position: relative;
}

.ProseMirror .action::before {
  content: 'ACTION';
  position: absolute;
  left: -45px;
  top: 0;
  font-size: 8pt;
  color: hsl(var(--muted-foreground));
  font-weight: normal;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  opacity: 0.7;
}

.ProseMirror .character {
  text-transform: uppercase;
  font-weight: bold;
  text-align: left;
  margin: 24px 0 6px 2.2in;
  color: hsl(var(--primary));
  max-width: 4in;
  font-size: 12pt;
  letter-spacing: 1px;
  position: relative;
}

.ProseMirror .character::before {
  content: 'CHAR';
  position: absolute;
  left: -45px;
  top: 0;
  font-size: 8pt;
  color: hsl(var(--muted-foreground));
  font-weight: normal;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

.ProseMirror .dialogue {
  text-align: left;
  margin: 0 0 12px 1.7in;
  max-width: 3.5in;
  color: hsl(var(--foreground));
  line-height: 1.5;
  font-size: 12pt;
  position: relative;
}

.ProseMirror .dialogue::before {
  content: 'DIAL';
  position: absolute;
  left: -45px;
  top: 0;
  font-size: 8pt;
  color: hsl(var(--muted-foreground));
  font-weight: normal;
  writing-mode: vertical-lr;
  text-orientation: mixed;
  opacity: 0.7;
}

.ProseMirror .parenthetical {
  text-align: left;
  margin: 6px 0 6px 2in;
  max-width: 2.5in;
  font-style: italic;
  color: hsl(var(--muted-foreground));
  font-size: 11pt;
  position: relative;
}

.ProseMirror .parenthetical::before {
  content: '(';
  font-size: 12pt;
  margin-right: 2px;
}

.ProseMirror .parenthetical::after {
  content: ')';
  font-size: 12pt;
  margin-left: 2px;
}

.ProseMirror .transition {
  text-transform: uppercase;
  font-weight: bold;
  text-align: right;
  margin: 24px 0 12px 0;
  color: hsl(var(--foreground));
  border-top: 1px solid hsl(var(--border));
  padding-top: 8px;
  font-size: 12pt;
  letter-spacing: 0.5px;
  position: relative;
}

.ProseMirror .transition::before {
  content: 'TRANS';
  position: absolute;
  right: -50px;
  top: 0;
  font-size: 8pt;
  color: hsl(var(--muted-foreground));
  font-weight: normal;
  writing-mode: vertical-lr;
  text-orientation: mixed;
}
