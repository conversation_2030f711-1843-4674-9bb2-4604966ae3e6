
import { keymap } from 'prosemirror-keymap';
import { undo, redo } from 'prosemirror-history';
import { baseKeymap } from 'prosemirror-commands';
import {
  insertSceneHeading,
  insertAction,
  insertCharacter,
  insertDialogue,
  insertParenthetical,
  insertTransition,
  toggleBold,
  toggleItalic,
  toggleUnderline,
  handleTab,
  handleEnter,
  handleShiftEnter,
  autoCapitalizeCharacter
} from './commands';

// Professional screenwriting keymap - matches industry standards
export const screenplayKeymap = keymap({
  // Basic editing
  'Mod-z': undo,
  'Mod-y': redo,
  'Mod-Shift-z': redo,
  
  // Text formatting (limited in screenwriting)
  'Mod-b': toggleBold,
  'Mod-i': toggleItalic,
  'Mod-u': toggleUnderline,
  
  // Core screenplay shortcuts (industry standard)
  'Mod-1': insertSceneHeading,    // Scene Heading
  'Mod-2': insertAction,          // Action
  'Mod-3': insert<PERSON><PERSON><PERSON>,       // Character
  'Mod-4': insertDialogue,        // Dialogue
  'Mod-5': insertParenthetical,   // Parenthetical
  'Mod-6': insertTransition,      // Transition
  
  // Alternative mnemonics (more intuitive for writers)
  'Mod-h': insertSceneHeading,    // (H)eading
  'Mod-a': insertAction,          // (A)ction
  'Mod-c': insertCharacter,       // (C)haracter
  'Mod-d': insertDialogue,        // (D)ialogue
  'Mod-p': insertParenthetical,   // (P)arenthetical
  'Mod-t': insertTransition,      // (T)ransition
  
  // Smart navigation
  'Tab': handleTab,               // Smart element flow
  'Enter': handleEnter,           // Smart element transitions
  'Shift-Enter': handleShiftEnter, // Same element type
  
  // Character auto-formatting
  'Mod-Shift-c': autoCapitalizeCharacter,
  
  // Quick element access (Final Draft style)
  'Ctrl-Shift-h': insertSceneHeading,
  'Ctrl-Shift-a': insertAction,
  'Ctrl-Shift-c': insertCharacter,
  'Ctrl-Shift-d': insertDialogue,
  'Ctrl-Shift-p': insertParenthetical,
  'Ctrl-Shift-t': insertTransition,
  
  // Combine with base keymap for standard text editing
  ...baseKeymap
});
