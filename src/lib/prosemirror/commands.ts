
import { EditorState, Transaction, Selection } from 'prosemirror-state';
import { toggleMark, setBlockType } from 'prosemirror-commands';
import { screenplaySchema } from './schema';
import { Fragment } from 'prosemirror-model';

// Screenplay element insertion commands
export const insertSceneHeading = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.scene_heading)(state, dispatch);
};

export const insertAction = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.action)(state, dispatch);
};

export const insertCharacter = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.character)(state, dispatch);
};

export const insertDialogue = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.dialogue)(state, dispatch);
};

export const insertParenthetical = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.parenthetical)(state, dispatch);
};

export const insertTransition = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  return setBlockType(screenplaySchema.nodes.transition)(state, dispatch);
};

// Text formatting commands
export const toggleBold = toggleMark(screenplaySchema.marks.strong);
export const toggleItalic = toggleMark(screenplaySchema.marks.emphasis);
export const toggleUnderline = toggleMark(screenplaySchema.marks.underline);

// Smart Tab behavior for screenplay flow
export const handleTab = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { $from } = state.selection;
  const currentNode = $from.parent;
  
  // Tab flow: Scene Heading -> Action -> Character -> Dialogue -> Parenthetical -> Action
  if (currentNode.type === screenplaySchema.nodes.scene_heading) {
    return insertAction(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.action) {
    return insertCharacter(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.character) {
    return insertDialogue(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.dialogue) {
    return insertParenthetical(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.parenthetical) {
    return insertAction(state, dispatch);
  }
  
  return false;
};

// Smart Enter key behavior - the heart of professional screenwriting software
export const handleEnter = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { $from } = state.selection;
  const currentNode = $from.parent;
  const currentText = currentNode.textContent.trim();
  
  // If current element is empty, cycle through common elements
  if (!currentText) {
    if (currentNode.type === screenplaySchema.nodes.action) {
      return insertCharacter(state, dispatch);
    } else if (currentNode.type === screenplaySchema.nodes.character) {
      return insertAction(state, dispatch);
    } else if (currentNode.type === screenplaySchema.nodes.dialogue) {
      return insertAction(state, dispatch);
    } else if (currentNode.type === screenplaySchema.nodes.parenthetical) {
      return insertDialogue(state, dispatch);
    }
  }
  
  // Smart transitions based on content and context
  if (currentNode.type === screenplaySchema.nodes.scene_heading) {
    return insertAction(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.character) {
    return insertDialogue(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.dialogue) {
    // If dialogue ends with a question or exclamation, suggest character response
    if (currentText.match(/[!?]$/)) {
      return insertCharacter(state, dispatch);
    }
    return insertAction(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.parenthetical) {
    return insertDialogue(state, dispatch);
  } else if (currentNode.type === screenplaySchema.nodes.action) {
    // Smart action flow - if it looks like a scene transition, suggest scene heading
    if (currentText.match(/\b(CUT TO|FADE TO|DISSOLVE TO|MATCH CUT|SMASH CUT):/i)) {
      return insertTransition(state, dispatch);
    }
    return insertAction(state, dispatch);
  }
  
  return false;
};

// Shift+Enter always creates a new paragraph of the same type
export const handleShiftEnter = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { $from } = state.selection;
  const currentNode = $from.parent;
  
  if (dispatch) {
    const tr = state.tr;
    const pos = $from.after();
    const newNode = currentNode.type.create(currentNode.attrs, Fragment.empty);
    tr.insert(pos, newNode);
    tr.setSelection(Selection.near(tr.doc.resolve(pos + 1)));
    dispatch(tr);
  }
  
  return true;
};

// Quick character name completion
export const handleCharacterCompletion = (characterName: string) => {
  return (state: EditorState, dispatch?: (tr: Transaction) => void) => {
    if (dispatch) {
      const tr = state.tr;
      const { from, to } = state.selection;
      tr.replaceWith(from, to, screenplaySchema.text(characterName.toUpperCase()));
      dispatch(tr);
    }
    return true;
  };
};

// Auto-capitalize character names
export const autoCapitalizeCharacter = (state: EditorState, dispatch?: (tr: Transaction) => void) => {
  const { $from } = state.selection;
  const currentNode = $from.parent;
  
  if (currentNode.type === screenplaySchema.nodes.character && dispatch) {
    const tr = state.tr;
    const text = currentNode.textContent.toUpperCase();
    const start = $from.start();
    const end = $from.end();
    tr.replaceWith(start, end, screenplaySchema.text(text));
    dispatch(tr);
    return true;
  }
  
  return false;
};
