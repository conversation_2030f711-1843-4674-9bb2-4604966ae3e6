
import React from 'react';

interface EditorStatusBarProps {
  wordCount: number;
  isSaving: boolean;
  hasUnsavedChanges: boolean;
}

const EditorStatusBar: React.FC<EditorStatusBarProps> = ({
  wordCount,
  isSaving,
  hasUnsavedChanges
}) => {
  return (
    <div className="absolute bottom-4 left-4 right-4 bg-slate-900/90 backdrop-blur-sm rounded-lg border border-slate-700 p-2">
      <div className="flex items-center justify-between text-xs text-slate-400">
        <div className="flex items-center space-x-4">
          <span>{wordCount.toLocaleString()} words</span>
          <span>~{Math.ceil(wordCount / 250)} pages</span>
          <div className="flex items-center space-x-1">
            <div className={`w-2 h-2 rounded-full ${
              isSaving ? 'bg-yellow-500 animate-pulse' : 
              hasUnsavedChanges ? 'bg-orange-500' : 'bg-green-500'
            }`} />
            <span>
              {isSaving ? 'Saving...' : hasUnsavedChanges ? 'Unsaved' : 'Saved'}
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          <span>Tab: Next Element</span>
          <span>Enter: Smart Flow</span>
          <span>Shift+Enter: Same Type</span>
        </div>
      </div>
    </div>
  );
};

export default EditorStatusBar;
