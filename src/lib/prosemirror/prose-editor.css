
/* Professional Screenwriting Editor Styles - Main Entry Point */

/* Import all modular CSS files */
@import './styles/base-editor.css';
@import './styles/screenplay-elements.css';
@import './styles/interactions.css';
@import './styles/responsive.css';

/* Additional editor-specific styles that don't fit in other modules */
.prose-editor {
  width: 100%;
  height: 100%;
}

.prose-mirror-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.prose-mirror-editor {
  flex: 1;
  overflow: auto;
}

/* Cinema scrollbar for consistency with the design system */
.cinema-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.cinema-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

.cinema-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

.cinema-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}
