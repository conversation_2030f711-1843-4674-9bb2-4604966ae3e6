
import React, { useEffect, useState, useCallback, memo, useMemo } from 'react';
import SmartFormattingToolbar from '@/components/prosemirror/SmartFormattingToolbar';
import EditorStatusBar from './components/EditorStatusBar';
import { useEditorState } from './hooks/useEditorState';
import { useAutoSave } from './hooks/useAutoSave';
import { useSaveOperations } from './hooks/useSaveOperations';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';
import './prose-editor.css';

interface ProseMirrorEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  onSave?: () => void;
  className?: string;
  placeholder?: string;
  showToolbar?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

const ProseMirrorEditor: React.FC<ProseMirrorEditorProps> = memo(({
  content = '',
  onChange,
  onSave,
  className = '',
  placeholder = 'FADE IN:\n\nEXT. LOCATION - DAY\n\nStart writing your screenplay here...',
  showToolbar = true,
  autoSave = true,
  autoSaveDelay = 2000
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const { useDebounce } = usePerformanceOptimization();

  // Debounce onChange to prevent excessive calls during typing
  const debouncedOnChange = useCallback(
    useDebounce((newContent: string) => {
      onChange?.(newContent);
    }, 150),
    [onChange]
  );

  // Memoize editor configuration to prevent recreation
  const editorConfig = useMemo(() => ({
    content,
    placeholder,
    className,
    autoSave,
    autoSaveDelay
  }), [content, placeholder, className, autoSave, autoSaveDelay]);

  // Auto-save hook
  const { triggerAutoSave, clearAutoSaveTimeout } = useAutoSave({
    onSave,
    autoSave,
    autoSaveDelay,
    hasUnsavedChanges,
    isSaving,
    setIsSaving,
    setHasUnsavedChanges
  });

  // Save operations hook
  const { setupKeyboardShortcuts } = useSaveOperations({
    onSave,
    isSaving,
    setIsSaving,
    setHasUnsavedChanges
  });

  // Memoized word count handler
  const handleWordCountChange = useCallback((count: number) => {
    setWordCount(count);
  }, []);

  // Memoized unsaved change handler
  const handleUnsavedChange = useCallback((hasChanges: boolean) => {
    setHasUnsavedChanges(hasChanges);
  }, []);

  // Editor state hook with optimized onChange
  const {
    editorRef,
    viewRef,
    isReady,
    initializeEditor,
    updateContent,
    destroyEditor
  } = useEditorState({
    content: editorConfig.content,
    placeholder: editorConfig.placeholder,
    onChange: debouncedOnChange,
    onSave,
    className: editorConfig.className,
    onWordCountChange: handleWordCountChange,
    onUnsavedChange: handleUnsavedChange,
    triggerAutoSave
  });

  useEffect(() => {
    const view = initializeEditor();
    const cleanupKeyboardShortcuts = setupKeyboardShortcuts();

    return () => {
      cleanupKeyboardShortcuts();
      clearAutoSaveTimeout();
      destroyEditor();
    };
  }, [initializeEditor, setupKeyboardShortcuts, clearAutoSaveTimeout, destroyEditor]);

  // Update content when prop changes (debounced to prevent excessive updates)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      updateContent(content);
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [content, updateContent]);

  // Memoized toolbar component
  const toolbar = useMemo(() => {
    if (!showToolbar) return null;
    return (
      <SmartFormattingToolbar
        editorView={viewRef.current}
        className="flex-shrink-0"
      />
    );
  }, [showToolbar, viewRef.current]);

  // Memoized status bar
  const statusBar = useMemo(() => (
    <EditorStatusBar
      wordCount={wordCount}
      isSaving={isSaving}
      hasUnsavedChanges={hasUnsavedChanges}
    />
  ), [wordCount, isSaving, hasUnsavedChanges]);

  return (
    <div className="prose-mirror-container h-full flex flex-col bg-slate-950">
      {toolbar}

      <div className="flex-1 relative overflow-hidden bg-gradient-to-br from-slate-900 via-slate-950 to-slate-900">
        <div
          ref={editorRef}
          className={`prose-mirror-editor border-0 h-full ${className}`}
        />

        {statusBar}
      </div>
    </div>
  );
});

ProseMirrorEditor.displayName = 'ProseMirrorEditor';

export default ProseMirrorEditor;
