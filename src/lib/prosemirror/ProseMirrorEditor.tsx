
import React, { useEffect, useState, useCallback } from 'react';
import SmartFormattingToolbar from '@/components/prosemirror/SmartFormattingToolbar';
import EditorStatusBar from './components/EditorStatusBar';
import { useEditorState } from './hooks/useEditorState';
import { useAutoSave } from './hooks/useAutoSave';
import { useSaveOperations } from './hooks/useSaveOperations';
import './prose-editor.css';

interface ProseMirrorEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  onSave?: () => void;
  className?: string;
  placeholder?: string;
  showToolbar?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

const ProseMirrorEditor: React.FC<ProseMirrorEditorProps> = ({
  content = '',
  onChange,
  onSave,
  className = '',
  placeholder = 'FADE IN:\n\nEXT. LOCATION - DAY\n\nStart writing your screenplay here...',
  showToolbar = true,
  autoSave = true,
  autoSaveDelay = 2000
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  // Auto-save hook
  const { triggerAutoSave, clearAutoSaveTimeout } = useAutoSave({
    onSave,
    autoSave,
    autoSaveDelay,
    hasUnsavedChanges,
    isSaving,
    setIsSaving,
    setHasUnsavedChanges
  });

  // Save operations hook
  const { setupKeyboardShortcuts } = useSaveOperations({
    onSave,
    isSaving,
    setIsSaving,
    setHasUnsavedChanges
  });

  // Editor state hook
  const {
    editorRef,
    viewRef,
    isReady,
    initializeEditor,
    updateContent,
    destroyEditor
  } = useEditorState({
    content,
    placeholder,
    onChange,
    onSave,
    className,
    onWordCountChange: setWordCount,
    onUnsavedChange: setHasUnsavedChanges,
    triggerAutoSave
  });

  useEffect(() => {
    const view = initializeEditor();
    const cleanupKeyboardShortcuts = setupKeyboardShortcuts();

    return () => {
      cleanupKeyboardShortcuts();
      clearAutoSaveTimeout();
      destroyEditor();
    };
  }, [initializeEditor, setupKeyboardShortcuts, clearAutoSaveTimeout, destroyEditor]);

  // Update content when prop changes
  useEffect(() => {
    updateContent(content);
  }, [content, updateContent]);

  return (
    <div className="prose-mirror-container h-full flex flex-col bg-slate-950">
      {showToolbar && (
        <SmartFormattingToolbar 
          editorView={viewRef.current}
          className="flex-shrink-0"
        />
      )}
      
      <div className="flex-1 relative overflow-hidden bg-gradient-to-br from-slate-900 via-slate-950 to-slate-900">
        <div 
          ref={editorRef} 
          className={`prose-mirror-editor border-0 h-full ${className}`}
        />
        
        <EditorStatusBar 
          wordCount={wordCount}
          isSaving={isSaving}
          hasUnsavedChanges={hasUnsavedChanges}
        />
      </div>
    </div>
  );
};

export default ProseMirrorEditor;
