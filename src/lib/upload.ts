import { logger } from './logger';

export interface UploadOptions {
  url: string;
  method?: 'POST' | 'PUT' | 'PATCH';
  headers?: Record<string, string>;
  maxFileSize?: number;
  allowedTypes?: string[];
  maxFiles?: number;
  chunkSize?: number;
  retryAttempts?: number;
  retryDelay?: number;
  onProgress?: (progress: number) => void;
  onSuccess?: (response: any) => void;
  onError?: (error: Error) => void;
}

export interface UploadFile {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: Error;
}

export class UploadManager {
  private static instance: UploadManager;
  private uploads: Map<string, UploadFile> = new Map();
  private defaultOptions: UploadOptions = {
    url: '',
    method: 'POST',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['*'],
    maxFiles: 1,
    chunkSize: 1024 * 1024, // 1MB
    retryAttempts: 3,
    retryDelay: 1000,
  };

  private constructor() {}

  static getInstance(): UploadManager {
    if (!UploadManager.instance) {
      UploadManager.instance = new UploadManager();
    }
    return UploadManager.instance;
  }

  // Validate file before upload
  private validateFile(file: File, options: UploadOptions): Error | null {
    if (options.maxFileSize && file.size > options.maxFileSize) {
      return new Error(`File size exceeds maximum limit of ${options.maxFileSize} bytes`);
    }

    if (options.allowedTypes && options.allowedTypes[0] !== '*') {
      const fileType = file.type.split('/')[0];
      if (!options.allowedTypes.includes(fileType)) {
        return new Error(`File type ${fileType} is not allowed`);
      }
    }

    return null;
  }

  // Generate unique ID for upload
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  // Upload a single file
  async uploadFile(file: File, options: Partial<UploadOptions>): Promise<string> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const error = this.validateFile(file, mergedOptions);
    if (error) {
      throw error;
    }

    const id = this.generateId();
    const uploadFile: UploadFile = {
      id,
      file,
      progress: 0,
      status: 'pending',
    };

    this.uploads.set(id, uploadFile);
    this.startUpload(id, mergedOptions);
    return id;
  }

  // Upload multiple files
  async uploadFiles(files: File[], options: Partial<UploadOptions>): Promise<string[]> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    if (mergedOptions.maxFiles && files.length > mergedOptions.maxFiles) {
      throw new Error(`Maximum ${mergedOptions.maxFiles} files allowed`);
    }

    const ids = await Promise.all(
      files.map(file => this.uploadFile(file, mergedOptions))
    );
    return ids;
  }

  // Start the upload process
  private async startUpload(id: string, options: UploadOptions): Promise<void> {
    const upload = this.uploads.get(id);
    if (!upload) return;

    upload.status = 'uploading';
    const { file, progress } = upload;

    try {
      if (file.size <= options.chunkSize!) {
        // Upload small files in one go
        await this.uploadChunk(id, file, 0, file.size, options);
      } else {
        // Upload large files in chunks
        await this.uploadInChunks(id, file, options);
      }

      upload.status = 'completed';
      upload.progress = 100;
      options.onSuccess?.(upload);
    } catch (error) {
      upload.status = 'error';
      upload.error = error as Error;
      options.onError?.(error as Error);
      logger.error('Upload failed:', error);
    }
  }

  // Upload file in chunks
  private async uploadInChunks(
    id: string,
    file: File,
    options: UploadOptions
  ): Promise<void> {
    const chunkSize = options.chunkSize!;
    const totalChunks = Math.ceil(file.size / chunkSize);

    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      await this.uploadChunk(id, file, start, end, options);
    }
  }

  // Upload a single chunk
  private async uploadChunk(
    id: string,
    file: File,
    start: number,
    end: number,
    options: UploadOptions
  ): Promise<void> {
    const upload = this.uploads.get(id);
    if (!upload) return;

    const chunk = file.slice(start, end);
    const formData = new FormData();
    formData.append('file', chunk);
    formData.append('chunkIndex', String(Math.floor(start / options.chunkSize!)));
    formData.append('totalChunks', String(Math.ceil(file.size / options.chunkSize!)));

    let attempts = 0;
    while (attempts < options.retryAttempts!) {
      try {
        const response = await fetch(options.url, {
          method: options.method,
          headers: options.headers,
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const progress = Math.round((end / file.size) * 100);
        upload.progress = progress;
        options.onProgress?.(progress);
        return;
      } catch (error) {
        attempts++;
        if (attempts === options.retryAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, options.retryDelay));
      }
    }
  }

  // Cancel an upload
  cancelUpload(id: string): void {
    const upload = this.uploads.get(id);
    if (upload && upload.status === 'uploading') {
      upload.status = 'error';
      upload.error = new Error('Upload cancelled');
    }
  }

  // Get upload status
  getUploadStatus(id: string): UploadFile | undefined {
    return this.uploads.get(id);
  }

  // Get all uploads
  getAllUploads(): UploadFile[] {
    return Array.from(this.uploads.values());
  }

  // Clear completed uploads
  clearCompletedUploads(): void {
    for (const [id, upload] of this.uploads.entries()) {
      if (upload.status === 'completed') {
        this.uploads.delete(id);
      }
    }
  }

  // Clear all uploads
  clearAllUploads(): void {
    this.uploads.clear();
  }
}

// Example usage:
/*
const uploadManager = UploadManager.getInstance();

// Upload a single file
const file = new File(['content'], 'test.txt', { type: 'text/plain' });
const id = await uploadManager.uploadFile(file, {
  url: '/api/upload',
  onProgress: (progress) => console.log(`Progress: ${progress}%`),
  onSuccess: (response) => console.log('Upload complete:', response),
  onError: (error) => console.error('Upload failed:', error),
});

// Upload multiple files
const files = [file1, file2, file3];
const ids = await uploadManager.uploadFiles(files, {
  url: '/api/upload',
  maxFiles: 5,
  allowedTypes: ['image/jpeg', 'image/png'],
  maxFileSize: 5 * 1024 * 1024, // 5MB
});

// Check upload status
const status = uploadManager.getUploadStatus(id);
console.log('Upload status:', status);

// Cancel upload
uploadManager.cancelUpload(id);

// Clear completed uploads
uploadManager.clearCompletedUploads();
*/ 