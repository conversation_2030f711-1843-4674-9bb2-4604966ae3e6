
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactNode } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { mockAuthContext, mockOrganizationContext } from './mockData';

// Create test query client
export const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

// Test wrapper component
interface TestWrapperProps {
  children: ReactNode;
  queryClient?: QueryClient;
  authContext?: any;
  organizationContext?: any;
}

export const TestWrapper = ({ 
  children, 
  queryClient = createTestQueryClient(),
  authContext = mockAuthContext,
  organizationContext = mockOrganizationContext
}: TestWrapperProps) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <div data-testid="auth-provider" data-context={JSON.stringify(authContext)}>
          <div data-testid="org-provider" data-context={JSON.stringify(organizationContext)}>
            {children}
          </div>
        </div>
      </BrowserRouter>
    </QueryClientProvider>
  );
};
