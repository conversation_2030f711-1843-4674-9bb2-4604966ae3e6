
import { vi } from 'vitest';

// Mock user for testing
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Test User',
    username: 'testuser'
  }
};

// Mock organization for testing
export const mockOrganization = {
  id: 'test-org-id',
  name: 'Test Organization',
  plan: 'pro-solo' as const,
  user_limit: 5,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  created_by: mockUser.id
};

// Mock auth context value
export const mockAuthContext = {
  user: mockUser,
  session: null,
  loading: false,
  signIn: vi.fn().mockResolvedValue({ error: null }),
  signUp: vi.fn().mockResolvedValue({ error: null }),
  signOut: vi.fn().mockResolvedValue({ error: null }),
  resetPassword: vi.fn().mockResolvedValue({ error: null })
};

// Mock organization context value
export const mockOrganizationContext = {
  currentOrganization: mockOrganization,
  organizations: [mockOrganization],
  isAdmin: true,
  isLoading: false,
  switchOrganization: vi.fn(),
  refetchOrganizations: vi.fn()
};

// Mock Supabase client for testing
export const mockSupabase = {
  auth: {
    getUser: vi.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signInWithPassword: vi.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signUp: vi.fn().mockResolvedValue({ data: { user: mockUser }, error: null }),
    signOut: vi.fn().mockResolvedValue({ error: null }),
    resetPasswordForEmail: vi.fn().mockResolvedValue({ error: null })
  },
  from: vi.fn(() => ({
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({ data: null, error: null }),
    then: vi.fn().mockResolvedValue({ data: [], error: null })
  })),
  functions: {
    invoke: vi.fn().mockResolvedValue({ data: null, error: null })
  }
};

// Test data generators
export const generateMockScene = (overrides = {}) => ({
  id: crypto.randomUUID(),
  title: 'Test Scene',
  content: 'This is test scene content.',
  description: 'Test scene description',
  user_id: mockUser.id,
  org_id: mockOrganization.id,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  order_index: 1,
  duration_minutes: 5,
  ...overrides
});

export const generateMockCoverageReport = (overrides = {}) => ({
  id: crypto.randomUUID(),
  scene_id: crypto.randomUUID(),
  user_id: mockUser.id,
  org_id: mockOrganization.id,
  fidelity_level: 'Standard',
  coverage_report: 'Test coverage report content',
  synopsis: 'Test synopsis',
  strengths: 'Test strengths',
  weaknesses: 'Test weaknesses',
  verdict: 'Test verdict',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides
});

// Mock API responses for testing
export const mockApiResponses = {
  success: { success: true, data: {}, error: null },
  error: { success: false, data: null, error: 'Test error' },
  loading: { success: false, data: null, error: null }
};
