
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient } from '@tanstack/react-query';
import { ReactElement } from 'react';
import { TestWrapper } from './testProviders';

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  authContext?: any;
  organizationContext?: any;
}

export const renderWithProviders = (
  ui: ReactElement,
  options?: CustomRenderOptions
) => {
  const { queryClient, authContext, organizationContext, ...renderOptions } = options || {};
  
  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper 
        queryClient={queryClient}
        authContext={authContext}
        organizationContext={organizationContext}
      >
        {children}
      </TestWrapper>
    ),
    ...renderOptions,
  });
};
