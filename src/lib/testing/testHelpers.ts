
import { within } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';

// Test utilities for async operations
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};

// Test helpers for form interactions
export const fillFormField = async (container: HTMLElement, labelText: string, value: string) => {
  const field = within(container).getByLabelText(labelText);
  await userEvent.clear(field);
  await userEvent.type(field, value);
};

// Performance testing utilities
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  await waitForLoadingToFinish();
  const end = performance.now();
  return end - start;
};
