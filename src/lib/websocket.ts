
import { logger } from './logger';

export interface WebSocketConfig {
  url: string;
  protocols?: string | string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeatInterval?: number;
  timeout?: number;
}

export interface WebSocketMessage {
  type: string;
  payload?: any;
  id?: string;
  timestamp?: number;
}

export type WebSocketEventHandler = (data: any) => void;
export type WebSocketErrorHandler = (error: Error) => void;
export type WebSocketStateHandler = () => void;

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private config: Required<WebSocketConfig>;
  private eventHandlers = new Map<string, WebSocketEventHandler[]>();
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private reconnectAttempts = 0;
  private isConnecting = false;
  private messageQueue: WebSocketMessage[] = [];

  // Event handlers
  private onOpenHandlers: WebSocketStateHandler[] = [];
  private onCloseHandlers: WebSocketStateHandler[] = [];
  private onErrorHandlers: WebSocketErrorHandler[] = [];

  constructor(config: WebSocketConfig) {
    this.config = {
      url: config.url,
      protocols: config.protocols,
      reconnectInterval: config.reconnectInterval || 5000,
      maxReconnectAttempts: config.maxReconnectAttempts || 10,
      heartbeatInterval: config.heartbeatInterval || 30000,
      timeout: config.timeout || 10000
    };
  }

  // Connection management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting) {
        reject(new Error('Connection already in progress'));
        return;
      }

      if (this.isConnected()) {
        resolve();
        return;
      }

      this.isConnecting = true;

      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols);

        const timeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            this.isConnecting = false;
            reject(new Error('Connection timeout'));
          }
        }, this.config.timeout);

        this.ws.onopen = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.flushMessageQueue();
          
          logger.info('WebSocket connected', { url: this.config.url });
          this.onOpenHandlers.forEach(handler => handler());
          resolve();
        };

        this.ws.onclose = (event) => {
          clearTimeout(timeout);
          this.isConnecting = false;
          this.stopHeartbeat();
          
          logger.info('WebSocket disconnected', { 
            code: event.code, 
            reason: event.reason,
            wasClean: event.wasClean 
          });
          
          this.onCloseHandlers.forEach(handler => handler());
          
          // Auto-reconnect if not a clean close
          if (!event.wasClean && this.reconnectAttempts < this.config.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };

        this.ws.onerror = () => {
          clearTimeout(timeout);
          this.isConnecting = false;
          
          const error = new Error('WebSocket connection error');
          logger.error('WebSocket error', error);
          this.onErrorHandlers.forEach(handler => handler(error));
          
          if (this.ws?.readyState === WebSocket.CONNECTING) {
            reject(error);
          }
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            logger.error('Failed to parse WebSocket message', error as Error, {
              data: event.data
            });
          }
        };

      } catch (error) {
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.stopReconnect();
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  // Message handling
  send(message: WebSocketMessage): void {
    const messageWithTimestamp = {
      ...message,
      timestamp: Date.now(),
      id: message.id || this.generateMessageId()
    };

    if (this.isConnected()) {
      try {
        this.ws!.send(JSON.stringify(messageWithTimestamp));
        logger.debug('WebSocket message sent', { type: message.type });
      } catch (error) {
        logger.error('Failed to send WebSocket message', error as Error);
        this.messageQueue.push(messageWithTimestamp);
      }
    } else {
      // Queue message for later sending
      this.messageQueue.push(messageWithTimestamp);
      logger.debug('WebSocket message queued', { type: message.type });
    }
  }

  // Event subscription
  on(eventType: string, handler: WebSocketEventHandler): () => void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    
    this.eventHandlers.get(eventType)!.push(handler);
    
    return () => this.off(eventType, handler);
  }

  off(eventType: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Connection state handlers
  onOpen(handler: WebSocketStateHandler): () => void {
    this.onOpenHandlers.push(handler);
    return () => {
      const index = this.onOpenHandlers.indexOf(handler);
      if (index > -1) this.onOpenHandlers.splice(index, 1);
    };
  }

  onClose(handler: WebSocketStateHandler): () => void {
    this.onCloseHandlers.push(handler);
    return () => {
      const index = this.onCloseHandlers.indexOf(handler);
      if (index > -1) this.onCloseHandlers.splice(index, 1);
    };
  }

  onError(handler: WebSocketErrorHandler): () => void {
    this.onErrorHandlers.push(handler);
    return () => {
      const index = this.onErrorHandlers.indexOf(handler);
      if (index > -1) this.onErrorHandlers.splice(index, 1);
    };
  }

  // State queries
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  getState(): string {
    if (!this.ws) return 'DISCONNECTED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'CONNECTED';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'DISCONNECTED';
      default: return 'UNKNOWN';
    }
  }

  // Private methods
  private handleMessage(message: WebSocketMessage): void {
    logger.debug('WebSocket message received', { type: message.type });
    
    const handlers = this.eventHandlers.get(message.type);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message.payload);
        } catch (error) {
          logger.error('Error in WebSocket message handler', error as Error, {
            messageType: message.type
          });
        }
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimer) return;

    this.reconnectAttempts++;
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1),
      30000 // Max 30 seconds
    );

    logger.info('Scheduling WebSocket reconnect', { 
      attempt: this.reconnectAttempts,
      delay: delay
    });

    this.reconnectTimer = setTimeout(() => {
      this.reconnectTimer = null;
      this.connect().catch(error => {
        logger.error('WebSocket reconnect failed', error);
      });
    }, delay);
  }

  private stopReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping' });
      }
    }, this.config.heartbeatInterval);
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!;
      try {
        this.ws!.send(JSON.stringify(message));
        logger.debug('Queued WebSocket message sent', { type: message.type });
      } catch (error) {
        logger.error('Failed to send queued message', error as Error);
        // Put message back at the front of queue
        this.messageQueue.unshift(message);
        break;
      }
    }
  }

  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Factory function for creating WebSocket connections
export function createWebSocket(config: WebSocketConfig): WebSocketManager {
  return new WebSocketManager(config);
}

// Global WebSocket registry for managing multiple connections
class WebSocketRegistry {
  private connections = new Map<string, WebSocketManager>();

  register(name: string, config: WebSocketConfig): WebSocketManager {
    if (this.connections.has(name)) {
      throw new Error(`WebSocket connection '${name}' already exists`);
    }

    const manager = new WebSocketManager(config);
    this.connections.set(name, manager);
    return manager;
  }

  get(name: string): WebSocketManager | undefined {
    return this.connections.get(name);
  }

  remove(name: string): boolean {
    const manager = this.connections.get(name);
    if (manager) {
      manager.disconnect();
      this.connections.delete(name);
      return true;
    }
    return false;
  }

  disconnectAll(): void {
    this.connections.forEach(manager => manager.disconnect());
    this.connections.clear();
  }

  getAll(): Map<string, WebSocketManager> {
    return new Map(this.connections);
  }
}

export const webSocketRegistry = new WebSocketRegistry();
