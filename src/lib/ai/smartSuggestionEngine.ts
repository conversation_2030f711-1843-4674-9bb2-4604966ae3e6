
import { Con<PERSON><PERSON><PERSON><PERSON><PERSON>, ScreenplayContext, ContentAnalysis } from './contextAnalyzer';

export interface SmartSuggestion {
  id: string;
  type: 'next_element' | 'dialogue_enhancement' | 'action_improvement' | 'character_development' | 'structure';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  example?: string;
  action: () => void;
  confidence: number;
}

export class SmartSuggestionEngine {
  static generateSuggestions(
    content: string, 
    context: ScreenplayContext,
    characters: string[] = []
  ): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    // Next element suggestions
    suggestions.push(...this.generateNextElementSuggestions(context));
    
    // Dialogue enhancement suggestions
    if (context.currentElement === 'dialogue' || context.previousElements.includes('dialogue')) {
      suggestions.push(...this.generateDialogueSuggestions(content, context));
    }
    
    // Action improvement suggestions
    if (context.currentElement === 'action') {
      suggestions.push(...this.generateActionSuggestions(content, context));
    }
    
    // Character development suggestions
    suggestions.push(...this.generateCharacterSuggestions(content, context, characters));
    
    // Structure suggestions
    suggestions.push(...this.generateStructureSuggestions(content, context));
    
    // Sort by priority and confidence
    return suggestions
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        if (priorityDiff !== 0) return priorityDiff;
        return b.confidence - a.confidence;
      })
      .slice(0, 5); // Return top 5 suggestions
  }

  private static generateNextElementSuggestions(context: ScreenplayContext): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    switch (context.currentElement) {
      case 'scene_heading':
        suggestions.push({
          id: 'next-action',
          type: 'next_element',
          priority: 'high',
          title: 'Add Scene Description',
          description: 'Establish the visual setting and atmosphere',
          example: 'A bustling coffee shop filled with morning commuters...',
          action: () => {}, // Will be implemented in the UI component
          confidence: 90
        });
        break;
        
      case 'action':
        if (context.previousElements.filter(el => el === 'action').length >= 3) {
          suggestions.push({
            id: 'add-character',
            type: 'next_element',
            priority: 'high',
            title: 'Introduce Character',
            description: 'Time to bring a character into the scene',
            example: 'SARAH enters, looking determined...',
            action: () => {},
            confidence: 85
          });
        } else {
          suggestions.push({
            id: 'continue-action',
            type: 'next_element',
            priority: 'medium',
            title: 'Continue Action',
            description: 'Develop the scene further with more visual details',
            action: () => {},
            confidence: 70
          });
        }
        break;
        
      case 'character':
        suggestions.push({
          id: 'add-dialogue',
          type: 'next_element',
          priority: 'high',
          title: 'Add Dialogue',
          description: 'What does this character say?',
          action: () => {},
          confidence: 95
        });
        break;
        
      case 'dialogue':
        if (context.dialoguePattern === 'conflict') {
          suggestions.push({
            id: 'escalate-conflict',
            type: 'next_element',
            priority: 'high',
            title: 'Escalate Tension',
            description: 'Add action or another character response to heighten drama',
            action: () => {},
            confidence: 85
          });
        } else {
          suggestions.push({
            id: 'character-response',
            type: 'next_element',
            priority: 'medium',
            title: 'Character Response',
            description: 'How does another character react?',
            action: () => {},
            confidence: 75
          });
        }
        break;
    }
    
    return suggestions;
  }

  private static generateDialogueSuggestions(content: string, context: ScreenplayContext): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    // Analyze recent dialogue
    const lines = content.split('\n').slice(-10);
    const recentDialogue = lines.filter(line => 
      !line.match(/^(INT\.|EXT\.|[A-Z\s]+$|\(.+\)$)/i) && line.trim()
    );
    
    if (recentDialogue.some(line => line.length > 100)) {
      suggestions.push({
        id: 'shorten-dialogue',
        type: 'dialogue_enhancement',
        priority: 'medium',
        title: 'Shorten Dialogue',
        description: 'Long speeches can slow pacing - consider breaking into shorter exchanges',
        example: 'Instead of a long monologue, try: "I can\'t do this." Pause. "Not anymore."',
        action: () => {},
        confidence: 80
      });
    }
    
    if (recentDialogue.every(line => line.endsWith('.'))) {
      suggestions.push({
        id: 'vary-punctuation',
        type: 'dialogue_enhancement',
        priority: 'low',
        title: 'Vary Sentence Types',
        description: 'Mix questions, exclamations, and statements for natural flow',
        example: '"What are you doing here?" vs "You\'re here." vs "I can\'t believe it!"',
        action: () => {},
        confidence: 65
      });
    }
    
    if (context.tonalContext === 'dramatic' && !recentDialogue.some(line => line.includes('...'))) {
      suggestions.push({
        id: 'add-subtext',
        type: 'dialogue_enhancement',
        priority: 'high',
        title: 'Add Subtext',
        description: 'Characters often don\'t say what they really mean in dramatic moments',
        example: 'Instead of "I\'m angry," try "Fine. Whatever you want."',
        action: () => {},
        confidence: 85
      });
    }
    
    return suggestions;
  }

  private static generateActionSuggestions(content: string, context: ScreenplayContext): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    const lines = content.split('\n').slice(-5);
    const actionLines = lines.filter(line => 
      !line.match(/^(INT\.|EXT\.|[A-Z\s]+$|\(.+\)$)/i) && line.trim()
    );
    
    // Check for passive voice
    if (actionLines.some(line => line.includes(' is ') || line.includes(' are '))) {
      suggestions.push({
        id: 'active-voice',
        type: 'action_improvement',
        priority: 'medium',
        title: 'Use Active Voice',
        description: 'Active voice creates more dynamic, engaging action lines',
        example: 'Instead of "The door is opened by Sarah," write "Sarah opens the door."',
        action: () => {},
        confidence: 75
      });
    }
    
    // Check for visual details
    if (actionLines.every(line => line.length < 30)) {
      suggestions.push({
        id: 'add-visual-details',
        type: 'action_improvement',
        priority: 'medium',
        title: 'Add Visual Details',
        description: 'Help readers visualize the scene with specific details',
        example: 'Instead of "He walks in," try "He strides through the doorway, raindrops glistening on his coat."',
        action: () => {},
        confidence: 80
      });
    }
    
    return suggestions;
  }

  private static generateCharacterSuggestions(content: string, context: ScreenplayContext, characters: string[]): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    // Character introduction suggestions
    if (characters.length < 2 && content.length > 500) {
      suggestions.push({
        id: 'introduce-character',
        type: 'character_development',
        priority: 'high',
        title: 'Introduce Another Character',
        description: 'Stories benefit from character interactions and conflict',
        action: () => {},
        confidence: 85
      });
    }
    
    // Character voice consistency
    if (characters.length > 1) {
      suggestions.push({
        id: 'distinct-voices',
        type: 'character_development',
        priority: 'medium',
        title: 'Develop Distinct Voices',
        description: 'Each character should have a unique way of speaking',
        example: 'One character might use short sentences, another might be verbose',
        action: () => {},
        confidence: 70
      });
    }
    
    return suggestions;
  }

  private static generateStructureSuggestions(content: string, context: ScreenplayContext): SmartSuggestion[] {
    const suggestions: SmartSuggestion[] = [];
    
    const lineCount = content.split('\n').filter(line => line.trim()).length;
    
    // Pacing suggestions
    if (lineCount > 50 && !content.includes('CUT TO:') && !content.includes('FADE TO:')) {
      suggestions.push({
        id: 'scene-transition',
        type: 'structure',
        priority: 'low',
        title: 'Consider Scene Transition',
        description: 'Long scenes might benefit from a transition or scene break',
        action: () => {},
        confidence: 60
      });
    }
    
    return suggestions;
  }

  static generateContextualHelp(context: ScreenplayContext): string[] {
    const tips: string[] = [];
    
    switch (context.currentElement) {
      case 'scene_heading':
        tips.push("Scene headings should be in ALL CAPS and specify location and time");
        tips.push("Use INT. for interior locations, EXT. for exterior");
        break;
      case 'action':
        tips.push("Write in present tense and active voice");
        tips.push("Keep action lines concise but visual");
        break;
      case 'character':
        tips.push("Character names should be in ALL CAPS when speaking");
        tips.push("Consider the character's emotional state and motivation");
        break;
      case 'dialogue':
        tips.push("Read dialogue aloud to test if it sounds natural");
        tips.push("Each character should have a distinct voice and way of speaking");
        break;
    }
    
    return tips;
  }
}
