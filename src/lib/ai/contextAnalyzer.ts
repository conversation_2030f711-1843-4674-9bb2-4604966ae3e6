
export interface ScreenplayContext {
  currentElement: string;
  previousElements: string[];
  characters: string[];
  location?: string;
  timeOfDay?: string;
  genre?: string;
  act?: number;
  sceneNumber?: number;
  tonalContext: 'dramatic' | 'comedic' | 'action' | 'romantic' | 'thriller' | 'neutral';
  dialoguePattern?: 'exposition' | 'conflict' | 'subtext' | 'casual';
}

export interface ContentAnalysis {
  strengths: string[];
  weaknesses: string[];
  suggestions: string[];
  characterVoiceConsistency: number;
  pacing: 'too-fast' | 'good' | 'too-slow';
  visualLanguage: number; // 0-100 score
  subtext: number; // 0-100 score
}

export class ContextAnalyzer {
  static analyzeContent(content: string, characters: string[] = []): ScreenplayContext {
    const lines = content.split('\n').filter(line => line.trim());
    const lastLines = lines.slice(-5); // Analyze last 5 lines for context
    
    // Detect current element type
    const currentLine = lines[lines.length - 1] || '';
    const currentElement = this.detectElementType(currentLine);
    
    // Extract previous elements
    const previousElements = lines.slice(-10, -1).map(line => this.detectElementType(line));
    
    // Detect location and time
    const sceneHeadings = lines.filter(line => 
      line.match(/^(INT\.|EXT\.)/i)
    );
    const lastSceneHeading = sceneHeadings[sceneHeadings.length - 1];
    const location = this.extractLocation(lastSceneHeading);
    const timeOfDay = this.extractTimeOfDay(lastSceneHeading);
    
    // Analyze tonal context
    const tonalContext = this.analyzeTone(content);
    
    // Detect dialogue patterns
    const dialoguePattern = this.analyzeDialoguePattern(lastLines);
    
    return {
      currentElement,
      previousElements,
      characters,
      location,
      timeOfDay,
      tonalContext,
      dialoguePattern
    };
  }

  static analyzeContentQuality(content: string): ContentAnalysis {
    const lines = content.split('\n').filter(line => line.trim());
    
    // Analyze visual language (show vs tell)
    const visualLanguage = this.assessVisualLanguage(content);
    
    // Analyze subtext in dialogue
    const subtext = this.assessSubtext(content);
    
    // Check character voice consistency
    const characterVoiceConsistency = this.assessCharacterVoice(content);
    
    // Analyze pacing
    const pacing = this.assessPacing(lines);
    
    // Generate specific suggestions
    const suggestions = this.generateSpecificSuggestions(content, {
      visualLanguage,
      subtext,
      characterVoiceConsistency,
      pacing
    });
    
    return {
      strengths: this.identifyStrengths(content),
      weaknesses: this.identifyWeaknesses(content),
      suggestions,
      characterVoiceConsistency,
      pacing,
      visualLanguage,
      subtext
    };
  }

  private static detectElementType(line: string): string {
    if (!line.trim()) return 'empty';
    if (line.match(/^(INT\.|EXT\.)/i)) return 'scene_heading';
    if (line.match(/^[A-Z\s]+$/) && line.length < 30) return 'character';
    if (line.match(/^\(.+\)$/)) return 'parenthetical';
    if (line.match(/^(FADE TO:|CUT TO:|DISSOLVE TO:)/i)) return 'transition';
    return 'action';
  }

  private static extractLocation(sceneHeading?: string): string | undefined {
    if (!sceneHeading) return undefined;
    const match = sceneHeading.match(/^(INT\.|EXT\.)\s+([^-]+)/i);
    return match ? match[2].trim() : undefined;
  }

  private static extractTimeOfDay(sceneHeading?: string): string | undefined {
    if (!sceneHeading) return undefined;
    const match = sceneHeading.match(/-\s*(.+)$/i);
    return match ? match[1].trim().toLowerCase() : undefined;
  }

  private static analyzeTone(content: string): ScreenplayContext['tonalContext'] {
    const lowerContent = content.toLowerCase();
    
    if (lowerContent.includes('explosion') || lowerContent.includes('chase') || lowerContent.includes('fight')) {
      return 'action';
    }
    if (lowerContent.includes('laugh') || lowerContent.includes('joke') || lowerContent.includes('funny')) {
      return 'comedic';
    }
    if (lowerContent.includes('love') || lowerContent.includes('kiss') || lowerContent.includes('romance')) {
      return 'romantic';
    }
    if (lowerContent.includes('suspense') || lowerContent.includes('danger') || lowerContent.includes('threat')) {
      return 'thriller';
    }
    if (lowerContent.includes('cry') || lowerContent.includes('death') || lowerContent.includes('tragic')) {
      return 'dramatic';
    }
    
    return 'neutral';
  }

  private static analyzeDialoguePattern(lines: string[]): ScreenplayContext['dialoguePattern'] {
    const dialogueLines = lines.filter(line => 
      !line.match(/^(INT\.|EXT\.|[A-Z\s]+$|\(.+\)$)/i) && line.trim()
    );
    
    if (dialogueLines.some(line => line.includes('explain') || line.includes('tell me about'))) {
      return 'exposition';
    }
    if (dialogueLines.some(line => line.includes('!') || line.includes('?') || line.includes('no'))) {
      return 'conflict';
    }
    if (dialogueLines.some(line => line.length > 100)) {
      return 'subtext';
    }
    
    return 'casual';
  }

  private static assessVisualLanguage(content: string): number {
    const lines = content.split('\n');
    const actionLines = lines.filter(line => 
      !line.match(/^(INT\.|EXT\.|[A-Z\s]+$|\(.+\)$)/i) && line.trim()
    );
    
    let visualScore = 0;
    let totalLines = actionLines.length || 1;
    
    actionLines.forEach(line => {
      // Positive indicators (showing)
      if (line.match(/\b(see|watch|look|observe|notice|glance)\b/i)) visualScore += 2;
      if (line.match(/\b(gesture|nod|smile|frown|point)\b/i)) visualScore += 3;
      if (line.match(/\b(enter|exit|walk|run|move)\b/i)) visualScore += 2;
      
      // Negative indicators (telling)
      if (line.match(/\b(feel|think|remember|realize|understand)\b/i)) visualScore -= 1;
      if (line.match(/\b(suddenly|immediately|quickly)\b/i)) visualScore -= 1;
    });
    
    return Math.max(0, Math.min(100, (visualScore / totalLines) * 20 + 50));
  }

  private static assessSubtext(content: string): number {
    const dialogueLines = content.split('\n').filter(line => 
      !line.match(/^(INT\.|EXT\.|[A-Z\s]+$|\(.+\)$)/i) && line.trim()
    );
    
    let subtextScore = 0;
    
    dialogueLines.forEach(line => {
      // Direct statements reduce subtext
      if (line.match(/\b(I love you|I hate you|I'm angry|I'm sad)\b/i)) subtextScore -= 2;
      
      // Indirect communication increases subtext
      if (line.includes('...') || line.includes('--')) subtextScore += 1;
      if (line.match(/\b(maybe|perhaps|I suppose|whatever)\b/i)) subtextScore += 1;
      if (line.length < 50 && line.includes('?')) subtextScore += 1;
    });
    
    return Math.max(0, Math.min(100, subtextScore * 10 + 40));
  }

  private static assessCharacterVoice(content: string): number {
    // This is a simplified implementation
    // In a real system, this would analyze speech patterns, vocabulary, etc.
    return 75; // Placeholder score
  }

  private static assessPacing(lines: string[]): ContentAnalysis['pacing'] {
    const avgLineLength = lines.reduce((sum, line) => sum + line.length, 0) / lines.length;
    const shortLines = lines.filter(line => line.length < 30).length;
    const longLines = lines.filter(line => line.length > 80).length;
    
    if (shortLines > longLines * 2) return 'too-fast';
    if (longLines > shortLines * 2) return 'too-slow';
    return 'good';
  }

  private static generateSpecificSuggestions(content: string, analysis: any): string[] {
    const suggestions: string[] = [];
    
    if (analysis.visualLanguage < 60) {
      suggestions.push("Try showing character emotions through actions rather than stating them directly");
      suggestions.push("Add more visual details to help readers 'see' the scene");
    }
    
    if (analysis.subtext < 50) {
      suggestions.push("Consider adding subtext to dialogue - what are characters really saying?");
      suggestions.push("Let characters speak indirectly about their true feelings");
    }
    
    if (analysis.pacing === 'too-fast') {
      suggestions.push("Slow down the pacing with more descriptive action lines");
    } else if (analysis.pacing === 'too-slow') {
      suggestions.push("Tighten the pacing by shortening action descriptions");
    }
    
    return suggestions;
  }

  private static identifyStrengths(content: string): string[] {
    const strengths: string[] = [];
    
    if (content.includes('FADE IN:')) {
      strengths.push("Strong opening with proper screenplay format");
    }
    
    if (content.match(/\b(gesture|nod|smile|frown)\b/gi)) {
      strengths.push("Good use of visual character actions");
    }
    
    return strengths;
  }

  private static identifyWeaknesses(content: string): string[] {
    const weaknesses: string[] = [];
    
    if (content.match(/\b(suddenly|immediately)\b/gi)) {
      weaknesses.push("Overuse of adverbs - consider stronger action verbs");
    }
    
    if (content.split('\n').some(line => line.length > 120)) {
      weaknesses.push("Some action lines are too long - break them into shorter, punchier descriptions");
    }
    
    return weaknesses;
  }
}
