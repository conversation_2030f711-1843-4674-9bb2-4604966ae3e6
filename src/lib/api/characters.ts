
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for character-related operations
 */
export const charactersApi = {
  /**
   * Fetches all characters for the current user's organization
   */
  getCharacters: (orgId?: string) => apiWrapper(async () => {
    let query = supabase
      .from('characters')
      .select('*')
      .order('name');
    
    if (orgId) {
      query = query.eq('org_id', orgId);
    }
    
    const result = await query;
    return result;
  }),

  /**
   * Creates a new character
   */
  createCharacter: (data: { 
    name: string; 
    description?: string; 
    notes?: string;
    org_id?: string;
  }) => apiWrapper(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const result = await supabase
      .from('characters')
      .insert([{ ...data, user_id: user.id }])
      .select()
      .single();
    return result;
  }),

  /**
   * Updates a character
   */
  updateCharacter: (id: string, updates: Partial<{ 
    name: string; 
    description: string; 
    notes: string;
  }>) => apiWrapper(async () => {
    const result = await supabase
      .from('characters')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    return result;
  }),

  /**
   * Deletes a character
   */
  deleteCharacter: (id: string) => apiWrapper(async () => {
    const result = await supabase
      .from('characters')
      .delete()
      .eq('id', id);
    return result;
  })
};
