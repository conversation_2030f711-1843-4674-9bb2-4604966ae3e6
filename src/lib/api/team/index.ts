
// Re-export all types
export type {
  TeamActivity,
  TeamDiscussion,
  DiscussionReply,
  TeamAccess,
  TeamMembership
} from './types';

// Re-export all APIs
export { teamAccessApi } from './access';
export { teamActivitiesApi } from './activities';
export { teamDiscussionsApi } from './discussions';
export { teamMembershipsApi } from './memberships';

// Combined team API object for backward compatibility
export const teamApi = {
  // Team access
  getTeamAccess: async (orgId: string) => {
    const { teamAccessApi } = await import('./access');
    return teamAccessApi.getTeamAccess(orgId);
  },

  // Team activities
  getTeamActivities: async (orgId: string, limit?: number) => {
    const { teamActivitiesApi } = await import('./activities');
    return teamActivitiesApi.getTeamActivities(orgId, limit);
  },
  trackActivity: async (orgId: string, activityType: string, description: string, entityType?: string, entityId?: string, metadata?: any) => {
    const { teamActivitiesApi } = await import('./activities');
    return teamActivitiesApi.trackActivity(orgId, activityType, description, entityType, entityId, metadata);
  },

  // Team discussions
  getDiscussions: async (orgId: string) => {
    const { teamDiscussionsApi } = await import('./discussions');
    return teamDiscussionsApi.getDiscussions(orgId);
  },
  createDiscussion: async (orgId: string, title: string, content: string, discussionType?: string, isAnnouncement?: boolean) => {
    const { teamDiscussionsApi } = await import('./discussions');
    return teamDiscussionsApi.createDiscussion(orgId, title, content, discussionType, isAnnouncement);
  },
  getDiscussionReplies: async (discussionId: string) => {
    const { teamDiscussionsApi } = await import('./discussions');
    return teamDiscussionsApi.getDiscussionReplies(discussionId);
  },
  addReply: async (discussionId: string, content: string) => {
    const { teamDiscussionsApi } = await import('./discussions');
    return teamDiscussionsApi.addReply(discussionId, content);
  },

  // Team memberships
  getProjectTeamMembers: async (projectId: string) => {
    const { teamMembershipsApi } = await import('./memberships');
    return teamMembershipsApi.getProjectTeamMembers(projectId);
  },
  addProjectMember: async (memberData: {
    projectId: string;
    userId: string;
    role: string;
    permissions: any;
    invitedBy: string;
  }) => {
    const { teamMembershipsApi } = await import('./memberships');
    return teamMembershipsApi.addProjectMember(memberData);
  },
  updateMemberRole: async (membershipId: string, role: string, permissions?: any) => {
    const { teamMembershipsApi } = await import('./memberships');
    return teamMembershipsApi.updateMemberRole(membershipId, role, permissions);
  },
  removeProjectMember: async (membershipId: string) => {
    const { teamMembershipsApi } = await import('./memberships');
    return teamMembershipsApi.removeProjectMember(membershipId);
  },
};
