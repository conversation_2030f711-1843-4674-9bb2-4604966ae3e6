
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { TeamDiscussion, DiscussionReply } from './types';

export const teamDiscussionsApi = {
  // Get team discussions
  getDiscussions: async (orgId: string): Promise<ApiResponse<TeamDiscussion[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_discussions')
        .select(`
          *,
          profiles (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('org_id', orgId)
        .order('is_pinned', { ascending: false })
        .order('created_at', { ascending: false });

      // Transform the data to match our TeamDiscussion type
      const transformedData = data?.map(discussion => ({
        ...discussion,
        profiles: discussion.profiles ? {
          full_name: discussion.profiles.full_name,
          username: discussion.profiles.username,
          avatar_url: discussion.profiles.avatar_url
        } : null
      })) || [];

      return { data: transformedData, error };
    });
  },

  // Create discussion
  createDiscussion: async (
    orgId: string,
    title: string,
    content: string,
    discussionType: string = 'general',
    isAnnouncement: boolean = false
  ): Promise<ApiResponse<TeamDiscussion>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('team_discussions')
        .insert({
          org_id: orgId,
          title,
          content,
          created_by: user.user?.id,
          discussion_type: discussionType,
          is_announcement: isAnnouncement
        })
        .select()
        .single();

      return { data, error };
    });
  },

  // Get discussion replies
  getDiscussionReplies: async (discussionId: string): Promise<ApiResponse<DiscussionReply[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_discussion_replies')
        .select(`
          *,
          profiles (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('discussion_id', discussionId)
        .order('created_at', { ascending: true });

      // Transform the data to match our DiscussionReply type
      const transformedData = data?.map(reply => ({
        ...reply,
        profiles: reply.profiles ? {
          full_name: reply.profiles.full_name,
          username: reply.profiles.username,
          avatar_url: reply.profiles.avatar_url
        } : null
      })) || [];

      return { data: transformedData, error };
    });
  },

  // Add reply to discussion
  addReply: async (discussionId: string, content: string): Promise<ApiResponse<DiscussionReply>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('team_discussion_replies')
        .insert({
          discussion_id: discussionId,
          user_id: user.user?.id,
          content
        })
        .select()
        .single();

      return { data, error };
    });
  },
};
