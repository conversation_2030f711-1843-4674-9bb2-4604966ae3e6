
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { TeamActivity } from './types';

export const teamActivitiesApi = {
  // Get team activities
  getTeamActivities: async (orgId: string, limit: number = 20): Promise<ApiResponse<TeamActivity[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_activities')
        .select(`
          *,
          profiles (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('org_id', orgId)
        .order('created_at', { ascending: false })
        .limit(limit);

      // Transform the data to match our TeamActivity type
      const transformedData = data?.map(activity => ({
        ...activity,
        profiles: activity.profiles ? {
          full_name: activity.profiles.full_name,
          username: activity.profiles.username,
          avatar_url: activity.profiles.avatar_url
        } : null
      })) || [];

      return { data: transformedData, error };
    });
  },

  // Track team activity
  trackActivity: async (
    orgId: string,
    activityType: string,
    description: string,
    entityType?: string,
    entityId?: string,
    metadata?: any
  ): Promise<ApiResponse<string>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('track_team_activity', {
          _org_id: orgId,
          _activity_type: activityType,
          _description: description,
          _entity_type: entityType,
          _entity_id: entityId,
          _metadata: metadata || {}
        });

      return { data, error };
    });
  },
};
