
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { TeamAccess } from './types';

export const teamAccessApi = {
  // Get team access for organization
  getTeamAccess: async (orgId: string): Promise<ApiResponse<TeamAccess>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('get_user_team_access', { target_org_id: orgId })
        .single();

      return { data, error };
    });
  },
};
