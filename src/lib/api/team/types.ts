
export interface TeamActivity {
  id: string;
  org_id: string;
  user_id: string;
  activity_type: string;
  entity_type?: string;
  entity_id?: string;
  description: string;
  metadata: any;
  created_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface TeamDiscussion {
  id: string;
  org_id: string;
  title: string;
  content: string;
  created_by: string;
  discussion_type: string;
  is_announcement: boolean;
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  };
  reply_count?: number;
}

export interface DiscussionReply {
  id: string;
  discussion_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  };
}

export interface TeamAccess {
  can_manage_teams: boolean;
  max_teams: number;
  max_members_per_team: number;
  can_access_production: boolean;
  can_create_custom_roles: boolean;
}

export interface TeamMembership {
  id: string;
  project_id: string;
  user_id: string;
  role: string;
  permissions: any;
  invited_by: string;
  joined_at: string;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  };
}
