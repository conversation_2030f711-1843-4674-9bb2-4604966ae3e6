
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

export interface TeamMembership {
  id: string;
  team_id?: string;
  project_id?: string;
  user_id: string;
  role: string;
  permissions: any;
  status: string;
  invited_by: string;
  joined_at: string | null;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name: string | null;
    username: string | null;
    avatar_url: string | null;
  } | null;
}

export const teamMembershipsApi = {
  async getTeamMembers(teamId: string): Promise<ApiResponse<TeamMembership[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .select(`
          *,
          profiles:user_id (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('team_id', teamId);

      return { data: data as any[], error };
    });
  },

  async getProjectTeamMembers(projectId: string): Promise<ApiResponse<TeamMembership[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .select(`
          *,
          profiles:user_id (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('project_id', projectId);

      return { data: data as any[], error };
    });
  },

  async addTeamMember(memberData: {
    teamId: string;
    userId: string;
    role: string;
    permissions: any;
    invitedBy: string;
  }): Promise<ApiResponse<TeamMembership>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .insert({
          team_id: memberData.teamId,
          project_id: null,
          user_id: memberData.userId,
          role: memberData.role,
          permissions: memberData.permissions,
          invited_by: memberData.invitedBy,
          status: 'active'
        })
        .select()
        .single();

      return { data: data as any, error };
    });
  },

  async addProjectMember(memberData: {
    projectId: string;
    userId: string;
    role: string;
    permissions: any;
    invitedBy: string;
  }): Promise<ApiResponse<TeamMembership>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .insert({
          project_id: memberData.projectId,
          team_id: '',
          user_id: memberData.userId,
          role: memberData.role,
          permissions: memberData.permissions,
          invited_by: memberData.invitedBy,
          status: 'active'
        })
        .select()
        .single();

      return { data: data as any, error };
    });
  },

  async updateMemberRole(membershipId: string, role: string, permissions: any): Promise<ApiResponse<TeamMembership>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .update({
          role,
          permissions,
          updated_at: new Date().toISOString()
        })
        .eq('id', membershipId)
        .select(`
          *,
          profiles:user_id (
            full_name,
            username,
            avatar_url
          )
        `)
        .single();

      return { data: data as any, error };
    });
  },

  async removeMember(membershipId: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .delete()
        .eq('id', membershipId);

      return { data, error };
    });
  },

  async removeProjectMember(membershipId: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .delete()
        .eq('id', membershipId);

      return { data, error };
    });
  },

  async getMembersByProject(projectId: string): Promise<ApiResponse<TeamMembership[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('team_memberships')
        .select(`
          *,
          profiles:user_id (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('project_id', projectId);

      return { data: data as any[], error };
    });
  }
};
