
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for scene dependency operations
 */
export const sceneDependenciesApi = {
  /**
   * Fetches dependencies for scenes
   */
  getSceneDependencies: (sceneId?: string) => apiWrapper(async () => {
    let query = supabase
      .from('scene_dependencies')
      .select(`
        *,
        scenes!scene_dependencies_scene_id_fkey(title),
        depends_on_scene:scenes!scene_dependencies_depends_on_scene_id_fkey(title)
      `);
    
    if (sceneId) {
      query = query.eq('scene_id', sceneId);
    }
    
    const result = await query.order('created_at');
    return result;
  }),

  /**
   * Creates a new scene dependency
   */
  createDependency: (data: {
    scene_id: string;
    depends_on_scene_id: string;
    dependency_type?: 'plot' | 'character' | 'location' | 'timeline';
    notes?: string;
  }) => apiWrapper(async () => {
    const result = await supabase
      .from('scene_dependencies')
      .insert([data])
      .select()
      .single();
    return result;
  }),

  /**
   * Updates a scene dependency
   */
  updateDependency: (id: string, updates: Partial<{
    dependency_type: 'plot' | 'character' | 'location' | 'timeline';
    notes: string;
  }>) => apiWrapper(async () => {
    const result = await supabase
      .from('scene_dependencies')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    return result;
  }),

  /**
   * Deletes a scene dependency
   */
  deleteDependency: (id: string) => apiWrapper(async () => {
    const result = await supabase
      .from('scene_dependencies')
      .delete()
      .eq('id', id);
    return result;
  })
};
