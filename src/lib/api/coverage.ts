
import { supabase } from '@/integrations/supabase/client';
import { handleSupabaseError, apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface CoverageReport {
  id: string;
  org_id: string;
  user_id: string;
  scene_id: string;
  fidelity_level: string;
  coverage_report?: string;
  synopsis?: string;
  strengths?: string;
  weaknesses?: string;
  verdict?: string;
  created_at: string;
  updated_at: string;
}

export interface GenerateCoverageData {
  scene_id: string;
  fidelity_level: string;
  org_id: string;
}

export const coverageApi = {
  async getCoverageReports(): Promise<ApiResponse<CoverageReport[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('coverage_reports')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async getCoverageReport(id: string): Promise<ApiResponse<CoverageReport>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('coverage_reports')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async generateCoverage(coverageData: GenerateCoverageData): Promise<ApiResponse<CoverageReport>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('generate_script_coverage', {
          scene_id_param: coverageData.scene_id,
          fidelity_level_param: coverageData.fidelity_level,
          org_id_param: coverageData.org_id
        });

      if (error) throw error;
      
      // The RPC function returns the full CoverageReport object
      const report = data?.[0] as CoverageReport;
      return { data: report || null, error: null };
    });
  },

  async deleteCoverageReport(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('coverage_reports')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  }
};
