
import type { PostgrestError } from '@supabase/supabase-js';
import type { ApiResponse, ApiOperation } from './types';

/**
 * Utility function to handle Supabase errors consistently
 * 
 * @param error - The error from Supabase
 * @returns Formatted error message
 */
export const handleSupabaseError = (error: PostgrestError | Error | null): string => {
  if (!error) return 'Unknown error occurred';
  
  if ('message' in error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

/**
 * Generic API wrapper with consistent error handling and response formatting
 * 
 * @template T
 * @param operation - The async operation to perform
 * @returns Promise with standardized API response
 */
export const apiWrapper = async <T>(
  operation: () => Promise<{ data: T | null; error: PostgrestError | null }>
): Promise<ApiResponse<T>> => {
  try {
    const result = await operation();
    
    if (result.error) {
      const errorMessage = handleSupabaseError(result.error);
      console.error('API Error:', result.error);
      
      return {
        data: null,
        error: errorMessage,
        success: false
      };
    }
    
    return {
      data: result.data,
      error: null,
      success: true
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('API Wrapper Error:', error);
    
    return {
      data: null,
      error: errorMessage,
      success: false
    };
  }
};
