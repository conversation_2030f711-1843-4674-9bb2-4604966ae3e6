
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type Notification = Tables<'notifications'>;
export type NotificationInsert = TablesInsert<'notifications'>;
export type NotificationUpdate = TablesUpdate<'notifications'>;

export const notificationsApi = {
  // Get user's notifications
  getMyNotifications: async (): Promise<ApiResponse<Notification[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Mark notification as read
  markAsRead: async (notificationId: string): Promise<ApiResponse<Notification>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Mark all notifications as read
  markAllAsRead: async (): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('read', false);
      
      return { data: undefined, error };
    });
  },

  // Get unread notification count
  getUnreadCount: async (): Promise<ApiResponse<number>> => {
    return apiWrapper(async () => {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('read', false);
      
      return { data: count || 0, error };
    });
  }
};
