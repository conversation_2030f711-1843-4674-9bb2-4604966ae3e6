
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for location-related operations
 */
export const locationsApi = {
  /**
   * Fetches all locations for the current user's organization
   */
  getLocations: (orgId?: string) => apiWrapper(async () => {
    let query = supabase
      .from('locations')
      .select('*')
      .order('name');
    
    if (orgId) {
      query = query.eq('org_id', orgId);
    }
    
    const result = await query;
    return result;
  }),

  /**
   * Creates a new location
   */
  createLocation: (data: { 
    name: string; 
    description?: string; 
    notes?: string;
    org_id?: string;
  }) => apiWrapper(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const result = await supabase
      .from('locations')
      .insert([{ ...data, user_id: user.id }])
      .select()
      .single();
    return result;
  }),

  /**
   * Updates a location
   */
  updateLocation: (id: string, updates: Partial<{ 
    name: string; 
    description: string; 
    notes: string;
  }>) => apiWrapper(async () => {
    const result = await supabase
      .from('locations')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    return result;
  }),

  /**
   * Deletes a location
   */
  deleteLocation: (id: string) => apiWrapper(async () => {
    const result = await supabase
      .from('locations')
      .delete()
      .eq('id', id);
    return result;
  })
};
