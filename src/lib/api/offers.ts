
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type ScreenplayOffer = Tables<'screenplay_offers'>;
export type ScreenplayOfferInsert = TablesInsert<'screenplay_offers'>;
export type ScreenplayOfferUpdate = TablesUpdate<'screenplay_offers'>;

// Define the joined type for offers with screenplay info
export type ScreenplayOfferWithScreenplay = ScreenplayOffer & {
  screenplay: {
    title: string;
  };
};

export const offersApi = {
  // Create new offer
  createOffer: async (offer: Omit<ScreenplayOfferInsert, 'buyer_id' | 'seller_id'>): Promise<ApiResponse<ScreenplayOffer>> => {
    return apiWrapper(async () => {
      // Get current user
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      // Get screenplay to find seller
      const { data: screenplay, error: screenplayError } = await supabase
        .from('screenplays')
        .select('writer_id')
        .eq('id', offer.screenplay_id)
        .single();

      if (screenplayError) throw screenplayError;

      const { data, error } = await supabase
        .from('screenplay_offers')
        .insert({
          ...offer,
          buyer_id: user.user.id,
          seller_id: screenplay.writer_id
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get offers for a screenplay
  getOffersForScreenplay: async (screenplayId: string): Promise<ApiResponse<ScreenplayOffer[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_offers')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Get offers made by user
  getMyOffers: async (): Promise<ApiResponse<ScreenplayOfferWithScreenplay[]>> => {
    return apiWrapper(async () => {
      // First get the offers
      const { data: offersData, error: offersError } = await supabase
        .from('screenplay_offers')
        .select('*')
        .order('created_at', { ascending: false });

      if (offersError) throw offersError;
      if (!offersData) return { data: [], error: null };

      // Then get screenplay titles for each offer
      const screenplayIds = offersData.map(offer => offer.screenplay_id);
      const { data: screenplaysData, error: screenplaysError } = await supabase
        .from('screenplays')
        .select('id, title')
        .in('id', screenplayIds);

      if (screenplaysError) throw screenplaysError;

      // Combine the data
      const transformedData = offersData.map(offer => {
        const screenplay = screenplaysData?.find(s => s.id === offer.screenplay_id);
        return {
          ...offer,
          screenplay: {
            title: screenplay?.title || 'Unknown Title'
          }
        };
      });

      return { data: transformedData, error: null };
    });
  },

  // Respond to offer
  respondToOffer: async (offerId: string, status: 'accepted' | 'rejected'): Promise<ApiResponse<ScreenplayOffer>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_offers')
        .update({
          status,
          responded_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', offerId)
        .select()
        .single();
      
      return { data, error };
    });
  }
};
