
export const API_VERSION = 'v1';
export const API_BASE_URL = `/api/${API_VERSION}`;

export interface ApiVersionConfig {
  version: string;
  deprecationDate?: string;
  supportedUntil?: string;
  migrationGuide?: string;
}

export const API_VERSIONS: Record<string, ApiVersionConfig> = {
  v1: {
    version: 'v1',
    // Future versions can be added here
  }
};

export function getVersionedEndpoint(endpoint: string, version: string = API_VERSION): string {
  return `/api/${version}${endpoint}`;
}

export function validateApiVersion(version: string): boolean {
  return version in API_VERSIONS;
}

export class ApiVersionManager {
  private currentVersion = API_VERSION;

  setVersion(version: string) {
    if (!validateApiVersion(version)) {
      throw new Error(`Unsupported API version: ${version}`);
    }
    this.currentVersion = version;
  }

  getVersion(): string {
    return this.currentVersion;
  }

  createUrl(endpoint: string): string {
    return getVersionedEndpoint(endpoint, this.currentVersion);
  }

  addVersionHeaders(headers: Record<string, string> = {}): Record<string, string> {
    return {
      ...headers,
      'API-Version': this.currentVersion,
      'Accept': `application/vnd.scriptgenius.${this.currentVersion}+json`
    };
  }
}

export const apiVersionManager = new ApiVersionManager();
