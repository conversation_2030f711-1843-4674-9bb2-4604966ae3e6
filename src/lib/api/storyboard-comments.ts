
import { supabase } from '@/integrations/supabase/client';
import { handleSupabaseError, apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface StoryboardPanelComment {
  id: string;
  panel_id: string;
  user_id: string;
  org_id: string;
  content: string;
  timestamp_position?: number;
  resolved: boolean;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name?: string;
    avatar_url?: string;
  };
}

export interface CreateCommentData {
  panel_id: string;
  org_id: string;
  content: string;
  timestamp_position?: number;
}

export const storyboardCommentsApi = {
  async getComments(panelId: string): Promise<ApiResponse<StoryboardPanelComment[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboard_panel_comments')
        .select('*')
        .eq('panel_id', panelId)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async createComment(commentData: CreateCommentData): Promise<ApiResponse<StoryboardPanelComment>> {
    return apiWrapper(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('storyboard_panel_comments')
        .insert({
          ...commentData,
          user_id: user.id
        })
        .select('*')
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updateComment(id: string, updates: { content?: string; resolved?: boolean }): Promise<ApiResponse<StoryboardPanelComment>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboard_panel_comments')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async deleteComment(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('storyboard_panel_comments')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  }
};
