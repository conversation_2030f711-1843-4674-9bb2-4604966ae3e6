
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for revision-related operations
 */
export const revisionsApi = {
  /**
   * Fetches revisions for a specific scene
   */
  getSceneRevisions: (sceneId: string) => apiWrapper(async () => {
    const result = await supabase
      .from('revisions')
      .select(`
        *,
        profiles(username, full_name)
      `)
      .eq('scene_id', sceneId)
      .order('version_number', { ascending: false });
    return result;
  }),

  /**
   * Creates a new revision
   */
  createRevision: (data: {
    scene_id: string;
    content: string;
    change_summary?: string;
  }) => apiWrapper(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    // Get the current highest version number
    const { data: latestRevision } = await supabase
      .from('revisions')
      .select('version_number')
      .eq('scene_id', data.scene_id)
      .order('version_number', { ascending: false })
      .limit(1)
      .single();

    const nextVersion = (latestRevision?.version_number || 0) + 1;

    const result = await supabase
      .from('revisions')
      .insert([{ 
        ...data, 
        user_id: user.id, 
        version_number: nextVersion 
      }])
      .select()
      .single();
    return result;
  })
};
