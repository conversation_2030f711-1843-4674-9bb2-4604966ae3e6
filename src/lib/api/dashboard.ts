
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface DashboardProject {
  id: string;
  title: string;
  description?: string;
  status: 'active' | 'draft' | 'completed' | 'archived';
  created_at: string;
  updated_at: string;
  collaborator_count: number;
  screenplay_id?: string;
  is_archived: boolean;
}

export interface TierLimits {
  tier_name: string;
  max_active_projects: number;
  max_teams: number;
  max_members_per_team: number;
  max_collaborators_per_project: number;
}

export interface UsageStats {
  active_projects: number;
  total_projects: number;
  team_count: number;
  total_collaborators: number;
  tier_name: string;
  max_active_projects: number;
  max_teams: number;
  max_collaborators_per_project: number;
}

export interface ToolUsageAnalytics {
  tool_name: string;
  usage_count: number;
  last_used: string;
}

export interface RecentActivity {
  id: string;
  type: string;
  description: string;
  created_at: string;
  project_title?: string;
}

const dashboardApi = {
  // Get user's projects
  getUserProjects: async (orgId: string): Promise<ApiResponse<DashboardProject[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('org_id', orgId)
        .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
        .order('updated_at', { ascending: false });

      return { data, error };
    });
  },

  // Create new project
  createProject: async (orgId: string, projectData: Partial<DashboardProject>): Promise<ApiResponse<DashboardProject>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('projects')
        .insert({
          title: projectData.title || '',
          description: projectData.description,
          status: projectData.status || 'draft',
          org_id: orgId,
          user_id: user.user?.id || ''
        })
        .select()
        .single();

      return { data, error };
    });
  },

  // Update project
  updateProject: async (projectId: string, updates: Partial<DashboardProject>): Promise<ApiResponse<DashboardProject>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', projectId)
        .select()
        .single();

      return { data, error };
    });
  },

  // Get tier limits
  getTierLimits: async (orgId: string): Promise<ApiResponse<TierLimits>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('get_user_tier_limits', { target_org_id: orgId })
        .single();

      return { data, error };
    });
  },

  // Get usage statistics
  getUsageStats: async (orgId: string): Promise<ApiResponse<UsageStats>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('get_user_usage_stats', { target_org_id: orgId })
        .single();

      return { data, error };
    });
  },

  // Check if user can create project
  canCreateProject: async (orgId: string): Promise<ApiResponse<boolean>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('can_create_project', { target_org_id: orgId });

      return { data, error };
    });
  },

  // Track tool usage
  trackToolUsage: async (orgId: string, toolName: string, eventData?: any): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('user_analytics')
        .insert({
          user_id: user.user?.id,
          org_id: orgId,
          event_type: 'tool_usage',
          tool_name: toolName,
          event_data: eventData
        });

      return { data, error };
    });
  },

  // Get tool usage analytics
  getToolUsageAnalytics: async (orgId: string, days: number = 30): Promise<ApiResponse<ToolUsageAnalytics[]>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const { data, error } = await supabase
        .from('user_analytics')
        .select('tool_name, created_at')
        .eq('user_id', user.user?.id)
        .eq('org_id', orgId)
        .eq('event_type', 'tool_usage')
        .gte('created_at', cutoffDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) return { data: null, error };

      // Process the data to get usage counts
      const toolUsage: Record<string, { count: number; lastUsed: string }> = {};
      
      data?.forEach(record => {
        if (record.tool_name) {
          if (!toolUsage[record.tool_name]) {
            toolUsage[record.tool_name] = { count: 0, lastUsed: record.created_at };
          }
          toolUsage[record.tool_name].count++;
          if (record.created_at > toolUsage[record.tool_name].lastUsed) {
            toolUsage[record.tool_name].lastUsed = record.created_at;
          }
        }
      });

      const result = Object.entries(toolUsage).map(([tool_name, stats]) => ({
        tool_name,
        usage_count: stats.count,
        last_used: stats.lastUsed
      }));

      return { data: result, error: null };
    });
  },

  // Get recent activity
  getRecentActivity: async (orgId: string, limit: number = 10): Promise<ApiResponse<RecentActivity[]>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('user_analytics')
        .select(`
          id,
          event_type,
          event_data,
          created_at,
          tool_name
        `)
        .eq('user_id', user.user?.id)
        .eq('org_id', orgId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) return { data: null, error };

      const activities: RecentActivity[] = data?.map(record => {
        let projectTitle: string | undefined;
        
        // Safe way to handle event_data which might be Json
        if (record.event_data && typeof record.event_data === 'object' && 'project_title' in record.event_data) {
          projectTitle = record.event_data.project_title as string;
        }

        return {
          id: record.id,
          type: record.event_type,
          description: record.tool_name ? 
            `Used ${record.tool_name}` : 
            record.event_type.replace('_', ' '),
          created_at: record.created_at,
          project_title: projectTitle
        };
      }) || [];

      return { data: activities, error: null };
    });
  },

  // Add to favorites
  addToFavorites: async (favoriteType: 'writer' | 'screenplay', favoriteId: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('user_favorites')
        .insert({
          user_id: user.user?.id,
          favorite_type: favoriteType,
          favorite_id: favoriteId
        });

      return { data, error };
    });
  },

  // Remove from favorites
  removeFromFavorites: async (favoriteType: 'writer' | 'screenplay', favoriteId: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', user.user?.id)
        .eq('favorite_type', favoriteType)
        .eq('favorite_id', favoriteId);

      return { data, error };
    });
  }
};

export { dashboardApi };
