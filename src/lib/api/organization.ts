
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for organization-related operations
 */
export const organizationApi = {
  /**
   * Fetches all organizations for the current user
   */
  getOrganizations: () => apiWrapper(async () => {
    const result = await supabase
      .from('organizations')
      .select('*')
      .order('created_at', { ascending: false });
    return result;
  }),

  /**
   * Fetches organization memberships for the current user
   */
  getMemberships: () => apiWrapper(async () => {
    const result = await supabase
      .from('organization_members')
      .select(`
        id,
        org_id,
        user_id,
        role,
        joined_at,
        profiles (
          username,
          full_name,
          avatar_url
        )
      `)
      .order('joined_at', { ascending: false });
    return result;
  }),

  /**
   * Fetches members for a specific organization
   * 
   * @param orgId - Organization ID
   */
  getOrganizationMembers: (orgId: string) => apiWrapper(async () => {
    const result = await supabase
      .from('organization_members')
      .select(`
        id,
        org_id,
        user_id,
        role,
        joined_at,
        profiles (
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('org_id', orgId)
      .order('joined_at', { ascending: false });
    return result;
  }),

  /**
   * Creates a new organization
   * 
   * @param data - Organization data
   */
  createOrganization: (data: { name: string; plan?: string; user_limit?: number }) => 
    apiWrapper(async () => {
      const result = await supabase
        .from('organizations')
        .insert([{
          name: data.name,
          plan: data.plan || 'starter',
          user_limit: data.user_limit || 1
        }])
        .select()
        .single();
      return result;
    }),

  /**
   * Gets pending invitations for an organization
   * 
   * @param orgId - Organization ID
   */
  getInvitations: (orgId: string) => apiWrapper(async () => {
    const result = await supabase
      .from('invitations')
      .select('*')
      .eq('org_id', orgId)
      .eq('status', 'pending')
      .order('created_at', { ascending: false });
    return result;
  }),

  /**
   * Accepts an invitation
   * 
   * @param invitationId - Invitation ID
   */
  acceptInvitation: (invitationId: string) => apiWrapper(async () => {
    const result = await supabase
      .from('invitations')
      .update({ status: 'accepted' })
      .eq('id', invitationId)
      .select()
      .single();
    return result;
  }),
};
