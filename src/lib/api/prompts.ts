
import { supabase } from '@/integrations/supabase/client';
import { handleSupabaseError, apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface PromptLibraryItem {
  id: string;
  tool_name: string;
  version: string;
  role: string;
  cot_enabled: boolean;
  output_format: string;
  prompt_content?: string;
  last_updated: string;
  created_at: string;
  created_by?: string;
}

export interface CreatePromptData {
  tool_name: string;
  version: string;
  role: string;
  cot_enabled: boolean;
  output_format: string;
  prompt_content?: string;
}

export interface UpdatePromptData extends Partial<CreatePromptData> {
  id: string;
}

export const promptsApi = {
  async getPrompts(): Promise<ApiResponse<PromptLibraryItem[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('prompt_library')
        .select('*')
        .order('last_updated', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async getPrompt(id: string): Promise<ApiResponse<PromptLibraryItem>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('prompt_library')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async createPrompt(promptData: CreatePromptData): Promise<ApiResponse<PromptLibraryItem>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('prompt_library')
        .insert({
          ...promptData,
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updatePrompt(promptData: UpdatePromptData): Promise<ApiResponse<PromptLibraryItem>> {
    return apiWrapper(async () => {
      const { id, ...updateData } = promptData;
      const { data, error } = await supabase
        .from('prompt_library')
        .update({
          ...updateData,
          last_updated: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async deletePrompt(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('prompt_library')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  },

  async duplicatePrompt(id: string, newVersion: string): Promise<ApiResponse<PromptLibraryItem>> {
    return apiWrapper(async () => {
      // First get the original prompt
      const { data: originalPrompt, error: fetchError } = await supabase
        .from('prompt_library')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      // Create a duplicate with new version
      const { data, error } = await supabase
        .from('prompt_library')
        .insert({
          tool_name: originalPrompt.tool_name,
          version: newVersion,
          role: originalPrompt.role,
          cot_enabled: originalPrompt.cot_enabled,
          output_format: originalPrompt.output_format,
          prompt_content: originalPrompt.prompt_content,
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  }
};
