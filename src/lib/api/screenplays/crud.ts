
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { Screenplay, ScreenplayInsert, ScreenplayUpdate } from './types';

export const screenplayCrudApi = {
  // Get published screenplays for marketplace
  getPublishedScreenplays: async (): Promise<ApiResponse<Screenplay[]>> => {
    return apiWrapper(async () => {
      const { data: rawData, error } = await supabase
        .from('screenplays')
        .select(`
          *,
          profiles:writer_id (
            full_name,
            username
          )
        `)
        .eq('status', 'published')
        .order('created_at', { ascending: false });
      
      if (error) {
        return { data: [], error };
      }
      
      // Process and transform the data to match our Screenplay type
      const data = rawData ? rawData.map(screenplay => ({
        ...screenplay,
        profiles: screenplay.profiles,
        views: 0,
        likes: 0,
        comments: 0
      })) : [];
      
      return { data, error: null };
    });
  },

  // Get writer's own screenplays
  getMyScreenplays: async (): Promise<ApiResponse<Screenplay[]>> => {
    return apiWrapper(async () => {
      const { data: rawData, error } = await supabase
        .from('screenplays')
        .select(`
          *,
          profiles:writer_id (
            full_name,
            username
          )
        `)
        .order('created_at', { ascending: false });
      
      if (error) {
        return { data: [], error };
      }
      
      // Process and transform the data to match our Screenplay type
      const data = rawData ? rawData.map(screenplay => ({
        ...screenplay,
        profiles: screenplay.profiles,
        views: 0,
        likes: 0,
        comments: 0
      })) : [];
      
      return { data, error: null };
    });
  },

  // Get single screenplay by ID
  getScreenplay: async (id: string): Promise<ApiResponse<Screenplay>> => {
    return apiWrapper(async () => {
      const { data: rawData, error } = await supabase
        .from('screenplays')
        .select(`
          *,
          profiles:writer_id (
            full_name,
            username
          )
        `)
        .eq('id', id)
        .single();
      
      if (error) {
        return { data: null, error };
      }
      
      // Process and transform the data to match our Screenplay type
      const data = rawData ? {
        ...rawData,
        profiles: rawData.profiles,
        views: 0,
        likes: 0,
        comments: 0
      } : null;
      
      return { data, error: null };
    });
  },

  // Create new screenplay
  createScreenplay: async (screenplay: ScreenplayInsert): Promise<ApiResponse<Screenplay>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplays')
        .insert(screenplay)
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Update screenplay
  updateScreenplay: async (id: string, updates: ScreenplayUpdate): Promise<ApiResponse<Screenplay>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplays')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      return { data, error };
    });
  }
};
