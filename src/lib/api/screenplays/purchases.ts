
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { Screenplay, ScreenplayPurchase } from './types';

export const screenplayPurchasesApi = {
  // Purchase screenplay
  purchaseScreenplay: async (screenplayId: string, price: number): Promise<ApiResponse<ScreenplayPurchase>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_purchases')
        .insert({
          screenplay_id: screenplayId,
          purchase_price: price,
          buyer_id: (await supabase.auth.getUser()).data.user?.id || ''
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get user's purchases
  getMyPurchases: async (): Promise<ApiResponse<(ScreenplayPurchase & { screenplay: Screenplay })[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_purchases')
        .select(`
          *,
          screenplay:screenplays(*)
        `)
        .order('purchased_at', { ascending: false });
      
      return { data, error };
    });
  }
};
