
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { ScreenplayReview } from './types';

export const screenplayReviewsApi = {
  // Add review for purchased screenplay
  addReview: async (
    screenplayId: string,
    purchaseId: string,
    rating: number,
    reviewText?: string
  ): Promise<ApiResponse<ScreenplayReview>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_reviews')
        .insert({
          screenplay_id: screenplayId,
          purchase_id: purchaseId,
          rating,
          review_text: reviewText,
          reviewer_id: (await supabase.auth.getUser()).data.user?.id || ''
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get reviews for a screenplay
  getScreenplayReviews: async (screenplayId: string): Promise<ApiResponse<ScreenplayReview[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_reviews')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  }
};
