
// Import the individual API modules
import { screenplayCrudApi } from './crud';
import { screenplayPurchasesApi } from './purchases';
import { screenplayReviewsApi } from './reviews';

// Export all types
export type {
  Screenplay,
  ScreenplayInsert,
  ScreenplayUpdate,
  ScreenplayPurchase,
  ScreenplayReview
} from './types';

// Export API modules
export { screenplayCrudApi } from './crud';
export { screenplayPurchasesApi } from './purchases';
export { screenplayReviewsApi } from './reviews';

// Create a unified API object for backward compatibility
export const screenplaysApi = {
  ...screenplayCrudApi,
  ...screenplayPurchasesApi,
  ...screenplayReviewsApi
};
