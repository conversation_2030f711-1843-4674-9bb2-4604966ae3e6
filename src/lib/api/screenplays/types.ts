
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

// Updated type definition to correctly handle the profiles join
export type Screenplay = Tables<'screenplays'> & {
  profiles?: {
    full_name: string | null;
    username: string | null;
  } | null;
  views?: number;
  likes?: number;
  comments?: number;
};

export type ScreenplayInsert = TablesInsert<'screenplays'>;
export type ScreenplayUpdate = TablesUpdate<'screenplays'>;
export type ScreenplayPurchase = Tables<'screenplay_purchases'>;
export type ScreenplayReview = Tables<'screenplay_reviews'>;
