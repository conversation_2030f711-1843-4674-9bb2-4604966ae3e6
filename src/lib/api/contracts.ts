
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type ScreenplayContract = Tables<'screenplay_contracts'>;
export type ScreenplayContractInsert = TablesInsert<'screenplay_contracts'>;
export type ScreenplayContractUpdate = TablesUpdate<'screenplay_contracts'>;

export type ContractWithDetails = ScreenplayContract & {
  screenplay: {
    title: string;
  } | null;
  buyer_profile?: {
    full_name: string | null;
    username: string | null;
  } | null;
  seller_profile?: {
    full_name: string | null;
    username: string | null;
  } | null;
};

export const contractsApi = {
  // Create new contract
  createContract: async (
    screenplayId: string,
    buyerId: string,
    sellerId: string,
    contractType: 'purchase' | 'option' | 'licensing',
    terms: Record<string, any>,
    offerId?: string
  ): Promise<ApiResponse<ScreenplayContract>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_contracts')
        .insert({
          screenplay_id: screenplayId,
          buyer_id: buyerId,
          seller_id: sellerId,
          offer_id: offerId,
          contract_type: contractType,
          terms,
          status: 'draft'
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get contracts for user
  getMyContracts: async (): Promise<ApiResponse<ContractWithDetails[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_contracts')
        .select(`
          *,
          screenplay:screenplays(title),
          buyer_profile:profiles!screenplay_contracts_buyer_id_fkey(full_name, username),
          seller_profile:profiles!screenplay_contracts_seller_id_fkey(full_name, username)
        `)
        .order('created_at', { ascending: false });
      
      // Transform the data to match our type structure
      const transformedData = data?.map(contract => ({
        ...contract,
        screenplay: Array.isArray(contract.screenplay) ? contract.screenplay[0] : contract.screenplay,
        buyer_profile: Array.isArray(contract.buyer_profile) ? contract.buyer_profile[0] : contract.buyer_profile,
        seller_profile: Array.isArray(contract.seller_profile) ? contract.seller_profile[0] : contract.seller_profile,
      })) as ContractWithDetails[];
      
      return { data: transformedData, error };
    });
  },

  // Sign contract
  signContract: async (contractId: string, role: 'buyer' | 'seller'): Promise<ApiResponse<ScreenplayContract>> => {
    return apiWrapper(async () => {
      const updateField = role === 'buyer' ? 'buyer_signed_at' : 'seller_signed_at';
      const { data, error } = await supabase
        .from('screenplay_contracts')
        .update({
          [updateField]: new Date().toISOString(),
          status: 'pending_signatures'
        })
        .eq('id', contractId)
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Update contract status
  updateContractStatus: async (
    contractId: string, 
    status: 'draft' | 'pending_signatures' | 'signed' | 'executed' | 'terminated'
  ): Promise<ApiResponse<ScreenplayContract>> => {
    return apiWrapper(async () => {
      const updates: any = { status };
      if (status === 'executed') {
        updates.executed_at = new Date().toISOString();
      }
      
      const { data, error } = await supabase
        .from('screenplay_contracts')
        .update(updates)
        .eq('id', contractId)
        .select()
        .single();
      
      return { data, error };
    });
  }
};
