
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type ScreenplayAssessment = Tables<'screenplay_assessments'>;
export type ScreenplayAssessmentInsert = TablesInsert<'screenplay_assessments'>;
export type ScreenplayAssessmentUpdate = TablesUpdate<'screenplay_assessments'>;

export const assessmentsApi = {
  // Request AI assessment for a screenplay
  requestAssessment: async (
    screenplayId: string, 
    assessmentType: 'quick' | 'standard' | 'full' = 'standard'
  ): Promise<ApiResponse<ScreenplayAssessment>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_assessments')
        .insert({
          screenplay_id: screenplayId,
          assessment_type: assessmentType,
          status: 'pending'
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get assessment for a screenplay
  getAssessment: async (screenplayId: string): Promise<ApiResponse<ScreenplayAssessment | null>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_assessments')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .order('created_at', { ascending: false })
        .limit(1)
        .maybeSingle();
      
      return { data, error };
    });
  },

  // Get all assessments for user's screenplays
  getMyAssessments: async (): Promise<ApiResponse<(ScreenplayAssessment & { screenplay: { title: string } })[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_assessments')
        .select(`
          *,
          screenplay:screenplays(title)
        `)
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  }
};
