
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert } from '@/integrations/supabase/types';

export type MarketplaceAnalytics = Tables<'marketplace_analytics'>;
export type MarketplaceAnalyticsInsert = TablesInsert<'marketplace_analytics'>;

export const analyticsApi = {
  // Track event
  trackEvent: async (
    screenplayId: string,
    eventType: string,
    eventData?: Record<string, any>
  ): Promise<ApiResponse<MarketplaceAnalytics>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('marketplace_analytics')
        .insert({
          screenplay_id: screenplayId,
          event_type: eventType,
          event_data: eventData || null,
          user_id: user.user?.id || null
        })
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Get analytics for screenplay
  getScreenplayAnalytics: async (screenplayId: string): Promise<ApiResponse<MarketplaceAnalytics[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('marketplace_analytics')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Get analytics summary
  getAnalyticsSummary: async (screenplayId: string): Promise<ApiResponse<{
    views: number;
    purchases: number;
    offers: number;
  }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('marketplace_analytics')
        .select('event_type')
        .eq('screenplay_id', screenplayId);

      if (error) throw error;

      const summary = data.reduce((acc, event) => {
        switch (event.event_type) {
          case 'view':
            acc.views++;
            break;
          case 'purchase':
            acc.purchases++;
            break;
          case 'offer':
            acc.offers++;
            break;
        }
        return acc;
      }, { views: 0, purchases: 0, offers: 0 });

      return { data: summary, error: null };
    });
  }
};
