
import type { PostgrestError } from '@supabase/supabase-js';

/**
 * Custom error class for API-related errors
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public originalError?: unknown
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Standardized API response type
 */
export interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

/**
 * Generic API operation type
 */
export type ApiOperation<T> = () => Promise<{ data: T | null; error: PostgrestError | null }>;
