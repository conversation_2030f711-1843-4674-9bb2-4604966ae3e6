
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { Profile } from '@/types/database';

/**
 * API service for user profile operations
 */
export const profileApi = {
  /**
   * Fetches the current user's profile
   */
  getCurrentProfile: async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        data: null,
        error: 'User not authenticated',
        success: false
      };
    }

    return apiWrapper(async () => {
      const result = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      return result;
    });
  },

  /**
   * Updates the current user's profile
   */
  updateProfile: async (updates: Partial<Profile>) => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return {
        data: null,
        error: 'User not authenticated',
        success: false
      };
    }

    return apiWrapper(async () => {
      const result = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', user.id)
        .select()
        .single();
      return result;
    });
  },

  /**
   * Creates a new profile (usually handled by trigger, but can be used as fallback)
   */
  createProfile: async (profileData: Omit<Profile, 'created_at' | 'updated_at'>) => {
    return apiWrapper(async () => {
      const result = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();
      return result;
    });
  },
};
