
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables } from '@/integrations/supabase/types';

export type ScreenplayAnalyticsSummary = Tables<'screenplay_analytics_summary'>;

export interface AnalyticsData {
  views: {
    total: number;
    unique: number;
    trend: Array<{ date: string; count: number }>;
  };
  offers: {
    total: number;
    average: number;
    trend: Array<{ date: string; count: number; amount: number }>;
  };
  purchases: {
    total: number;
    revenue: number;
    conversion: number;
  };
  engagement: {
    viewsToOffers: number;
    offersToSales: number;
  };
}

export const enhancedAnalyticsApi = {
  // Get analytics summary for a screenplay
  getScreenplayAnalytics: async (screenplayId: string): Promise<ApiResponse<ScreenplayAnalyticsSummary | null>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_analytics_summary')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .maybeSingle();
      
      return { data, error };
    });
  },

  // Get detailed analytics for a screenplay
  getDetailedAnalytics: async (screenplayId: string): Promise<ApiResponse<AnalyticsData>> => {
    return apiWrapper(async () => {
      // Get basic analytics summary
      const { data: summary } = await supabase
        .from('screenplay_analytics_summary')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .single();

      // Get view trends
      const { data: viewTrends } = await supabase
        .from('marketplace_analytics')
        .select('created_at')
        .eq('screenplay_id', screenplayId)
        .eq('event_type', 'view')
        .order('created_at', { ascending: false })
        .limit(30);

      // Get offer trends
      const { data: offerTrends } = await supabase
        .from('screenplay_offers')
        .select('created_at, offer_amount')
        .eq('screenplay_id', screenplayId)
        .order('created_at', { ascending: false })
        .limit(30);

      // Process trends data
      const processedViewTrends = viewTrends?.reduce((acc: Array<{ date: string; count: number }>, view) => {
        const date = new Date(view.created_at).toDateString();
        const existing = acc.find(item => item.date === date);
        if (existing) {
          existing.count++;
        } else {
          acc.push({ date, count: 1 });
        }
        return acc;
      }, []) || [];

      const processedOfferTrends = offerTrends?.reduce((acc: Array<{ date: string; count: number; amount: number }>, offer) => {
        const date = new Date(offer.created_at).toDateString();
        const existing = acc.find(item => item.date === date);
        if (existing) {
          existing.count++;
          existing.amount += Number(offer.offer_amount);
        } else {
          acc.push({ date, count: 1, amount: Number(offer.offer_amount) });
        }
        return acc;
      }, []) || [];

      const analyticsData: AnalyticsData = {
        views: {
          total: summary?.total_views || 0,
          unique: summary?.unique_viewers || 0,
          trend: processedViewTrends
        },
        offers: {
          total: summary?.total_offers || 0,
          average: Number(summary?.avg_offer_amount || 0),
          trend: processedOfferTrends
        },
        purchases: {
          total: summary?.total_purchases || 0,
          revenue: Number(summary?.total_revenue || 0),
          conversion: Number(summary?.conversion_rate || 0)
        },
        engagement: {
          viewsToOffers: summary?.total_views ? (summary.total_offers / summary.total_views) * 100 : 0,
          offersToSales: summary?.total_offers ? (summary.total_purchases / summary.total_offers) * 100 : 0
        }
      };

      return { data: analyticsData, error: null };
    });
  },

  // Get analytics overview for user's screenplays
  getMyAnalyticsOverview: async (): Promise<ApiResponse<{
    totalViews: number;
    totalOffers: number;
    totalRevenue: number;
    averageConversion: number;
    topPerforming: Array<{ title: string; views: number; revenue: number }>;
  }>> => {
    return apiWrapper(async () => {
      const { data: summaries, error } = await supabase
        .from('screenplay_analytics_summary')
        .select(`
          *,
          screenplay:screenplays(title)
        `);

      if (error) throw error;

      const overview = summaries?.reduce((acc, summary) => {
        acc.totalViews += summary.total_views;
        acc.totalOffers += summary.total_offers;
        acc.totalRevenue += Number(summary.total_revenue);
        return acc;
      }, {
        totalViews: 0,
        totalOffers: 0,
        totalRevenue: 0,
        averageConversion: 0,
        topPerforming: [] as Array<{ title: string; views: number; revenue: number }>
      });

      if (overview && summaries) {
        overview.averageConversion = summaries.length > 0 
          ? summaries.reduce((acc, s) => acc + Number(s.conversion_rate || 0), 0) / summaries.length 
          : 0;

        overview.topPerforming = summaries
          .sort((a, b) => Number(b.total_revenue) - Number(a.total_revenue))
          .slice(0, 5)
          .map(s => ({
            title: (s.screenplay as any)?.title || 'Unknown',
            views: s.total_views,
            revenue: Number(s.total_revenue)
          }));
      }

      return { data: overview || null, error: null };
    });
  }
};
