
// Re-export secure schedules API for backward compatibility
// This file maintains the existing API interface while the implementation
// has been split into smaller, focused modules

export { schedulesApi as secureSchedulesApi } from './schedulesApi';
export { scheduleItemsApi } from './scheduleItemsApi';

// For backward compatibility, create the combined API object
import { schedulesApi } from './schedulesApi';
import { scheduleItemsApi } from './scheduleItemsApi';

export const combinedSecureSchedulesApi = {
  ...schedulesApi,
  ...scheduleItemsApi
};
