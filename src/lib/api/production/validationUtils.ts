
import { z } from 'zod';
import { securityHelpers } from '../../security/securityMiddleware';
import { 
  createScheduleSchema, 
  updateScheduleSchema,
  createScheduleItemSchema 
} from '../../validation/schedule-schemas';

// Infer schema types from Zod
type ScheduleInput = z.infer<typeof createScheduleSchema>;
type ScheduleUpdateInput = z.infer<typeof updateScheduleSchema>;
type ScheduleItemInput = z.infer<typeof createScheduleItemSchema>;

// Standardized discriminated union type with consistent error structure
export type ValidationResult<T> = 
  | { success: true; data: T; errors?: never }
  | { success: false; errors: Record<string, string>; data?: never };

// Type guard functions for better narrowing
export function isValidationSuccess<T>(result: ValidationResult<T>): result is { success: true; data: T; errors?: never } {
  return result.success === true;
}

export function isValidationError<T>(result: ValidationResult<T>): result is { success: false; errors: Record<string, string>; data?: never } {
  return result.success === false;
}

export function validateScheduleData(data: unknown): ValidationResult<ScheduleInput> {
  return securityHelpers.validateInput(data, createScheduleSchema);
}

export function validateScheduleUpdateData(data: unknown): ValidationResult<ScheduleUpdateInput> {
  return securityHelpers.validateInput(data, updateScheduleSchema);
}

export function validateScheduleItemData(data: unknown): ValidationResult<ScheduleItemInput> {
  return securityHelpers.validateInput(data, createScheduleItemSchema);
}
