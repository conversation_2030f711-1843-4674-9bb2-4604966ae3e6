
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

// Types for location scouts
export interface LocationScout {
  id: string;
  org_id: string;
  user_id: string;
  location_name: string;
  address?: string;
  coordinates?: any;
  description?: string;
  accessibility_notes?: string;
  parking_info?: string;
  permits_required: boolean;
  cost_per_day?: number;
  contact_person?: string;
  contact_phone?: string;
  contact_email?: string;
  availability_notes?: string;
  photos?: any;
  status: string;
  scouted_date?: string;
  created_at: string;
  updated_at: string;
}

// Location Scouts API
export const locationScoutsApi = {
  getLocationScouts: (orgId: string): Promise<ApiResponse<LocationScout[]>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('location_scouts')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });
      return { data, error };
    }),

  createLocationScout: (location: Omit<LocationScout, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<LocationScout>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('location_scouts')
        .insert(location)
        .select()
        .single();
      return { data, error };
    }),

  updateLocationScout: (id: string, updates: Partial<LocationScout>): Promise<ApiResponse<LocationScout>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('location_scouts')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    })
};
