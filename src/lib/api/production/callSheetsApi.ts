
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

// Types for call sheets
export interface CallSheet {
  id: string;
  org_id: string;
  user_id: string;
  schedule_id: string;
  title: string;
  call_date: string;
  weather_info?: any;
  general_notes?: string;
  emergency_contacts?: any;
  status: string;
  created_at: string;
  updated_at: string;
}

// Call Sheets API
export const callSheetsApi = {
  getCallSheets: (orgId: string): Promise<ApiResponse<CallSheet[]>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_call_sheets')
        .select('*')
        .eq('org_id', orgId)
        .order('call_date', { ascending: false });
      return { data, error };
    }),

  createCallSheet: (callSheet: Omit<CallSheet, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<CallSheet>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_call_sheets')
        .insert(callSheet)
        .select()
        .single();
      return { data, error };
    }),

  updateCallSheet: (id: string, updates: Partial<CallSheet>): Promise<ApiResponse<CallSheet>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_call_sheets')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    }),

  deleteCallSheet: (id: string): Promise<ApiResponse<null>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_call_sheets')
        .delete()
        .eq('id', id);
      return { data, error };
    })
};
