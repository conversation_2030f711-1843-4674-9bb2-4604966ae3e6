
import type { ProductionSchedule, ProductionScheduleItem } from './types';

// Type coercion functions to handle raw DB strings
function coerceScheduleStatus(value: string): ProductionSchedule['status'] {
  if (['draft', 'active', 'completed', 'cancelled'].includes(value)) {
    return value as ProductionSchedule['status'];
  }
  return 'draft'; // fallback to safe default
}

function coerceScheduleItemStatus(value: string): ProductionScheduleItem['status'] {
  if (['scheduled', 'in_progress', 'completed', 'cancelled'].includes(value)) {
    return value as ProductionScheduleItem['status'];
  }
  return 'scheduled'; // fallback to safe default
}

// Helper to map raw DB data to typed objects
export function mapToProductionSchedule(data: any): ProductionSchedule {
  return {
    id: data.id,
    org_id: data.org_id,
    user_id: data.user_id,
    title: data.title,
    description: data.description || null,
    start_date: data.start_date,
    end_date: data.end_date,
    status: coerceScheduleStatus(data.status),
    created_at: data.created_at,
    updated_at: data.updated_at
  };
}

export function mapToProductionScheduleItem(data: any): ProductionScheduleItem {
  return {
    id: data.id,
    schedule_id: data.schedule_id,
    scene_id: data.scene_id || null,
    location_id: data.location_id || null,
    title: data.title,
    description: data.description || null,
    scheduled_date: data.scheduled_date,
    start_time: data.start_time || null,
    end_time: data.end_time || null,
    estimated_duration: data.estimated_duration || null,
    status: coerceScheduleItemStatus(data.status),
    notes: data.notes || null,
    created_at: data.created_at,
    updated_at: data.updated_at
  };
}
