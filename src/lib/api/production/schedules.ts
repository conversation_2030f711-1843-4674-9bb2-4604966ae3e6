
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { ProductionSchedule, ProductionScheduleItem } from './types';
import { createNormalizer, normalizeApiResponse, handleNullable } from './utils';

// Normalization functions using the new utilities
const scheduleNormalizer = createNormalizer<ProductionSchedule>((data: any) => ({
  id: data.id,
  org_id: data.org_id,
  user_id: data.user_id,
  title: data.title,
  description: handleNullable(data.description),
  start_date: data.start_date,
  end_date: data.end_date,
  status: data.status as ProductionSchedule['status'],
  created_at: data.created_at,
  updated_at: data.updated_at
}));

const scheduleItemNormalizer = createNormalizer<ProductionScheduleItem>((data: any) => ({
  id: data.id,
  schedule_id: data.schedule_id,
  scene_id: handleNullable(data.scene_id),
  location_id: handleNullable(data.location_id),
  title: data.title,
  description: handleNullable(data.description),
  scheduled_date: data.scheduled_date,
  start_time: handleNullable(data.start_time),
  end_time: handleNullable(data.end_time),
  estimated_duration: handleNullable(data.estimated_duration),
  status: data.status as ProductionScheduleItem['status'],
  notes: handleNullable(data.notes),
  created_at: data.created_at,
  updated_at: data.updated_at
}));

export const schedulesApi = {
  async getSchedules(orgId: string): Promise<ApiResponse<ProductionSchedule[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_schedules')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });
      
      const normalizedResult = await normalizeApiResponse(result, scheduleNormalizer.normalizeSingle, true);
      return normalizedResult as { data: ProductionSchedule[] | null; error: any };
    });
  },

  async createSchedule(schedule: Omit<ProductionSchedule, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ProductionSchedule>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_schedules')
        .insert([schedule])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, scheduleNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionSchedule | null; error: any };
    });
  },

  async updateSchedule(id: string, updates: Partial<ProductionSchedule>): Promise<ApiResponse<ProductionSchedule>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_schedules')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, scheduleNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionSchedule | null; error: any };
    });
  },

  async deleteSchedule(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      return await supabase
        .from('production_schedules')
        .delete()
        .eq('id', id);
    });
  },

  async getScheduleItems(scheduleId: string): Promise<ApiResponse<ProductionScheduleItem[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_schedule_items')
        .select('*')
        .eq('schedule_id', scheduleId)
        .order('scheduled_date', { ascending: true });
      
      const normalizedResult = await normalizeApiResponse(result, scheduleItemNormalizer.normalizeSingle, true);
      return normalizedResult as { data: ProductionScheduleItem[] | null; error: any };
    });
  },

  async createScheduleItem(item: Omit<ProductionScheduleItem, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ProductionScheduleItem>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_schedule_items')
        .insert([item])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, scheduleItemNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionScheduleItem | null; error: any };
    });
  }
};
