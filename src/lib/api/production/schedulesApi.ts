
import { supabase } from '@/integrations/supabase/client';
import { SecurityMiddleware } from '../../security/securityMiddleware';
import type { ApiResponse } from '../types';
import type { ProductionSchedule } from './types';
import { mapToProductionSchedule } from './scheduleMappers';
import { validateScheduleData, validateScheduleUpdateData, isValidationError } from './validationUtils';

export const schedulesApi = {
  async getSchedules(orgId: string): Promise<ApiResponse<ProductionSchedule[]>> {
    return SecurityMiddleware.secureOperation(async () => {
      const { data, error } = await supabase
        .from('production_schedules')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      const mappedSchedules = (data || []).map(mapToProductionSchedule);
      return { success: true, data: mappedSchedules, error: null };
    }, 'API_GENERAL');
  },

  async createSchedule(scheduleData: unknown): Promise<ApiResponse<ProductionSchedule>> {
    return SecurityMiddleware.secureOperation(async () => {
      const validation = validateScheduleData(scheduleData);
      if (isValidationError(validation)) {
        return { success: false, data: null, error: Object.values(validation.errors).join(', ') };
      }

      // Ensure all required fields are present for Supabase insert
      const insertData = {
        title: validation.data.title,
        description: validation.data.description || null,
        start_date: validation.data.start_date,
        end_date: validation.data.end_date,
        status: validation.data.status || 'draft',
        org_id: (scheduleData as any).org_id,
        user_id: (scheduleData as any).user_id
      };

      const { data, error } = await supabase
        .from('production_schedules')
        .insert([insertData])
        .select()
        .single();

      if (error) throw error;
      
      const mappedSchedule = mapToProductionSchedule(data);
      return { success: true, data: mappedSchedule, error: null };
    }, 'CREATE_OPERATIONS');
  },

  async updateSchedule(id: string, updates: unknown): Promise<ApiResponse<ProductionSchedule>> {
    return SecurityMiddleware.secureOperation(async () => {
      const validation = validateScheduleUpdateData(updates);
      if (isValidationError(validation)) {
        return { success: false, data: null, error: Object.values(validation.errors).join(', ') };
      }

      // Create update object with safe spread
      const updateData = {
        ...(validation.data || {}),
        updated_at: new Date().toISOString()
      };
      
      const { data, error } = await supabase
        .from('production_schedules')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      const mappedSchedule = mapToProductionSchedule(data);
      return { success: true, data: mappedSchedule, error: null };
    }, 'API_GENERAL');
  },

  async deleteSchedule(id: string): Promise<ApiResponse<void>> {
    return SecurityMiddleware.secureOperation(async () => {
      const { error } = await supabase
        .from('production_schedules')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true, data: null, error: null };
    }, 'API_GENERAL');
  }
};
