
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { ProductionReport } from './types';
import { createNormalizer, normalizeApiResponse, handleNullable } from './utils';

// Normalization function using the new utilities
const reportNormalizer = createNormalizer<ProductionReport>((data: any) => ({
  id: data.id,
  org_id: data.org_id,
  user_id: data.user_id,
  schedule_item_id: handleNullable(data.schedule_item_id),
  title: data.title,
  report_type: data.report_type as ProductionReport['report_type'],
  content: data.content,
  date: data.date,
  status: data.status as ProductionReport['status'],
  created_at: data.created_at,
  updated_at: data.updated_at
}));

export const reportsApi = {
  async getReports(orgId: string): Promise<ApiResponse<ProductionReport[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_reports')
        .select('*')
        .eq('org_id', orgId)
        .order('date', { ascending: false });
      
      const normalizedResult = await normalizeApiResponse(result, reportNormalizer.normalizeSingle, true);
      return normalizedResult as { data: ProductionReport[] | null; error: any };
    });
  },

  async createReport(report: Omit<ProductionReport, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ProductionReport>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_reports')
        .insert([{
          ...report,
          content: report.content as any // Cast to match Supabase Json type
        }])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, reportNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionReport | null; error: any };
    });
  }
};
