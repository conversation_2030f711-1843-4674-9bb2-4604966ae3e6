
import type { ApiResponse } from '../types';
import type { PostgrestError } from '@supabase/supabase-js';

/**
 * Generic normalization utility for production API entities
 */
export function createNormalizer<T>(
  normalizeFunction: (data: any) => T
) {
  return {
    normalizeArray: (data: any[]): T[] => data.map(normalizeFunction),
    normalizeSingle: (data: any): T => normalizeFunction(data)
  };
}

/**
 * Generic API response wrapper for production entities
 */
export async function normalizeApiResponse<T>(
  result: { data: any; error: PostgrestError | null },
  normalizer: (data: any) => T,
  isArray = false
): Promise<{ data: T | T[] | null; error: PostgrestError | null }> {
  if (result.error) {
    return { data: null, error: result.error };
  }
  
  if (result.data) {
    const normalizedData = isArray 
      ? (result.data as any[]).map(normalizer)
      : normalizer(result.data);
    return { data: normalizedData, error: null };
  }
  
  return { data: null, error: null };
}

/**
 * Helper to handle nullable fields consistently
 */
export function handleNullable<T>(value: T | null | undefined, defaultValue: T | null = null): T | null {
  return value ?? defaultValue;
}
