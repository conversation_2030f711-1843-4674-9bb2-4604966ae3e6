
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface SearchFilters {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const enhancedProductionApi = {
  async getSchedulesPaginated(
    orgId: string, 
    page: number = 1, 
    pageSize: number = 20,
    filters: SearchFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiWrapper(async () => {
      let query = supabase
        .from('production_schedules')
        .select('*', { count: 'exact' })
        .eq('org_id', orgId);

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.dateFrom) {
        query = query.gte('start_date', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('end_date', filters.dateTo);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const result = await query;
      
      if (result.error) throw result.error;

      const totalPages = Math.ceil((result.count || 0) / pageSize);

      return {
        data: {
          data: result.data || [],
          count: result.count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null
      };
    });
  },

  async getBudgetsPaginated(
    orgId: string, 
    page: number = 1, 
    pageSize: number = 20,
    filters: SearchFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiWrapper(async () => {
      let query = supabase
        .from('production_budgets')
        .select('*', { count: 'exact' })
        .eq('org_id', orgId);

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const result = await query;
      
      if (result.error) throw result.error;

      const totalPages = Math.ceil((result.count || 0) / pageSize);

      return {
        data: {
          data: result.data || [],
          count: result.count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null
      };
    });
  },

  async getResourcesPaginated(
    orgId: string, 
    page: number = 1, 
    pageSize: number = 20,
    filters: SearchFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiWrapper(async () => {
      let query = supabase
        .from('production_resources')
        .select('*', { count: 'exact' })
        .eq('org_id', orgId);

      // Apply filters
      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }
      if (filters.status) {
        query = query.eq('availability_status', filters.status);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const result = await query;
      
      if (result.error) throw result.error;

      const totalPages = Math.ceil((result.count || 0) / pageSize);

      return {
        data: {
          data: result.data || [],
          count: result.count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null
      };
    });
  },

  async getReportsPaginated(
    orgId: string, 
    page: number = 1, 
    pageSize: number = 20,
    filters: SearchFilters = {}
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    return apiWrapper(async () => {
      let query = supabase
        .from('production_reports')
        .select('*', { count: 'exact' })
        .eq('org_id', orgId);

      // Apply filters
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%`);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.dateFrom) {
        query = query.gte('date', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('date', filters.dateTo);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const result = await query;
      
      if (result.error) throw result.error;

      const totalPages = Math.ceil((result.count || 0) / pageSize);

      return {
        data: {
          data: result.data || [],
          count: result.count || 0,
          page,
          pageSize,
          totalPages
        },
        error: null
      };
    });
  }
};
