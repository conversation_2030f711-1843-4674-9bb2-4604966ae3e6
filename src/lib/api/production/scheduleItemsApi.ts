
import { supabase } from '@/integrations/supabase/client';
import { SecurityMiddleware } from '../../security/securityMiddleware';
import type { ApiResponse } from '../types';
import type { ProductionScheduleItem } from './types';
import { mapToProductionScheduleItem } from './scheduleMappers';
import { validateScheduleItemData, isValidationError } from './validationUtils';

export const scheduleItemsApi = {
  async getScheduleItems(scheduleId: string): Promise<ApiResponse<ProductionScheduleItem[]>> {
    return SecurityMiddleware.secureOperation(async () => {
      const { data, error } = await supabase
        .from('production_schedule_items')
        .select('*')
        .eq('schedule_id', scheduleId)
        .order('scheduled_date', { ascending: true });

      if (error) throw error;
      
      const mappedItems = (data || []).map(mapToProductionScheduleItem);
      return { success: true, data: mappedItems, error: null };
    }, 'API_GENERAL');
  },

  async createScheduleItem(itemData: unknown): Promise<ApiResponse<ProductionScheduleItem>> {
    return SecurityMiddleware.secureOperation(async () => {
      const validation = validateScheduleItemData(itemData);
      if (isValidationError(validation)) {
        return { success: false, data: null, error: Object.values(validation.errors).join(', ') };
      }

      // Ensure all required fields are present for Supabase insert
      const insertData = {
        schedule_id: validation.data.schedule_id,
        title: validation.data.title,
        description: validation.data.description || null,
        scheduled_date: validation.data.scheduled_date,
        start_time: validation.data.start_time || null,
        end_time: validation.data.end_time || null,
        estimated_duration: validation.data.estimated_duration || null,
        status: validation.data.status || 'scheduled',
        scene_id: validation.data.scene_id || null,
        location_id: validation.data.location_id || null,
        notes: validation.data.notes || null
      };

      const { data, error } = await supabase
        .from('production_schedule_items')
        .insert([insertData])
        .select()
        .single();

      if (error) throw error;
      
      const mappedItem = mapToProductionScheduleItem(data);
      return { success: true, data: mappedItem, error: null };
    }, 'CREATE_OPERATIONS');
  },

  async updateScheduleItem(id: string, updates: unknown): Promise<ApiResponse<ProductionScheduleItem>> {
    return SecurityMiddleware.secureOperation(async () => {
      const validation = validateScheduleItemData(updates);
      if (isValidationError(validation)) {
        return { success: false, data: null, error: Object.values(validation.errors).join(', ') };
      }

      const updateData = {
        ...validation.data,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('production_schedule_items')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      
      const mappedItem = mapToProductionScheduleItem(data);
      return { success: true, data: mappedItem, error: null };
    }, 'API_GENERAL');
  },

  async deleteScheduleItem(id: string): Promise<ApiResponse<void>> {
    return SecurityMiddleware.secureOperation(async () => {
      const { error } = await supabase
        .from('production_schedule_items')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { success: true, data: null, error: null };
    }, 'API_GENERAL');
  }
};
