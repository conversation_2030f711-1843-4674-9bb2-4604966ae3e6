
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

// Types for crew members
export interface CrewMember {
  id: string;
  org_id: string;
  name: string;
  role: string;
  email?: string;
  phone?: string;
  union_status?: string;
  daily_rate?: number;
  overtime_rate?: number;
  availability_status: 'available' | 'busy' | 'unavailable' | 'tentative';
  skills?: any;
  emergency_contact?: any;
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Crew Members API
export const crewApi = {
  getCrewMembers: (orgId: string): Promise<ApiResponse<CrewMember[]>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('crew_members')
        .select('*')
        .eq('org_id', orgId)
        .order('name');
      return { data, error };
    }),

  createCrewMember: (crew: Omit<CrewMember, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<CrewMember>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('crew_members')
        .insert(crew)
        .select()
        .single();
      return { data, error };
    }),

  updateCrewMember: (id: string, updates: Partial<CrewMember>): Promise<ApiResponse<CrewMember>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('crew_members')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    })
};
