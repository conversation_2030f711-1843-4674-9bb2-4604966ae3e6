
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

// Types for budget approvals
export interface BudgetApproval {
  id: string;
  budget_id: string;
  org_id: string;
  approver_id: string;
  approval_level: number;
  status: 'pending' | 'approved' | 'rejected' | 'revision_requested';
  comments?: string;
  approved_amount?: number;
  approved_at?: string;
  created_at: string;
  updated_at: string;
}

// Budget Approvals API
export const budgetApprovalsApi = {
  getBudgetApprovals: (orgId: string): Promise<ApiResponse<BudgetApproval[]>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('budget_approvals')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });
      return { data, error };
    }),

  createBudgetApproval: (approval: Omit<BudgetApproval, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<BudgetApproval>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('budget_approvals')
        .insert(approval)
        .select()
        .single();
      return { data, error };
    }),

  updateApprovalStatus: (id: string, status: BudgetApproval['status'], comments?: string): Promise<ApiResponse<BudgetApproval>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('budget_approvals')
        .update({ 
          status, 
          comments,
          approved_at: status === 'approved' ? new Date().toISOString() : null
        })
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    })
};
