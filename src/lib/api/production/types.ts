
// Shared types for production API with strict TypeScript compliance
export interface ProductionSchedule {
  readonly id: string;
  readonly org_id: string;
  readonly user_id: string;
  title: string;
  description?: string | null;
  start_date: string;
  end_date: string;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
  readonly created_at: string;
  readonly updated_at: string;
}

export interface ProductionScheduleItem {
  readonly id: string;
  readonly schedule_id: string;
  scene_id?: string | null;
  location_id?: string | null;
  title: string;
  description?: string | null;
  scheduled_date: string;
  start_time?: string | null;
  end_time?: string | null;
  estimated_duration?: number | null;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string | null;
  readonly created_at: string;
  readonly updated_at: string;
}

export interface ProductionBudget {
  readonly id: string;
  readonly org_id: string;
  readonly user_id: string;
  title: string;
  description?: string | null;
  total_budget: number;
  currency: string;
  status: 'draft' | 'approved' | 'active' | 'completed';
  readonly created_at: string;
  readonly updated_at: string;
}

export interface BudgetLineItem {
  readonly id: string;
  readonly budget_id: string;
  category: string;
  subcategory?: string | null;
  description: string;
  estimated_cost: number;
  actual_cost?: number | null;
  quantity?: number | null;
  unit_cost?: number | null;
  vendor?: string | null;
  status: 'estimated' | 'approved' | 'ordered' | 'received' | 'paid';
  notes?: string | null;
  readonly created_at: string;
  readonly updated_at: string;
}

export interface ProductionResource {
  readonly id: string;
  readonly org_id: string;
  readonly user_id: string;
  name: string;
  type: 'equipment' | 'location' | 'talent' | 'crew' | 'vehicle' | 'other';
  description?: string | null;
  availability_status: 'available' | 'booked' | 'maintenance' | 'unavailable';
  cost_per_day?: number | null;
  contact_info?: Record<string, unknown>;
  specifications?: Record<string, unknown>;
  readonly created_at: string;
  readonly updated_at: string;
}

export interface ProductionReport {
  readonly id: string;
  readonly org_id: string;
  readonly user_id: string;
  schedule_item_id?: string | null;
  title: string;
  report_type: 'daily' | 'wrap' | 'incident' | 'progress';
  content: Record<string, unknown>;
  date: string;
  status: 'draft' | 'submitted' | 'approved';
  readonly created_at: string;
  readonly updated_at: string;
}

// API Response types for better type safety
export interface ApiSuccessResponse<T> {
  readonly success: true;
  readonly data: T;
  readonly error: null;
}

export interface ApiErrorResponse {
  readonly success: false;
  readonly data: null;
  readonly error: string;
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse;

// Input types for creating new entities
export type CreateProductionScheduleInput = Omit<ProductionSchedule, 'id' | 'created_at' | 'updated_at'>;
export type CreateProductionBudgetInput = Omit<ProductionBudget, 'id' | 'created_at' | 'updated_at'>;
export type CreateProductionResourceInput = Omit<ProductionResource, 'id' | 'created_at' | 'updated_at'>;
export type CreateProductionReportInput = Omit<ProductionReport, 'id' | 'created_at' | 'updated_at'>;
export type CreateScheduleItemInput = Omit<ProductionScheduleItem, 'id' | 'created_at' | 'updated_at'>;
export type CreateBudgetLineItemInput = Omit<BudgetLineItem, 'id' | 'created_at' | 'updated_at'>;

// Update types for partial updates
export type UpdateProductionScheduleInput = Partial<Omit<ProductionSchedule, 'id' | 'org_id' | 'user_id' | 'created_at' | 'updated_at'>>;
export type UpdateProductionBudgetInput = Partial<Omit<ProductionBudget, 'id' | 'org_id' | 'user_id' | 'created_at' | 'updated_at'>>;
export type UpdateProductionResourceInput = Partial<Omit<ProductionResource, 'id' | 'org_id' | 'user_id' | 'created_at' | 'updated_at'>>;
export type UpdateProductionReportInput = Partial<Omit<ProductionReport, 'id' | 'org_id' | 'user_id' | 'created_at' | 'updated_at'>>;
