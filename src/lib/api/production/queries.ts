
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useAuth } from '@/contexts/AuthContext';
import { schedulesApi, budgetsApi, resourcesApi, reportsApi } from './index';
import type { 
  ProductionSchedule, 
  ProductionBudget, 
  ProductionResource, 
  ProductionReport,
  ProductionScheduleItem 
} from './types';

// Query Keys Factory
export const productionKeys = {
  all: ['production'] as const,
  schedules: () => [...productionKeys.all, 'schedules'] as const,
  schedule: (id: string) => [...productionKeys.schedules(), id] as const,
  scheduleItems: (scheduleId: string) => [...productionKeys.schedule(scheduleId), 'items'] as const,
  budgets: () => [...productionKeys.all, 'budgets'] as const,
  budget: (id: string) => [...productionKeys.budgets(), id] as const,
  budgetItems: (budgetId: string) => [...productionKeys.budget(budgetId), 'items'] as const,
  resources: () => [...productionKeys.all, 'resources'] as const,
  resource: (id: string) => [...productionKeys.resources(), id] as const,
  reports: () => [...productionKeys.all, 'reports'] as const,
  report: (id: string) => [...productionKeys.reports(), id] as const,
} as const;

// Schedules Queries
export const useSchedulesQuery = () => {
  const { currentOrganization } = useOrganization();
  
  return useQuery({
    queryKey: productionKeys.schedules(),
    queryFn: async () => {
      if (!currentOrganization) throw new Error('No organization selected');
      const result = await schedulesApi.getSchedules(currentOrganization.id);
      if (!result.success) throw new Error(result.error);
      return result.data || [];
    },
    enabled: !!currentOrganization,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useScheduleItemsQuery = (scheduleId: string | undefined) => {
  return useQuery({
    queryKey: productionKeys.scheduleItems(scheduleId || ''),
    queryFn: async () => {
      if (!scheduleId) throw new Error('Schedule ID required');
      const result = await schedulesApi.getScheduleItems(scheduleId);
      if (!result.success) throw new Error(result.error);
      return result.data || [];
    },
    enabled: !!scheduleId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Budgets Queries
export const useBudgetsQuery = () => {
  const { currentOrganization } = useOrganization();
  
  return useQuery({
    queryKey: productionKeys.budgets(),
    queryFn: async () => {
      if (!currentOrganization) throw new Error('No organization selected');
      const result = await budgetsApi.getBudgets(currentOrganization.id);
      if (!result.success) throw new Error(result.error);
      return result.data || [];
    },
    enabled: !!currentOrganization,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Resources Queries
export const useResourcesQuery = () => {
  const { currentOrganization } = useOrganization();
  
  return useQuery({
    queryKey: productionKeys.resources(),
    queryFn: async () => {
      if (!currentOrganization) throw new Error('No organization selected');
      const result = await resourcesApi.getResources(currentOrganization.id);
      if (!result.success) throw new Error(result.error);
      return result.data || [];
    },
    enabled: !!currentOrganization,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Reports Queries
export const useReportsQuery = () => {
  const { currentOrganization } = useOrganization();
  
  return useQuery({
    queryKey: productionKeys.reports(),
    queryFn: async () => {
      if (!currentOrganization) throw new Error('No organization selected');
      const result = await reportsApi.getReports(currentOrganization.id);
      if (!result.success) throw new Error(result.error);
      return result.data || [];
    },
    enabled: !!currentOrganization,
    staleTime: 3 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
};

// Mutations
export const useCreateScheduleMutation = () => {
  const queryClient = useQueryClient();
  const { currentOrganization } = useOrganization();
  const { user } = useAuth();

  return useMutation({
    mutationFn: async (data: Omit<ProductionSchedule, 'id' | 'created_at' | 'updated_at'>) => {
      const result = await schedulesApi.createSchedule(data);
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productionKeys.schedules() });
    },
  });
};

export const useCreateBudgetMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Omit<ProductionBudget, 'id' | 'created_at' | 'updated_at'>) => {
      const result = await budgetsApi.createBudget(data);
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productionKeys.budgets() });
    },
  });
};

export const useCreateResourceMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Omit<ProductionResource, 'id' | 'created_at' | 'updated_at'>) => {
      const result = await resourcesApi.createResource(data);
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productionKeys.resources() });
    },
  });
};

export const useCreateReportMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Omit<ProductionReport, 'id' | 'created_at' | 'updated_at'>) => {
      const result = await reportsApi.createReport(data);
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: productionKeys.reports() });
    },
  });
};
