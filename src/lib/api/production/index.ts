
// Re-export all types
export type {
  ProductionSchedule,
  ProductionScheduleItem,
  ProductionBudget,
  BudgetLineItem,
  ProductionResource,
  ProductionReport
} from './types';

// Import and re-export all API services
import { schedulesApi } from './schedules';
import { budgetsApi } from './budgets';
import { resourcesApi } from './resources';
import { reportsApi } from './reports';

// Re-export new workflow-related APIs directly from their individual files
export type { CallSheet } from './callSheetsApi';
export type { Equipment, EquipmentCheckout } from './equipmentApi';
export type { CrewMember } from './crewApi';
export type { LocationScout } from './locationScoutsApi';
export type { BudgetApproval } from './budgetApprovalsApi';

export { callSheetsApi } from './callSheetsApi';
export { equipmentApi } from './equipmentApi';
export { crewApi } from './crewApi';
export { locationScoutsApi } from './locationScoutsApi';
export { budgetApprovalsApi } from './budgetApprovalsApi';

export { schedulesApi, budgetsApi, resourcesApi, reportsApi };

// Combined production API for backward compatibility
export const productionApi = {
  // Schedules
  getSchedules: (orgId: string) => schedulesApi.getSchedules(orgId),
  createSchedule: (schedule: any) => schedulesApi.createSchedule(schedule),
  updateSchedule: (id: string, updates: any) => schedulesApi.updateSchedule(id, updates),
  deleteSchedule: (id: string) => schedulesApi.deleteSchedule(id),
  
  // Schedule Items
  getScheduleItems: (scheduleId: string) => schedulesApi.getScheduleItems(scheduleId),
  createScheduleItem: (item: any) => schedulesApi.createScheduleItem(item),
  
  // Budgets
  getBudgets: (orgId: string) => budgetsApi.getBudgets(orgId),
  createBudget: (budget: any) => budgetsApi.createBudget(budget),
  getBudgetLineItems: (budgetId: string) => budgetsApi.getBudgetLineItems(budgetId),
  createBudgetLineItem: (item: any) => budgetsApi.createBudgetLineItem(item),
  
  // Resources
  getResources: (orgId: string) => resourcesApi.getResources(orgId),
  createResource: (resource: any) => resourcesApi.createResource(resource),
  
  // Reports
  getReports: (orgId: string) => reportsApi.getReports(orgId),
  createReport: (report: any) => reportsApi.createReport(report)
};
