
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { ProductionResource } from './types';
import { createNormalizer, normalizeApiResponse, handleNullable } from './utils';

// Normalization function using the new utilities
const resourceNormalizer = createNormalizer<ProductionResource>((data: any) => ({
  id: data.id,
  org_id: data.org_id,
  user_id: data.user_id,
  name: data.name,
  type: data.type as ProductionResource['type'],
  description: handleNullable(data.description),
  availability_status: data.availability_status as ProductionResource['availability_status'],
  cost_per_day: handleNullable(data.cost_per_day),
  contact_info: data.contact_info || undefined,
  specifications: data.specifications || undefined,
  created_at: data.created_at,
  updated_at: data.updated_at
}));

export const resourcesApi = {
  async getResources(orgId: string): Promise<ApiResponse<ProductionResource[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_resources')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });
      
      const normalizedResult = await normalizeApiResponse(result, resourceNormalizer.normalizeSingle, true);
      return normalizedResult as { data: ProductionResource[] | null; error: any };
    });
  },

  async createResource(resource: Omit<ProductionResource, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ProductionResource>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_resources')
        .insert([{
          ...resource,
          contact_info: resource.contact_info as any, // Cast to match Supabase Json type
          specifications: resource.specifications as any // Cast to match Supabase Json type
        }])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, resourceNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionResource | null; error: any };
    });
  }
};
