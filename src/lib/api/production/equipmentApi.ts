
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';

// Types for equipment
export interface Equipment {
  id: string;
  org_id: string;
  name: string;
  category: string;
  serial_number?: string;
  purchase_date?: string;
  condition: string;
  status: 'available' | 'checked_out' | 'maintenance' | 'retired';
  daily_rate?: number;
  specifications?: any;
  maintenance_notes?: string;
  location?: string;
  created_at: string;
  updated_at: string;
}

export interface EquipmentCheckout {
  id: string;
  equipment_id: string;
  org_id: string;
  checked_out_by: string;
  checked_out_at: string;
  expected_return_date?: string;
  checked_in_at?: string;
  checked_in_by?: string;
  condition_out: string;
  condition_in?: string;
  notes?: string;
  schedule_item_id?: string;
  created_at: string;
  updated_at: string;
}

// Equipment API
export const equipmentApi = {
  getEquipment: (orgId: string): Promise<ApiResponse<Equipment[]>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_equipment')
        .select('*')
        .eq('org_id', orgId)
        .order('name');
      return { data, error };
    }),

  createEquipment: (equipment: Omit<Equipment, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Equipment>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_equipment')
        .insert(equipment)
        .select()
        .single();
      return { data, error };
    }),

  updateEquipment: (id: string, updates: Partial<Equipment>): Promise<ApiResponse<Equipment>> =>
    apiWrapper(async () => {
      const { data, error } = await supabase
        .from('production_equipment')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      return { data, error };
    }),

  checkoutEquipment: (checkout: Omit<EquipmentCheckout, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<EquipmentCheckout>> =>
    apiWrapper(async () => {
      // First create the checkout record
      const { data: checkoutData, error: checkoutError } = await supabase
        .from('equipment_checkouts')
        .insert(checkout)
        .select()
        .single();

      if (checkoutError) throw checkoutError;

      // Then update equipment status
      const { error: updateError } = await supabase
        .from('production_equipment')
        .update({ status: 'checked_out' })
        .eq('id', checkout.equipment_id);

      if (updateError) throw updateError;

      return { data: checkoutData, error: null };
    }),

  checkinEquipment: (checkoutId: string, condition: string, notes?: string): Promise<ApiResponse<EquipmentCheckout>> =>
    apiWrapper(async () => {
      // Get the checkout record to find equipment ID
      const { data: checkout, error: fetchError } = await supabase
        .from('equipment_checkouts')
        .select('equipment_id')
        .eq('id', checkoutId)
        .single();

      if (fetchError) throw fetchError;

      // Update checkout record
      const { data: checkoutData, error: checkoutError } = await supabase
        .from('equipment_checkouts')
        .update({
          checked_in_at: new Date().toISOString(),
          condition_in: condition,
          notes
        })
        .eq('id', checkoutId)
        .select()
        .single();

      if (checkoutError) throw checkoutError;

      // Update equipment status back to available
      const { error: updateError } = await supabase
        .from('production_equipment')
        .update({ status: 'available' })
        .eq('id', checkout.equipment_id);

      if (updateError) throw updateError;

      return { data: checkoutData, error: null };
    })
};
