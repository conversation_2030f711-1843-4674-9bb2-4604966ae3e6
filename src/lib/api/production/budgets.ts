
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { ProductionBudget, BudgetLineItem } from './types';
import { createNormalizer, normalizeApiResponse, handleNullable } from './utils';

// Normalization functions using the new utilities
const budgetNormalizer = createNormalizer<ProductionBudget>((data: any) => ({
  id: data.id,
  org_id: data.org_id,
  user_id: data.user_id,
  title: data.title,
  description: handleNullable(data.description),
  total_budget: data.total_budget,
  currency: data.currency,
  status: data.status as ProductionBudget['status'],
  created_at: data.created_at,
  updated_at: data.updated_at
}));

const budgetLineItemNormalizer = createNormalizer<BudgetLineItem>((data: any) => ({
  id: data.id,
  budget_id: data.budget_id,
  category: data.category,
  subcategory: handleNullable(data.subcategory),
  description: data.description,
  estimated_cost: data.estimated_cost,
  actual_cost: handleNullable(data.actual_cost),
  quantity: handleNullable(data.quantity),
  unit_cost: handleNullable(data.unit_cost),
  vendor: handleNullable(data.vendor),
  status: data.status as BudgetLineItem['status'],
  notes: handleNullable(data.notes),
  created_at: data.created_at,
  updated_at: data.updated_at
}));

export const budgetsApi = {
  async getBudgets(orgId: string): Promise<ApiResponse<ProductionBudget[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_budgets')
        .select('*')
        .eq('org_id', orgId)
        .order('created_at', { ascending: false });
      
      const normalizedResult = await normalizeApiResponse(result, budgetNormalizer.normalizeSingle, true);
      return normalizedResult as { data: ProductionBudget[] | null; error: any };
    });
  },

  async createBudget(budget: Omit<ProductionBudget, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<ProductionBudget>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('production_budgets')
        .insert([budget])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, budgetNormalizer.normalizeSingle);
      return normalizedResult as { data: ProductionBudget | null; error: any };
    });
  },

  async getBudgetLineItems(budgetId: string): Promise<ApiResponse<BudgetLineItem[]>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('budget_line_items')
        .select('*')
        .eq('budget_id', budgetId)
        .order('created_at', { ascending: true });
      
      const normalizedResult = await normalizeApiResponse(result, budgetLineItemNormalizer.normalizeSingle, true);
      return normalizedResult as { data: BudgetLineItem[] | null; error: any };
    });
  },

  async createBudgetLineItem(item: Omit<BudgetLineItem, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<BudgetLineItem>> {
    return apiWrapper(async () => {
      const result = await supabase
        .from('budget_line_items')
        .insert([item])
        .select()
        .single();
      
      const normalizedResult = await normalizeApiResponse(result, budgetLineItemNormalizer.normalizeSingle);
      return normalizedResult as { data: BudgetLineItem | null; error: any };
    });
  }
};
