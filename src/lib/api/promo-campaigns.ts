
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export type PromoCampaign = {
  id: string;
  name: string;
  description?: string;
  campaign_code: string;
  discount_type: 'percentage' | 'fixed_amount' | 'free_trial';
  discount_value: number;
  target_audience: 'all_users' | 'new_users' | 'existing_users' | 'specific_plans' | 'custom_segment';
  target_plans?: string[];
  custom_criteria?: Record<string, any> | null;
  start_date: string;
  end_date?: string;
  usage_limit?: number;
  usage_limit_per_user: number;
  is_active: boolean;
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';
  created_by: string;
  created_at: string;
  updated_at: string;
};

export type PromoCampaignUsage = {
  id: string;
  campaign_id: string;
  user_id: string;
  applied_at: string;
  discount_amount: number;
  original_amount: number;
  final_amount: number;
  subscription_id?: string;
  payment_intent_id?: string;
  metadata?: Record<string, any>;
};

export type PromoCampaignAnalytics = {
  id: string;
  campaign_id: string;
  date: string;
  views: number;
  applications: number;
  conversions: number;
  total_discount_given: number;
  total_revenue: number;
  unique_users: number;
  created_at: string;
  updated_at: string;
};

export type PromoCampaignInsert = Omit<PromoCampaign, 'id' | 'created_by' | 'created_at' | 'updated_at'>;
export type PromoCampaignUpdate = Partial<Omit<PromoCampaign, 'id' | 'created_by' | 'created_at' | 'updated_at'>>;

export const promoCampaignsApi = {
  // Get all campaigns
  getCampaigns: async (): Promise<ApiResponse<PromoCampaign[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('promo_campaigns')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { 
        data: data?.map(campaign => ({
          ...campaign,
          custom_criteria: typeof campaign.custom_criteria === 'string' 
            ? JSON.parse(campaign.custom_criteria) 
            : campaign.custom_criteria
        })) as PromoCampaign[], 
        error 
      };
    });
  },

  // Get campaign by ID
  getCampaign: async (id: string): Promise<ApiResponse<PromoCampaign>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('promo_campaigns')
        .select('*')
        .eq('id', id)
        .single();
      
      return { 
        data: data ? {
          ...data,
          custom_criteria: typeof data.custom_criteria === 'string' 
            ? JSON.parse(data.custom_criteria) 
            : data.custom_criteria
        } as PromoCampaign : null, 
        error 
      };
    });
  },

  // Create campaign
  createCampaign: async (campaign: PromoCampaignInsert): Promise<ApiResponse<PromoCampaign>> => {
    return apiWrapper(async () => {
      const { data: userData } = await supabase.auth.getUser();
      if (!userData.user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('promo_campaigns')
        .insert({
          ...campaign,
          created_by: userData.user.id
        })
        .select()
        .single();
      
      return { 
        data: data ? {
          ...data,
          custom_criteria: typeof data.custom_criteria === 'string' 
            ? JSON.parse(data.custom_criteria) 
            : data.custom_criteria
        } as PromoCampaign : null, 
        error 
      };
    });
  },

  // Update campaign
  updateCampaign: async (id: string, updates: PromoCampaignUpdate): Promise<ApiResponse<PromoCampaign>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('promo_campaigns')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
      
      return { 
        data: data ? {
          ...data,
          custom_criteria: typeof data.custom_criteria === 'string' 
            ? JSON.parse(data.custom_criteria) 
            : data.custom_criteria
        } as PromoCampaign : null, 
        error 
      };
    });
  },

  // Delete campaign
  deleteCampaign: async (id: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('promo_campaigns')
        .delete()
        .eq('id', id);
      
      return { data: null, error };
    });
  },

  // Get campaign usage
  getCampaignUsage: async (campaignId: string): Promise<ApiResponse<PromoCampaignUsage[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('promo_campaign_usage')
        .select('*')
        .eq('campaign_id', campaignId)
        .order('applied_at', { ascending: false });
      
      return { 
        data: data?.map(usage => ({
          ...usage,
          metadata: typeof usage.metadata === 'string' 
            ? JSON.parse(usage.metadata) 
            : usage.metadata
        })) as PromoCampaignUsage[], 
        error 
      };
    });
  },

  // Get campaign analytics
  getCampaignAnalytics: async (campaignId: string): Promise<ApiResponse<PromoCampaignAnalytics[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('promo_campaign_analytics')
        .select('*')
        .eq('campaign_id', campaignId)
        .order('date', { ascending: false });
      
      return { data: data as PromoCampaignAnalytics[], error };
    });
  },

  // Validate promo code
  validatePromoCode: async (code: string): Promise<ApiResponse<{
    valid: boolean;
    campaign_id?: string;
    discount_type?: string;
    discount_value?: number;
    error_message?: string;
  }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('validate_promo_code', { code });
      
      return { data: data?.[0] || null, error };
    });
  },

  // Apply promo code
  applyPromoCode: async (
    code: string,
    userId: string,
    originalAmount: number,
    subscriptionId?: string,
    paymentIntentId?: string
  ): Promise<ApiResponse<{
    success: boolean;
    discount_amount?: number;
    final_amount?: number;
    error_message?: string;
  }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .rpc('apply_promo_code', {
          code,
          user_id_param: userId,
          original_amount_param: originalAmount,
          subscription_id_param: subscriptionId,
          payment_intent_id_param: paymentIntentId
        });
      
      return { data: data?.[0] || null, error };
    });
  },

  // Get analytics summary
  getAnalyticsSummary: async (): Promise<ApiResponse<{
    total_campaigns: number;
    active_campaigns: number;
    total_usage: number;
    total_discount_given: number;
    total_revenue: number;
  }>> => {
    return apiWrapper(async () => {
      const { data: campaigns, error: campaignsError } = await supabase
        .from('promo_campaigns')
        .select('id, status');

      if (campaignsError) {
        return { data: null, error: campaignsError };
      }

      const { data: usage, error: usageError } = await supabase
        .from('promo_campaign_usage')
        .select('discount_amount, final_amount');

      if (usageError) {
        return { data: null, error: usageError };
      }

      const summary = {
        total_campaigns: campaigns?.length || 0,
        active_campaigns: campaigns?.filter(c => c.status === 'active').length || 0,
        total_usage: usage?.length || 0,
        total_discount_given: usage?.reduce((sum, u) => sum + (u.discount_amount || 0), 0) || 0,
        total_revenue: usage?.reduce((sum, u) => sum + (u.final_amount || 0), 0) || 0,
      };

      return { data: summary, error: null };
    });
  }
};
