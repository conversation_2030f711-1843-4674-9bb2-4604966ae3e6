
import { supabase } from '@/integrations/supabase/client';
import { handleSupabaseError, apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface StoryboardTemplate {
  id: string;
  org_id: string;
  user_id: string;
  name: string;
  description?: string;
  is_public: boolean;
  template_data: any;
  thumbnail_url?: string;
  usage_count: number;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name?: string;
  };
}

export interface CreateTemplateData {
  org_id: string;
  name: string;
  description?: string;
  is_public?: boolean;
  template_data: any;
  thumbnail_url?: string;
}

export const storyboardTemplatesApi = {
  async getTemplates(orgId?: string): Promise<ApiResponse<StoryboardTemplate[]>> {
    return apiWrapper(async () => {
      let query = supabase
        .from('storyboard_templates')
        .select('*');

      if (orgId) {
        query = query.or(`org_id.eq.${orgId},is_public.eq.true`);
      } else {
        query = query.eq('is_public', true);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async createTemplate(templateData: CreateTemplateData): Promise<ApiResponse<StoryboardTemplate>> {
    return apiWrapper(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('storyboard_templates')
        .insert({
          ...templateData,
          user_id: user.id
        })
        .select('*')
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updateTemplate(id: string, updates: Partial<CreateTemplateData>): Promise<ApiResponse<StoryboardTemplate>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboard_templates')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('storyboard_templates')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  },

  async incrementUsage(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      // First get the current usage count
      const { data: template, error: fetchError } = await supabase
        .from('storyboard_templates')
        .select('usage_count')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      // Then increment it
      const { error } = await supabase
        .from('storyboard_templates')
        .update({ 
          usage_count: (template.usage_count || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  }
};
