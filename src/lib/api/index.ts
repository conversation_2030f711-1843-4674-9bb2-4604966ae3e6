// Re-export all types
export type { ApiResponse, ApiOperation } from './types';
export { ApiError } from './types';

// Re-export error handling utilities
export { handleSupabaseError, apiWrapper } from './error-handling';

// Re-export API services
export { organizationApi } from './organization';
export { profileApi } from './profile';
export { charactersApi } from './characters';
export { locationsApi } from './locations';
export { scenesApi } from './scenes';
export { commentsApi } from './comments';
export { revisionsApi } from './revisions';
export { sceneDependenciesApi } from './sceneDependencies';
export { storyboardsApi } from './storyboards';
export { storyboardCommentsApi } from './storyboard-comments';
export { storyboardTemplatesApi } from './storyboard-templates';
export { coverageApi } from './coverage';
export { promptsApi } from './prompts';
export { screenplaysApi } from './screenplays/index';
export { offersApi } from './offers';
export { paymentsApi } from './payments';
export { filesApi } from './files';
export { analytics as analyticsApi } from '../analytics';

// Re-export new advanced features
export { assessmentsApi } from './assessments';
export { notificationsApi } from './notifications';
export { contractsApi } from './contracts';
export { enhancedAnalyticsApi } from './enhanced-analytics';

// Re-export admin analytics (now modular)
export { adminAnalyticsApi } from './admin-analytics';

// Re-export production tools API (now modular)
export { productionApi, schedulesApi, budgetsApi, resourcesApi, reportsApi } from './production';

// Re-export new dashboard API
export { dashboardApi } from './dashboard';

// Re-export team collaboration API (now modular)
export { 
  teamApi,
  teamAccessApi,
  teamActivitiesApi,
  teamDiscussionsApi,
  teamMembershipsApi
} from './team';
export type {
  TeamActivity,
  TeamDiscussion,
  DiscussionReply,
  TeamAccess,
  TeamMembership
} from './team';
