import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

// Blog types
export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_url?: string;
  category_id?: string;
  category?: BlogCategory;
  tags: string[];
  status: 'draft' | 'published' | 'scheduled';
  featured: boolean;
  published_at?: string;
  scheduled_for?: string;
  author_id: string;
  author?: {
    full_name?: string;
    username?: string;
  };
  seo_title?: string;
  seo_description?: string;
  seo_keywords: string[];
  read_time_minutes?: number;
  view_count: number;
  created_at: string;
  updated_at: string;
}

export interface BlogImage {
  id: string;
  blog_post_id?: string;
  file_name: string;
  storage_path: string;
  alt_text?: string;
  width?: number;
  height?: number;
  file_size: number;
  is_featured: boolean;
  uploaded_by: string;
  uploaded_at: string;
}

export interface CreateBlogPostData {
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_url?: string;
  category_id?: string;
  tags?: string[];
  status?: 'draft' | 'published' | 'scheduled';
  featured?: boolean;
  published_at?: string;
  scheduled_for?: string;
  seo_title?: string;
  seo_description?: string;
  seo_keywords?: string[];
}

export interface UpdateBlogPostData extends Partial<CreateBlogPostData> {
  id: string;
}

export const blogApi = {
  // Categories
  getCategories: async (): Promise<ApiResponse<BlogCategory[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_categories')
        .select('*')
        .order('name');
      
      return { data, error };
    });
  },

  createCategory: async (category: Omit<BlogCategory, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<BlogCategory>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_categories')
        .insert(category)
        .select()
        .single();
      
      return { data, error };
    });
  },

  // Blog Posts - Public (for frontend display)
  getPublishedPosts: async (limit?: number): Promise<ApiResponse<BlogPost[]>> => {
    return apiWrapper(async () => {
      let query = supabase
        .from('blog_posts')
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;
      return { data, error };
    });
  },

  getPublishedPostBySlug: async (slug: string): Promise<ApiResponse<BlogPost>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      // Increment view count
      if (data && !error) {
        await supabase
          .from('blog_posts')
          .update({ view_count: (data.view_count || 0) + 1 })
          .eq('id', data.id);
      }

      return { data, error };
    });
  },

  getFeaturedPosts: async (): Promise<ApiResponse<BlogPost[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .eq('status', 'published')
        .eq('featured', true)
        .order('published_at', { ascending: false })
        .limit(3);

      return { data, error };
    });
  },

  getPostsByCategory: async (categorySlug: string): Promise<ApiResponse<BlogPost[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .eq('status', 'published')
        .eq('category.slug', categorySlug)
        .order('published_at', { ascending: false });

      return { data, error };
    });
  },

  // Admin-only functions (Super_Admin role required)
  getAllPosts: async (): Promise<ApiResponse<BlogPost[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('blog_posts')
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .order('created_at', { ascending: false });

      return { data, error };
    });
  },

  createPost: async (postData: CreateBlogPostData): Promise<ApiResponse<BlogPost>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('blog_posts')
        .insert({
          ...postData,
          author_id: user.user.id,
          published_at: postData.status === 'published' ? new Date().toISOString() : null
        })
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .single();

      return { data, error };
    });
  },

  updatePost: async (postData: UpdateBlogPostData): Promise<ApiResponse<BlogPost>> => {
    return apiWrapper(async () => {
      const { id, ...updateData } = postData;
      
      // Set published_at if status is being changed to published
      if (updateData.status === 'published' && !updateData.published_at) {
        updateData.published_at = new Date().toISOString();
      }

      const { data, error } = await supabase
        .from('blog_posts')
        .update(updateData)
        .eq('id', id)
        .select(`
          *,
          category:blog_categories(*),
          author:profiles(full_name, username)
        `)
        .single();

      return { data, error };
    });
  },

  deletePost: async (id: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('blog_posts')
        .delete()
        .eq('id', id);

      return { data: undefined, error };
    });
  },

  // Image management
  uploadImage: async (file: File, altText?: string): Promise<ApiResponse<string>> => {
    return apiWrapper(async () => {
      // Validate file
      const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed.');
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB
        throw new Error('File size too large. Maximum size is 5MB.');
      }

      // Generate unique filename
      const fileExtension = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;
      const storagePath = `blog/${fileName}`;

      // Upload to storage
      const { error: uploadError } = await supabase.storage
        .from('blog-images')
        .upload(storagePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('blog-images')
        .getPublicUrl(storagePath);

      // Save image metadata
      const { data: user } = await supabase.auth.getUser();
      if (user.user) {
        await supabase
          .from('blog_images')
          .insert({
            file_name: file.name,
            storage_path: storagePath,
            alt_text: altText,
            file_size: file.size,
            is_featured: false,
            uploaded_by: user.user.id
          });
      }

      return { data: urlData.publicUrl, error: null };
    });
  },

  deleteImage: async (storagePath: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('blog-images')
        .remove([storagePath]);

      if (storageError) throw storageError;

      // Delete metadata
      const { error: dbError } = await supabase
        .from('blog_images')
        .delete()
        .eq('storage_path', storagePath);

      return { data: undefined, error: dbError };
    });
  }
};
