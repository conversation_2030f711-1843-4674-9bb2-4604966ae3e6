
export interface UserAnalytics {
  id: string;
  email: string;
  full_name: string | null;
  username: string | null;
  created_at: string;
  last_sign_in_at: string | null;
  organization_plan: string;
  organization_name: string;
  total_coverage_reports: number;
  total_scenes: number;
  total_screenplays: number;
  last_activity: string | null;
}

export interface PlatformStats {
  total_users: number;
  total_organizations: number;
  active_users_30_days: number;
  total_coverage_reports: number;
  total_scenes: number;
  total_screenplays: number;
  subscription_distribution: Record<string, number>;
  monthly_signups: Array<{ month: string; count: number }>;
}

export interface SystemUsage {
  coverage_generations_today: number;
  coverage_generations_week: number;
  coverage_generations_month: number;
  active_organizations: number;
  average_reports_per_user: number;
  top_features_used: Array<{ feature: string; usage_count: number }>;
}
