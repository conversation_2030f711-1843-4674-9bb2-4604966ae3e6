
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { SystemUsage } from './types';

/**
 * Get system usage metrics
 */
export const getSystemUsage = async (): Promise<ApiResponse<SystemUsage>> => {
  return apiWrapper(async () => {
    // Verify super admin status
    const { data: isSuperAdmin } = await supabase.rpc('is_super_admin');
    if (!isSuperAdmin) {
      throw new Error('Access denied: Super Admin privileges required');
    }

    const today = new Date().toISOString().split('T')[0];
    const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
    const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();

    const [
      todayUsage,
      weekUsage,
      monthUsage,
      activeOrgs,
      avgReports
    ] = await Promise.all([
      supabase.from('coverage_usage_tracking').select('generation_count').eq('usage_date', today),
      supabase.from('coverage_usage_tracking').select('generation_count').gte('usage_date', weekAgo.split('T')[0]),
      supabase.from('coverage_usage_tracking').select('generation_count').gte('usage_date', monthAgo.split('T')[0]),
      supabase.from('organizations').select('id').neq('plan', 'starter'),
      supabase.from('coverage_reports').select('user_id')
    ]);

    const todayTotal = (todayUsage.data || []).reduce((sum, usage) => sum + usage.generation_count, 0);
    const weekTotal = (weekUsage.data || []).reduce((sum, usage) => sum + usage.generation_count, 0);
    const monthTotal = (monthUsage.data || []).reduce((sum, usage) => sum + usage.generation_count, 0);

    // Calculate average reports per user
    const userReportCounts = (avgReports.data || []).reduce((acc, report) => {
      acc[report.user_id] = (acc[report.user_id] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageReportsPerUser = Object.keys(userReportCounts).length > 0 
      ? Object.values(userReportCounts).reduce((sum, count) => sum + count, 0) / Object.keys(userReportCounts).length
      : 0;

    const usage: SystemUsage = {
      coverage_generations_today: todayTotal,
      coverage_generations_week: weekTotal,
      coverage_generations_month: monthTotal,
      active_organizations: activeOrgs.count || 0,
      average_reports_per_user: Math.round(averageReportsPerUser * 100) / 100,
      top_features_used: [
        { feature: 'Coverage Generation', usage_count: monthTotal },
        { feature: 'Scene Management', usage_count: 0 }, // Would need tracking
        { feature: 'Storyboard Studio', usage_count: 0 }, // Would need tracking
      ]
    };

    return { data: usage, error: null };
  });
};
