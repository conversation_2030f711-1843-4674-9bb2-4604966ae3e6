
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { PlatformStats } from './types';

/**
 * Get platform-wide statistics
 */
export const getPlatformStats = async (): Promise<ApiResponse<PlatformStats>> => {
  return apiWrapper(async () => {
    // Verify super admin status
    const { data: isSuperAdmin } = await supabase.rpc('is_super_admin');
    if (!isSuperAdmin) {
      throw new Error('Access denied: Super Admin privileges required');
    }

    const [
      usersCount,
      orgsCount,
      coverageCount,
      scenesCount,
      screenplaysCount,
      subscriptionDist,
      monthlySignups
    ] = await Promise.all([
      supabase.from('profiles').select('id', { count: 'exact', head: true }),
      supabase.from('organizations').select('id', { count: 'exact', head: true }),
      supabase.from('coverage_reports').select('id', { count: 'exact', head: true }),
      supabase.from('scenes').select('id', { count: 'exact', head: true }),
      supabase.from('screenplays').select('id', { count: 'exact', head: true }),
      supabase.from('organizations').select('plan'),
      supabase.from('profiles').select('created_at').gte('created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString())
    ]);

    // Process subscription distribution
    const subscriptionDistribution = (subscriptionDist.data || []).reduce((acc, org) => {
      acc[org.plan] = (acc[org.plan] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Process monthly signups for the last 12 months
    const monthlyData = (monthlySignups.data || []).reduce((acc, profile) => {
      const month = new Date(profile.created_at).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const monthlySignupsArray = Object.entries(monthlyData).map(([month, count]) => ({ month, count }));

    const stats: PlatformStats = {
      total_users: usersCount.count || 0,
      total_organizations: orgsCount.count || 0,
      active_users_30_days: 0, // Will be calculated separately
      total_coverage_reports: coverageCount.count || 0,
      total_scenes: scenesCount.count || 0,
      total_screenplays: screenplaysCount.count || 0,
      subscription_distribution: subscriptionDistribution,
      monthly_signups: monthlySignupsArray
    };

    return { data: stats, error: null };
  });
};
