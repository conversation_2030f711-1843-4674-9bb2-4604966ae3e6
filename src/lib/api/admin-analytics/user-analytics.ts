
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from '../error-handling';
import type { ApiResponse } from '../types';
import type { UserAnalytics } from './types';

export interface UserAnalyticsFilters {
  role?: string;
  subscription_status?: string;
  date_range?: {
    start: string;
    end: string;
  };
  search?: string;
}

export const getUserAnalytics = async (
  filters?: UserAnalyticsFilters,
  page: number = 1,
  limit: number = 50
): Promise<ApiResponse<{ users: UserAnalytics[]; total: number }>> => {
  return apiWrapper(async () => {
    // Build the query - get profiles with organization data
    let query = supabase
      .from('profiles')
      .select(`
        id,
        full_name,
        username,
        role,
        created_at,
        organization_members (
          org_id,
          organizations (
            name,
            plan
          )
        )
      `, { count: 'exact' });

    // Apply filters
    if (filters?.role) {
      query = query.eq('role', filters.role);
    }

    if (filters?.search) {
      query = query.or(`full_name.ilike.%${filters.search}%,username.ilike.%${filters.search}%`);
    }

    if (filters?.date_range) {
      query = query
        .gte('created_at', filters.date_range.start)
        .lte('created_at', filters.date_range.end);
    }

    // Apply pagination
    const offset = (page - 1) * limit;
    query = query.range(offset, offset + limit - 1);

    const { data, error, count } = await query;

    if (error) {
      return { data: null, error };
    }

    const transformedUsers: UserAnalytics[] = (data || []).map(user => {
      // Handle the organization_members array and extract organization data
      const orgMember = Array.isArray(user.organization_members) ? user.organization_members[0] : null;
      const organization = orgMember && typeof orgMember === 'object' && 'organizations' in orgMember ? orgMember.organizations : null;
      
      return {
        id: user.id,
        email: '', // Email not available in profiles table
        full_name: user.full_name,
        username: user.username,
        created_at: user.created_at,
        last_sign_in_at: null, // Not available in profiles table
        organization_plan: organization && typeof organization === 'object' && 'plan' in organization ? organization.plan || 'starter' : 'starter',
        organization_name: organization && typeof organization === 'object' && 'name' in organization ? organization.name || 'Unknown' : 'Unknown',
        total_coverage_reports: 0,
        total_scenes: 0,
        total_screenplays: 0,
        last_activity: null
      };
    });

    return {
      data: {
        users: transformedUsers,
        total: count || 0
      },
      error: null
    };
  });
};

export const getUserById = async (userId: string): Promise<ApiResponse<UserAnalytics>> => {
  return apiWrapper(async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        id,
        full_name,
        username,
        role,
        created_at,
        organization_members (
          org_id,
          organizations (
            name,
            plan
          )
        )
      `)
      .eq('id', userId)
      .single();

    if (error) {
      return { data: null, error };
    }

    // Handle the organization_members array and extract organization data
    const orgMember = Array.isArray(data.organization_members) ? data.organization_members[0] : null;
    const organization = orgMember && typeof orgMember === 'object' && 'organizations' in orgMember ? orgMember.organizations : null;

    const transformedUser: UserAnalytics = {
      id: data.id,
      email: '', // Email not available in profiles table
      full_name: data.full_name,
      username: data.username,
      created_at: data.created_at,
      last_sign_in_at: null, // Not available in profiles table
      organization_plan: organization && typeof organization === 'object' && 'plan' in organization ? organization.plan || 'starter' : 'starter',
      organization_name: organization && typeof organization === 'object' && 'name' in organization ? organization.name || 'Unknown' : 'Unknown',
      total_coverage_reports: 0,
      total_scenes: 0,
      total_screenplays: 0,
      last_activity: null
    };

    return { data: transformedUser, error: null };
  });
};

export const userAnalyticsApi = {
  getUserAnalytics,
  getUserById
};
