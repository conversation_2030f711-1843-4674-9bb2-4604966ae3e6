
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert } from '@/integrations/supabase/types';

export type ScreenplayFile = Tables<'screenplay_files'>;
export type ScreenplayFileInsert = TablesInsert<'screenplay_files'>;

export const filesApi = {
  // Upload file
  uploadFile: async (
    screenplayId: string,
    file: File,
    isPreview: boolean = false
  ): Promise<ApiResponse<ScreenplayFile>> => {
    return apiWrapper(async () => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('User not authenticated');

      // Create unique file path
      const fileExtension = file.name.split('.').pop();
      const fileName = `${Date.now()}-${file.name}`;
      const filePath = `${user.user.id}/${screenplayId}/${fileName}`;

      // Upload to storage
      const { error: uploadError } = await supabase.storage
        .from('screenplay-files')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Create database record
      const { data, error } = await supabase
        .from('screenplay_files')
        .insert({
          screenplay_id: screenplayId,
          file_name: file.name,
          file_type: file.type,
          file_size: file.size,
          storage_path: filePath,
          is_preview: isPreview,
          uploaded_by: user.user.id
        })
        .select()
        .single();

      return { data, error };
    });
  },

  // Get files for screenplay
  getScreenplayFiles: async (screenplayId: string): Promise<ApiResponse<ScreenplayFile[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('screenplay_files')
        .select('*')
        .eq('screenplay_id', screenplayId)
        .order('uploaded_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Get download URL
  getDownloadUrl: async (filePath: string): Promise<ApiResponse<string>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase.storage
        .from('screenplay-files')
        .createSignedUrl(filePath, 3600); // 1 hour expiry

      if (error) throw error;
      return { data: data.signedUrl, error: null };
    });
  },

  // Delete file
  deleteFile: async (fileId: string): Promise<ApiResponse<void>> => {
    return apiWrapper(async () => {
      // Get file info first
      const { data: fileData, error: getError } = await supabase
        .from('screenplay_files')
        .select('storage_path')
        .eq('id', fileId)
        .single();

      if (getError) throw getError;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('screenplay-files')
        .remove([fileData.storage_path]);

      if (storageError) throw storageError;

      // Delete from database
      const { error } = await supabase
        .from('screenplay_files')
        .delete()
        .eq('id', fileId);

      return { data: undefined, error };
    });
  }
};
