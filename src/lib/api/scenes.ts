
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for scene-related operations
 */
export const scenesApi = {
  /**
   * Fetches all scenes for the current user's organization
   */
  getScenes: (orgId?: string) => apiWrapper(async () => {
    let query = supabase
      .from('scenes')
      .select(`
        *,
        locations(name),
        scene_characters(
          id,
          is_main_character,
          characters(name)
        )
      `)
      .order('act')
      .order('order_index');
    
    if (orgId) {
      query = query.eq('org_id', orgId);
    }
    
    const result = await query;
    return result;
  }),

  /**
   * Creates a new scene
   */
  createScene: (data: { 
    title: string;
    description?: string;
    content?: string;
    act?: number;
    order_index?: number;
    location_id?: string;
    duration_minutes?: number;
    notes?: string;
    org_id?: string;
  }) => apiWrapper(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const result = await supabase
      .from('scenes')
      .insert([{ ...data, user_id: user.id }])
      .select()
      .single();
    return result;
  }),

  /**
   * Updates a scene
   */
  updateScene: (id: string, updates: Partial<{ 
    title: string;
    description: string;
    content: string;
    act: number;
    order_index: number;
    location_id: string;
    duration_minutes: number;
    notes: string;
  }>) => apiWrapper(async () => {
    const result = await supabase
      .from('scenes')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    return result;
  }),

  /**
   * Deletes a scene
   */
  deleteScene: (id: string) => apiWrapper(async () => {
    const result = await supabase
      .from('scenes')
      .delete()
      .eq('id', id);
    return result;
  }),

  /**
   * Adds a character to a scene
   */
  addCharacterToScene: (sceneId: string, characterId: string, isMain: boolean = false) => 
    apiWrapper(async () => {
      const result = await supabase
        .from('scene_characters')
        .insert([{ 
          scene_id: sceneId, 
          character_id: characterId, 
          is_main_character: isMain 
        }])
        .select()
        .single();
      return result;
    }),

  /**
   * Removes a character from a scene
   */
  removeCharacterFromScene: (sceneId: string, characterId: string) => 
    apiWrapper(async () => {
      const result = await supabase
        .from('scene_characters')
        .delete()
        .eq('scene_id', sceneId)
        .eq('character_id', characterId);
      return result;
    })
};
