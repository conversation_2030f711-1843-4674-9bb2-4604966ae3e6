
import { supabase } from '@/integrations/supabase/client';
import { handleSupabaseError, apiWrapper } from './error-handling';
import type { ApiResponse } from './types';

export interface Storyboard {
  id: string;
  org_id: string;
  user_id: string;
  title: string;
  description?: string;
  fidelity: string;
  created_at: string;
  updated_at: string;
}

export interface StoryboardPanel {
  id: string;
  storyboard_id: string;
  org_id: string;
  user_id: string;
  image_url?: string;
  dialogue?: string;
  order_index: number;
  feedback?: string;
  scene_id?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateStoryboardData {
  org_id: string;
  title: string;
  description?: string;
  fidelity?: string;
}

export interface CreateStoryboardPanelData {
  storyboard_id: string;
  org_id: string;
  image_url?: string;
  dialogue?: string;
  order_index: number;
  feedback?: string;
  scene_id?: string;
}

export const storyboardsApi = {
  async getStoryboards(): Promise<ApiResponse<Storyboard[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboards')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async getStoryboard(id: string): Promise<ApiResponse<Storyboard>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboards')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async createStoryboard(storyboardData: CreateStoryboardData): Promise<ApiResponse<Storyboard>> {
    return apiWrapper(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('storyboards')
        .insert({
          ...storyboardData,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updateStoryboard(id: string, updates: Partial<CreateStoryboardData>): Promise<ApiResponse<Storyboard>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboards')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async deleteStoryboard(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('storyboards')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  },

  async getStoryboardPanels(storyboardId: string): Promise<ApiResponse<StoryboardPanel[]>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboard_panels')
        .select('*')
        .eq('storyboard_id', storyboardId)
        .order('order_index', { ascending: true });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async createStoryboardPanel(panelData: CreateStoryboardPanelData): Promise<ApiResponse<StoryboardPanel>> {
    return apiWrapper(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('storyboard_panels')
        .insert({
          ...panelData,
          user_id: user.id
        })
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updateStoryboardPanel(id: string, updates: Partial<CreateStoryboardPanelData>): Promise<ApiResponse<StoryboardPanel>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboard_panels')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  },

  async deleteStoryboardPanel(id: string): Promise<ApiResponse<void>> {
    return apiWrapper(async () => {
      const { error } = await supabase
        .from('storyboard_panels')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return { data: null, error: null };
    });
  },

  async searchStoryboards(orgId: string, query: string): Promise<ApiResponse<Storyboard[]>> {
    return apiWrapper(async () => {
      let supabaseQuery = supabase
        .from('storyboards')
        .select('*')
        .eq('org_id', orgId);

      if (query.trim()) {
        supabaseQuery = supabaseQuery.textSearch('fts', query.trim());
      }

      const { data, error } = await supabaseQuery.order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async searchStoryboardPanels(storyboardId: string, query: string): Promise<ApiResponse<StoryboardPanel[]>> {
    return apiWrapper(async () => {
      let supabaseQuery = supabase
        .from('storyboard_panels')
        .select('*')
        .eq('storyboard_id', storyboardId);

      if (query.trim()) {
        supabaseQuery = supabaseQuery.textSearch('search_vector', query.trim());
      }

      const { data, error } = await supabaseQuery.order('order_index', { ascending: true });

      if (error) throw error;
      return { data, error: null };
    });
  },

  async updateStoryboardExportSettings(id: string, exportSettings: any): Promise<ApiResponse<Storyboard>> {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('storyboards')
        .update({
          export_settings: exportSettings,
          last_exported_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    });
  }
};
