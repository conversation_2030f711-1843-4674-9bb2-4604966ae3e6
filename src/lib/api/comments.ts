
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';

/**
 * API service for comment-related operations
 */
export const commentsApi = {
  /**
   * Fetches comments for a specific scene
   */
  getSceneComments: (sceneId: string) => apiWrapper(async () => {
    const result = await supabase
      .from('comments')
      .select(`
        *,
        profiles(username, full_name)
      `)
      .eq('scene_id', sceneId)
      .order('created_at', { ascending: false });
    return result;
  }),

  /**
   * Creates a new comment
   */
  createComment: (data: {
    scene_id: string;
    content: string;
    line_number?: number;
  }) => apiWrapper(async () => {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const result = await supabase
      .from('comments')
      .insert([{ ...data, user_id: user.id }])
      .select()
      .single();
    return result;
  }),

  /**
   * Updates a comment
   */
  updateComment: (id: string, updates: Partial<{
    content: string;
    resolved: boolean;
  }>) => apiWrapper(async () => {
    const result = await supabase
      .from('comments')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();
    return result;
  }),

  /**
   * Deletes a comment
   */
  deleteComment: (id: string) => apiWrapper(async () => {
    const result = await supabase
      .from('comments')
      .delete()
      .eq('id', id);
    return result;
  })
};
