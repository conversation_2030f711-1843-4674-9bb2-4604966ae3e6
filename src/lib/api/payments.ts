
import { supabase } from '@/integrations/supabase/client';
import { apiWrapper } from './error-handling';
import type { ApiResponse } from './types';
import type { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

export type Transaction = Tables<'transactions'>;
export type TransactionInsert = TablesInsert<'transactions'>;
export type TransactionUpdate = TablesUpdate<'transactions'>;
export type SellerAccount = Tables<'seller_accounts'>;
export type EarningsSummary = Tables<'earnings_summary'>;
export type SellerOnboardingSession = Tables<'seller_onboarding_sessions'>;

// Define the onboarding status response type
export type OnboardingStatusResponse = {
  needsOnboarding: boolean;
  accountLink?: string;
};

export const paymentsApi = {
  // Create Stripe Connect account for seller
  createSellerAccount: async (): Promise<ApiResponse<{ accountLink: string; accountId: string }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase.functions.invoke('create-seller-account');
      
      if (error) throw error;
      return { data, error: null };
    });
  },

  // Get seller account status with enhanced Connect data
  getSellerAccount: async (): Promise<ApiResponse<SellerAccount>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('seller_accounts')
        .select('*')
        .single();
      
      return { data, error };
    });
  },

  // Get seller onboarding sessions
  getOnboardingSessions: async (): Promise<ApiResponse<SellerOnboardingSession[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('seller_onboarding_sessions')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Check if seller needs to complete onboarding
  checkOnboardingStatus: async (): Promise<ApiResponse<OnboardingStatusResponse>> => {
    return apiWrapper(async () => {
      const { data: account, error } = await supabase
        .from('seller_accounts')
        .select('*')
        .single();

      // If no account exists, they need onboarding
      if (error) {
        return { 
          data: { needsOnboarding: true }, 
          error: null 
        };
      }

      const needsOnboarding = !account.onboarding_completed || 
                             !account.charges_enabled || 
                             !account.details_submitted;

      // Prepare the base response
      const response: OnboardingStatusResponse = { needsOnboarding };

      if (needsOnboarding) {
        // Check for active onboarding session
        const { data: sessions } = await supabase
          .from('seller_onboarding_sessions')
          .select('*')
          .eq('completed', false)
          .gt('expires_at', new Date().toISOString())
          .order('created_at', { ascending: false })
          .limit(1);

        const activeSession = sessions?.[0];
        if (activeSession?.stripe_account_link_id) {
          response.accountLink = activeSession.stripe_account_link_id;
        }
      }

      return { data: response, error: null };
    });
  },

  // Process screenplay purchase
  purchaseScreenplay: async (screenplayId: string, amount: number): Promise<ApiResponse<{ sessionUrl: string }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase.functions.invoke('process-payment', {
        body: {
          screenplay_id: screenplayId,
          amount: amount,
          type: 'purchase'
        }
      });
      
      if (error) throw error;
      return { data, error: null };
    });
  },

  // Process offer payment
  processOfferPayment: async (offerId: string, amount: number): Promise<ApiResponse<{ sessionUrl: string }>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase.functions.invoke('process-payment', {
        body: {
          offer_id: offerId,
          amount: amount,
          type: 'offer_payment'
        }
      });
      
      if (error) throw error;
      return { data, error: null };
    });
  },

  // Get user's transactions
  getMyTransactions: async (): Promise<ApiResponse<Transaction[]>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .order('created_at', { ascending: false });
      
      return { data, error };
    });
  },

  // Get earnings summary
  getEarningsSummary: async (): Promise<ApiResponse<EarningsSummary>> => {
    return apiWrapper(async () => {
      const { data, error } = await supabase
        .from('earnings_summary')
        .select('*')
        .single();
      
      return { data, error };
    });
  },

  // Get seller account requirements and status
  getAccountRequirements: async (): Promise<ApiResponse<{
    requirements: any;
    accountStatus: string;
    missingRequirements: string[];
  }>> => {
    return apiWrapper(async () => {
      const { data: account, error } = await supabase
        .from('seller_accounts')
        .select('requirements, account_status, capabilities')
        .single();

      if (error) {
        return { data: null, error };
      }

      // Cast requirements to proper type and handle it safely
      const requirements = (account.requirements as any) || {};
      const missingRequirements = [
        ...(Array.isArray(requirements.currently_due) ? requirements.currently_due : []),
        ...(Array.isArray(requirements.past_due) ? requirements.past_due : [])
      ];

      return { 
        data: {
          requirements,
          accountStatus: account.account_status,
          missingRequirements
        }, 
        error: null 
      };
    });
  }
};
