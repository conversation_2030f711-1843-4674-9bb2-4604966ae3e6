import { toast } from 'sonner';

interface ToastOptions {
  duration?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  action?: {
    label: string;
    onClick: () => void;
  };
}

const defaultOptions: ToastOptions = {
  duration: 4000,
  position: 'top-right',
};

export const showToast = {
  success: (message: string, options: ToastOptions = {}) => {
    toast.success(message, { ...defaultOptions, ...options });
  },

  error: (message: string, options: ToastOptions = {}) => {
    toast.error(message, { ...defaultOptions, ...options });
  },

  warning: (message: string, options: ToastOptions = {}) => {
    toast.warning(message, { ...defaultOptions, ...options });
  },

  info: (message: string, options: ToastOptions = {}) => {
    toast.info(message, { ...defaultOptions, ...options });
  },

  promise: <T>(
    promise: Promise<T>,
    {
      loading = 'Loading...',
      success = 'Success!',
      error = 'Something went wrong',
    }: {
      loading?: string;
      success?: string;
      error?: string;
    } = {}
  ) => {
    return toast.promise(promise, {
      loading,
      success,
      error,
    });
  },

  dismiss: (toastId?: string) => {
    toast.dismiss(toastId);
  },

  dismissAll: () => {
    toast.dismiss();
  },
}; 