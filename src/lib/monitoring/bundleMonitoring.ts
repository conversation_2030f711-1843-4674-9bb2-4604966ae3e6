
class BundleMonitoring {
  analyzeBundleSize() {
    if (typeof window === 'undefined' || !import.meta.env.DEV) return;

    try {
      // Basic bundle analysis for development
      const performanceEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      const navigationEntry = performanceEntries[0];

      if (navigationEntry) {
        const transferSize = navigationEntry.transferSize || 0;
        const decodedBodySize = navigationEntry.decodedBodySize || 0;

        console.info('📦 Bundle Analysis:', {
          transferSize: `${(transferSize / 1024).toFixed(2)} KB`,
          decodedSize: `${(decodedBodySize / 1024).toFixed(2)} KB`,
          compressionRatio: transferSize > 0 ? ((decodedBodySize - transferSize) / decodedBodySize * 100).toFixed(1) + '%' : 'N/A'
        });
      }

      // Analyze resource loading
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const jsResources = resources.filter(r => r.name.includes('.js'));
      const cssResources = resources.filter(r => r.name.includes('.css'));

      console.info('📊 Resource Breakdown:', {
        jsFiles: jsResources.length,
        cssFiles: cssResources.length,
        totalResources: resources.length,
        heaviestJS: jsResources.reduce((max, r) => r.transferSize > max.transferSize ? r : max, { transferSize: 0, name: 'none' })
      });
    } catch (error) {
      console.warn('Bundle analysis failed:', error);
    }
  }

  trackLazyLoadedModule(moduleName: string, loadTime: number) {
    if (import.meta.env.DEV) {
      console.info(`🔄 Lazy loaded: ${moduleName} in ${loadTime.toFixed(2)}ms`);
    }
  }
}

export const bundleMonitoring = new BundleMonitoring();
