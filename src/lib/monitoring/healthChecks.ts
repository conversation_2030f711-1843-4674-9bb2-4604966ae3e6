
export interface HealthCheckResult {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  error?: string;
  metadata?: Record<string, any>;
}

export interface SystemHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

export class HealthCheckManager {
  private static instance: HealthCheckManager;
  private checks: Map<string, () => Promise<HealthCheckResult>> = new Map();
  private startTime = Date.now();

  static getInstance(): HealthCheckManager {
    if (!HealthCheckManager.instance) {
      HealthCheckManager.instance = new HealthCheckManager();
    }
    return HealthCheckManager.instance;
  }

  constructor() {
    this.registerDefaultChecks();
  }

  private registerDefaultChecks() {
    this.registerCheck('database', this.checkDatabase.bind(this));
    this.registerCheck('auth', this.checkAuth.bind(this));
    this.registerCheck('storage', this.checkStorage.bind(this));
    this.registerCheck('memory', this.checkMemory.bind(this));
    this.registerCheck('network', this.checkNetwork.bind(this));
  }

  registerCheck(name: string, checkFn: () => Promise<HealthCheckResult>) {
    this.checks.set(name, checkFn);
  }

  async runAllChecks(): Promise<SystemHealth> {
    const results: HealthCheckResult[] = [];
    
    for (const [name, checkFn] of this.checks) {
      try {
        const result = await Promise.race([
          checkFn(),
          this.timeoutCheck(name, 5000) // 5 second timeout
        ]);
        results.push(result);
      } catch (error) {
        results.push({
          name,
          status: 'unhealthy',
          responseTime: 5000,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const summary = this.calculateSummary(results);
    const overallStatus = this.determineOverallStatus(summary);

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      environment: import.meta.env.NODE_ENV || 'development',
      uptime: Date.now() - this.startTime,
      checks: results,
      summary
    };
  }

  async runCheck(name: string): Promise<HealthCheckResult> {
    const checkFn = this.checks.get(name);
    if (!checkFn) {
      throw new Error(`Health check '${name}' not found`);
    }
    return await checkFn();
  }

  private async timeoutCheck(name: string, timeout: number): Promise<HealthCheckResult> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Health check '${name}' timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  private async checkDatabase(): Promise<HealthCheckResult> {
    const start = performance.now();
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { error } = await supabase.from('profiles').select('id').limit(1);
      
      return {
        name: 'database',
        status: error ? 'unhealthy' : 'healthy',
        responseTime: performance.now() - start,
        error: error?.message,
        metadata: { connection: 'supabase' }
      };
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : 'Unknown database error'
      };
    }
  }

  private async checkAuth(): Promise<HealthCheckResult> {
    const start = performance.now();
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { error } = await supabase.auth.getSession();
      
      return {
        name: 'auth',
        status: error ? 'unhealthy' : 'healthy',
        responseTime: performance.now() - start,
        error: error?.message,
        metadata: { provider: 'supabase-auth' }
      };
    } catch (error) {
      return {
        name: 'auth',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : 'Unknown auth error'
      };
    }
  }

  private async checkStorage(): Promise<HealthCheckResult> {
    const start = performance.now();
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase.storage.listBuckets();
      
      return {
        name: 'storage',
        status: error ? 'unhealthy' : 'healthy',
        responseTime: performance.now() - start,
        error: error?.message,
        metadata: { 
          provider: 'supabase-storage',
          buckets: data?.length || 0
        }
      };
    } catch (error) {
      return {
        name: 'storage',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : 'Unknown storage error'
      };
    }
  }

  private async checkMemory(): Promise<HealthCheckResult> {
    const start = performance.now();
    try {
      let memoryInfo = { used: 0, total: 0 };
      
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryInfo = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize
        };
      }

      const usagePercent = memoryInfo.total > 0 ? (memoryInfo.used / memoryInfo.total) * 100 : 0;
      const status = usagePercent > 90 ? 'unhealthy' : usagePercent > 75 ? 'degraded' : 'healthy';

      return {
        name: 'memory',
        status,
        responseTime: performance.now() - start,
        metadata: {
          usedBytes: memoryInfo.used,
          totalBytes: memoryInfo.total,
          usagePercent: Math.round(usagePercent)
        }
      };
    } catch (error) {
      return {
        name: 'memory',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : 'Unknown memory error'
      };
    }
  }

  private async checkNetwork(): Promise<HealthCheckResult> {
    const start = performance.now();
    try {
      // Check network connectivity with a simple request
      const response = await fetch('https://httpbin.org/status/200', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const responseTime = performance.now() - start;
      const status = response.ok ? 'healthy' : 'unhealthy';

      return {
        name: 'network',
        status,
        responseTime,
        metadata: {
          statusCode: response.status,
          online: navigator.onLine
        }
      };
    } catch (error) {
      return {
        name: 'network',
        status: 'unhealthy',
        responseTime: performance.now() - start,
        error: error instanceof Error ? error.message : 'Network connectivity error',
        metadata: {
          online: navigator.onLine
        }
      };
    }
  }

  private calculateSummary(results: HealthCheckResult[]) {
    return {
      total: results.length,
      healthy: results.filter(r => r.status === 'healthy').length,
      unhealthy: results.filter(r => r.status === 'unhealthy').length,
      degraded: results.filter(r => r.status === 'degraded').length
    };
  }

  private determineOverallStatus(summary: { healthy: number; unhealthy: number; degraded: number; total: number }): 'healthy' | 'unhealthy' | 'degraded' {
    if (summary.unhealthy > 0) return 'unhealthy';
    if (summary.degraded > 0) return 'degraded';
    return 'healthy';
  }
}

export const healthCheckManager = HealthCheckManager.getInstance();
