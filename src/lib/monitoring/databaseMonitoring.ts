
export interface DatabaseBackup {
  id: string;
  backupType: 'full' | 'incremental' | 'point_in_time';
  backupStatus: 'pending' | 'running' | 'completed' | 'failed';
  backupLocation: string;
  backupSize?: number;
  startedAt: string;
  completedAt?: string;
  retentionUntil: string;
  metadata: Record<string, any>;
  checksum?: string;
  errorMessage?: string;
  createdBy?: string;
  orgId?: string;
}

export interface MigrationRecord {
  id: string;
  migrationName: string;
  migrationVersion: string;
  appliedAt: string;
  rollbackSql?: string;
  migrationChecksum?: string;
  appliedBy?: string;
  rollbackAvailable: boolean;
  rollbackTested: boolean;
  environment: string;
}

export interface DatabasePerformanceMetrics {
  id: string;
  timestamp: string;
  connectionCount: number;
  activeConnections: number;
  idleConnections: number;
  maxConnections: number;
  queryPerformance: Record<string, any>;
  cacheHitRatio?: number;
  indexUsage?: Record<string, any>;
  slowQueries?: any[];
  lockWaits?: number;
  deadlocks?: number;
  tempFilesCreated?: number;
  tempBytes?: number;
  databaseSize?: number;
  tableStats?: Record<string, any>;
  bufferStats?: Record<string, any>;
}

export interface ConnectionPoolMetrics {
  id: string;
  timestamp: string;
  poolName: string;
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingConnections: number;
  maxConnections: number;
  connectionErrors?: number;
  avgConnectionTime?: number;
  poolExhaustedCount?: number;
  metadata?: Record<string, any>;
}

// Transform functions to convert database records to interface types
function transformDatabaseBackup(dbRecord: any): DatabaseBackup {
  return {
    id: dbRecord.id,
    backupType: dbRecord.backup_type,
    backupStatus: dbRecord.backup_status,
    backupLocation: dbRecord.backup_location,
    backupSize: dbRecord.backup_size,
    startedAt: dbRecord.started_at,
    completedAt: dbRecord.completed_at,
    retentionUntil: dbRecord.retention_until,
    metadata: dbRecord.metadata || {},
    checksum: dbRecord.checksum,
    errorMessage: dbRecord.error_message,
    createdBy: dbRecord.created_by,
    orgId: dbRecord.org_id
  };
}

function transformMigrationRecord(dbRecord: any): MigrationRecord {
  return {
    id: dbRecord.id,
    migrationName: dbRecord.migration_name,
    migrationVersion: dbRecord.migration_version,
    appliedAt: dbRecord.applied_at,
    rollbackSql: dbRecord.rollback_sql,
    migrationChecksum: dbRecord.migration_checksum,
    appliedBy: dbRecord.applied_by,
    rollbackAvailable: dbRecord.rollback_available,
    rollbackTested: dbRecord.rollback_tested,
    environment: dbRecord.environment
  };
}

function transformPerformanceMetrics(dbRecord: any): DatabasePerformanceMetrics {
  return {
    id: dbRecord.id,
    timestamp: dbRecord.timestamp,
    connectionCount: dbRecord.connection_count,
    activeConnections: dbRecord.active_connections,
    idleConnections: dbRecord.idle_connections,
    maxConnections: dbRecord.max_connections,
    queryPerformance: dbRecord.query_performance || {},
    cacheHitRatio: dbRecord.cache_hit_ratio,
    indexUsage: dbRecord.index_usage,
    slowQueries: dbRecord.slow_queries,
    lockWaits: dbRecord.lock_waits,
    deadlocks: dbRecord.deadlocks,
    tempFilesCreated: dbRecord.temp_files_created,
    tempBytes: dbRecord.temp_bytes,
    databaseSize: dbRecord.database_size,
    tableStats: dbRecord.table_stats,
    bufferStats: dbRecord.buffer_stats
  };
}

function transformConnectionPoolMetrics(dbRecord: any): ConnectionPoolMetrics {
  return {
    id: dbRecord.id,
    timestamp: dbRecord.timestamp,
    poolName: dbRecord.pool_name,
    totalConnections: dbRecord.total_connections,
    activeConnections: dbRecord.active_connections,
    idleConnections: dbRecord.idle_connections,
    waitingConnections: dbRecord.waiting_connections,
    maxConnections: dbRecord.max_connections,
    connectionErrors: dbRecord.connection_errors,
    avgConnectionTime: dbRecord.avg_connection_time,
    poolExhaustedCount: dbRecord.pool_exhausted_count,
    metadata: dbRecord.metadata
  };
}

export class DatabaseMonitoringService {
  private static instance: DatabaseMonitoringService;
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;

  static getInstance(): DatabaseMonitoringService {
    if (!DatabaseMonitoringService.instance) {
      DatabaseMonitoringService.instance = new DatabaseMonitoringService();
    }
    return DatabaseMonitoringService.instance;
  }

  async initiateBackup(
    backupType: 'full' | 'incremental' | 'point_in_time' = 'full',
    retentionDays: number = 30
  ): Promise<string> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase.rpc('initiate_database_backup', {
        backup_type_param: backupType,
        retention_days: retentionDays
      });

      if (error) throw error;
      
      console.log(`✅ Database backup initiated: ${data}`);
      return data;
    } catch (error) {
      console.error('Failed to initiate backup:', error);
      throw error;
    }
  }

  async getBackups(limit: number = 50): Promise<DatabaseBackup[]> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase
        .from('database_backups')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return (data || []).map(transformDatabaseBackup);
    } catch (error) {
      console.error('Failed to fetch backups:', error);
      return [];
    }
  }

  async recordMigration(
    migrationName: string,
    migrationVersion: string,
    rollbackSql?: string,
    migrationChecksum?: string
  ): Promise<string> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase.rpc('record_migration', {
        migration_name_param: migrationName,
        migration_version_param: migrationVersion,
        rollback_sql_param: rollbackSql,
        migration_checksum_param: migrationChecksum
      });

      if (error) throw error;
      
      console.log(`📝 Migration recorded: ${data}`);
      return data;
    } catch (error) {
      console.error('Failed to record migration:', error);
      throw error;
    }
  }

  async getMigrationHistory(limit: number = 50): Promise<MigrationRecord[]> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase
        .from('migration_history')
        .select('*')
        .order('applied_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return (data || []).map(transformMigrationRecord);
    } catch (error) {
      console.error('Failed to fetch migration history:', error);
      return [];
    }
  }

  async collectPerformanceMetrics(): Promise<string> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase.rpc('collect_database_metrics');

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Failed to collect performance metrics:', error);
      throw error;
    }
  }

  async getPerformanceMetrics(limit: number = 100): Promise<DatabasePerformanceMetrics[]> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase
        .from('database_performance_metrics')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return (data || []).map(transformPerformanceMetrics);
    } catch (error) {
      console.error('Failed to fetch performance metrics:', error);
      return [];
    }
  }

  async getConnectionPoolMetrics(limit: number = 100): Promise<ConnectionPoolMetrics[]> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase
        .from('connection_pool_metrics')
        .select('*')
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return (data || []).map(transformConnectionPoolMetrics);
    } catch (error) {
      console.error('Failed to fetch connection pool metrics:', error);
      return [];
    }
  }

  startMonitoring(intervalMs: number = 60000): void {
    if (this.isMonitoring) {
      this.stopMonitoring();
    }

    console.log('🔄 Starting database monitoring...');
    this.isMonitoring = true;

    // Collect initial metrics
    this.collectPerformanceMetrics();

    // Set up periodic collection
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectPerformanceMetrics();
      } catch (error) {
        console.error('Error during periodic metrics collection:', error);
      }
    }, intervalMs);
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    this.isMonitoring = false;
    console.log('⏹️ Database monitoring stopped');
  }

  isCurrentlyMonitoring(): boolean {
    return this.isMonitoring;
  }

  // Simulate connection pool monitoring
  async recordConnectionPoolMetrics(poolName: string): Promise<void> {
    try {
      // Simulate connection pool statistics
      const metrics = {
        poolName,
        totalConnections: Math.floor(Math.random() * 100) + 10,
        activeConnections: Math.floor(Math.random() * 50) + 5,
        idleConnections: Math.floor(Math.random() * 30) + 2,
        waitingConnections: Math.floor(Math.random() * 5),
        maxConnections: 100,
        connectionErrors: Math.floor(Math.random() * 3),
        avgConnectionTime: Math.random() * 100 + 10,
        poolExhaustedCount: Math.floor(Math.random() * 2)
      };

      const { supabase } = await import('@/integrations/supabase/client');
      const { error } = await supabase
        .from('connection_pool_metrics')
        .insert([{
          pool_name: metrics.poolName,
          total_connections: metrics.totalConnections,
          active_connections: metrics.activeConnections,
          idle_connections: metrics.idleConnections,
          waiting_connections: metrics.waitingConnections,
          max_connections: metrics.maxConnections,
          connection_errors: metrics.connectionErrors,
          avg_connection_time: metrics.avgConnectionTime,
          pool_exhausted_count: metrics.poolExhaustedCount
        }]);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to record connection pool metrics:', error);
    }
  }

  // Generate database health report
  async generateHealthReport(): Promise<{
    backupStatus: 'healthy' | 'warning' | 'critical';
    performanceStatus: 'healthy' | 'warning' | 'critical';
    connectionStatus: 'healthy' | 'warning' | 'critical';
    recommendations: string[];
  }> {
    try {
      const [backups, metrics, poolMetrics] = await Promise.all([
        this.getBackups(5),
        this.getPerformanceMetrics(10),
        this.getConnectionPoolMetrics(10)
      ]);

      const recommendations: string[] = [];

      // Check backup status
      const recentBackups = backups.filter(b => 
        new Date(b.startedAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
      );
      const backupStatus = recentBackups.length > 0 ? 'healthy' : 'warning';
      
      if (backupStatus === 'warning') {
        recommendations.push('No recent backups found. Consider scheduling regular backups.');
      }

      // Check performance metrics
      const latestMetrics = metrics[0];
      let performanceStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (latestMetrics) {
        const connectionUtilization = latestMetrics.activeConnections / latestMetrics.maxConnections;
        if (connectionUtilization > 0.8) {
          performanceStatus = 'warning';
          recommendations.push('High connection utilization detected. Consider connection pooling optimization.');
        }
        
        if (latestMetrics.cacheHitRatio && latestMetrics.cacheHitRatio < 0.9) {
          performanceStatus = 'warning';
          recommendations.push('Low cache hit ratio. Consider index optimization or query tuning.');
        }
      }

      // Check connection pool status
      const latestPoolMetrics = poolMetrics[0];
      let connectionStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
      
      if (latestPoolMetrics?.poolExhaustedCount && latestPoolMetrics.poolExhaustedCount > 0) {
        connectionStatus = 'warning';
        recommendations.push('Connection pool exhaustion detected. Consider increasing pool size.');
      }

      return {
        backupStatus,
        performanceStatus,
        connectionStatus,
        recommendations
      };
    } catch (error) {
      console.error('Failed to generate health report:', error);
      return {
        backupStatus: 'critical',
        performanceStatus: 'critical',
        connectionStatus: 'critical',
        recommendations: ['Unable to generate health report. Check system connectivity.']
      };
    }
  }
}

export const databaseMonitoring = DatabaseMonitoringService.getInstance();
