
interface ErrorContext {
  tags?: Record<string, any>;
  user?: string;
  organization?: string;
  url?: string;
  timestamp?: number;
}

class ErrorReporting {
  private enabled = false;
  private context: ErrorContext = {};

  setEnabled(enabled: boolean) {
    this.enabled = enabled;
  }

  setUser(userId: string, organizationId?: string) {
    this.context.user = userId;
    this.context.organization = organizationId;
  }

  captureError(error: Error, context: ErrorContext = {}) {
    if (!this.enabled) return;

    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        name: error.name,
        timestamp: Date.now(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        ...this.context,
        ...context
      };

      // Only log in development, avoid heavy operations in production
      if (import.meta.env.DEV) {
        console.error('Error captured:', errorData);
      }

      // In production, you could send to a service like Sentry
      // await this.sendToService(errorData);
    } catch (captureError) {
      console.warn('Failed to capture error:', captureError);
    }
  }

  captureMessage(message: string, level: 'info' | 'warning' | 'error' = 'info', context: ErrorContext = {}) {
    if (!this.enabled) return;

    try {
      const messageData = {
        message,
        level,
        timestamp: Date.now(),
        url: window.location.href,
        ...this.context,
        ...context
      };

      if (import.meta.env.DEV) {
        console.log(`Message captured [${level}]:`, messageData);
      }
    } catch (error) {
      console.warn('Failed to capture message:', error);
    }
  }
}

export const errorReporting = new ErrorReporting();
