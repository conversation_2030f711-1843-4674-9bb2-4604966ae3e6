import { healthCheckManager } from './healthChecks';
import { alertingSystem } from './alertingSystem';
import { enhancedLogger } from '../logging/enhancedLogger';
import { MetricsCollector } from './core/metricsCollector';
import { TrendAnalyzer } from './core/trendAnalyzer';
import { MonitoringMetrics, MonitoringReport } from './core/monitoringTypes';
import { errorTracker } from './core/errorTracker';
import { healthMonitor } from './core/healthMonitor';
import { alertManager } from './core/alertManager';

export type { MonitoringMetrics } from './core/monitoringTypes';

export class SystemMonitor {
  private static instance: SystemMonitor;
  private metricsHistory: MonitoringMetrics[] = [];
  private maxMetricsHistory = 1000;
  private monitoringInterval?: NodeJS.Timeout;
  private metricsCollector = new MetricsCollector();
  private trendAnalyzer = new TrendAnalyzer();

  static getInstance(): SystemMonitor {
    if (!SystemMonitor.instance) {
      SystemMonitor.instance = new SystemMonitor();
    }
    return SystemMonitor.instance;
  }

  startMonitoring(intervalMs: number = 60000) {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    enhancedLogger.info('Starting enhanced system monitoring', {
      component: 'system-monitor',
      action: 'start-monitoring',
      metadata: { intervalMs }
    });

    // Start the new monitoring systems
    healthMonitor.startMonitoring(intervalMs);

    this.monitoringInterval = setInterval(async () => {
      await this.collectMetrics();
    }, intervalMs);

    // Initial collection
    this.collectMetrics();
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
      
      // Stop new monitoring systems
      healthMonitor.stopMonitoring();
      
      enhancedLogger.info('Stopped enhanced system monitoring', {
        component: 'system-monitor',
        action: 'stop-monitoring'
      });
    }
  }

  async collectMetrics(): Promise<MonitoringMetrics> {
    const timestamp = new Date().toISOString();
    
    try {
      // Collect health information using new system
      const healthStatus = await healthMonitor.runHealthChecks();
      
      // Get performance and system metrics
      const performance = this.metricsCollector.getPerformanceMetrics();
      const system = this.metricsCollector.getSystemMetrics();

      const metrics: MonitoringMetrics = {
        timestamp,
        performance,
        health: {
          status: healthStatus.overallStatus,
          failedChecks: Object.entries(healthStatus.checks)
            .filter(([_, check]) => check.status === 'unhealthy')
            .map(([name, _]) => name)
        },
        system
      };

      // Store metrics
      this.metricsHistory.unshift(metrics);
      if (this.metricsHistory.length > this.maxMetricsHistory) {
        this.metricsHistory = this.metricsHistory.slice(0, this.maxMetricsHistory);
      }

      // Evaluate alerting rules using new system
      await alertManager.evaluateRules({
        databaseStatus: healthStatus.checks.database?.status,
        authStatus: healthStatus.checks.auth?.status,
        storageStatus: healthStatus.checks.storage?.status,
        memoryUsage: healthStatus.checks.memory?.details?.usagePercent || 0,
        avgResponseTime: Object.values(healthStatus.checks).reduce((sum, check) => sum + check.responseTime, 0) / Object.keys(healthStatus.checks).length,
        errorRate: performance.errorRate || 0
      });

      enhancedLogger.debug('Collected enhanced system metrics', {
        component: 'system-monitor',
        action: 'collect-metrics',
        metadata: metrics
      });

      return metrics;
    } catch (error) {
      const errorMessage = 'Failed to collect system metrics';
      
      // Use new error tracking system
      errorTracker.captureError({
        level: 'error',
        message: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'system-monitor'
        }
      });

      enhancedLogger.error(errorMessage, error as Error, {
        component: 'system-monitor',
        action: 'collect-metrics-error'
      });

      throw error;
    }
  }

  recordRequest() {
    this.metricsCollector.recordRequest();
  }

  recordError() {
    this.metricsCollector.recordError();
  }

  getMetrics(limit?: number): MonitoringMetrics[] {
    return limit ? this.metricsHistory.slice(0, limit) : [...this.metricsHistory];
  }

  getLatestMetrics(): MonitoringMetrics | null {
    return this.metricsHistory[0] || null;
  }

  generateReport(): MonitoringReport {
    const latest = this.getLatestMetrics();
    const recent = this.getMetrics(10);
    
    return {
      summary: {
        status: latest?.health.status || 'unknown',
        uptime: latest?.system.uptime || 0,
        totalRequests: (this.metricsCollector as any).requestCount || 0,
        totalErrors: (this.metricsCollector as any).errorCount || 0,
        averageResponseTime: latest?.performance.responseTime || 0
      },
      recentMetrics: recent,
      alerts: [], // Will be populated by new alert system
      trends: this.trendAnalyzer.calculateTrends(recent)
    };
  }
}

export const systemMonitor = SystemMonitor.getInstance();
