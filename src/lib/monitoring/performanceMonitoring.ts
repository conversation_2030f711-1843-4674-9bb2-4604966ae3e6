interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: number;
  tags?: Record<string, string>;
}

export class PerformanceMonitoring {
  private static instance: PerformanceMonitoring;
  private metrics: PerformanceMetric[] = [];
  private observer?: PerformanceObserver;
  private enabled = false;

  static getInstance(): PerformanceMonitoring {
    if (!PerformanceMonitoring.instance) {
      PerformanceMonitoring.instance = new PerformanceMonitoring();
    }
    return PerformanceMonitoring.instance;
  }

  initialize() {
    // Only enable in development or when explicitly needed
    this.enabled = import.meta.env.DEV || false;
    
    if (!this.enabled || typeof window === 'undefined') return;

    try {
      this.setupCoreWebVitals();
      this.setupResourceTiming();
    } catch (error) {
      console.warn('Performance monitoring initialization failed:', error);
    }
  }

  private setupCoreWebVitals() {
    if (!('PerformanceObserver' in window)) return;

    // Simplified observers to reduce overhead
    try {
      // Largest Contentful Paint
      this.observeMetric('largest-contentful-paint', (entry: any) => {
        this.trackMetric('LCP', entry.startTime, 'ms');
      });

      // Cumulative Layout Shift
      this.observeMetric('layout-shift', (entry: any) => {
        if (!entry.hadRecentInput) {
          this.trackMetric('CLS', entry.value, 'score');
        }
      });
    } catch (error) {
      console.warn('Core Web Vitals setup failed:', error);
    }
  }

  private setupResourceTiming() {
    try {
      this.observeMetric('resource', (entry: any) => {
        // Only track JS and CSS resources to reduce noise
        if (entry.name.includes('.js') || entry.name.includes('.css')) {
          this.trackMetric(`resource_${entry.initiatorType}`, entry.duration, 'ms', {
            name: entry.name.split('/').pop() || 'unknown'
          });
        }
      });
    } catch (error) {
      console.warn('Resource timing setup failed:', error);
    }
  }

  private observeMetric(entryType: string, callback: (entry: any) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback);
      });
      observer.observe({ entryTypes: [entryType] });
    } catch (error) {
      console.warn(`Failed to observe ${entryType}:`, error);
    }
  }

  trackMetric(name: string, value: number, unit: string = 'ms', tags?: Record<string, string>) {
    if (!this.enabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: Date.now(),
      tags
    };

    this.metrics.push(metric);
    
    // Keep only recent metrics to avoid memory issues
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-50);
    }

    // Only log important metrics in development
    if (import.meta.env.DEV && (name === 'LCP' || name === 'CLS' || value > 1000)) {
      console.log(`📊 Performance: ${name} = ${value}${unit}`, tags);
    }
  }

  getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  clearMetrics() {
    this.metrics = [];
  }
}

export const performanceMonitoring = PerformanceMonitoring.getInstance();
