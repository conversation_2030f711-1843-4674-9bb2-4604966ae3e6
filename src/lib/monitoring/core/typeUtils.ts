
import type { Json } from '@/integrations/supabase/types';

/**
 * Type-safe utilities for handling Supabase JSON types
 */

export function safeJsonToRecord(json: Json): Record<string, any> {
  if (!json) return {};
  
  if (typeof json === 'string') {
    try {
      const parsed = JSON.parse(json);
      return typeof parsed === 'object' && parsed !== null ? parsed : { raw: json };
    } catch {
      return { raw: json };
    }
  }
  
  if (typeof json === 'object' && json !== null && !Array.isArray(json)) {
    return json as Record<string, any>;
  }
  
  if (Array.isArray(json)) {
    return { items: json };
  }
  
  return { value: json };
}

export function recordToSafeJson(record: Record<string, any>): Json {
  try {
    // Ensure the record can be safely serialized
    const serialized = JSON.stringify(record);
    return JSON.parse(serialized);
  } catch {
    return {};
  }
}

export function validateSeverityLevel(value: any): 'low' | 'medium' | 'high' | 'critical' {
  const validLevels = ['low', 'medium', 'high', 'critical'] as const;
  return validLevels.includes(value) ? value : 'medium';
}

export function validateAlertType(value: any): string {
  return typeof value === 'string' && value.trim() ? value.trim() : 'system';
}

export function sanitizeStringField(value: any): string {
  return typeof value === 'string' ? value : String(value || '');
}
