
import { supabase } from '@/integrations/supabase/client';
import { recordToSafeJson, safeJsonToRecord } from './typeUtils';

export interface ErrorContext {
  component?: string;
  url?: string;
  userId?: string;
  orgId?: string;
  sessionId?: string;
  userAgent?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  level: 'error' | 'warning' | 'info' | 'debug';
  message: string;
  stack?: string;
  context?: ErrorContext;
}

class ErrorTracker {
  private static instance: ErrorTracker;
  private errorQueue: ErrorReport[] = [];
  private isOnline = true;
  private maxQueueSize = 100;
  private flushInterval?: NodeJS.Timeout;

  static getInstance(): ErrorTracker {
    if (!ErrorTracker.instance) {
      ErrorTracker.instance = new ErrorTracker();
    }
    return ErrorTracker.instance;
  }

  constructor() {
    this.setupGlobalErrorHandlers();
    this.setupNetworkHandlers();
    this.startPeriodicFlush();
  }

  private setupGlobalErrorHandlers() {
    // Global error handler
    window.addEventListener('error', (event) => {
      const errorReport = {
        level: 'error' as const,
        message: event.message,
        stack: event.error?.stack,
        context: {
          component: 'global-error-handler',
          url: window.location.href,
          additionalData: {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
          }
        }
      };

      this.captureError(errorReport);
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const errorReport = {
        level: 'error' as const,
        message: event.reason instanceof Error ? event.reason.message : String(event.reason),
        stack: event.reason instanceof Error ? event.reason.stack : undefined,
        context: {
          component: 'unhandled-promise',
          url: window.location.href,
          additionalData: {
            type: 'unhandledrejection'
          }
        }
      };

      this.captureError(errorReport);
    });
  }

  private setupNetworkHandlers() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  private startPeriodicFlush() {
    this.flushInterval = setInterval(() => {
      if (this.isOnline && this.errorQueue.length > 0) {
        this.flushErrorQueue();
      }
    }, 30000); // Flush every 30 seconds
  }

  captureError(error: ErrorReport) {
    // Add to queue
    this.errorQueue.push({
      ...error,
      context: {
        ...error.context,
        url: error.context?.url || window.location.href,
        userAgent: navigator.userAgent,
        sessionId: this.getSessionId()
      }
    });

    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }

    // Try to send immediately if online and it's a critical error
    if (this.isOnline && error.level === 'error') {
      this.flushErrorQueue();
    }

    // Also log to console in development
    if (import.meta.env.DEV) {
      console.error('Error captured:', error);
    }
  }

  private async flushErrorQueue() {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      await Promise.all(
        errors.map(async (error) => {
          // Prepare metadata with type safety
          const metadata = recordToSafeJson({
            ...error.context?.additionalData,
            userAgent: error.context?.userAgent,
            sessionId: error.context?.sessionId
          });

          const { error: dbError } = await supabase.rpc('log_error', {
            error_level_param: error.level,
            error_message_param: error.message,
            error_stack_param: error.stack,
            component_param: error.context?.component,
            url_param: error.context?.url,
            metadata_param: metadata
          });

          if (dbError) {
            console.error('Failed to log error to database:', dbError);
          }
        })
      );
    } catch (error) {
      console.error('Failed to flush error queue:', error);
      // Re-queue errors if sending fails
      this.errorQueue.unshift(...errors);
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('monitoring-session-id');
    if (!sessionId) {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('monitoring-session-id', sessionId);
    }
    return sessionId;
  }

  // Performance monitoring
  measurePerformance<T>(operation: string, fn: () => T | Promise<T>): T | Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - startTime;
          this.recordPerformanceMetric(operation, duration);
        });
      } else {
        const duration = performance.now() - startTime;
        this.recordPerformanceMetric(operation, duration);
        return result;
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.captureError({
        level: 'error',
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'performance-monitor',
          additionalData: {
            operation,
            duration
          }
        }
      });
      throw error;
    }
  }

  private async recordPerformanceMetric(operation: string, duration: number) {
    try {
      const tags = recordToSafeJson({
        type: 'operation_duration',
        operation
      });

      await supabase.rpc('record_performance_metric', {
        metric_name_param: operation,
        metric_value_param: duration,
        metric_unit_param: 'ms',
        tags_param: tags
      });

      // Alert on slow operations
      if (duration > 5000) { // 5 seconds
        this.captureError({
          level: 'warning',
          message: `Slow operation detected: ${operation}`,
          context: {
            component: 'performance-monitor',
            additionalData: {
              operation,
              duration,
              threshold: 5000
            }
          }
        });
      }
    } catch (error) {
      console.error('Failed to record performance metric:', error);
    }
  }

  destroy() {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
    }
  }
}

export const errorTracker = ErrorTracker.getInstance();
