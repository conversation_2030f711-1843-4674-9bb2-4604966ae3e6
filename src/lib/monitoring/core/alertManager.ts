
import { supabase } from '@/integrations/supabase/client';
import { errorTracker } from './errorTracker';

export interface Alert {
  id?: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  metadata?: Record<string, any>;
  acknowledged?: boolean;
  resolved?: boolean;
  createdAt?: string;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: (context: any) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  cooldownMs?: number;
}

// Type-safe metadata conversion helper
function convertMetadata(metadata: any): Record<string, any> {
  if (!metadata) return {};
  
  if (typeof metadata === 'string') {
    try {
      return JSON.parse(metadata);
    } catch {
      return { raw: metadata };
    }
  }
  
  if (typeof metadata === 'object' && metadata !== null) {
    return metadata as Record<string, any>;
  }
  
  return { value: metadata };
}

// Type-safe severity validation
function validateSeverity(severity: any): 'low' | 'medium' | 'high' | 'critical' {
  const validSeverities = ['low', 'medium', 'high', 'critical'] as const;
  return validSeverities.includes(severity) ? severity : 'medium';
}

class AlertManager {
  private static instance: AlertManager;
  private rules: Map<string, AlertRule> = new Map();
  private cooldowns: Map<string, number> = new Map();
  private alerts: Alert[] = [];

  static getInstance(): AlertManager {
    if (!AlertManager.instance) {
      AlertManager.instance = new AlertManager();
    }
    return AlertManager.instance;
  }

  constructor() {
    this.setupDefaultRules();
  }

  private setupDefaultRules() {
    // High error rate rule
    this.addRule({
      id: 'high-error-rate',
      name: 'High Error Rate',
      condition: (context) => context.errorRate > 0.1, // 10% error rate
      severity: 'high',
      message: 'Error rate has exceeded 10% in the last 5 minutes',
      cooldownMs: 300000 // 5 minutes
    });

    // Slow response time rule
    this.addRule({
      id: 'slow-response-time',
      name: 'Slow Response Time',
      condition: (context) => context.avgResponseTime > 5000, // 5 seconds
      severity: 'medium',
      message: 'Average response time has exceeded 5 seconds',
      cooldownMs: 300000
    });

    // Database connection failure
    this.addRule({
      id: 'database-unhealthy',
      name: 'Database Unhealthy',
      condition: (context) => context.databaseStatus === 'unhealthy',
      severity: 'critical',
      message: 'Database health check failed',
      cooldownMs: 60000 // 1 minute
    });

    // High memory usage
    this.addRule({
      id: 'high-memory-usage',
      name: 'High Memory Usage',
      condition: (context) => context.memoryUsage > 90,
      severity: 'high',
      message: 'Memory usage has exceeded 90%',
      cooldownMs: 300000
    });
  }

  addRule(rule: AlertRule) {
    this.rules.set(rule.id, rule);
  }

  async evaluateRules(context: any) {
    const promises: Promise<void>[] = [];

    for (const [ruleId, rule] of this.rules) {
      promises.push(this.evaluateRule(ruleId, rule, context));
    }

    await Promise.all(promises);
  }

  private async evaluateRule(ruleId: string, rule: AlertRule, context: any) {
    try {
      // Check cooldown
      const lastTriggered = this.cooldowns.get(ruleId);
      if (lastTriggered && Date.now() - lastTriggered < (rule.cooldownMs || 300000)) {
        return;
      }

      // Evaluate condition
      if (rule.condition(context)) {
        await this.triggerAlert({
          type: 'system',
          severity: rule.severity,
          title: rule.name,
          message: rule.message,
          source: 'alert-manager',
          metadata: {
            ruleId,
            context: JSON.stringify(context)
          }
        });

        // Set cooldown
        this.cooldowns.set(ruleId, Date.now());
      }
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: `Failed to evaluate alert rule: ${ruleId}`,
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'alert-manager',
          additionalData: { ruleId, ruleName: rule.name }
        }
      });
    }
  }

  async triggerAlert(alert: Omit<Alert, 'id' | 'createdAt'>): Promise<string | null> {
    try {
      // Ensure metadata is properly formatted for database storage
      const sanitizedMetadata = convertMetadata(alert.metadata);
      
      const { data, error } = await supabase.rpc('create_system_alert', {
        alert_type_param: alert.type,
        severity_param: alert.severity,
        title_param: alert.title,
        message_param: alert.message,
        source_param: alert.source,
        metadata_param: sanitizedMetadata
      });

      if (error) {
        throw new Error(`Failed to create alert: ${error.message}`);
      }

      // Add to local cache
      this.alerts.unshift({
        id: data as string,
        ...alert,
        metadata: sanitizedMetadata,
        createdAt: new Date().toISOString()
      });

      // Keep only recent alerts in memory
      if (this.alerts.length > 100) {
        this.alerts = this.alerts.slice(0, 100);
      }

      return data as string;
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to trigger alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'alert-manager',
          additionalData: { alert }
        }
      });
      return null;
    }
  }

  async getRecentAlerts(limit: number = 50): Promise<Alert[]> {
    try {
      const { data, error } = await supabase
        .from('system_alerts')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(`Failed to fetch alerts: ${error.message}`);
      }

      return data.map(alert => ({
        id: alert.id,
        type: alert.alert_type,
        severity: validateSeverity(alert.severity),
        title: alert.title,
        message: alert.message,
        source: alert.source,
        metadata: convertMetadata(alert.metadata),
        acknowledged: alert.acknowledged,
        resolved: alert.resolved,
        createdAt: alert.created_at
      }));
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to fetch recent alerts',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'alert-manager'
        }
      });
      return [];
    }
  }

  async acknowledgeAlert(alertId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('system_alerts')
        .update({ 
          acknowledged: true, 
          acknowledged_at: new Date().toISOString() 
        })
        .eq('id', alertId);

      if (error) {
        throw new Error(`Failed to acknowledge alert: ${error.message}`);
      }

      // Update local cache
      const alertIndex = this.alerts.findIndex(a => a.id === alertId);
      if (alertIndex !== -1) {
        this.alerts[alertIndex].acknowledged = true;
      }
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to acknowledge alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'alert-manager',
          additionalData: { alertId }
        }
      });
    }
  }

  async resolveAlert(alertId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('system_alerts')
        .update({ 
          resolved: true, 
          resolved_at: new Date().toISOString() 
        })
        .eq('id', alertId);

      if (error) {
        throw new Error(`Failed to resolve alert: ${error.message}`);
      }

      // Update local cache
      const alertIndex = this.alerts.findIndex(a => a.id === alertId);
      if (alertIndex !== -1) {
        this.alerts[alertIndex].resolved = true;
      }
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to resolve alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'alert-manager',
          additionalData: { alertId }
        }
      });
    }
  }
}

export const alertManager = AlertManager.getInstance();
