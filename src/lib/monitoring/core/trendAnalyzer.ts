
import { MonitoringMetrics, TrendAnalysis } from './monitoringTypes';

export class TrendAnalyzer {
  calculateTrends(metrics: MonitoringMetrics[]): TrendAnalysis {
    if (metrics.length < 2) {
      return { errorRateTrend: 'stable', responseTimeTrend: 'stable' };
    }

    const recent = metrics.slice(0, Math.floor(metrics.length / 2));
    const older = metrics.slice(Math.floor(metrics.length / 2));

    const recentErrorRate = recent.reduce((sum, m) => sum + m.performance.errorRate, 0) / recent.length;
    const olderErrorRate = older.reduce((sum, m) => sum + m.performance.errorRate, 0) / older.length;

    const recentResponseTime = recent.reduce((sum, m) => sum + m.performance.responseTime, 0) / recent.length;
    const olderResponseTime = older.reduce((sum, m) => sum + m.performance.responseTime, 0) / older.length;

    return {
      errorRateTrend: recentErrorRate < olderErrorRate ? 'improving' : 
                     recentErrorRate > olderErrorRate ? 'degrading' : 'stable',
      responseTimeTrend: recentResponseTime < olderResponseTime ? 'improving' : 
                        recentResponseTime > olderResponseTime ? 'degrading' : 'stable'
    };
  }

  detectAnomalies(metrics: MonitoringMetrics[]): string[] {
    const anomalies: string[] = [];
    
    if (metrics.length < 5) return anomalies;

    const recent = metrics.slice(0, 5);
    const baseline = metrics.slice(5);

    // Calculate baseline averages
    const baselineErrorRate = baseline.reduce((sum, m) => sum + m.performance.errorRate, 0) / baseline.length;
    const baselineResponseTime = baseline.reduce((sum, m) => sum + m.performance.responseTime, 0) / baseline.length;

    // Check for anomalies in recent metrics
    const recentErrorRate = recent.reduce((sum, m) => sum + m.performance.errorRate, 0) / recent.length;
    const recentResponseTime = recent.reduce((sum, m) => sum + m.performance.responseTime, 0) / recent.length;

    if (recentErrorRate > baselineErrorRate * 2) {
      anomalies.push('High error rate detected');
    }

    if (recentResponseTime > baselineResponseTime * 1.5) {
      anomalies.push('Slow response time detected');
    }

    return anomalies;
  }
}
