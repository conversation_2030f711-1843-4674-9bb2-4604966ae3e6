
export interface PerformanceMetrics {
  responseTime: number;
  memoryUsage: number;
  errorRate: number;
  throughput: number;
}

export interface SystemMetrics {
  uptime: number;
  loadAverage?: number[];
  diskUsage?: number;
}

export class MetricsCollector {
  private errorCount = 0;
  private requestCount = 0;
  private startTime = Date.now();

  recordRequest() {
    this.requestCount++;
  }

  recordError() {
    this.errorCount++;
  }

  getPerformanceMetrics(): PerformanceMetrics {
    return {
      responseTime: this.calculateAverageResponseTime(),
      memoryUsage: this.getMemoryUsage(),
      errorRate: this.calculateErrorRate(),
      throughput: this.calculateThroughput()
    };
  }

  getSystemMetrics(): SystemMetrics {
    return {
      uptime: Date.now() - this.startTime
    };
  }

  private calculateAverageResponseTime(): number {
    const entries = performance.getEntriesByType('navigation');
    if (entries.length > 0) {
      const navEntry = entries[0] as PerformanceNavigationTiming;
      return navEntry.loadEventEnd - navEntry.fetchStart;
    }
    return 0;
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
    return 0;
  }

  private calculateErrorRate(): number {
    if (this.requestCount === 0) return 0;
    return this.errorCount / this.requestCount;
  }

  private calculateThroughput(): number {
    const uptimeSeconds = (Date.now() - this.startTime) / 1000;
    return uptimeSeconds > 0 ? this.requestCount / uptimeSeconds : 0;
  }

  reset() {
    this.errorCount = 0;
    this.requestCount = 0;
    this.startTime = Date.now();
  }
}
