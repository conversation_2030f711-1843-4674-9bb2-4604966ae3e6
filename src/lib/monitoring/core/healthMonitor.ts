import { supabase } from '@/integrations/supabase/client';
import { errorTracker } from './errorTracker';
import { recordToSafeJson, safeJsonToRecord } from './typeUtils';

export interface HealthCheck {
  name: string;
  check: () => Promise<HealthCheckResult>;
  timeout?: number;
}

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  responseTime: number;
  details?: Record<string, any>;
  error?: string;
}

export interface SystemHealthStatus {
  overallStatus: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, HealthCheckResult>;
  timestamp: string;
}

class HealthMonitor {
  private static instance: HealthMonitor;
  private checks: Map<string, HealthCheck> = new Map();
  private monitoringInterval?: NodeJS.Timeout;
  private isMonitoring = false;

  static getInstance(): HealthMonitor {
    if (!HealthMonitor.instance) {
      HealthMonitor.instance = new HealthMonitor();
    }
    return HealthMonitor.instance;
  }

  constructor() {
    this.registerDefaultChecks();
  }

  private registerDefaultChecks() {
    this.registerCheck({
      name: 'database',
      check: this.checkDatabase.bind(this),
      timeout: 5000
    });

    this.registerCheck({
      name: 'auth',
      check: this.checkAuth.bind(this),
      timeout: 3000
    });

    this.registerCheck({
      name: 'storage',
      check: this.checkStorage.bind(this),
      timeout: 3000
    });

    this.registerCheck({
      name: 'memory',
      check: this.checkMemory.bind(this),
      timeout: 1000
    });
  }

  registerCheck(check: HealthCheck) {
    this.checks.set(check.name, check);
  }

  async runHealthChecks(): Promise<SystemHealthStatus> {
    const results: Record<string, HealthCheckResult> = {};
    const promises: Promise<void>[] = [];

    for (const [name, check] of this.checks) {
      promises.push(
        this.runSingleCheck(name, check).then(result => {
          results[name] = result;
        })
      );
    }

    await Promise.all(promises);

    const overallStatus = this.determineOverallStatus(results);
    
    return {
      overallStatus,
      checks: results,
      timestamp: new Date().toISOString()
    };
  }

  private async runSingleCheck(name: string, check: HealthCheck): Promise<HealthCheckResult> {
    const startTime = performance.now();
    
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), check.timeout || 5000);
      });

      const result = await Promise.race([
        check.check(),
        timeoutPromise
      ]);

      const responseTime = performance.now() - startTime;

      // Record the health check result with type-safe details
      await this.recordHealthCheck(name, result.status, responseTime, result.details, result.error);

      return {
        ...result,
        responseTime
      };
    } catch (error) {
      const responseTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      await this.recordHealthCheck(name, 'unhealthy', responseTime, {}, errorMessage);

      return {
        status: 'unhealthy',
        responseTime,
        error: errorMessage
      };
    }
  }

  private async recordHealthCheck(
    name: string, 
    status: string, 
    responseTime: number, 
    details?: Record<string, any>, 
    error?: string
  ) {
    try {
      const safeDetails = recordToSafeJson(details || {});
      
      await supabase.rpc('record_health_check', {
        check_name_param: name,
        status_param: status,
        response_time_param: Math.round(responseTime),
        details_param: safeDetails,
        error_message_param: error
      });
    } catch (err) {
      console.error('Failed to record health check:', err);
    }
  }

  private determineOverallStatus(results: Record<string, HealthCheckResult>): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(results).map(r => r.status);
    
    if (statuses.some(s => s === 'unhealthy')) {
      return 'unhealthy';
    }
    
    if (statuses.some(s => s === 'degraded')) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  private async checkDatabase(): Promise<HealthCheckResult> {
    try {
      const { error } = await supabase.from('profiles').select('id').limit(1);
      
      return {
        status: error ? 'unhealthy' : 'healthy',
        responseTime: 0, // Will be set by runSingleCheck
        details: { connection: 'supabase' },
        error: error?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Database connection failed'
      };
    }
  }

  private async checkAuth(): Promise<HealthCheckResult> {
    try {
      const { error } = await supabase.auth.getSession();
      
      return {
        status: error ? 'degraded' : 'healthy',
        responseTime: 0,
        details: { provider: 'supabase-auth' },
        error: error?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Auth service failed'
      };
    }
  }

  private async checkStorage(): Promise<HealthCheckResult> {
    try {
      const { data, error } = await supabase.storage.listBuckets();
      
      return {
        status: error ? 'degraded' : 'healthy',
        responseTime: 0,
        details: { 
          provider: 'supabase-storage',
          buckets: data?.length || 0
        },
        error: error?.message
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Storage service failed'
      };
    }
  }

  private async checkMemory(): Promise<HealthCheckResult> {
    try {
      let memoryInfo = { used: 0, total: 0 };
      
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryInfo = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize
        };
      }

      const usagePercent = memoryInfo.total > 0 ? (memoryInfo.used / memoryInfo.total) * 100 : 0;
      const status = usagePercent > 90 ? 'unhealthy' : usagePercent > 75 ? 'degraded' : 'healthy';

      return {
        status,
        responseTime: 0,
        details: {
          usagePercent: Math.round(usagePercent),
          usedMB: Math.round(memoryInfo.used / 1024 / 1024),
          totalMB: Math.round(memoryInfo.total / 1024 / 1024)
        }
      };
    } catch (error) {
      return {
        status: 'degraded',
        responseTime: 0,
        error: 'Memory check not supported'
      };
    }
  }

  startMonitoring(intervalMs: number = 60000) {
    if (this.isMonitoring) {
      this.stopMonitoring();
    }

    this.isMonitoring = true;
    
    // Run initial check
    this.runHealthChecks().catch(error => {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to run initial health checks',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'health-monitor'
        }
      });
    });

    // Set up periodic checks
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.runHealthChecks();
      } catch (error) {
        errorTracker.captureError({
          level: 'error',
          message: 'Failed to run periodic health checks',
          stack: error instanceof Error ? error.stack : undefined,
          context: {
            component: 'health-monitor'
          }
        });
      }
    }, intervalMs);
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
    this.isMonitoring = false;
  }

  isCurrentlyMonitoring(): boolean {
    return this.isMonitoring;
  }
}

export const healthMonitor = HealthMonitor.getInstance();
