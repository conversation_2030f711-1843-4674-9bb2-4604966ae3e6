
import { PerformanceMetrics, SystemMetrics } from './metricsCollector';

export interface MonitoringMetrics {
  timestamp: string;
  performance: PerformanceMetrics;
  health: {
    status: 'healthy' | 'unhealthy' | 'degraded';
    failedChecks: string[];
  };
  system: SystemMetrics;
}

export interface TrendAnalysis {
  errorRateTrend: 'improving' | 'degrading' | 'stable';
  responseTimeTrend: 'improving' | 'degrading' | 'stable';
}

export interface MonitoringReport {
  summary: {
    status: string;
    uptime: number;
    totalRequests: number;
    totalErrors: number;
    averageResponseTime: number;
  };
  recentMetrics: MonitoringMetrics[];
  alerts: any[];
  trends: TrendAnalysis;
}
