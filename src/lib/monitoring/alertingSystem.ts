
export interface Alert {
  id: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  source: string;
  metadata?: Record<string, any>;
  acknowledged?: boolean;
  resolvedAt?: string;
}

export interface AlertRule {
  id: string;
  name: string;
  condition: (data: any) => boolean;
  level: Alert['level'];
  message: string;
  enabled: boolean;
  cooldownMs: number;
  lastTriggered?: number;
}

export interface AlertChannel {
  name: string;
  type: 'console' | 'webhook' | 'email' | 'slack';
  config: Record<string, any>;
  enabled: boolean;
}

export class AlertingSystem {
  private static instance: AlertingSystem;
  private alerts: Alert[] = [];
  private rules: Map<string, AlertRule> = new Map();
  private channels: AlertChannel[] = [];
  private maxAlerts = 1000;

  static getInstance(): AlertingSystem {
    if (!AlertingSystem.instance) {
      AlertingSystem.instance = new AlertingSystem();
    }
    return AlertingSystem.instance;
  }

  constructor() {
    this.setupDefaultRules();
    this.setupDefaultChannels();
  }

  private setupDefaultRules() {
    this.addRule({
      id: 'high-error-rate',
      name: 'High Error Rate',
      condition: (data) => data.errorRate > 0.1, // 10% error rate
      level: 'error',
      message: 'Error rate exceeded 10%',
      enabled: true,
      cooldownMs: 300000 // 5 minutes
    });

    this.addRule({
      id: 'slow-response',
      name: 'Slow Response Time',
      condition: (data) => data.responseTime > 5000, // 5 seconds
      level: 'warning',
      message: 'Response time exceeded 5 seconds',
      enabled: true,
      cooldownMs: 300000
    });

    this.addRule({
      id: 'health-check-failed',
      name: 'Health Check Failed',
      condition: (data) => data.status === 'unhealthy',
      level: 'critical',
      message: 'System health check failed',
      enabled: true,
      cooldownMs: 60000 // 1 minute
    });

    this.addRule({
      id: 'high-memory-usage',
      name: 'High Memory Usage',
      condition: (data) => data.memoryUsage > 90,
      level: 'warning',
      message: 'Memory usage exceeded 90%',
      enabled: true,
      cooldownMs: 600000 // 10 minutes
    });
  }

  private setupDefaultChannels() {
    this.channels.push({
      name: 'console',
      type: 'console',
      config: {},
      enabled: true
    });

    // Add webhook channel if endpoint is configured
    if (import.meta.env.VITE_ALERT_WEBHOOK_URL) {
      this.channels.push({
        name: 'webhook',
        type: 'webhook',
        config: {
          url: import.meta.env.VITE_ALERT_WEBHOOK_URL
        },
        enabled: true
      });
    }
  }

  addRule(rule: AlertRule) {
    this.rules.set(rule.id, rule);
  }

  removeRule(ruleId: string) {
    this.rules.delete(ruleId);
  }

  addChannel(channel: AlertChannel) {
    const existingIndex = this.channels.findIndex(c => c.name === channel.name);
    if (existingIndex >= 0) {
      this.channels[existingIndex] = channel;
    } else {
      this.channels.push(channel);
    }
  }

  async evaluateRules(data: any, source: string = 'system') {
    for (const [ruleId, rule] of this.rules) {
      if (!rule.enabled) continue;

      // Check cooldown
      if (rule.lastTriggered && Date.now() - rule.lastTriggered < rule.cooldownMs) {
        continue;
      }

      try {
        if (rule.condition(data)) {
          await this.triggerAlert({
            level: rule.level,
            title: rule.name,
            message: rule.message,
            source,
            metadata: { ruleId, data }
          });

          // Update last triggered time
          rule.lastTriggered = Date.now();
        }
      } catch (error) {
        console.error(`Error evaluating rule ${ruleId}:`, error);
      }
    }
  }

  async triggerAlert(alertData: Omit<Alert, 'id' | 'timestamp'>) {
    const alert: Alert = {
      id: this.generateAlertId(),
      timestamp: new Date().toISOString(),
      ...alertData
    };

    // Add to alerts list
    this.alerts.unshift(alert);
    if (this.alerts.length > this.maxAlerts) {
      this.alerts = this.alerts.slice(0, this.maxAlerts);
    }

    // Send through all enabled channels
    await this.sendAlert(alert);

    return alert;
  }

  private async sendAlert(alert: Alert) {
    const enabledChannels = this.channels.filter(c => c.enabled);
    
    await Promise.allSettled(
      enabledChannels.map(channel => this.sendToChannel(alert, channel))
    );
  }

  private async sendToChannel(alert: Alert, channel: AlertChannel) {
    try {
      switch (channel.type) {
        case 'console':
          this.sendToConsole(alert);
          break;
        case 'webhook':
          await this.sendToWebhook(alert, channel.config);
          break;
        case 'email':
          await this.sendToEmail(alert, channel.config);
          break;
        case 'slack':
          await this.sendToSlack(alert, channel.config);
          break;
      }
    } catch (error) {
      console.error(`Failed to send alert through ${channel.name}:`, error);
    }
  }

  private sendToConsole(alert: Alert) {
    const logLevel = alert.level === 'critical' || alert.level === 'error' ? 'error' :
                     alert.level === 'warning' ? 'warn' : 'info';
    
    console[logLevel](`🚨 ALERT [${alert.level.toUpperCase()}]: ${alert.title}`, {
      message: alert.message,
      source: alert.source,
      timestamp: alert.timestamp,
      metadata: alert.metadata
    });
  }

  private async sendToWebhook(alert: Alert, config: Record<string, any>) {
    await fetch(config.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        alert,
        timestamp: new Date().toISOString(),
        environment: import.meta.env.NODE_ENV
      })
    });
  }

  private async sendToEmail(alert: Alert, config: Record<string, any>) {
    // Placeholder for email integration
    console.info('Email alert would be sent:', { alert, config });
  }

  private async sendToSlack(alert: Alert, config: Record<string, any>) {
    const payload = {
      text: `🚨 ${alert.level.toUpperCase()}: ${alert.title}`,
      attachments: [{
        color: this.getSlackColor(alert.level),
        fields: [
          { title: 'Message', value: alert.message, short: false },
          { title: 'Source', value: alert.source, short: true },
          { title: 'Time', value: alert.timestamp, short: true }
        ]
      }]
    };

    await fetch(config.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
  }

  private getSlackColor(level: Alert['level']): string {
    switch (level) {
      case 'critical': return 'danger';
      case 'error': return 'danger';
      case 'warning': return 'warning';
      case 'info': return 'good';
      default: return 'good';
    }
  }

  acknowledgeAlert(alertId: string) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
    }
  }

  resolveAlert(alertId: string) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolvedAt = new Date().toISOString();
    }
  }

  getAlerts(filters?: {
    level?: Alert['level'];
    source?: string;
    acknowledged?: boolean;
    resolved?: boolean;
  }) {
    let filtered = [...this.alerts];

    if (filters) {
      if (filters.level) {
        filtered = filtered.filter(a => a.level === filters.level);
      }
      if (filters.source) {
        filtered = filtered.filter(a => a.source === filters.source);
      }
      if (filters.acknowledged !== undefined) {
        filtered = filtered.filter(a => !!a.acknowledged === filters.acknowledged);
      }
      if (filters.resolved !== undefined) {
        filtered = filtered.filter(a => !!a.resolvedAt === filters.resolved);
      }
    }

    return filtered;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup old resolved alerts
  cleanup(olderThanMs: number = 7 * 24 * 60 * 60 * 1000) { // 7 days
    const cutoff = Date.now() - olderThanMs;
    this.alerts = this.alerts.filter(alert => {
      if (alert.resolvedAt) {
        const resolvedTime = new Date(alert.resolvedAt).getTime();
        return resolvedTime > cutoff;
      }
      return true;
    });
  }
}

export const alertingSystem = AlertingSystem.getInstance();
