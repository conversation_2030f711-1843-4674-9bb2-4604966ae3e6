
import { HealthCheckResult } from './types';

export const checkMemory = async (): Promise<HealthCheckResult> => {
  const start = performance.now();
  try {
    let memoryInfo = { used: 0, total: 0 };
    
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      memoryInfo = {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize
      };
    }

    const usagePercent = memoryInfo.total > 0 ? (memoryInfo.used / memoryInfo.total) * 100 : 0;
    const status = usagePercent > 90 ? 'unhealthy' : usagePercent > 75 ? 'degraded' : 'healthy';

    return {
      name: 'memory',
      status,
      responseTime: performance.now() - start,
      metadata: {
        usedBytes: memoryInfo.used,
        totalBytes: memoryInfo.total,
        usagePercent: Math.round(usagePercent)
      }
    };
  } catch (error) {
    return {
      name: 'memory',
      status: 'unhealthy',
      responseTime: performance.now() - start,
      error: error instanceof Error ? error.message : 'Unknown memory error'
    };
  }
};
