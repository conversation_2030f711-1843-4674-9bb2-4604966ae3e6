
import { SystemHealth, HealthCheckResult, HealthCheckFunction } from './types';
import { checkDatabase } from './databaseCheck';
import { checkAuth } from './authCheck';
import { checkStorage } from './storageCheck';
import { checkMemory } from './memoryCheck';
import { checkNetwork } from './networkCheck';

export type { SystemHealth, HealthCheckResult } from './types';

export class HealthCheckManager {
  private static instance: HealthCheckManager;
  private checks: Map<string, HealthCheckFunction> = new Map();
  private startTime = Date.now();

  static getInstance(): HealthCheckManager {
    if (!HealthCheckManager.instance) {
      HealthCheckManager.instance = new HealthCheckManager();
    }
    return HealthCheckManager.instance;
  }

  constructor() {
    this.registerDefaultChecks();
  }

  private registerDefaultChecks() {
    this.registerCheck('database', checkDatabase);
    this.registerCheck('auth', checkAuth);
    this.registerCheck('storage', checkStorage);
    this.registerCheck('memory', checkMemory);
    this.registerCheck('network', checkNetwork);
  }

  registerCheck(name: string, checkFn: HealthCheckFunction) {
    this.checks.set(name, checkFn);
  }

  async runAllChecks(): Promise<SystemHealth> {
    const results: HealthCheckResult[] = [];
    
    for (const [name, checkFn] of this.checks) {
      try {
        const result = await Promise.race([
          checkFn(),
          this.timeoutCheck(name, 5000) // 5 second timeout
        ]);
        results.push(result);
      } catch (error) {
        results.push({
          name,
          status: 'unhealthy',
          responseTime: 5000,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const summary = this.calculateSummary(results);
    const overallStatus = this.determineOverallStatus(summary);

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      environment: import.meta.env.NODE_ENV || 'development',
      uptime: Date.now() - this.startTime,
      checks: results,
      summary
    };
  }

  async runCheck(name: string): Promise<HealthCheckResult> {
    const checkFn = this.checks.get(name);
    if (!checkFn) {
      throw new Error(`Health check '${name}' not found`);
    }
    return await checkFn();
  }

  private async timeoutCheck(name: string, timeout: number): Promise<HealthCheckResult> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Health check '${name}' timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  private calculateSummary(results: HealthCheckResult[]) {
    return {
      total: results.length,
      healthy: results.filter(r => r.status === 'healthy').length,
      unhealthy: results.filter(r => r.status === 'unhealthy').length,
      degraded: results.filter(r => r.status === 'degraded').length
    };
  }

  private determineOverallStatus(summary: { healthy: number; unhealthy: number; degraded: number; total: number }): 'healthy' | 'unhealthy' | 'degraded' {
    if (summary.unhealthy > 0) return 'unhealthy';
    if (summary.degraded > 0) return 'degraded';
    return 'healthy';
  }
}

export const healthCheckManager = HealthCheckManager.getInstance();
