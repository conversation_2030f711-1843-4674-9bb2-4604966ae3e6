
import { HealthCheckResult } from './types';

export const checkAuth = async (): Promise<HealthCheckResult> => {
  const start = performance.now();
  try {
    const { supabase } = await import('@/integrations/supabase/client');
    const { error } = await supabase.auth.getSession();
    
    return {
      name: 'auth',
      status: error ? 'unhealthy' : 'healthy',
      responseTime: performance.now() - start,
      error: error?.message,
      metadata: { provider: 'supabase-auth' }
    };
  } catch (error) {
    return {
      name: 'auth',
      status: 'unhealthy',
      responseTime: performance.now() - start,
      error: error instanceof Error ? error.message : 'Unknown auth error'
    };
  }
};
