
import { HealthCheckResult } from './types';

export const checkDatabase = async (): Promise<HealthCheckResult> => {
  const start = performance.now();
  try {
    const { supabase } = await import('@/integrations/supabase/client');
    const { error } = await supabase.from('profiles').select('id').limit(1);
    
    return {
      name: 'database',
      status: error ? 'unhealthy' : 'healthy',
      responseTime: performance.now() - start,
      error: error?.message,
      metadata: { connection: 'supabase' }
    };
  } catch (error) {
    return {
      name: 'database',
      status: 'unhealthy',
      responseTime: performance.now() - start,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
};
