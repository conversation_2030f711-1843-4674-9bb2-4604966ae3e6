
import { HealthCheckResult } from './types';

export const checkStorage = async (): Promise<HealthCheckResult> => {
  const start = performance.now();
  try {
    const { supabase } = await import('@/integrations/supabase/client');
    const { data, error } = await supabase.storage.listBuckets();
    
    return {
      name: 'storage',
      status: error ? 'unhealthy' : 'healthy',
      responseTime: performance.now() - start,
      error: error?.message,
      metadata: { 
        provider: 'supabase-storage',
        buckets: data?.length || 0
      }
    };
  } catch (error) {
    return {
      name: 'storage',
      status: 'unhealthy',
      responseTime: performance.now() - start,
      error: error instanceof Error ? error.message : 'Unknown storage error'
    };
  }
};
