
import { HealthCheckResult } from './types';

export const checkNetwork = async (): Promise<HealthCheckResult> => {
  const start = performance.now();
  try {
    // Check network connectivity with a simple request
    const response = await fetch('https://httpbin.org/status/200', {
      method: 'HEAD',
      cache: 'no-cache'
    });
    
    const responseTime = performance.now() - start;
    const status = response.ok ? 'healthy' : 'unhealthy';

    return {
      name: 'network',
      status,
      responseTime,
      metadata: {
        statusCode: response.status,
        online: navigator.onLine
      }
    };
  } catch (error) {
    return {
      name: 'network',
      status: 'unhealthy',
      responseTime: performance.now() - start,
      error: error instanceof Error ? error.message : 'Network connectivity error',
      metadata: {
        online: navigator.onLine
      }
    };
  }
};
