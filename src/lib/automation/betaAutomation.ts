/**
 * Beta Request Automation Service
 * 
 * Handles automated processing of beta requests based on configurable rules,
 * including auto-approval, auto-rejection, and email notifications.
 */

import { supabase } from '@/integrations/supabase/client';
import { emailService } from '@/lib/email/emailService';

export interface AutomationRule {
  name: string;
  enabled: boolean;
  conditions: AutomationCondition[];
  action: 'approve' | 'reject' | 'flag_for_review';
  priority: number;
}

export interface AutomationCondition {
  field: string;
  operator: 'equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'regex';
  value: string | number;
  caseSensitive?: boolean;
}

export interface BetaRequest {
  id: string;
  email: string;
  name: string;
  company?: string;
  use_case: string;
  referral_source: string;
  status: string;
}

class BetaAutomationService {
  private defaultRules: AutomationRule[] = [
    {
      name: 'Auto-approve detailed requests',
      enabled: true,
      priority: 1,
      action: 'approve',
      conditions: [
        { field: 'use_case', operator: 'greater_than', value: 100 },
        { field: 'email', operator: 'not_contains', value: 'temp' },
        { field: 'email', operator: 'not_contains', value: 'disposable' },
      ],
    },
    {
      name: 'Auto-approve company requests',
      enabled: true,
      priority: 2,
      action: 'approve',
      conditions: [
        { field: 'company', operator: 'greater_than', value: 2 },
        { field: 'use_case', operator: 'greater_than', value: 50 },
      ],
    },
    {
      name: 'Auto-reject spam patterns',
      enabled: true,
      priority: 10,
      action: 'reject',
      conditions: [
        { field: 'use_case', operator: 'less_than', value: 10 },
      ],
    },
    {
      name: 'Auto-reject disposable emails',
      enabled: true,
      priority: 11,
      action: 'reject',
      conditions: [
        { field: 'email', operator: 'regex', value: '.*@(10minutemail|tempmail|guerrillamail|mailinator)\\.' },
      ],
    },
    {
      name: 'Flag suspicious requests',
      enabled: true,
      priority: 5,
      action: 'flag_for_review',
      conditions: [
        { field: 'use_case', operator: 'contains', value: 'test', caseSensitive: false },
      ],
    },
  ];

  /**
   * Process a new beta request through automation rules
   */
  async processNewRequest(requestId: string): Promise<{
    action: string;
    rule?: string;
    automated: boolean;
  }> {
    try {
      // Get the beta request
      const { data: request, error } = await supabase
        .from('beta_requests')
        .select('*')
        .eq('id', requestId)
        .eq('status', 'pending')
        .single();

      if (error || !request) {
        console.error('Beta request not found:', error);
        return { action: 'none', automated: false };
      }

      // Get automation settings
      const automationEnabled = await this.isAutomationEnabled();
      if (!automationEnabled) {
        console.log('Automation is disabled');
        return { action: 'manual_review', automated: false };
      }

      // Get active rules
      const rules = await this.getActiveRules();
      
      // Process rules in priority order
      for (const rule of rules) {
        if (await this.evaluateRule(rule, request)) {
          console.log(`Rule matched: ${rule.name} -> ${rule.action}`);
          
          const result = await this.executeAction(rule.action, request, rule.name);
          
          return {
            action: rule.action,
            rule: rule.name,
            automated: true,
          };
        }
      }

      // No rules matched - manual review required
      console.log('No automation rules matched - manual review required');
      return { action: 'manual_review', automated: false };

    } catch (error) {
      console.error('Automation processing failed:', error);
      return { action: 'error', automated: false };
    }
  }

  /**
   * Evaluate if a rule matches a beta request
   */
  private async evaluateRule(rule: AutomationRule, request: BetaRequest): Promise<boolean> {
    if (!rule.enabled) return false;

    // All conditions must be true for the rule to match
    for (const condition of rule.conditions) {
      if (!await this.evaluateCondition(condition, request)) {
        return false;
      }
    }

    return true;
  }

  /**
   * Evaluate a single condition against a beta request
   */
  private async evaluateCondition(condition: AutomationCondition, request: BetaRequest): Promise<boolean> {
    const fieldValue = this.getFieldValue(condition.field, request);
    
    if (fieldValue === null || fieldValue === undefined) {
      return false;
    }

    const { operator, value, caseSensitive = true } = condition;
    
    switch (operator) {
      case 'equals':
        return caseSensitive 
          ? fieldValue === value 
          : String(fieldValue).toLowerCase() === String(value).toLowerCase();
      
      case 'contains':
        const searchValue = caseSensitive ? String(value) : String(value).toLowerCase();
        const searchIn = caseSensitive ? String(fieldValue) : String(fieldValue).toLowerCase();
        return searchIn.includes(searchValue);
      
      case 'not_contains':
        const notSearchValue = caseSensitive ? String(value) : String(value).toLowerCase();
        const notSearchIn = caseSensitive ? String(fieldValue) : String(fieldValue).toLowerCase();
        return !notSearchIn.includes(notSearchValue);
      
      case 'greater_than':
        return Number(fieldValue) > Number(value);
      
      case 'less_than':
        return Number(fieldValue) < Number(value);
      
      case 'regex':
        try {
          const regex = new RegExp(String(value), caseSensitive ? 'g' : 'gi');
          return regex.test(String(fieldValue));
        } catch (error) {
          console.error('Invalid regex pattern:', value, error);
          return false;
        }
      
      default:
        console.warn('Unknown operator:', operator);
        return false;
    }
  }

  /**
   * Get field value from beta request
   */
  private getFieldValue(field: string, request: BetaRequest): any {
    switch (field) {
      case 'email':
        return request.email;
      case 'name':
        return request.name;
      case 'company':
        return request.company || '';
      case 'use_case':
        return request.use_case;
      case 'referral_source':
        return request.referral_source;
      case 'use_case_length':
        return request.use_case.length;
      case 'name_length':
        return request.name.length;
      case 'company_length':
        return (request.company || '').length;
      default:
        console.warn('Unknown field:', field);
        return null;
    }
  }

  /**
   * Execute the action determined by automation rules
   */
  private async executeAction(action: string, request: BetaRequest, ruleName: string): Promise<boolean> {
    try {
      switch (action) {
        case 'approve':
          return await this.autoApproveRequest(request, ruleName);
        
        case 'reject':
          return await this.autoRejectRequest(request, ruleName);
        
        case 'flag_for_review':
          return await this.flagForReview(request, ruleName);
        
        default:
          console.warn('Unknown action:', action);
          return false;
      }
    } catch (error) {
      console.error('Action execution failed:', error);
      return false;
    }
  }

  /**
   * Auto-approve a beta request
   */
  private async autoApproveRequest(request: BetaRequest, ruleName: string): Promise<boolean> {
    try {
      // Use the database function to approve the request
      const { data: promoCode, error } = await supabase.rpc('approve_beta_request', {
        request_id: request.id,
        approver_id: null, // System approval
      });

      if (error) throw error;

      // Mark as auto-approved
      await supabase
        .from('beta_requests')
        .update({ 
          auto_approved: true,
          notes: `Auto-approved by rule: ${ruleName}`,
        })
        .eq('id', request.id);

      // Send approval email
      await emailService.sendBetaApprovalEmail({
        name: request.name,
        email: request.email,
        promoCode: promoCode,
        discountPercentage: 90,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        validTiers: ['starter', 'pro'],
      });

      console.log(`Auto-approved request ${request.id} with promo code ${promoCode}`);
      return true;
    } catch (error) {
      console.error('Auto-approval failed:', error);
      return false;
    }
  }

  /**
   * Auto-reject a beta request
   */
  private async autoRejectRequest(request: BetaRequest, ruleName: string): Promise<boolean> {
    try {
      // Use the database function to reject the request
      const { error } = await supabase.rpc('reject_beta_request', {
        request_id: request.id,
        rejector_id: null, // System rejection
        reason: `Auto-rejected by rule: ${ruleName}`,
      });

      if (error) throw error;

      // Send rejection email
      await emailService.sendBetaRejectionEmail({
        name: request.name,
        email: request.email,
        reason: 'Your request did not meet our current beta criteria. Please feel free to reapply in the future.',
      });

      console.log(`Auto-rejected request ${request.id}`);
      return true;
    } catch (error) {
      console.error('Auto-rejection failed:', error);
      return false;
    }
  }

  /**
   * Flag a request for manual review
   */
  private async flagForReview(request: BetaRequest, ruleName: string): Promise<boolean> {
    try {
      // Add a note to the request
      await supabase
        .from('beta_requests')
        .update({ 
          notes: `Flagged for review by rule: ${ruleName}`,
        })
        .eq('id', request.id);

      // Send admin notification
      await emailService.sendAdminNotification({
        name: request.name,
        email: request.email,
        company: request.company,
        useCase: request.use_case,
        referralSource: request.referral_source,
      });

      console.log(`Flagged request ${request.id} for review`);
      return true;
    } catch (error) {
      console.error('Flag for review failed:', error);
      return false;
    }
  }

  /**
   * Check if automation is enabled
   */
  private async isAutomationEnabled(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('beta_settings')
        .select('setting_value')
        .eq('setting_key', 'auto_approval_enabled')
        .single();

      if (error) return false;
      return data.setting_value === true || data.setting_value === 'true';
    } catch (error) {
      console.error('Failed to check automation status:', error);
      return false;
    }
  }

  /**
   * Get active automation rules
   */
  private async getActiveRules(): Promise<AutomationRule[]> {
    try {
      const { data, error } = await supabase
        .from('beta_settings')
        .select('setting_value')
        .eq('setting_key', 'automation_rules')
        .single();

      if (error || !data) {
        console.log('Using default automation rules');
        return this.defaultRules.sort((a, b) => a.priority - b.priority);
      }

      const customRules = data.setting_value as AutomationRule[];
      return customRules
        .filter(rule => rule.enabled)
        .sort((a, b) => a.priority - b.priority);
    } catch (error) {
      console.error('Failed to get automation rules:', error);
      return this.defaultRules.sort((a, b) => a.priority - b.priority);
    }
  }

  /**
   * Update automation settings
   */
  async updateAutomationSettings(enabled: boolean, rules?: AutomationRule[]): Promise<boolean> {
    try {
      // Update automation enabled status
      await supabase
        .from('beta_settings')
        .upsert({
          setting_key: 'auto_approval_enabled',
          setting_value: enabled,
        });

      // Update rules if provided
      if (rules) {
        await supabase
          .from('beta_settings')
          .upsert({
            setting_key: 'automation_rules',
            setting_value: rules,
          });
      }

      return true;
    } catch (error) {
      console.error('Failed to update automation settings:', error);
      return false;
    }
  }

  /**
   * Get automation statistics
   */
  async getAutomationStats(): Promise<{
    totalProcessed: number;
    autoApproved: number;
    autoRejected: number;
    flaggedForReview: number;
    automationRate: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('beta_requests')
        .select('auto_approved, status, notes');

      if (error) throw error;

      const requests = data || [];
      const totalProcessed = requests.length;
      const autoApproved = requests.filter(r => r.auto_approved).length;
      const autoRejected = requests.filter(r => 
        r.status === 'rejected' && 
        r.notes?.includes('Auto-rejected')
      ).length;
      const flaggedForReview = requests.filter(r => 
        r.notes?.includes('Flagged for review')
      ).length;
      
      const automatedActions = autoApproved + autoRejected + flaggedForReview;
      const automationRate = totalProcessed > 0 ? (automatedActions / totalProcessed) * 100 : 0;

      return {
        totalProcessed,
        autoApproved,
        autoRejected,
        flaggedForReview,
        automationRate,
      };
    } catch (error) {
      console.error('Failed to get automation stats:', error);
      return {
        totalProcessed: 0,
        autoApproved: 0,
        autoRejected: 0,
        flaggedForReview: 0,
        automationRate: 0,
      };
    }
  }
}

// Export singleton instance
export const betaAutomationService = new BetaAutomationService();

export default betaAutomationService;
