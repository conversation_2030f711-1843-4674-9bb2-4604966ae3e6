/**
 * Email Templates for Beta Access System
 * 
 * Provides HTML email templates for beta request notifications,
 * approvals, rejections, and follow-up communications.
 */

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface BetaApprovalData {
  name: string;
  email: string;
  promoCode: string;
  discountPercentage: number;
  expiresAt: string;
  validTiers: string[];
}

export interface BetaRejectionData {
  name: string;
  email: string;
  reason?: string;
}

export interface BetaRequestData {
  name: string;
  email: string;
  company?: string;
  useCase: string;
  referralSource: string;
}

class EmailTemplateService {
  /**
   * Beta request approval email template
   */
  getBetaApprovalTemplate(data: BetaApprovalData): EmailTemplate {
    const expiryDate = new Date(data.expiresAt).toLocaleDateString();
    const tiersText = data.validTiers.map(tier => 
      tier.charAt(0).toUpperCase() + tier.slice(1)
    ).join(' or ');

    const subject = `🎉 Welcome to ScriptGenius Beta - Your ${data.discountPercentage}% Off Code Inside!`;

    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome to ScriptGenius Beta</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
    .container { max-width: 600px; margin: 0 auto; background: white; }
    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; }
    .content { padding: 40px 30px; }
    .promo-code { background: #f0f9ff; border: 2px dashed #0ea5e9; border-radius: 12px; padding: 20px; text-align: center; margin: 30px 0; }
    .code { font-family: 'Courier New', monospace; font-size: 24px; font-weight: bold; color: #0369a1; letter-spacing: 2px; }
    .cta-button { display: inline-block; background: #0ea5e9; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 20px 0; }
    .benefits { background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; }
    .benefit-item { display: flex; align-items: center; margin: 10px 0; }
    .checkmark { color: #10b981; margin-right: 10px; font-weight: bold; }
    .footer { background: #f1f5f9; padding: 30px; text-align: center; color: #64748b; font-size: 14px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 style="margin: 0; font-size: 28px;">🎬 Welcome to ScriptGenius Beta!</h1>
      <p style="margin: 10px 0 0 0; opacity: 0.9;">Your exclusive access is ready</p>
    </div>
    
    <div class="content">
      <h2>Hi ${data.name}! 👋</h2>
      
      <p>Congratulations! Your beta access request has been approved. We're excited to have you join our community of writers, producers, and directors who are shaping the future of screenwriting.</p>
      
      <div class="promo-code">
        <h3 style="margin-top: 0; color: #0369a1;">Your Exclusive Promo Code</h3>
        <div class="code">${data.promoCode}</div>
        <p style="margin-bottom: 0; color: #0369a1; font-weight: 500;">
          ${data.discountPercentage}% off ${tiersText} • Expires ${expiryDate}
        </p>
      </div>
      
      <div style="text-align: center;">
        <a href="https://scriptgenius.com/pricing?promo=${data.promoCode}" class="cta-button">
          Claim Your Discount Now →
        </a>
      </div>
      
      <div class="benefits">
        <h3 style="margin-top: 0;">What you get with ScriptGenius:</h3>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>AI-powered script analysis and suggestions</span>
        </div>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>Real-time collaboration with your team</span>
        </div>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>Industry-standard formatting tools</span>
        </div>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>Advanced storyboard and character development</span>
        </div>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>Priority customer support</span>
        </div>
        <div class="benefit-item">
          <span class="checkmark">✓</span>
          <span>Early access to new features</span>
        </div>
      </div>
      
      <h3>Next Steps:</h3>
      <ol>
        <li><strong>Choose your plan:</strong> Visit our <a href="https://scriptgenius.com/pricing">pricing page</a></li>
        <li><strong>Enter your code:</strong> Use <code>${data.promoCode}</code> at checkout</li>
        <li><strong>Start writing:</strong> Begin your screenwriting journey immediately</li>
      </ol>
      
      <p><strong>Questions?</strong> Reply to this email or reach out to our support team. We're here to help you make the most of ScriptGenius.</p>
      
      <p>Welcome aboard!</p>
      <p><strong>The ScriptGenius Team</strong></p>
    </div>
    
    <div class="footer">
      <p>ScriptGenius - Professional Screenwriting Platform</p>
      <p>This promo code is exclusive to you and expires on ${expiryDate}</p>
      <p><a href="https://scriptgenius.com/unsubscribe">Unsubscribe</a> | <a href="https://scriptgenius.com/privacy">Privacy Policy</a></p>
    </div>
  </div>
</body>
</html>`;

    const text = `
Welcome to ScriptGenius Beta!

Hi ${data.name}!

Congratulations! Your beta access request has been approved. We're excited to have you join our community of writers, producers, and directors.

Your Exclusive Promo Code: ${data.promoCode}
${data.discountPercentage}% off ${tiersText} • Expires ${expiryDate}

Claim your discount: https://scriptgenius.com/pricing?promo=${data.promoCode}

What you get with ScriptGenius:
✓ AI-powered script analysis and suggestions
✓ Real-time collaboration with your team
✓ Industry-standard formatting tools
✓ Advanced storyboard and character development
✓ Priority customer support
✓ Early access to new features

Next Steps:
1. Choose your plan: https://scriptgenius.com/pricing
2. Enter your code: ${data.promoCode}
3. Start writing immediately

Questions? Reply to this email or contact our support team.

Welcome aboard!
The ScriptGenius Team

---
This promo code is exclusive to you and expires on ${expiryDate}
Unsubscribe: https://scriptgenius.com/unsubscribe
`;

    return { subject, html, text };
  }

  /**
   * Beta request rejection email template
   */
  getBetaRejectionTemplate(data: BetaRejectionData): EmailTemplate {
    const subject = `ScriptGenius Beta Request Update`;

    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ScriptGenius Beta Request Update</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f8fafc; }
    .container { max-width: 600px; margin: 0 auto; background: white; }
    .header { background: #64748b; color: white; padding: 40px 30px; text-align: center; }
    .content { padding: 40px 30px; }
    .cta-button { display: inline-block; background: #0ea5e9; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; margin: 20px 0; }
    .footer { background: #f1f5f9; padding: 30px; text-align: center; color: #64748b; font-size: 14px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1 style="margin: 0; font-size: 28px;">ScriptGenius Beta Update</h1>
    </div>
    
    <div class="content">
      <h2>Hi ${data.name},</h2>
      
      <p>Thank you for your interest in ScriptGenius beta access. After reviewing your request, we're unable to approve your beta access at this time.</p>
      
      ${data.reason ? `<p><strong>Reason:</strong> ${data.reason}</p>` : ''}
      
      <p>However, we'd love to keep you updated on our progress! Here's what you can do:</p>
      
      <ul>
        <li><strong>Join our waitlist:</strong> Be the first to know when we launch publicly</li>
        <li><strong>Follow our updates:</strong> Get insights into our development process</li>
        <li><strong>Reapply later:</strong> Beta access criteria may change as we grow</li>
      </ul>
      
      <div style="text-align: center;">
        <a href="https://scriptgenius.com/waitlist" class="cta-button">
          Join Our Waitlist →
        </a>
      </div>
      
      <p>We appreciate your interest in ScriptGenius and hope to welcome you to our community soon.</p>
      
      <p>Best regards,<br><strong>The ScriptGenius Team</strong></p>
    </div>
    
    <div class="footer">
      <p>ScriptGenius - Professional Screenwriting Platform</p>
      <p><a href="https://scriptgenius.com/unsubscribe">Unsubscribe</a> | <a href="https://scriptgenius.com/privacy">Privacy Policy</a></p>
    </div>
  </div>
</body>
</html>`;

    const text = `
ScriptGenius Beta Update

Hi ${data.name},

Thank you for your interest in ScriptGenius beta access. After reviewing your request, we're unable to approve your beta access at this time.

${data.reason ? `Reason: ${data.reason}` : ''}

However, we'd love to keep you updated on our progress! Here's what you can do:

• Join our waitlist: Be the first to know when we launch publicly
• Follow our updates: Get insights into our development process  
• Reapply later: Beta access criteria may change as we grow

Join our waitlist: https://scriptgenius.com/waitlist

We appreciate your interest in ScriptGenius and hope to welcome you to our community soon.

Best regards,
The ScriptGenius Team

---
Unsubscribe: https://scriptgenius.com/unsubscribe
`;

    return { subject, html, text };
  }

  /**
   * Admin notification for new beta request
   */
  getAdminNotificationTemplate(data: BetaRequestData): EmailTemplate {
    const subject = `New Beta Request: ${data.name} (${data.email})`;

    const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>New Beta Request</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
    .field { margin: 15px 0; }
    .label { font-weight: bold; color: #64748b; }
    .value { margin-top: 5px; }
    .use-case { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .actions { background: #f0f9ff; padding: 20px; border-radius: 8px; margin-top: 20px; }
    .button { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 6px; font-weight: bold; }
    .approve { background: #10b981; color: white; }
    .reject { background: #ef4444; color: white; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2 style="margin: 0;">🎬 New Beta Access Request</h2>
      <p style="margin: 5px 0 0 0; color: #64748b;">Review and take action</p>
    </div>
    
    <div class="field">
      <div class="label">Name:</div>
      <div class="value">${data.name}</div>
    </div>
    
    <div class="field">
      <div class="label">Email:</div>
      <div class="value">${data.email}</div>
    </div>
    
    <div class="field">
      <div class="label">Company:</div>
      <div class="value">${data.company || 'Not provided'}</div>
    </div>
    
    <div class="field">
      <div class="label">Referral Source:</div>
      <div class="value">${data.referralSource}</div>
    </div>
    
    <div class="field">
      <div class="label">Use Case:</div>
      <div class="use-case">${data.useCase}</div>
    </div>
    
    <div class="actions">
      <h3 style="margin-top: 0;">Quick Actions:</h3>
      <a href="https://scriptgenius.com/admin/beta-requests" class="button approve">
        Review in Admin Panel →
      </a>
    </div>
  </div>
</body>
</html>`;

    const text = `
New Beta Access Request

Name: ${data.name}
Email: ${data.email}
Company: ${data.company || 'Not provided'}
Referral Source: ${data.referralSource}

Use Case:
${data.useCase}

Review in admin panel: https://scriptgenius.com/admin/beta-requests
`;

    return { subject, html, text };
  }
}

// Export singleton instance
export const emailTemplateService = new EmailTemplateService();

export default emailTemplateService;
