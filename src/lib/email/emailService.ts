/**
 * Email Service for Beta Access System
 * 
 * Handles sending emails for beta request notifications,
 * approvals, rejections, and admin notifications.
 */

import { supabase } from '@/integrations/supabase/client';
import { 
  emailTemplateService, 
  BetaApprovalData, 
  BetaRejectionData, 
  BetaRequestData 
} from './emailTemplates';

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

class EmailService {
  private readonly fromEmail = 'ScriptGenius <<EMAIL>>';
  private readonly adminEmail = '<EMAIL>';

  /**
   * Send beta approval email with promo code
   */
  async sendBetaApprovalEmail(data: BetaApprovalData): Promise<EmailSendResult> {
    try {
      const template = emailTemplateService.getBetaApprovalTemplate(data);
      
      const result = await this.sendEmail({
        to: data.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      // Log the email send attempt
      await this.logEmailActivity({
        recipient: data.email,
        type: 'beta_approval',
        success: result.success,
        details: {
          promo_code: data.promoCode,
          discount_percentage: data.discountPercentage,
        },
      });

      return result;
    } catch (error) {
      console.error('Failed to send beta approval email:', error);
      return {
        success: false,
        error: 'Failed to send approval email',
      };
    }
  }

  /**
   * Send beta rejection email
   */
  async sendBetaRejectionEmail(data: BetaRejectionData): Promise<EmailSendResult> {
    try {
      const template = emailTemplateService.getBetaRejectionTemplate(data);
      
      const result = await this.sendEmail({
        to: data.email,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      // Log the email send attempt
      await this.logEmailActivity({
        recipient: data.email,
        type: 'beta_rejection',
        success: result.success,
        details: {
          reason: data.reason,
        },
      });

      return result;
    } catch (error) {
      console.error('Failed to send beta rejection email:', error);
      return {
        success: false,
        error: 'Failed to send rejection email',
      };
    }
  }

  /**
   * Send admin notification for new beta request
   */
  async sendAdminNotification(data: BetaRequestData): Promise<EmailSendResult> {
    try {
      const template = emailTemplateService.getAdminNotificationTemplate(data);
      
      const result = await this.sendEmail({
        to: this.adminEmail,
        subject: template.subject,
        html: template.html,
        text: template.text,
      });

      // Log the email send attempt
      await this.logEmailActivity({
        recipient: this.adminEmail,
        type: 'admin_notification',
        success: result.success,
        details: {
          beta_request_email: data.email,
          beta_request_name: data.name,
        },
      });

      return result;
    } catch (error) {
      console.error('Failed to send admin notification:', error);
      return {
        success: false,
        error: 'Failed to send admin notification',
      };
    }
  }

  /**
   * Send email using Supabase Edge Function or external service
   */
  private async sendEmail(params: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<EmailSendResult> {
    try {
      // Option 1: Use Supabase Edge Function for email sending
      const { data, error } = await supabase.functions.invoke('send-email', {
        body: {
          to: params.to,
          from: this.fromEmail,
          subject: params.subject,
          html: params.html,
          text: params.text,
        },
      });

      if (error) {
        console.error('Supabase email function error:', error);
        
        // Fallback to webhook/external service
        return await this.sendEmailViaWebhook(params);
      }

      return {
        success: true,
        messageId: data?.messageId,
      };
    } catch (error) {
      console.error('Email sending failed:', error);
      
      // Fallback to webhook/external service
      return await this.sendEmailViaWebhook(params);
    }
  }

  /**
   * Fallback email sending via webhook (e.g., Resend, SendGrid, etc.)
   */
  private async sendEmailViaWebhook(params: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<EmailSendResult> {
    try {
      // This would integrate with your preferred email service
      // For now, we'll simulate the email sending
      
      console.log('📧 Email would be sent:', {
        to: params.to,
        subject: params.subject,
        from: this.fromEmail,
      });

      // In production, replace this with actual email service integration
      // Example for Resend:
      /*
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: this.fromEmail,
          to: params.to,
          subject: params.subject,
          html: params.html,
          text: params.text,
        }),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Email sending failed');
      }

      return {
        success: true,
        messageId: data.id,
      };
      */

      // Simulate successful email sending for development
      return {
        success: true,
        messageId: `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      };
    } catch (error) {
      console.error('Webhook email sending failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Log email activity for audit trail
   */
  private async logEmailActivity(params: {
    recipient: string;
    type: string;
    success: boolean;
    details?: Record<string, any>;
  }): Promise<void> {
    try {
      await supabase
        .from('beta_request_logs')
        .insert({
          beta_request_id: null, // Will be linked by trigger if applicable
          action: `email_${params.type}`,
          details: {
            recipient: params.recipient,
            success: params.success,
            timestamp: new Date().toISOString(),
            ...params.details,
          },
        });
    } catch (error) {
      console.error('Failed to log email activity:', error);
      // Don't throw error - email logging failure shouldn't break email sending
    }
  }

  /**
   * Get email sending statistics
   */
  async getEmailStats(): Promise<{
    totalSent: number;
    approvalsSent: number;
    rejectionsSent: number;
    adminNotificationsSent: number;
    successRate: number;
  }> {
    try {
      const { data, error } = await supabase
        .from('beta_request_logs')
        .select('action, details')
        .like('action', 'email_%');

      if (error) throw error;

      const logs = data || [];
      const totalSent = logs.length;
      const approvalsSent = logs.filter(log => log.action === 'email_beta_approval').length;
      const rejectionsSent = logs.filter(log => log.action === 'email_beta_rejection').length;
      const adminNotificationsSent = logs.filter(log => log.action === 'email_admin_notification').length;
      
      const successfulSends = logs.filter(log => 
        log.details && typeof log.details === 'object' && 
        'success' in log.details && log.details.success === true
      ).length;
      
      const successRate = totalSent > 0 ? (successfulSends / totalSent) * 100 : 0;

      return {
        totalSent,
        approvalsSent,
        rejectionsSent,
        adminNotificationsSent,
        successRate,
      };
    } catch (error) {
      console.error('Failed to get email stats:', error);
      return {
        totalSent: 0,
        approvalsSent: 0,
        rejectionsSent: 0,
        adminNotificationsSent: 0,
        successRate: 0,
      };
    }
  }

  /**
   * Test email configuration
   */
  async testEmailConfiguration(): Promise<EmailSendResult> {
    return await this.sendEmail({
      to: this.adminEmail,
      subject: 'ScriptGenius Email Test',
      html: '<h1>Email Test</h1><p>This is a test email from ScriptGenius.</p>',
      text: 'Email Test\n\nThis is a test email from ScriptGenius.',
    });
  }
}

// Export singleton instance
export const emailService = new EmailService();

export default emailService;
