import { logger } from './logger';

interface A11yOptions {
  enableLogging?: boolean;
  enableFocusTrap?: boolean;
  enableKeyboardNavigation?: boolean;
}

class A11y {
  private static instance: A11y;
  private options: Required<A11yOptions>;
  private focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
  private focusTrapStack: HTMLElement[] = [];

  private constructor(options: A11yOptions = {}) {
    this.options = {
      enableLogging: options.enableLogging ?? true,
      enableFocusTrap: options.enableFocusTrap ?? true,
      enableKeyboardNavigation: options.enableKeyboardNavigation ?? true,
    };

    if (this.options.enableKeyboardNavigation) {
      this.setupKeyboardNavigation();
    }
  }

  static getInstance(options?: A11yOptions): A11y {
    if (!A11y.instance) {
      A11y.instance = new A11y(options);
    }
    return A11y.instance;
  }

  // Focus management
  focusFirstElement(container: HTMLElement) {
    const focusable = this.getFocusableElements(container);
    if (focusable.length > 0) {
      focusable[0].focus();
    }
  }

  focusLastElement(container: HTMLElement) {
    const focusable = this.getFocusableElements(container);
    if (focusable.length > 0) {
      focusable[focusable.length - 1].focus();
    }
  }

  // Focus trap
  trapFocus(element: HTMLElement) {
    if (!this.options.enableFocusTrap) return;

    this.focusTrapStack.push(element);
    const focusable = this.getFocusableElements(element);
    
    if (focusable.length === 0) {
      this.log('No focusable elements found in focus trap');
      return;
    }

    const firstFocusable = focusable[0];
    const lastFocusable = focusable[focusable.length - 1];

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstFocusable) {
          e.preventDefault();
          lastFocusable.focus();
        }
      } else {
        if (document.activeElement === lastFocusable) {
          e.preventDefault();
          firstFocusable.focus();
        }
      }
    };

    element.addEventListener('keydown', handleKeyDown);
    firstFocusable.focus();

    return () => {
      element.removeEventListener('keydown', handleKeyDown);
      this.focusTrapStack = this.focusTrapStack.filter(el => el !== element);
    };
  }

  // ARIA helpers
  setAriaLabel(element: HTMLElement, label: string) {
    element.setAttribute('aria-label', label);
  }

  setAriaDescribedBy(element: HTMLElement, descriptionId: string) {
    element.setAttribute('aria-describedby', descriptionId);
  }

  setAriaExpanded(element: HTMLElement, expanded: boolean) {
    element.setAttribute('aria-expanded', String(expanded));
  }

  setAriaHidden(element: HTMLElement, hidden: boolean) {
    element.setAttribute('aria-hidden', String(hidden));
  }

  setAriaLive(element: HTMLElement, politeness: 'off' | 'polite' | 'assertive' = 'polite') {
    element.setAttribute('aria-live', politeness);
  }

  // Keyboard navigation
  private setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Skip if modifier keys are pressed
      if (e.ctrlKey || e.altKey || e.metaKey) return;

      // Handle common keyboard shortcuts
      switch (e.key) {
        case 'Escape':
          this.handleEscape();
          break;
        case 'Enter':
          this.handleEnter(e);
          break;
        case ' ':
          this.handleSpace(e);
          break;
      }
    });
  }

  private handleEscape() {
    // Close the topmost focus trap if exists
    const topTrap = this.focusTrapStack[this.focusTrapStack.length - 1];
    if (topTrap) {
      const closeEvent = new CustomEvent('a11y:close');
      topTrap.dispatchEvent(closeEvent);
    }
  }

  private handleEnter(e: KeyboardEvent) {
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'A') {
      e.preventDefault();
      target.click();
    }
  }

  private handleSpace(e: KeyboardEvent) {
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'A') {
      e.preventDefault();
      target.click();
    }
  }

  // Utility methods
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(
      container.querySelectorAll<HTMLElement>(this.focusableElements)
    ).filter(el => {
      const style = window.getComputedStyle(el);
      return style.display !== 'none' && style.visibility !== 'hidden';
    });
  }

  private log(message: string, data?: any) {
    if (this.options.enableLogging) {
      logger.debug(`[A11y] ${message}`, data);
    }
  }
}

export const a11y = A11y.getInstance();

// Example usage:
/*
// Focus management
const modal = document.getElementById('modal');
a11y.focusFirstElement(modal);

// Focus trap
const cleanup = a11y.trapFocus(modal);
// Later...
cleanup();

// ARIA attributes
const button = document.getElementById('menu-button');
a11y.setAriaExpanded(button, true);
a11y.setAriaLabel(button, 'Toggle menu');

// Keyboard navigation is automatically enabled
*/ 