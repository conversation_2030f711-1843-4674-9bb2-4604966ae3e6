/**
 * Promo Code Service
 * 
 * Handles validation, application, and usage tracking of promo codes
 * for the beta access system and general promotional campaigns.
 */

import { supabase } from '@/integrations/supabase/client';

export interface PromoCodeValidation {
  valid: boolean;
  error?: string;
  discountPercentage?: number;
  validTiers?: string[];
  expiresAt?: string;
  code?: string;
}

export interface PromoCodeApplication {
  originalPrice: number;
  discountAmount: number;
  finalPrice: number;
  discountPercentage: number;
  promoCode: string;
}

export interface TierPricing {
  starter: number;
  pro: number;
  studio: number;
  enterprise: number;
}

// Standard tier pricing (in cents to avoid floating point issues)
export const TIER_PRICING: TierPricing = {
  starter: 9900,    // $99.00
  pro: 19900,       // $199.00
  studio: 49900,    // $499.00
  enterprise: 99900, // $999.00
};

class PromoCodeService {
  /**
   * Validate a promo code for a specific tier
   */
  async validatePromoCode(
    code: string, 
    tier?: keyof TierPricing
  ): Promise<PromoCodeValidation> {
    try {
      const { data, error } = await supabase.rpc('validate_promo_code', {
        code_input: code,
        tier_input: tier || null,
      });

      if (error) {
        console.error('Promo code validation error:', error);
        return {
          valid: false,
          error: 'Failed to validate promo code',
        };
      }

      return data as PromoCodeValidation;
    } catch (error) {
      console.error('Promo code validation failed:', error);
      return {
        valid: false,
        error: 'Validation service unavailable',
      };
    }
  }

  /**
   * Apply a promo code to calculate final pricing
   */
  async applyPromoCode(
    code: string, 
    tier: keyof TierPricing
  ): Promise<PromoCodeApplication> {
    const validation = await this.validatePromoCode(code, tier);
    
    if (!validation.valid) {
      throw new Error(validation.error || 'Invalid promo code');
    }

    const originalPrice = TIER_PRICING[tier];
    const discountPercentage = validation.discountPercentage || 0;
    const discountAmount = Math.round((originalPrice * discountPercentage) / 100);
    const finalPrice = originalPrice - discountAmount;

    return {
      originalPrice,
      discountAmount,
      finalPrice,
      discountPercentage,
      promoCode: code,
    };
  }

  /**
   * Mark a promo code as used
   */
  async usePromoCode(
    code: string, 
    userId: string, 
    tier: keyof TierPricing,
    amount: number
  ): Promise<boolean> {
    try {
      // Update promo code usage
      const { error: updateError } = await supabase
        .from('promo_codes')
        .update({
          current_uses: supabase.sql`current_uses + 1`,
          used_by: supabase.sql`array_append(used_by, ${userId}::uuid)`,
        })
        .eq('code', code)
        .eq('is_active', true);

      if (updateError) throw updateError;

      // Update beta request if this is a beta promo code
      const { error: betaUpdateError } = await supabase
        .from('beta_requests')
        .update({
          status: 'converted',
          promo_code_used_at: new Date().toISOString(),
          conversion_tier: tier,
          conversion_amount: amount / 100, // Convert cents to dollars
        })
        .eq('promo_code', code);

      // Don't throw error if beta request update fails (might be a manual promo code)
      if (betaUpdateError) {
        console.warn('Beta request update failed:', betaUpdateError);
      }

      // Log the usage
      await supabase
        .from('beta_request_logs')
        .insert({
          beta_request_id: null, // Will be updated by trigger if applicable
          action: 'promo_code_used',
          performed_by: userId,
          details: {
            promo_code: code,
            tier,
            amount: amount / 100,
          },
        });

      return true;
    } catch (error) {
      console.error('Failed to mark promo code as used:', error);
      return false;
    }
  }

  /**
   * Get promo code usage statistics
   */
  async getPromoCodeStats(code: string) {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('*')
        .eq('code', code)
        .single();

      if (error) throw error;

      return {
        code: data.code,
        discountPercentage: data.discount_percentage,
        maxUses: data.max_uses,
        currentUses: data.current_uses,
        remainingUses: data.max_uses - data.current_uses,
        isActive: data.is_active,
        expiresAt: data.expires_at,
        validTiers: data.valid_tiers,
      };
    } catch (error) {
      console.error('Failed to get promo code stats:', error);
      return null;
    }
  }

  /**
   * Check if a user has already used a specific promo code
   */
  async hasUserUsedPromoCode(code: string, userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select('used_by')
        .eq('code', code)
        .single();

      if (error) return false;

      return data.used_by?.includes(userId) || false;
    } catch (error) {
      console.error('Failed to check promo code usage:', error);
      return false;
    }
  }

  /**
   * Get all available promo codes for a user (for testing/admin purposes)
   */
  async getAvailablePromoCodes(userId?: string) {
    try {
      let query = supabase
        .from('promo_codes')
        .select('code, discount_percentage, valid_tiers, expires_at')
        .eq('is_active', true)
        .lt('current_uses', supabase.sql`max_uses`);

      // Filter out expired codes
      query = query.or(`expires_at.is.null,expires_at.gt.${new Date().toISOString()}`);

      const { data, error } = await query;

      if (error) throw error;

      // Filter out codes already used by this user
      if (userId) {
        const filteredData = [];
        for (const code of data || []) {
          const hasUsed = await this.hasUserUsedPromoCode(code.code, userId);
          if (!hasUsed) {
            filteredData.push(code);
          }
        }
        return filteredData;
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get available promo codes:', error);
      return [];
    }
  }

  /**
   * Format price for display
   */
  formatPrice(priceInCents: number): string {
    return `$${(priceInCents / 100).toFixed(2)}`;
  }

  /**
   * Calculate savings display
   */
  calculateSavings(originalPrice: number, finalPrice: number): {
    savingsAmount: number;
    savingsPercentage: number;
    formattedSavings: string;
  } {
    const savingsAmount = originalPrice - finalPrice;
    const savingsPercentage = Math.round((savingsAmount / originalPrice) * 100);
    const formattedSavings = this.formatPrice(savingsAmount);

    return {
      savingsAmount,
      savingsPercentage,
      formattedSavings,
    };
  }

  /**
   * Validate tier selection
   */
  isValidTier(tier: string): tier is keyof TierPricing {
    return tier in TIER_PRICING;
  }

  /**
   * Get tier price
   */
  getTierPrice(tier: keyof TierPricing): number {
    return TIER_PRICING[tier];
  }

  /**
   * Get all tier prices for display
   */
  getAllTierPrices(): Record<keyof TierPricing, string> {
    return {
      starter: this.formatPrice(TIER_PRICING.starter),
      pro: this.formatPrice(TIER_PRICING.pro),
      studio: this.formatPrice(TIER_PRICING.studio),
      enterprise: this.formatPrice(TIER_PRICING.enterprise),
    };
  }
}

// Export singleton instance
export const promoCodeService = new PromoCodeService();

// Export types and constants
export { PromoCodeService, TIER_PRICING };
export default promoCodeService;
