
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context: LogContext;
  stack?: string;
  performance?: {
    duration?: number;
    memory?: number;
  };
  tags?: string[];
}

export class EnhancedLogger {
  private static instance: EnhancedLogger;
  private logs: LogEntry[] = [];
  private maxLogs = 1000;
  private logLevel: LogLevel = 'info';
  private enableRemoteLogging = false;
  private remoteEndpoint?: string;
  private batchQueue: LogEntry[] = [];
  private batchTimer?: NodeJS.Timeout;
  private sessionId: string;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.setupPerformanceLogging();
    this.setupErrorCapture();
  }

  static getInstance(): EnhancedLogger {
    if (!EnhancedLogger.instance) {
      EnhancedLogger.instance = new EnhancedLogger();
    }
    return EnhancedLogger.instance;
  }

  configure(options: {
    level?: LogLevel;
    enableRemoteLogging?: boolean;
    remoteEndpoint?: string;
    maxLogs?: number;
  }) {
    this.logLevel = options.level || this.logLevel;
    this.enableRemoteLogging = options.enableRemoteLogging || false;
    this.remoteEndpoint = options.remoteEndpoint;
    this.maxLogs = options.maxLogs || this.maxLogs;
  }

  debug(message: string, context: LogContext = {}) {
    this.log('debug', message, context);
  }

  info(message: string, context: LogContext = {}) {
    this.log('info', message, context);
  }

  warn(message: string, context: LogContext = {}) {
    this.log('warn', message, context);
  }

  error(message: string, error?: Error, context: LogContext = {}) {
    this.log('error', message, {
      ...context,
      metadata: {
        ...context.metadata,
        error: error ? {
          name: error.name,
          message: error.message,
          stack: error.stack
        } : undefined
      }
    }, error?.stack);
  }

  fatal(message: string, error?: Error, context: LogContext = {}) {
    this.log('fatal', message, context, error?.stack);
  }

  // Performance logging
  time(label: string): () => void {
    const start = performance.now();
    const startMemory = this.getMemoryUsage();

    return () => {
      const duration = performance.now() - start;
      const endMemory = this.getMemoryUsage();
      
      this.info(`Performance: ${label}`, {
        action: 'performance-measurement',
        metadata: {
          label,
          duration: Math.round(duration * 100) / 100,
          memoryDelta: endMemory - startMemory
        }
      });
    };
  }

  // Structured logging with correlation
  async trace<T>(
    operation: string,
    fn: () => Promise<T> | T,
    context: LogContext = {}
  ): Promise<T> {
    const requestId = this.generateRequestId();
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();

    this.debug(`Starting: ${operation}`, {
      ...context,
      requestId,
      action: 'operation-start'
    });

    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      const endMemory = this.getMemoryUsage();

      this.info(`Completed: ${operation}`, {
        ...context,
        requestId,
        action: 'operation-complete',
        metadata: {
          success: true,
          duration: Math.round(duration * 100) / 100,
          memoryUsed: endMemory - startMemory
        }
      });

      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.error(`Failed: ${operation}`, error as Error, {
        ...context,
        requestId,
        action: 'operation-failed',
        metadata: {
          success: false,
          duration: Math.round(duration * 100) / 100
        }
      });

      throw error;
    }
  }

  // Get logs with filtering
  getLogs(filters: {
    level?: LogLevel;
    component?: string;
    timeRange?: { start: Date; end: Date };
    tags?: string[];
  } = {}): LogEntry[] {
    return this.logs.filter(log => {
      if (filters.level && !this.isLevelEnabled(filters.level, log.level)) {
        return false;
      }
      
      if (filters.component && log.context.component !== filters.component) {
        return false;
      }
      
      if (filters.timeRange) {
        const logTime = new Date(log.timestamp);
        if (logTime < filters.timeRange.start || logTime > filters.timeRange.end) {
          return false;
        }
      }
      
      if (filters.tags && !filters.tags.some(tag => log.tags?.includes(tag))) {
        return false;
      }
      
      return true;
    });
  }

  // Export logs
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'level', 'message', 'component', 'action', 'userId'];
      const rows = this.logs.map(log => [
        log.timestamp,
        log.level,
        log.message.replace(/"/g, '""'),
        log.context.component || '',
        log.context.action || '',
        log.context.userId || ''
      ]);
      
      return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
    }
    
    return JSON.stringify(this.logs, null, 2);
  }

  private log(level: LogLevel, message: string, context: LogContext = {}, stack?: string) {
    if (!this.isLevelEnabled(this.logLevel, level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: {
        ...context,
        sessionId: context.sessionId || this.sessionId
      },
      stack,
      performance: {
        memory: this.getMemoryUsage()
      }
    };

    // Add to local storage
    this.logs.push(entry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console output in development
    if (import.meta.env.DEV) {
      this.logToConsole(entry);
    }

    // Queue for remote logging
    if (this.enableRemoteLogging) {
      this.queueForRemote(entry);
    }
  }

  private isLevelEnabled(configLevel: LogLevel, logLevel: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error', 'fatal'];
    return levels.indexOf(logLevel) >= levels.indexOf(configLevel);
  }

  private logToConsole(entry: LogEntry) {
    const { level, message, context, timestamp } = entry;
    const logFn = level === 'error' || level === 'fatal' ? console.error :
                  level === 'warn' ? console.warn :
                  level === 'debug' ? console.debug : console.info;

    const prefix = `[${timestamp}] ${level.toUpperCase()}:`;
    const contextStr = Object.keys(context).length > 0 ? JSON.stringify(context) : '';
    
    logFn(prefix, message, contextStr);
  }

  private queueForRemote(entry: LogEntry) {
    this.batchQueue.push(entry);
    
    if (this.batchQueue.length >= 10) {
      this.flushRemoteLogs();
    } else if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => this.flushRemoteLogs(), 5000);
    }
  }

  private async flushRemoteLogs() {
    if (!this.remoteEndpoint || this.batchQueue.length === 0) {
      return;
    }

    const batch = [...this.batchQueue];
    this.batchQueue = [];
    
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = undefined;
    }

    try {
      await fetch(this.remoteEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          logs: batch,
          metadata: {
            sessionId: this.sessionId,
            userAgent: navigator.userAgent,
            url: window.location.href
          }
        })
      });
    } catch (error) {
      // Re-queue failed logs
      this.batchQueue.unshift(...batch);
      console.error('Failed to send logs to remote endpoint:', error);
    }
  }

  private setupPerformanceLogging() {
    // Log slow operations
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const start = performance.now();
      try {
        const response = await originalFetch(...args);
        const duration = performance.now() - start;
        
        if (duration > 1000) { // Log slow requests
          this.warn('Slow network request detected', {
            action: 'slow-request',
            metadata: {
              url: args[0]?.toString(),
              duration: Math.round(duration),
              status: response.status
            }
          });
        }
        
        return response;
      } catch (error) {
        const duration = performance.now() - start;
        this.error('Network request failed', error as Error, {
          action: 'request-failed',
          metadata: {
            url: args[0]?.toString(),
            duration: Math.round(duration)
          }
        });
        throw error;
      }
    };
  }

  private setupErrorCapture() {
    // Capture unhandled errors
    window.addEventListener('error', (event) => {
      this.error('Unhandled error', event.error, {
        action: 'unhandled-error',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('Unhandled promise rejection', new Error(event.reason), {
        action: 'unhandled-rejection'
      });
    });
  }

  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize || 0;
    }
    return 0;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const enhancedLogger = EnhancedLogger.getInstance();
