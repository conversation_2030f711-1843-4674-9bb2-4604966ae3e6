
import type { ValidationRule } from './core';

export const createRule = (
  validate: (value: any) => boolean | string | Promise<boolean | string>,
  message?: string
): ValidationRule => {
  return async (value: any) => {
    try {
      const result = await validate(value);
      if (result === true) return true;
      return message || (typeof result === 'string' ? result : 'Invalid value');
    } catch (error) {
      console.error('Validation rule error:', error);
      return message || 'Validation failed';
    }
  };
};

export const rules = {
  required: (message = 'This field is required'): ValidationRule =>
    createRule(value => {
      if (value === undefined || value === null) return false;
      if (typeof value === 'string') return value.trim().length > 0;
      if (Array.isArray(value)) return value.length > 0;
      return true;
    }, message),

  minLength: (length: number, message?: string): ValidationRule =>
    createRule(value => {
      if (typeof value !== 'string') return false;
      return value.length >= length;
    }, message || `Minimum length is ${length} characters`),

  maxLength: (length: number, message?: string): ValidationRule =>
    createRule(value => {
      if (typeof value !== 'string') return false;
      return value.length <= length;
    }, message || `Maximum length is ${length} characters`),

  email: (message = 'Invalid email address'): ValidationRule =>
    createRule(value => {
      if (typeof value !== 'string') return false;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    }, message),

  pattern: (regex: RegExp, message?: string): ValidationRule =>
    createRule(value => {
      if (typeof value !== 'string') return false;
      return regex.test(value);
    }, message || 'Invalid format'),

  min: (min: number, message?: string): ValidationRule =>
    createRule(value => {
      const num = Number(value);
      return !isNaN(num) && num >= min;
    }, message || `Minimum value is ${min}`),

  max: (max: number, message?: string): ValidationRule =>
    createRule(value => {
      const num = Number(value);
      return !isNaN(num) && num <= max;
    }, message || `Maximum value is ${max}`),

  url: (message = 'Invalid URL'): ValidationRule =>
    createRule(value => {
      if (typeof value !== 'string') return false;
      try {
        new URL(value);
        return true;
      } catch {
        return false;
      }
    }, message),

  match: (compareValue: any, message?: string): ValidationRule =>
    createRule((value) => {
      return value === compareValue;
    }, message || 'Values do not match'),

  custom: (validate: (value: any) => boolean | string | Promise<boolean | string>, message?: string): ValidationRule =>
    createRule(validate, message)
};
