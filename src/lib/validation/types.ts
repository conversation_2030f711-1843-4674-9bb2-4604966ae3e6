
export interface ValidationRule<T = any> {
  validate: (value: T) => string | boolean | Promise<string | boolean>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface FieldValidation<T = any> {
  required?: boolean;
  rules?: ValidationRule<T>[];
  message?: string;
}

export interface FormValidation {
  [fieldName: string]: FieldValidation;
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}
