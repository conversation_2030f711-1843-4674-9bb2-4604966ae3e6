
import type { ValidationRule } from './types';

// Helper function to create validation rules
export const createValidationRule = <T = any>(
  validate: (value: T) => string | boolean | Promise<string | boolean>
): ValidationRule<T> => ({
  validate
});

// Common validation rules
export const required = createValidationRule((value: any) => {
  if (value === undefined || value === null || value === '') {
    return 'This field is required';
  }
  return true;
});

export const minLength = (min: number) => createValidationRule((value: string) => {
  if (value.length < min) {
    return `Minimum length is ${min} characters`;
  }
  return true;
});

export const maxLength = (max: number) => createValidationRule((value: string) => {
  if (value.length > max) {
    return `Maximum length is ${max} characters`;
  }
  return true;
});

export const pattern = (regex: RegExp, message: string) => createValidationRule((value: string) => {
  if (!regex.test(value)) {
    return message;
  }
  return true;
});

export const email = createValidationRule((value: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    return 'Invalid email format';
  }
  return true;
});

export const number = createValidationRule((value: any) => {
  if (isNaN(Number(value))) {
    return 'Must be a number';
  }
  return true;
});

export const min = (min: number) => createValidationRule((value: number) => {
  if (value < min) {
    return `Minimum value is ${min}`;
  }
  return true;
});

export const max = (max: number) => createValidationRule((value: number) => {
  if (value > max) {
    return `Maximum value is ${max}`;
  }
  return true;
});
