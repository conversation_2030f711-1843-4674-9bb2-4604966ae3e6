
import { Validator } from './validatorClass';
import type { ValidationResult } from './types';

export class AsyncValidator extends Validator {
  private asyncRules: Map<string, ((value: any) => Promise<string | null>)[]> = new Map();

  addAsyncRule(
    field: string, 
    rule: (value: any) => Promise<string | null>, 
    message?: string
  ): this {
    if (!this.asyncRules.has(field)) {
      this.asyncRules.set(field, []);
    }
    
    this.asyncRules.get(field)!.push(rule);
    
    if (message) {
      this.messages.push(message);
    }
    
    return this;
  }

  async validateFieldAsync(field: string, value: any): Promise<ValidationResult> {
    // First run synchronous validation
    const syncResult = await this.validate(value, [field]);
    if (!syncResult.isValid) {
      return syncResult;
    }

    // Then run async validation
    const asyncRules = this.asyncRules.get(field) || [];
    const errors: string[] = [];

    for (let i = 0; i < asyncRules.length; i++) {
      const rule = asyncRules[i];
      try {
        const error = await rule(value);
        if (error) {
          errors.push(error);
        }
      } catch (err) {
        errors.push(`Validation error: ${(err as Error).message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async validateAsync(data: Record<string, any>): Promise<ValidationResult> {
    const errors: string[] = [];

    // Run synchronous validation first
    const syncResult = await this.validate(data, Object.keys(data));
    if (!syncResult.isValid) {
      errors.push(...syncResult.errors);
    }

    // Run async validation for fields that have async rules
    const asyncPromises = Object.entries(data)
      .filter(([field]) => this.asyncRules.has(field))
      .map(async ([field, value]) => {
        const result = await this.validateFieldAsync(field, value);
        if (!result.isValid) {
          return result.errors.map(err => `${field}: ${err}`);
        }
        return [];
      });

    const asyncResults = await Promise.all(asyncPromises);
    const asyncErrors = asyncResults.flat();

    return {
      isValid: errors.length === 0 && asyncErrors.length === 0,
      errors: [...errors, ...asyncErrors]
    };
  }
}
