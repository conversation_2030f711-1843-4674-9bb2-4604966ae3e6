
import { z } from 'zod';
import { uuidSchema, nonEmptyStringSchema, dateSchema } from './base-schemas';

// Resource Schemas
export const createResourceSchema = z.object({
  name: nonEmptyStringSchema.max(200, 'Name must be 200 characters or less'),
  type: z.enum(['equipment', 'location', 'talent', 'crew', 'vehicle', 'other']),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  availability_status: z.enum(['available', 'booked', 'maintenance', 'unavailable']).default('available'),
  cost_per_day: z.number().min(0, 'Cost must be non-negative').max(99999.99, 'Cost too large').optional(),
  contact_info: z.record(z.any()).optional(),
  specifications: z.record(z.any()).optional(),
});

export const updateResourceSchema = createResourceSchema.partial();

// Equipment Schemas
export const createEquipmentSchema = z.object({
  name: nonEmptyStringSchema.max(200, 'Name must be 200 characters or less'),
  category: nonEmptyStringSchema.max(100, 'Category must be 100 characters or less'),
  serial_number: z.string().max(100, 'Serial number too long').optional(),
  purchase_date: dateSchema.optional(),
  condition: z.string().max(50, 'Condition description too long').default('good'),
  status: z.enum(['available', 'checked_out', 'maintenance', 'retired']).default('available'),
  daily_rate: z.number().min(0, 'Rate must be non-negative').max(9999.99, 'Rate too large').optional(),
  specifications: z.record(z.any()).optional(),
  maintenance_notes: z.string().max(2000, 'Notes must be 2000 characters or less').optional(),
  location: z.string().max(200, 'Location too long').optional(),
});

export const updateEquipmentSchema = createEquipmentSchema.partial();

// Location Scout Schemas
export const createLocationScoutSchema = z.object({
  location_name: nonEmptyStringSchema.max(200, 'Location name must be 200 characters or less'),
  address: z.string().max(500, 'Address too long').optional(),
  coordinates: z.any().optional(),
  description: z.string().max(2000, 'Description must be 2000 characters or less').optional(),
  accessibility_notes: z.string().max(1000, 'Notes must be 1000 characters or less').optional(),
  parking_info: z.string().max(1000, 'Parking info must be 1000 characters or less').optional(),
  permits_required: z.boolean().default(false),
  cost_per_day: z.number().min(0, 'Cost must be non-negative').max(99999.99, 'Cost too large').optional(),
  contact_person: z.string().max(200, 'Contact name too long').optional(),
  contact_phone: z.string().max(20, 'Phone number too long').optional(),
  contact_email: z.string().email('Invalid email format').max(254, 'Email too long').optional(),
  availability_notes: z.string().max(1000, 'Notes must be 1000 characters or less').optional(),
  photos: z.record(z.any()).optional(),
  status: z.enum(['scouting', 'approved', 'rejected', 'pending']).default('scouting'),
  scouted_date: dateSchema.optional(),
});

export const updateLocationScoutSchema = createLocationScoutSchema.partial();
