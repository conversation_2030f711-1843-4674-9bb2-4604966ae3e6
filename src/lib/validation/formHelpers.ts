
import { Validator } from './validatorClass';
import { required } from './validationRules';
import type { FormValidation } from './types';

// Form validation helper
export function createFormValidator(schema: FormValidation): Validator {
  const validator = new Validator();

  Object.entries(schema).forEach(([field, config]) => {
    if (config.required) {
      validator.addRule(field, required);
    }

    if (config.rules) {
      config.rules.forEach(rule => {
        validator.addRule(field, rule);
      });
    }
  });

  return validator;
}
