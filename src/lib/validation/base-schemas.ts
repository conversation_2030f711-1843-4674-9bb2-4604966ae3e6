
import { z } from 'zod';

// Base validation helpers
export const uuidSchema = z.string().uuid();
export const nonEmptyStringSchema = z.string().min(1, 'This field is required');
export const positiveNumberSchema = z.number().positive('Must be a positive number');
export const dateSchema = z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)');
export const timeSchema = z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)');

// Generic validation helper
export function validateInput<T>(
  schema: z.ZodSchema<T>, 
  data: unknown
): { success: true; data: T } | { success: false; errors: Record<string, string> } {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string> = {};
      error.errors.forEach((err) => {
        const path = err.path.join('.');
        errors[path] = err.message;
      });
      return { success: false, errors };
    }
    return { success: false, errors: { general: 'Validation failed' } };
  }
}

// SQL injection prevention helpers
export function sanitizeSearchQuery(query: string): string {
  return query
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .trim()
    .slice(0, 100); // Limit length
}

export function validateSortField(field: string, allowedFields: string[]): string {
  if (!allowedFields.includes(field)) {
    return allowedFields[0]; // Return default safe field
  }
  return field;
}

export function validateSortDirection(direction: string): 'asc' | 'desc' {
  return direction === 'desc' ? 'desc' : 'asc';
}
