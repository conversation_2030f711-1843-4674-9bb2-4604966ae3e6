
import { z } from 'zod';
import { uuidSchema, nonEmptyStringSchema, dateSchema, timeSchema } from './base-schemas';

// Production Schedule Schemas - Base object for composition
const scheduleBaseSchema = z.object({
  title: nonEmptyStringSchema.max(200, 'Title must be 200 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  start_date: dateSchema,
  end_date: dateSchema,
  status: z.enum(['draft', 'active', 'completed', 'cancelled']).default('draft'),
});

export const createScheduleSchema = scheduleBaseSchema.refine(data => new Date(data.end_date) > new Date(data.start_date), {
  message: 'End date must be after start date',
  path: ['end_date']
});

export const updateScheduleSchema = scheduleBaseSchema.partial();

// Export the type
export type CreateScheduleInput = z.infer<typeof createScheduleSchema>;

// Schedule Item Schemas - Base object for composition
const scheduleItemBaseSchema = z.object({
  schedule_id: uuidSchema,
  scene_id: uuidSchema.optional(),
  location_id: uuidSchema.optional(),
  title: nonEmptyStringSchema.max(200, 'Title must be 200 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  scheduled_date: dateSchema,
  start_time: timeSchema.optional(),
  end_time: timeSchema.optional(),
  estimated_duration: z.number().int().positive().max(1440, 'Duration cannot exceed 24 hours').optional(),
  status: z.enum(['scheduled', 'in_progress', 'completed', 'cancelled']).default('scheduled'),
  notes: z.string().max(2000, 'Notes must be 2000 characters or less').optional(),
});

export const createScheduleItemSchema = scheduleItemBaseSchema.refine(data => {
  if (data.start_time && data.end_time) {
    return data.end_time > data.start_time;
  }
  return true;
}, {
  message: 'End time must be after start time',
  path: ['end_time']
});

export type CreateScheduleItemInput = z.infer<typeof createScheduleItemSchema>;
