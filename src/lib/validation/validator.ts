
import { logger } from '@/utils/logger';
import type { ValidationRule, ValidationRules, ValidationErrors, ValidationOptions, ValidationResult } from './core';

export class Validator {
  private static instance: Validator;
  private rules: ValidationRules = {};
  private options: ValidationOptions = {
    validateOnChange: true,
    validateOnBlur: true,
    validateOnSubmit: true,
    stopOnFirstError: false
  };

  private constructor() {}

  static getInstance(): Validator {
    if (!Validator.instance) {
      Validator.instance = new Validator();
    }
    return Validator.instance;
  }

  setRules(rules: ValidationRules): void {
    this.rules = rules;
  }

  setOptions(options: ValidationOptions): void {
    this.options = { ...this.options, ...options };
  }

  addRule(field: string, rule: ValidationRule): void {
    if (!this.rules[field]) {
      this.rules[field] = [];
    }
    this.rules[field].push(rule);
  }

  removeRule(field: string, rule: ValidationRule): void {
    if (this.rules[field]) {
      this.rules[field] = this.rules[field].filter(r => r !== rule);
    }
  }

  async validateField(field: string, value: any): Promise<string[]> {
    const errors: string[] = [];
    const fieldRules = this.rules[field] || [];

    for (const rule of fieldRules) {
      try {
        const result = await rule(value);
        if (result !== true) {
          errors.push(typeof result === 'string' ? result : 'Invalid value');
          if (this.options.stopOnFirstError) break;
        }
      } catch (error) {
        logger.error(`Validation error for field ${field}:`, error);
        errors.push('Validation failed');
        if (this.options.stopOnFirstError) break;
      }
    }

    return errors;
  }

  async validateForm(values: Record<string, any>): Promise<ValidationResult> {
    const errors: ValidationErrors = {};
    let isValid = true;

    for (const [field, value] of Object.entries(values)) {
      const fieldErrors = await this.validateField(field, value);
      if (fieldErrors.length > 0) {
        errors[field] = fieldErrors;
        isValid = false;
        if (this.options.stopOnFirstError) break;
      }
    }

    return { isValid, errors };
  }
}
