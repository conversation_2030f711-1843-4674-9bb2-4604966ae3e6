
import type { ValidationRule, ValidationResult } from './types';

export class Validator {
  private static instance: Validator;
  protected messages: string[] = [];
  private rules: Map<string, ValidationRule> = new Map();

  constructor() {}

  static getInstance(): Validator {
    if (!this.instance) {
      this.instance = new Validator();
    }
    return this.instance;
  }

  addRule(name: string, rule: ValidationRule): void {
    this.rules.set(name, rule);
  }

  async validate(value: any, rules: string[]): Promise<ValidationResult> {
    const errors: string[] = [];
    
    for (const ruleName of rules) {
      const rule = this.rules.get(ruleName);
      if (!rule) {
        throw new Error(`Validation rule '${ruleName}' not found`);
      }

      const result = await rule.validate(value);
      if (typeof result === 'string') {
        errors.push(result);
      } else if (result === false) {
        errors.push(`Validation failed for rule '${ruleName}'`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  getMessages(): string[] {
    return [...this.messages];
  }

  clearMessages(): void {
    this.messages = [];
  }
}
