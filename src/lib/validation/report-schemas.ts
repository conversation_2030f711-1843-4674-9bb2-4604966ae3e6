
import { z } from 'zod';
import { uuidSchema, nonEmptyStringSchema, dateSchema } from './base-schemas';

// Report Schemas
export const createReportSchema = z.object({
  schedule_item_id: uuidSchema.optional(),
  title: nonEmptyStringSchema.max(200, 'Title must be 200 characters or less'),
  report_type: z.enum(['daily', 'wrap', 'incident', 'progress']),
  content: z.record(z.any()),
  date: dateSchema,
  status: z.enum(['draft', 'submitted', 'approved']).default('draft'),
});

export const updateReportSchema = createReportSchema.partial();
