
import { z } from 'zod';
import { uuidSchema, nonEmptyStringSchema } from './base-schemas';

// Budget Schemas
export const createBudgetSchema = z.object({
  title: nonEmptyStringSchema.max(200, 'Title must be 200 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  total_budget: z.number().min(0, 'Budget must be non-negative').max(999999999.99, 'Budget amount too large'),
  currency: z.string().length(3, 'Currency must be a 3-letter code').default('USD'),
  status: z.enum(['draft', 'approved', 'active', 'completed']).default('draft'),
});

export const updateBudgetSchema = createBudgetSchema.partial();

// Budget Line Item Schemas
export const createBudgetLineItemSchema = z.object({
  budget_id: uuidSchema,
  category: nonEmptyStringSchema.max(100, 'Category must be 100 characters or less'),
  subcategory: z.string().max(100, 'Subcategory must be 100 characters or less').optional(),
  description: nonEmptyStringSchema.max(500, 'Description must be 500 characters or less'),
  estimated_cost: z.number().min(0, 'Cost must be non-negative').max(9999999.99, 'Cost amount too large'),
  actual_cost: z.number().min(0, 'Cost must be non-negative').max(9999999.99, 'Cost amount too large').optional(),
  quantity: z.number().int().positive().max(99999, 'Quantity too large').optional(),
  unit_cost: z.number().min(0, 'Unit cost must be non-negative').max(999999.99, 'Unit cost too large').optional(),
  vendor: z.string().max(200, 'Vendor name must be 200 characters or less').optional(),
  status: z.enum(['estimated', 'approved', 'ordered', 'received', 'paid']).default('estimated'),
  notes: z.string().max(1000, 'Notes must be 1000 characters or less').optional(),
});

// Budget Approval Schemas
export const createBudgetApprovalSchema = z.object({
  budget_id: uuidSchema,
  approver_id: uuidSchema,
  approval_level: z.number().int().positive().max(10, 'Approval level too high'),
  status: z.enum(['pending', 'approved', 'rejected', 'revision_requested']).default('pending'),
  comments: z.string().max(2000, 'Comments must be 2000 characters or less').optional(),
  approved_amount: z.number().min(0, 'Amount must be non-negative').max(999999999.99, 'Amount too large').optional(),
});

export const updateBudgetApprovalSchema = z.object({
  status: z.enum(['pending', 'approved', 'rejected', 'revision_requested']),
  comments: z.string().max(2000, 'Comments must be 2000 characters or less').optional(),
  approved_amount: z.number().min(0, 'Amount must be non-negative').max(999999999.99, 'Amount too large').optional(),
});
