
interface ScreenplayValidationResult {
  isValid: boolean;
  pageCount?: number;
  error?: string;
}

/**
 * Validates screenplay content for Coverage Generator requirements
 * Estimates page count based on industry standard formatting
 */
export const validateScreenplayForCoverage = (content: string): ScreenplayValidationResult => {
  if (!content || content.trim().length === 0) {
    return {
      isValid: false,
      error: 'Screenplay content is required'
    };
  }

  // Estimate page count based on screenplay formatting standards
  // Industry standard: ~250 words per page or ~1 page per minute of screen time
  // More accurate estimation based on formatted screenplay elements
  const pageCount = estimateScreenplayPageCount(content);

  if (pageCount < 20) {
    return {
      isValid: false,
      pageCount,
      error: 'Screenplay must be at least 20 pages long (short film minimum). Current estimate: ' + Math.round(pageCount) + ' pages.'
    };
  }

  if (pageCount > 120) {
    return {
      isValid: false,
      pageCount,
      error: 'Screenplay must be no more than 120 pages long (feature film maximum). Current estimate: ' + Math.round(pageCount) + ' pages.'
    };
  }

  return {
    isValid: true,
    pageCount,
  };
};

/**
 * Estimates page count based on screenplay formatting conventions
 * This is an approximation - actual page count would depend on precise formatting
 */
function estimateScreenplayPageCount(content: string): number {
  const lines = content.split('\n');
  let estimatedPages = 0;
  
  // Count different types of screenplay elements with different weightings
  let actionLines = 0;
  let dialogueLines = 0;
  let characterLines = 0;
  let sceneHeadingLines = 0;
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine) continue;
    
    // Scene headings (INT./EXT.)
    if (/^(INT\.|EXT\.|FADE IN:|FADE OUT)/i.test(trimmedLine)) {
      sceneHeadingLines++;
    }
    // Character names (all caps, centered-ish)
    else if (/^[A-Z][A-Z\s]+$/.test(trimmedLine) && trimmedLine.length < 50) {
      characterLines++;
    }
    // Dialogue (indented text following character names)
    else if (trimmedLine.length > 0 && characterLines > dialogueLines) {
      dialogueLines++;
    }
    // Action/description
    else {
      actionLines++;
    }
  }
  
  // Screenplay page estimation formula based on industry standards
  // Scene headings: ~8-12 per page
  // Character names + dialogue: ~15-20 lines per page  
  // Action lines: ~4-6 lines per page
  const scenePages = sceneHeadingLines / 10;
  const dialoguePages = (characterLines + dialogueLines) / 17;
  const actionPages = actionLines / 5;
  
  estimatedPages = Math.max(scenePages, dialoguePages, actionPages);
  
  // Fallback: use total line count with industry average
  if (estimatedPages < 1) {
    // Industry standard: ~55 lines per page for properly formatted screenplay
    estimatedPages = lines.filter(line => line.trim().length > 0).length / 55;
  }
  
  return Math.max(1, estimatedPages);
}
