
export type ValidationRule = (value: any) => boolean | string | Promise<boolean | string>;
export type ValidationRules = Record<string, ValidationRule[]>;
export type ValidationErrors = Record<string, string[]>;

export interface ValidationOptions {
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnSubmit?: boolean;
  stopOnFirstError?: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationErrors;
}
