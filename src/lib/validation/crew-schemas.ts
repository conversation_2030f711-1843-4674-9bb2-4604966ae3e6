
import { z } from 'zod';
import { uuidSchema, nonEmptyStringSchema, dateSchema } from './base-schemas';

// Crew Member Schemas
export const createCrewMemberSchema = z.object({
  name: nonEmptyStringSchema.max(200, 'Name must be 200 characters or less'),
  role: nonEmptyStringSchema.max(100, 'Role must be 100 characters or less'),
  email: z.string().email('Invalid email format').max(254, 'Email too long').optional(),
  phone: z.string().max(20, 'Phone number too long').optional(),
  union_status: z.string().max(100, 'Union status too long').optional(),
  daily_rate: z.number().min(0, 'Rate must be non-negative').max(9999.99, 'Rate too large').optional(),
  overtime_rate: z.number().min(0, 'Rate must be non-negative').max(9999.99, 'Rate too large').optional(),
  availability_status: z.enum(['available', 'busy', 'unavailable', 'tentative']).default('available'),
  skills: z.record(z.any()).optional(),
  emergency_contact: z.record(z.any()).optional(),
  notes: z.string().max(2000, 'Notes must be 2000 characters or less').optional(),
});

export const updateCrewMemberSchema = createCrewMemberSchema.partial();

// Call Sheet Schemas
export const createCallSheetSchema = z.object({
  schedule_id: uuidSchema,
  title: nonEmptyStringSchema.max(200, 'Title must be 200 characters or less'),
  call_date: dateSchema,
  weather_info: z.record(z.any()).optional(),
  general_notes: z.string().max(2000, 'Notes must be 2000 characters or less').optional(),
  emergency_contacts: z.record(z.any()).optional(),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
});

export const updateCallSheetSchema = createCallSheetSchema.partial();
