interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of items
}

interface CacheItem<T> {
  value: T;
  timestamp: number;
  expiresAt?: number;
}

class Cache {
  private static instance: Cache;
  private storage: Map<string, CacheItem<any>>;
  private options: Required<CacheOptions>;

  private constructor(options: CacheOptions = {}) {
    this.storage = new Map();
    this.options = {
      ttl: options.ttl || 5 * 60 * 1000, // 5 minutes default
      maxSize: options.maxSize || 1000, // 1000 items default
    };
  }

  static getInstance(options?: CacheOptions): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache(options);
    }
    return Cache.instance;
  }

  set<T>(key: string, value: T, options?: CacheOptions): void {
    // Check if we need to remove old items
    if (this.storage.size >= this.options.maxSize) {
      this.removeOldest();
    }

    const item: CacheItem<T> = {
      value,
      timestamp: Date.now(),
      expiresAt: options?.ttl ? Date.now() + options.ttl : undefined,
    };

    this.storage.set(key, item);
  }

  get<T>(key: string): T | null {
    const item = this.storage.get(key) as CacheItem<T> | undefined;
    
    if (!item) return null;

    // Check if item has expired
    if (item.expiresAt && Date.now() > item.expiresAt) {
      this.storage.delete(key);
      return null;
    }

    return item.value;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): void {
    this.storage.delete(key);
  }

  clear(): void {
    this.storage.clear();
  }

  keys(): string[] {
    return Array.from(this.storage.keys());
  }

  size(): number {
    return this.storage.size;
  }

  private removeOldest(): void {
    let oldestKey: string | null = null;
    let oldestTimestamp = Infinity;

    for (const [key, item] of this.storage.entries()) {
      if (item.timestamp < oldestTimestamp) {
        oldestTimestamp = item.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.storage.delete(oldestKey);
    }
  }

  // Utility methods for common use cases
  async getOrSet<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options?: CacheOptions
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await fetchFn();
    this.set(key, value, options);
    return value;
  }

  // Clear expired items
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.storage.entries()) {
      if (item.expiresAt && now > item.expiresAt) {
        this.storage.delete(key);
      }
    }
  }
}

export const cache = Cache.getInstance();

// Example usage:
/*
// Basic usage
cache.set('user:123', { name: 'John', age: 30 });
const user = cache.get('user:123');

// With TTL
cache.set('temp:data', { foo: 'bar' }, { ttl: 60 * 1000 }); // 1 minute

// Async data fetching with caching
const user = await cache.getOrSet(
  'user:123',
  async () => {
    const response = await fetch('/api/users/123');
    return response.json();
  },
  { ttl: 5 * 60 * 1000 } // 5 minutes
);
*/ 