
import { logger } from './logger';

type Action<T = any> = {
  type: string;
  payload?: T;
  timestamp: number;
};

type Reducer<S = any, A extends Action = Action> = (state: S, action: A) => S;
type Middleware<S = any, A extends Action = Action> = (store: Store<S, A>) => (next: (action: A) => void) => (action: A) => void;
type Listener<S = any> = (state: S, action: Action) => void;

interface StoreOptions<S = any> {
  initialState: S;
  name?: string;
  persist?: boolean;
  middleware?: Middleware<S, any>[];
  devTools?: boolean;
}

class Store<S = any, A extends Action = Action> {
  private static instance: Store;
  private state: S;
  private reducer: Reducer<S, A>;
  private listeners: Set<Listener<S>> = new Set();
  private middleware: ((next: (action: A) => void) => (action: A) => void)[] = [];
  private actionHistory: Action[] = [];
  private isDispatching = false;
  private options: StoreOptions<S>;

  private constructor(
    reducer: Reducer<S, A>,
    options: StoreOptions<S>
  ) {
    this.reducer = reducer;
    this.options = {
      initialState: options.initialState,
      name: options.name || 'store',
      persist: options.persist ?? false,
      middleware: options.middleware || [],
      devTools: options.devTools ?? import.meta.env.DEV,
    };

    // Initialize state
    if (this.options.persist) {
      const savedState = this.loadState();
      this.state = savedState || options.initialState;
    } else {
      this.state = options.initialState;
    }

    // Set up middleware chain - fix the type compatibility
    this.middleware = (this.options.middleware || []).map(m => {
      const middlewareInstance = m(this);
      return middlewareInstance;
    });

    // Set up dev tools
    if (this.options.devTools) {
      this.setupDevTools();
    }
  }

  static create<S = any, A extends Action = Action>(
    reducer: Reducer<S, A>,
    options: StoreOptions<S>
  ): Store<S, A> {
    return new Store(reducer, options);
  }

  getState(): S {
    return this.state;
  }

  dispatch(action: Omit<A, 'timestamp'>) {
    if (this.isDispatching) {
      throw new Error('Reducers may not dispatch actions.');
    }

    try {
      this.isDispatching = true;

      // Add timestamp to action
      const actionWithTimestamp = {
        ...action,
        timestamp: Date.now(),
      } as A;

      // Apply middleware chain
      const dispatch = (finalAction: A) => {
        // Update state
        this.state = this.reducer(this.state, finalAction);

        // Store action in history
        this.actionHistory.push(finalAction);

        // Notify listeners
        this.listeners.forEach(listener => listener(this.state, finalAction));

        // Persist state if needed
        if (this.options.persist) {
          this.saveState();
        }

        // Log to dev tools
        if (this.options.devTools) {
          this.logToDevTools(finalAction);
        }
      };

      // Execute middleware chain - fix the reduceRight call
      const chain = this.middleware.reduceRight(
        (next: (action: A) => void, middleware: (next: (action: A) => void) => (action: A) => void) => {
          return middleware(next);
        },
        dispatch
      );

      chain(actionWithTimestamp);
    } finally {
      this.isDispatching = false;
    }
  }

  subscribe(listener: Listener<S>): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Time-travel debugging
  timeTravel(actionIndex: number) {
    if (actionIndex < 0 || actionIndex >= this.actionHistory.length) {
      throw new Error('Invalid action index');
    }

    // Reset state to initial
    this.state = this.options.initialState;

    // Replay actions up to the specified index
    for (let i = 0; i <= actionIndex; i++) {
      this.state = this.reducer(this.state, this.actionHistory[i] as A);
    }

    // Notify listeners
    this.listeners.forEach(listener => 
      listener(this.state, this.actionHistory[actionIndex])
    );

    // Update dev tools
    if (this.options.devTools) {
      this.logToDevTools(this.actionHistory[actionIndex], true);
    }
  }

  // State persistence
  private saveState() {
    try {
      localStorage.setItem(
        `${this.options.name}_state`,
        JSON.stringify(this.state)
      );
    } catch (error) {
      logger.error('Failed to save state', error);
    }
  }

  private loadState(): S | null {
    try {
      const savedState = localStorage.getItem(`${this.options.name}_state`);
      return savedState ? JSON.parse(savedState) : null;
    } catch (error) {
      logger.error('Failed to load state', error);
      return null;
    }
  }

  // Dev tools integration
  private setupDevTools() {
    if (typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__) {
      (window as any).__REDUX_DEVTOOLS_EXTENSION__.connect({
        name: this.options.name,
      });
    }
  }

  private logToDevTools(action: Action, isTimeTravel = false) {
    if (typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__) {
      (window as any).__REDUX_DEVTOOLS_EXTENSION__.send(
        { ...action, isTimeTravel },
        this.state
      );
    }
  }
}

// Example usage:
/*
// Define action types
const ActionTypes = {
  INCREMENT: 'INCREMENT',
  DECREMENT: 'DECREMENT',
  SET_VALUE: 'SET_VALUE',
} as const;

// Define state interface
interface CounterState {
  count: number;
  lastUpdated: number | null;
}

// Create reducer
const reducer: Reducer<CounterState, Action> = (state, action) => {
  switch (action.type) {
    case ActionTypes.INCREMENT:
      return {
        ...state,
        count: state.count + 1,
        lastUpdated: action.timestamp,
      };
    case ActionTypes.DECREMENT:
      return {
        ...state,
        count: state.count - 1,
        lastUpdated: action.timestamp,
      };
    case ActionTypes.SET_VALUE:
      return {
        ...state,
        count: action.payload,
        lastUpdated: action.timestamp,
      };
    default:
      return state;
  }
};

// Create store
const store = Store.create(reducer, {
  initialState: { count: 0, lastUpdated: null },
  name: 'counter',
  persist: true,
  devTools: true,
});

// Subscribe to changes
const unsubscribe = store.subscribe((state, action) => {
  console.log('State updated:', state);
  console.log('Action:', action);
});

// Dispatch actions
store.dispatch({ type: ActionTypes.INCREMENT });
store.dispatch({ type: ActionTypes.SET_VALUE, payload: 10 });

// Time travel
store.timeTravel(0); // Go back to first action

// Cleanup
unsubscribe();
*/ 
