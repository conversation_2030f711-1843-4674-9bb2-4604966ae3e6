
import { render, RenderOptions } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactElement } from 'react';
import { vi } from 'vitest';

export interface TestUtils {
  render: typeof render;
  userEvent: typeof userEvent;
  mockApi: Mock<PERSON><PERSON>;
  mockStorage: MockStorage;
  mockRouter: MockRouter;
  mockStore: MockStore;
  generateMockData: GenerateMockData;
  waitForCondition: WaitForCondition;
  createTestWrapper: CreateTestWrapper;
}

export interface MockApi {
  get: ReturnType<typeof vi.fn>;
  post: ReturnType<typeof vi.fn>;
  put: ReturnType<typeof vi.fn>;
  delete: ReturnType<typeof vi.fn>;
  reset: () => void;
}

export interface MockStorage {
  getItem: ReturnType<typeof vi.fn>;
  setItem: ReturnType<typeof vi.fn>;
  removeItem: ReturnType<typeof vi.fn>;
  clear: ReturnType<typeof vi.fn>;
  reset: () => void;
}

export interface MockRouter {
  push: ReturnType<typeof vi.fn>;
  replace: ReturnType<typeof vi.fn>;
  back: ReturnType<typeof vi.fn>;
  forward: ReturnType<typeof vi.fn>;
  pathname: string;
  query: Record<string, string>;
  reset: () => void;
}

export interface MockStore {
  dispatch: ReturnType<typeof vi.fn>;
  getState: ReturnType<typeof vi.fn>;
  subscribe: ReturnType<typeof vi.fn>;
  reset: () => void;
}

export type GenerateMockData = <T>(
  template: Partial<T>,
  count?: number
) => T[];

export type WaitForCondition = (
  condition: () => boolean | Promise<boolean>,
  options?: {
    timeout?: number;
    interval?: number;
  }
) => Promise<void>;

export type CreateTestWrapper = (
  options?: RenderOptions
) => (ui: ReactElement) => ReactElement;

// Create mock API
const createMockApi = (): MockApi => {
  const mockApi = {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    reset: () => {
      mockApi.get.mockReset();
      mockApi.post.mockReset();
      mockApi.put.mockReset();
      mockApi.delete.mockReset();
    }
  };

  return mockApi;
};

// Create mock storage
const createMockStorage = (): MockStorage => {
  const storage: Record<string, string> = {};
  const mockStorage = {
    getItem: vi.fn((key: string) => storage[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach(key => {
        delete storage[key];
      });
    }),
    reset: () => {
      mockStorage.getItem.mockReset();
      mockStorage.setItem.mockReset();
      mockStorage.removeItem.mockReset();
      mockStorage.clear.mockReset();
      mockStorage.clear();
    }
  };

  return mockStorage;
};

// Create mock router
const createMockRouter = (): MockRouter => {
  const mockRouter = {
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    pathname: '/',
    query: {},
    reset: () => {
      mockRouter.push.mockReset();
      mockRouter.replace.mockReset();
      mockRouter.back.mockReset();
      mockRouter.forward.mockReset();
      mockRouter.pathname = '/';
      mockRouter.query = {};
    }
  };

  return mockRouter;
};

// Create mock store
const createMockStore = (): MockStore => {
  const mockStore = {
    dispatch: vi.fn(),
    getState: vi.fn(),
    subscribe: vi.fn(),
    reset: () => {
      mockStore.dispatch.mockReset();
      mockStore.getState.mockReset();
      mockStore.subscribe.mockReset();
    }
  };

  return mockStore;
};

// Generate mock data
const generateMockData = <T>(
  template: Partial<T>,
  count: number = 1
): T[] => {
  const generateItem = (index: number): T => {
    const item: any = { ...template };
    Object.keys(item).forEach(key => {
      if (typeof item[key] === 'function') {
        item[key] = item[key](index);
      }
    });
    return item as T;
  };

  return Array.from({ length: count }, (_, index) => generateItem(index));
};

// Wait for condition
const waitForCondition = async (
  condition: () => boolean | Promise<boolean>,
  options: {
    timeout?: number;
    interval?: number;
  } = {}
): Promise<void> => {
  const {
    timeout = 5000,
    interval = 100
  } = options;

  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    const result = await condition();
    if (result) return;

    await new Promise(resolve => setTimeout(resolve, interval));
  }

  throw new Error(`Condition not met after ${timeout}ms`);
};

// Create test wrapper
const createTestWrapper = (options: RenderOptions = {}) => {
  return (ui: ReactElement): ReactElement => {
    return ui;
  };
};

// Create test utilities
export const createTestUtils = (): TestUtils => {
  const mockApi = createMockApi();
  const mockStorage = createMockStorage();
  const mockRouter = createMockRouter();
  const mockStore = createMockStore();

  return {
    render,
    userEvent,
    mockApi,
    mockStorage,
    mockRouter,
    mockStore,
    generateMockData,
    waitForCondition,
    createTestWrapper
  };
};
