type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  data?: any;
  error?: Error;
}

interface LoggerOptions {
  minLevel?: LogLevel;
  enableConsole?: boolean;
  enableRemote?: boolean;
  remoteEndpoint?: string;
  batchSize?: number;
  flushInterval?: number;
}

class Logger {
  private static instance: Logger;
  private options: Required<LoggerOptions>;
  private queue: LogEntry[] = [];
  private isProcessing = false;

  private constructor(options: LoggerOptions = {}) {
    this.options = {
      minLevel: options.minLevel || 'info',
      enableConsole: options.enableConsole ?? true,
      enableRemote: options.enableRemote ?? false,
      remoteEndpoint: options.remoteEndpoint || '/api/logs',
      batchSize: options.batchSize || 10,
      flushInterval: options.flushInterval || 5000,
    };

    if (this.options.enableRemote) {
      this.startPeriodicFlush();
    }
  }

  static getInstance(options?: LoggerOptions): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(options);
    }
    return Logger.instance;
  }

  debug(message: string, data?: any) {
    this.log('debug', message, data);
  }

  info(message: string, data?: any) {
    this.log('info', message, data);
  }

  warn(message: string, data?: any) {
    this.log('warn', message, data);
  }

  error(message: string, error?: Error, data?: any) {
    this.log('error', message, data, error);
  }

  private log(level: LogLevel, message: string, data?: any, error?: Error) {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      data,
      error,
    };

    if (this.options.enableConsole) {
      this.consoleLog(entry);
    }

    if (this.options.enableRemote) {
      this.queue.push(entry);
      if (this.queue.length >= this.options.batchSize) {
        this.flush();
      }
    }
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.options.minLevel);
  }

  private consoleLog(entry: LogEntry) {
    const { level, message, data, error } = entry;
    const timestamp = new Date().toLocaleTimeString();

    const logFn = console[level] || console.log;
    const prefix = `[${timestamp}] ${level.toUpperCase()}:`;

    if (error) {
      logFn(prefix, message, error);
      if (data) logFn('Additional data:', data);
    } else if (data) {
      logFn(prefix, message, data);
    } else {
      logFn(prefix, message);
    }
  }

  private async flush() {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;
    const entries = this.queue.splice(0, this.options.batchSize);

    try {
      if (process.env.NODE_ENV === 'production') {
        await fetch(this.options.remoteEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(entries),
        });
      }
    } catch (error) {
      console.error('Failed to send logs:', error);
      // Put entries back in queue
      this.queue.unshift(...entries);
    } finally {
      this.isProcessing = false;
    }
  }

  private startPeriodicFlush() {
    setInterval(() => this.flush(), this.options.flushInterval);
  }

  // For testing purposes
  getQueueLength() {
    return this.queue.length;
  }
}

export const logger = Logger.getInstance();

// Example usage:
/*
logger.debug('Debug message', { foo: 'bar' });
logger.info('Info message', { user: 'John' });
logger.warn('Warning message', { action: 'delete' });
logger.error('Error message', new Error('Something went wrong'), { context: 'api' });
*/ 