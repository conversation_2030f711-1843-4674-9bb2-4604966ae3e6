import { logger } from './logger';

export interface Route {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  guards?: Guard[];
  middleware?: Middleware[];
  children?: Route[];
}

export interface Guard {
  canActivate: (context: RouteContext) => Promise<boolean> | boolean;
  redirectTo?: string;
}

export interface Middleware {
  process: (context: RouteContext) => Promise<void> | void;
}

export interface RouteContext {
  path: string;
  params: Record<string, string>;
  query: Record<string, string>;
  state: any;
}

export interface RouterOptions {
  routes: Route[];
  defaultPath?: string;
  onRouteChange?: (context: RouteContext) => void;
  onError?: (error: Error) => void;
}

export class Router {
  private static instance: Router;
  private routes: Route[];
  private defaultPath: string;
  private currentContext: RouteContext | null = null;
  private onRouteChange?: (context: RouteContext) => void;
  private onError?: (error: Error) => void;

  private constructor(options: RouterOptions) {
    this.routes = options.routes;
    this.defaultPath = options.defaultPath || '/';
    this.onRouteChange = options.onRouteChange;
    this.onError = options.onError;
    this.setupHistoryListener();
  }

  static getInstance(options: RouterOptions): Router {
    if (!Router.instance) {
      Router.instance = new Router(options);
    }
    return Router.instance;
  }

  // Setup history listener
  private setupHistoryListener(): void {
    window.addEventListener('popstate', () => {
      this.handleRouteChange(window.location.pathname);
    });
  }

  // Handle route change
  private async handleRouteChange(path: string): Promise<void> {
    try {
      const context = this.createRouteContext(path);
      const route = this.findRoute(path);

      if (!route) {
        throw new Error(`Route not found: ${path}`);
      }

      // Check guards
      if (route.guards) {
        for (const guard of route.guards) {
          const canActivate = await guard.canActivate(context);
          if (!canActivate) {
            if (guard.redirectTo) {
              this.navigate(guard.redirectTo);
              return;
            }
            throw new Error(`Route guard failed: ${path}`);
          }
        }
      }

      // Process middleware
      if (route.middleware) {
        for (const middleware of route.middleware) {
          await middleware.process(context);
        }
      }

      this.currentContext = context;
      this.onRouteChange?.(context);
    } catch (error) {
      this.onError?.(error as Error);
      logger.error('Route change failed:', error);
    }
  }

  // Create route context
  private createRouteContext(path: string): RouteContext {
    const url = new URL(path, window.location.origin);
    const params = this.extractParams(path);
    const query = Object.fromEntries(url.searchParams.entries());
    const state = window.history.state || {};

    return {
      path,
      params,
      query,
      state,
    };
  }

  // Extract route parameters
  private extractParams(path: string): Record<string, string> {
    const params: Record<string, string> = {};
    const route = this.findRoute(path);

    if (route) {
      const routeParts = route.path.split('/');
      const pathParts = path.split('/');

      routeParts.forEach((part, index) => {
        if (part.startsWith(':')) {
          const paramName = part.slice(1);
          params[paramName] = pathParts[index];
        }
      });
    }

    return params;
  }

  // Find matching route
  private findRoute(path: string): Route | null {
    const findRouteRecursive = (routes: Route[], path: string): Route | null => {
      for (const route of routes) {
        if (this.isRouteMatch(route, path)) {
          return route;
        }

        if (route.children) {
          const childRoute = findRouteRecursive(route.children, path);
          if (childRoute) {
            return childRoute;
          }
        }
      }
      return null;
    };

    return findRouteRecursive(this.routes, path);
  }

  // Check if route matches path
  private isRouteMatch(route: Route, path: string): boolean {
    const routeParts = route.path.split('/');
    const pathParts = path.split('/');

    if (routeParts.length !== pathParts.length && route.exact) {
      return false;
    }

    return routeParts.every((part, index) => {
      if (part.startsWith(':')) {
        return true;
      }
      return part === pathParts[index];
    });
  }

  // Navigate to a new route
  navigate(path: string, state: any = {}): void {
    window.history.pushState(state, '', path);
    this.handleRouteChange(path);
  }

  // Replace current route
  replace(path: string, state: any = {}): void {
    window.history.replaceState(state, '', path);
    this.handleRouteChange(path);
  }

  // Go back
  back(): void {
    window.history.back();
  }

  // Go forward
  forward(): void {
    window.history.forward();
  }

  // Get current route context
  getCurrentContext(): RouteContext | null {
    return this.currentContext;
  }

  // Get route parameters
  getParams(): Record<string, string> {
    return this.currentContext?.params || {};
  }

  // Get query parameters
  getQuery(): Record<string, string> {
    return this.currentContext?.query || {};
  }

  // Get route state
  getState(): any {
    return this.currentContext?.state || {};
  }
}

// Example usage:
/*
const router = Router.getInstance({
  routes: [
    {
      path: '/',
      component: HomePage,
      guards: [
        {
          canActivate: async (context) => {
            // Check if user is authenticated
            return true;
          },
          redirectTo: '/login',
        },
      ],
      middleware: [
        {
          process: async (context) => {
            // Load initial data
            await loadData();
          },
        },
      ],
      children: [
        {
          path: '/profile/:id',
          component: ProfilePage,
          guards: [
            {
              canActivate: async (context) => {
                // Check if user has access to profile
                return true;
              },
            },
          ],
        },
      ],
    },
    {
      path: '/login',
      component: LoginPage,
    },
  ],
  defaultPath: '/',
  onRouteChange: (context) => {
    console.log('Route changed:', context);
  },
  onError: (error) => {
    console.error('Router error:', error);
  },
});

// Navigate to a new route
router.navigate('/profile/123', { data: 'some data' });

// Get current route parameters
const params = router.getParams();
console.log('User ID:', params.id);

// Get query parameters
const query = router.getQuery();
console.log('Search term:', query.q);

// Get route state
const state = router.getState();
console.log('Route state:', state);
*/ 