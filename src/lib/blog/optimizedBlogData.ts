
import { blogApi, type BlogPost as ApiBlogPost } from '@/lib/api/blog';

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  category: string;
  tags: string[];
  author: {
    name: string;
    bio: string;
    avatar: string;
  };
  publishedAt: string;
  updatedAt: string;
  readTime: string;
  image: string;
  featured?: boolean;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
}

// Transform API blog post to frontend format
const transformBlogPost = (apiPost: ApiBlogPost): BlogPost => {
  return {
    id: apiPost.id,
    title: apiPost.title,
    excerpt: apiPost.excerpt || '',
    content: apiPost.content,
    slug: apiPost.slug,
    category: apiPost.category?.name || 'Uncategorized',
    tags: apiPost.tags || [],
    author: {
      name: apiPost.author?.full_name || apiPost.author?.username || 'Anonymous',
      bio: 'ScriptGenius Team Member',
      avatar: '✍️'
    },
    publishedAt: apiPost.published_at || apiPost.created_at,
    updatedAt: apiPost.updated_at,
    readTime: `${apiPost.read_time_minutes || 5} min read`,
    image: apiPost.featured_image_url || '📝',
    featured: apiPost.featured,
    seo: {
      metaTitle: apiPost.seo_title,
      metaDescription: apiPost.seo_description,
      keywords: apiPost.seo_keywords
    }
  };
};

// Fallback to static data if API fails
import {
  blogPosts as staticBlogPosts,
  getFeaturedPosts as staticGetFeaturedPosts,
  getPostsByCategory as staticGetPostsByCategory,
  getBlogPostBySlug as staticGetBlogPostBySlug,
  getRelatedPosts as staticGetRelatedPosts
} from './blogData';

// API-first functions with static fallback
export const getFeaturedPosts = async (): Promise<BlogPost[]> => {
  try {
    const result = await blogApi.getFeaturedPosts();
    if (result.success && result.data) {
      return result.data.map(transformBlogPost);
    }
  } catch (error) {
    console.warn('Failed to fetch featured posts from API, using static data:', error);
  }
  return staticGetFeaturedPosts();
};

export const getPostsByCategory = async (category: string): Promise<BlogPost[]> => {
  try {
    if (category === 'All') {
      const result = await blogApi.getPublishedPosts();
      if (result.success && result.data) {
        return result.data.map(transformBlogPost);
      }
    } else {
      // For specific categories, we'd need to implement category filtering in the API
      const result = await blogApi.getPublishedPosts();
      if (result.success && result.data) {
        return result.data
          .filter(post => post.category?.name === category)
          .map(transformBlogPost);
      }
    }
  } catch (error) {
    console.warn('Failed to fetch posts by category from API, using static data:', error);
  }
  return staticGetPostsByCategory(category);
};

export const getBlogPostBySlug = async (slug: string): Promise<BlogPost | undefined> => {
  try {
    const result = await blogApi.getPublishedPostBySlug(slug);
    if (result.success && result.data) {
      return transformBlogPost(result.data);
    }
  } catch (error) {
    console.warn('Failed to fetch post by slug from API, using static data:', error);
  }
  return staticGetBlogPostBySlug(slug);
};

export const getRelatedPosts = async (currentPost: BlogPost, limit: number = 3): Promise<BlogPost[]> => {
  try {
    const result = await blogApi.getPublishedPosts();
    if (result.success && result.data) {
      const allPosts = result.data.map(transformBlogPost);
      return allPosts
        .filter(post =>
          post.id !== currentPost.id &&
          (post.category === currentPost.category ||
           post.tags.some(tag => currentPost.tags.includes(tag)))
        )
        .slice(0, limit);
    }
  } catch (error) {
    console.warn('Failed to fetch related posts from API, using static data:', error);
  }
  return staticGetRelatedPosts(currentPost, limit);
};

// For backward compatibility, export a blogPosts array (though it's now async)
export const getAllPosts = async (): Promise<BlogPost[]> => {
  try {
    const result = await blogApi.getPublishedPosts();
    if (result.success && result.data) {
      return result.data.map(transformBlogPost);
    }
  } catch (error) {
    console.warn('Failed to fetch all posts from API, using static data:', error);
  }
  return staticBlogPosts;
};

// Legacy export for backward compatibility
export const blogPosts = staticBlogPosts;
