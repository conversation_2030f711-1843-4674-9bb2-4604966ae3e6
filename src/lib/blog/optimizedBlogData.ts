
export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  slug: string;
  category: string;
  tags: string[];
  author: {
    name: string;
    bio: string;
    avatar: string;
  };
  publishedAt: string;
  updatedAt: string;
  readTime: string;
  image: string;
  featured?: boolean;
  seo: {
    metaTitle?: string;
    metaDescription?: string;
    keywords?: string[];
  };
}

// Import from the main blog data
import { 
  blogPosts as mainBlogPosts, 
  getFeaturedPosts as mainGetFeaturedPosts, 
  getPostsByCategory as mainGetPostsByCategory,
  getBlogPostBySlug as mainGetBlogPostBySlug,
  getRelatedPosts as mainGetRelatedPosts
} from './blogData';

export const blogPosts = mainBlogPosts;
export const getFeaturedPosts = mainGetFeaturedPosts;
export const getPostsByCategory = mainGetPostsByCategory;
export const getBlogPostBySlug = mainGetBlogPostBySlug;
export const getRelatedPosts = mainGetRelatedPosts;
