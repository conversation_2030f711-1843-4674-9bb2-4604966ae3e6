
import { performanceMonitoring as performanceMonitoringModule } from './monitoring/performanceMonitoring';
import { errorReporting } from './monitoring/errorReporting';
import { bundleMonitoring } from './monitoring/bundleMonitoring';

class PerformanceManager {
  private static instance: PerformanceManager;
  private isInitialized = false;

  static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  initialize() {
    if (this.isInitialized) return;

    try {
      // Initialize monitoring systems
      performanceMonitoringModule.initialize();
      errorReporting.setEnabled(true);

      // Track custom metrics
      this.trackCustomMetric('app_initialized', performance.now());
      
      // Analyze bundle size in development
      if (import.meta.env.DEV) {
        setTimeout(() => {
          bundleMonitoring.analyzeBundleSize();
        }, 2000);
      }

      this.isInitialized = true;
      
      if (import.meta.env.DEV) {
        console.info('🔧 Performance monitoring initialized');
      }
    } catch (error) {
      console.warn('Performance monitoring initialization failed:', error);
    }
  }

  trackCustomMetric(name: string, value: number, tags?: Record<string, string>) {
    performanceMonitoringModule.trackMetric(name, value, 'ms', tags);
  }

  trackError(error: Error, context?: Record<string, any>) {
    errorReporting.captureError(error, { tags: context });
  }

  setUser(userId: string, organizationId?: string) {
    errorReporting.setUser(userId, organizationId);
  }

  cleanup() {
    // Cleanup logic if needed
  }

  // Legacy compatibility methods
  getMetrics() {
    return performanceMonitoringModule.getMetrics();
  }

  getReport() {
    const metrics = performanceMonitoringModule.getMetrics();
    return {
      metrics: metrics.reduce((acc, metric) => {
        if (!acc[metric.name]) acc[metric.name] = [];
        acc[metric.name].push(metric.value);
        return acc;
      }, {} as Record<string, number[]>)
    };
  }
}

export const performanceMonitoring = PerformanceManager.getInstance();
