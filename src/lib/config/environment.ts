/**
 * Environment Configuration and Validation
 * 
 * This module provides type-safe access to environment variables
 * with validation and fallbacks for development/production environments.
 */

interface EnvironmentConfig {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
  };
  
  // Application Configuration
  app: {
    version: string;
    apiUrl: string;
    environment: 'development' | 'production' | 'test';
  };
  
  // Feature Flags
  features: {
    performanceMonitoring: boolean;
    errorReporting: boolean;
    analytics: boolean;
  };
  
  // Rate Limiting
  rateLimiting: {
    requestsPerMinute: number;
    burstSize: number;
  };
  
  // Security
  security: {
    enforceHttps: boolean;
    csrfProtection: boolean;
    contentSecurityPolicy: boolean;
  };
  
  // Monitoring
  monitoring: {
    errorEndpoint?: string;
    metricsEndpoint?: string;
    analyticsId?: string;
  };
}

/**
 * Validates and parses environment variables
 */
function parseEnvironmentConfig(): EnvironmentConfig {
  // Required environment variables
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;
  
  // Validate required variables
  if (!supabaseUrl) {
    throw new Error(
      'VITE_SUPABASE_URL is required. Please check your environment configuration.'
    );
  }
  
  if (!supabaseAnonKey) {
    throw new Error(
      'VITE_SUPABASE_ANON_KEY is required. Please check your environment configuration.'
    );
  }
  
  // Validate URL format
  try {
    new URL(supabaseUrl);
  } catch {
    throw new Error(
      `Invalid VITE_SUPABASE_URL format: ${supabaseUrl}. Please provide a valid URL.`
    );
  }
  
  // Determine environment
  const isDevelopment = import.meta.env.DEV;
  const isProduction = import.meta.env.PROD;
  const isTest = import.meta.env.MODE === 'test';
  
  let environment: 'development' | 'production' | 'test';
  if (isTest) {
    environment = 'test';
  } else if (isProduction) {
    environment = 'production';
  } else {
    environment = 'development';
  }
  
  // Parse optional configuration with defaults
  const config: EnvironmentConfig = {
    supabase: {
      url: supabaseUrl,
      anonKey: supabaseAnonKey,
    },
    
    app: {
      version: import.meta.env.VITE_APP_VERSION || '2.1.0',
      apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8080',
      environment,
    },
    
    features: {
      performanceMonitoring: parseBooleanEnv('VITE_ENABLE_PERFORMANCE_MONITORING', isDevelopment),
      errorReporting: parseBooleanEnv('VITE_ENABLE_ERROR_REPORTING', isProduction),
      analytics: parseBooleanEnv('VITE_ENABLE_ANALYTICS', false),
    },
    
    rateLimiting: {
      requestsPerMinute: parseIntEnv('VITE_RATE_LIMIT_REQUESTS_PER_MINUTE', 60),
      burstSize: parseIntEnv('VITE_RATE_LIMIT_BURST_SIZE', 10),
    },
    
    security: {
      enforceHttps: parseBooleanEnv('VITE_ENFORCE_HTTPS', isProduction),
      csrfProtection: parseBooleanEnv('VITE_CSRF_PROTECTION', isProduction),
      contentSecurityPolicy: parseBooleanEnv('VITE_CONTENT_SECURITY_POLICY', isProduction),
    },
    
    monitoring: {
      errorEndpoint: import.meta.env.VITE_ERROR_ENDPOINT,
      metricsEndpoint: import.meta.env.VITE_METRICS_ENDPOINT,
      analyticsId: import.meta.env.VITE_ANALYTICS_ID,
    },
  };
  
  // Production-specific validations
  if (isProduction) {
    validateProductionConfig(config);
  }
  
  return config;
}

/**
 * Parse boolean environment variable with fallback
 */
function parseBooleanEnv(key: string, defaultValue: boolean): boolean {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1';
}

/**
 * Parse integer environment variable with fallback
 */
function parseIntEnv(key: string, defaultValue: number): number {
  const value = import.meta.env[key];
  if (value === undefined) return defaultValue;
  const parsed = parseInt(value, 10);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Validate production-specific configuration
 */
function validateProductionConfig(config: EnvironmentConfig): void {
  const warnings: string[] = [];
  const errors: string[] = [];
  
  // Check HTTPS enforcement
  if (!config.supabase.url.startsWith('https://')) {
    errors.push('Supabase URL must use HTTPS in production');
  }
  
  if (!config.security.enforceHttps) {
    warnings.push('HTTPS enforcement is disabled in production');
  }
  
  // Check localhost usage
  if (config.supabase.url.includes('localhost')) {
    errors.push('Cannot use localhost URLs in production');
  }
  
  if (config.app.apiUrl.includes('localhost')) {
    warnings.push('API URL uses localhost in production');
  }
  
  // Check monitoring configuration
  if (!config.monitoring.errorEndpoint && config.features.errorReporting) {
    warnings.push('Error reporting enabled but no error endpoint configured');
  }
  
  // Log warnings
  warnings.forEach(warning => {
    console.warn(`⚠️ Production Warning: ${warning}`);
  });
  
  // Throw errors
  if (errors.length > 0) {
    throw new Error(
      `Production configuration errors:\n${errors.map(e => `- ${e}`).join('\n')}`
    );
  }
}

/**
 * Get validated environment configuration
 */
export const env = parseEnvironmentConfig();

/**
 * Environment utilities
 */
export const envUtils = {
  isDevelopment: env.app.environment === 'development',
  isProduction: env.app.environment === 'production',
  isTest: env.app.environment === 'test',
  
  // Feature flags
  isFeatureEnabled: (feature: keyof typeof env.features): boolean => {
    return env.features[feature];
  },
  
  // Security checks
  shouldEnforceHttps: (): boolean => {
    return env.security.enforceHttps;
  },
  
  // Rate limiting
  getRateLimitConfig: () => ({
    requestsPerMinute: env.rateLimiting.requestsPerMinute,
    burstSize: env.rateLimiting.burstSize,
  }),
  
  // Monitoring
  getMonitoringConfig: () => ({
    errorEndpoint: env.monitoring.errorEndpoint,
    metricsEndpoint: env.monitoring.metricsEndpoint,
    analyticsId: env.monitoring.analyticsId,
  }),
};

/**
 * Development-only configuration logging
 */
if (envUtils.isDevelopment) {
  console.log('🔧 Environment Configuration:', {
    environment: env.app.environment,
    version: env.app.version,
    features: env.features,
    security: env.security,
    supabaseUrl: env.supabase.url,
  });
}

export default env;
