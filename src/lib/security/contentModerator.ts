
import { InputSanitizer } from './sanitizers';

export interface ModerationResult {
  isAllowed: boolean;
  confidence: number;
  flaggedContent: string[];
  suggestedActions: string[];
  metadata?: Record<string, any>;
}

export interface ModerationOptions {
  enableProfanityFilter?: boolean;
  enableSpamDetection?: boolean;
  enableToxicityDetection?: boolean;
  strictMode?: boolean;
  customRules?: string[];
}

export class ContentModerator {
  private static readonly PROFANITY_PATTERNS = [
    // Common profanity patterns (simplified for example)
    /\b(damn|hell|crap)\b/gi,
    /\b(stupid|idiot|moron)\b/gi
  ];

  private static readonly SPAM_INDICATORS = [
    /(.)\1{10,}/g, // Repeated characters
    /^[A-Z\s!]{20,}$/g, // All caps with exclamation
    /(click here|buy now|limited time|act now)/gi,
    /(\$\d+|\d+%\s*off)/g // Price indicators
  ];

  private static readonly TOXIC_PATTERNS = [
    /\b(hate|kill|die|stupid)\s+(you|u|yourself)\b/gi,
    /\b(go\s+)?(kill|kys|die)\s+(yourself|urself|u)\b/gi,
    /\b(shut\s+up|stfu)\b/gi
  ];

  static moderateContent(
    content: string,
    options: ModerationOptions = {}
  ): ModerationResult {
    const {
      enableProfanityFilter = true,
      enableSpamDetection = true,
      enableToxicityDetection = true,
      strictMode = false,
      customRules = []
    } = options;

    const flaggedContent: string[] = [];
    const suggestedActions: string[] = [];
    let confidence = 1.0;

    // Sanitize input first
    const sanitizedContent = InputSanitizer.sanitizeText(content);

    // Profanity detection
    if (enableProfanityFilter) {
      const profanityMatches = this.detectProfanity(sanitizedContent);
      if (profanityMatches.length > 0) {
        flaggedContent.push(...profanityMatches);
        suggestedActions.push('Consider removing or replacing inappropriate language');
        confidence -= 0.2;
      }
    }

    // Spam detection
    if (enableSpamDetection) {
      const spamScore = this.detectSpam(sanitizedContent);
      if (spamScore > 0.5) {
        flaggedContent.push('Potential spam content detected');
        suggestedActions.push('Review for spam-like characteristics');
        confidence -= 0.3;
      }
    }

    // Toxicity detection
    if (enableToxicityDetection) {
      const toxicMatches = this.detectToxicity(sanitizedContent);
      if (toxicMatches.length > 0) {
        flaggedContent.push(...toxicMatches);
        suggestedActions.push('Content contains potentially toxic language');
        confidence -= 0.4;
      }
    }

    // Custom rules
    if (customRules.length > 0) {
      const customMatches = this.checkCustomRules(sanitizedContent, customRules);
      if (customMatches.length > 0) {
        flaggedContent.push(...customMatches);
        suggestedActions.push('Content violates custom moderation rules');
        confidence -= 0.2;
      }
    }

    const isAllowed = strictMode 
      ? flaggedContent.length === 0 
      : confidence > 0.3;

    return {
      isAllowed,
      confidence: Math.max(0, confidence),
      flaggedContent,
      suggestedActions,
      metadata: {
        originalLength: content.length,
        sanitizedLength: sanitizedContent.length,
        strictMode,
        timestamp: new Date().toISOString()
      }
    };
  }

  private static detectProfanity(content: string): string[] {
    const matches: string[] = [];
    
    this.PROFANITY_PATTERNS.forEach(pattern => {
      const found = content.match(pattern);
      if (found) {
        matches.push(...found.map(match => `Profanity detected: "${match}"`));
      }
    });

    return matches;
  }

  private static detectSpam(content: string): number {
    let spamScore = 0;
    
    this.SPAM_INDICATORS.forEach(pattern => {
      if (pattern.test(content)) {
        spamScore += 0.25;
      }
    });

    // Check for excessive repetition
    const words = content.toLowerCase().split(/\s+/);
    const wordCounts = new Map<string, number>();
    
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    const maxWordCount = Math.max(...wordCounts.values());
    if (maxWordCount > words.length * 0.3) {
      spamScore += 0.3;
    }

    return Math.min(1, spamScore);
  }

  private static detectToxicity(content: string): string[] {
    const matches: string[] = [];
    
    this.TOXIC_PATTERNS.forEach(pattern => {
      const found = content.match(pattern);
      if (found) {
        matches.push(...found.map(match => `Toxic language detected: "${match}"`));
      }
    });

    return matches;
  }

  private static checkCustomRules(content: string, rules: string[]): string[] {
    const matches: string[] = [];
    
    rules.forEach(rule => {
      try {
        const pattern = new RegExp(rule, 'gi');
        if (pattern.test(content)) {
          matches.push(`Custom rule violation: "${rule}"`);
        }
      } catch (error) {
        console.warn('Invalid custom moderation rule:', rule, error);
      }
    });

    return matches;
  }

  static createModerationSummary(results: ModerationResult[]): {
    totalModerated: number;
    allowedCount: number;
    flaggedCount: number;
    commonIssues: string[];
    averageConfidence: number;
  } {
    const totalModerated = results.length;
    const allowedCount = results.filter(r => r.isAllowed).length;
    const flaggedCount = totalModerated - allowedCount;
    
    const allFlags = results.flatMap(r => r.flaggedContent);
    const flagCounts = new Map<string, number>();
    
    allFlags.forEach(flag => {
      const key = flag.split(':')[0]; // Get the type of flag
      flagCounts.set(key, (flagCounts.get(key) || 0) + 1);
    });
    
    const commonIssues = Array.from(flagCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([issue]) => issue);
    
    const averageConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / totalModerated;

    return {
      totalModerated,
      allowedCount,
      flaggedCount,
      commonIssues,
      averageConfidence
    };
  }
}
