
// Import all security middleware functionality from modular files
import { SecurityCore } from './middleware/core';
import { SecurityValidation } from './middleware/validation';
import { SecurityFileUpload } from './middleware/fileUpload';
import { SecurityOperations } from './middleware/operations';
import { SecurityLogging } from './middleware/logging';
import { securityHelpers } from './middleware/helpers';

// Re-export all security middleware functionality for external use
export { SecurityCore, SecurityValidation, SecurityFileUpload, SecurityOperations, SecurityLogging, securityHelpers };

// Main SecurityMiddleware class that combines all functionality for backward compatibility
export class SecurityMiddleware {
  // Core functionality
  static applyRateLimit = SecurityCore.applyRateLimit;
  static validateCSRFToken = SecurityCore.validateCSRFToken;
  static handleSecurityError = SecurityCore.handleSecurityError;

  // Validation functionality
  static sanitizeAndValidate = SecurityValidation.sanitizeAndValidate;

  // File upload functionality
  static validateFileUpload = SecurityFileUpload.validateFileUpload;
  static validateFileUploadSecurity = SecurityFileUpload.validateFileUploadSecurity;

  // Operations functionality
  static secureOperation = SecurityOperations.secureOperation;

  // Logging functionality
  static logSecurityEvent = SecurityLogging.logSecurityEvent;
}
