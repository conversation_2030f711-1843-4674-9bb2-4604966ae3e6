
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export class CSRFProtection {
  private static tokens = new Map<string, string>();

  static async generateToken(): Promise<string> {
    const { data } = await supabase.auth.getSession();
    if (!data?.session?.user) {
      throw new Error('User must be authenticated to generate CSRF token');
    }

    const token = crypto.randomUUID();
    const sessionId = data.session.user.id;
    
    this.tokens.set(sessionId, token);
    
    // Store in session storage for client-side access
    sessionStorage.setItem('csrf_token', token);
    
    return token;
  }

  static async validateToken(token: string): Promise<boolean> {
    const { data } = await supabase.auth.getSession();
    if (!data?.session?.user) {
      return false;
    }

    const sessionId = data.session.user.id;
    const storedToken = this.tokens.get(sessionId);
    
    return storedToken === token;
  }

  static getTokenFromStorage(): string | null {
    return sessionStorage.getItem('csrf_token');
  }

  static async clearToken(): Promise<void> {
    const { data } = await supabase.auth.getSession();
    if (data?.session?.user) {
      this.tokens.delete(data.session.user.id);
    }
    sessionStorage.removeItem('csrf_token');
  }
}

// Custom hook for CSRF protection
export const useCSRFProtection = () => {
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    const initializeToken = async () => {
      try {
        const newToken = await CSRFProtection.generateToken();
        setToken(newToken);
      } catch (error) {
        console.warn('Failed to initialize CSRF token:', error);
      }
    };

    initializeToken();

    // Cleanup on unmount
    return () => {
      CSRFProtection.clearToken();
    };
  }, []);

  const validateAndRefreshToken = async (submittedToken: string): Promise<boolean> => {
    const isValid = await CSRFProtection.validateToken(submittedToken);
    if (isValid) {
      // Generate new token after successful validation
      const newToken = await CSRFProtection.generateToken();
      setToken(newToken);
    }
    return isValid;
  };

  return { token, validateAndRefreshToken };
};
