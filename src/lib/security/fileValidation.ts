
export interface FileValidationOptions {
  maxSize?: number;
  allowedTypes?: string[];
  allowedExtensions?: string[];
  scanForMalware?: boolean;
  checkDimensions?: boolean;
  maxDimensions?: { width: number; height: number };
}

export interface FileValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
  sanitizedName?: string;
}

export class FileValidator {
  // Dangerous file extensions that should never be allowed
  private static readonly DANGEROUS_EXTENSIONS = [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.app', '.deb', '.pkg', '.dmg', '.sh', '.ps1', '.php', '.asp', '.jsp'
  ];

  // MIME types that are commonly spoofed
  private static readonly SUSPICIOUS_MIMES = [
    'application/x-msdownload',
    'application/x-msdos-program',
    'application/x-executable',
    'application/x-winexe'
  ];

  static async validateFile(
    file: File,
    options: FileValidationOptions = {}
  ): Promise<FileValidationResult> {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain', 'text/csv',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ],
      allowedExtensions = [
        '.jpg', '.jpeg', '.png', '.gif', '.webp',
        '.pdf', '.txt', '.csv', '.doc', '.docx'
      ],
      scanForMalware = true,
      checkDimensions = true,
      maxDimensions = { width: 4096, height: 4096 }
    } = options;

    const warnings: string[] = [];

    // 1. File size validation
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size exceeds maximum allowed size of ${Math.round(maxSize / 1024 / 1024)}MB`
      };
    }

    // 2. File extension validation
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    
    if (this.DANGEROUS_EXTENSIONS.includes(extension)) {
      return {
        valid: false,
        error: `File type ${extension} is not allowed for security reasons`
      };
    }

    if (!allowedExtensions.includes(extension)) {
      return {
        valid: false,
        error: `File extension ${extension} is not allowed`
      };
    }

    // 3. MIME type validation
    if (this.SUSPICIOUS_MIMES.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed for security reasons`
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not allowed`
      };
    }

    // 4. Filename sanitization and validation
    const sanitizedName = this.sanitizeFileName(file.name);
    if (!sanitizedName || sanitizedName !== file.name) {
      warnings.push('File name contains special characters and has been sanitized');
    }

    // 5. Content validation for images
    if (file.type.startsWith('image/') && checkDimensions) {
      try {
        const dimensions = await this.getImageDimensions(file);
        if (dimensions.width > maxDimensions.width || dimensions.height > maxDimensions.height) {
          return {
            valid: false,
            error: `Image dimensions exceed maximum allowed size of ${maxDimensions.width}x${maxDimensions.height}`
          };
        }
      } catch (error) {
        return {
          valid: false,
          error: 'Unable to validate image file'
        };
      }
    }

    // 6. Basic malware scanning (file signature validation)
    if (scanForMalware) {
      const hasSuspiciousSignature = await this.checkFileSignature(file);
      if (hasSuspiciousSignature) {
        return {
          valid: false,
          error: 'File appears to contain suspicious content'
        };
      }
    }

    // 7. Additional PDF validation
    if (file.type === 'application/pdf') {
      const isPdfValid = await this.validatePDF(file);
      if (!isPdfValid) {
        return {
          valid: false,
          error: 'PDF file appears to be corrupted or contains suspicious content'
        };
      }
    }

    return {
      valid: true,
      warnings: warnings.length > 0 ? warnings : undefined,
      sanitizedName
    };
  }

  private static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/\.{2,}/g, '.')
      .replace(/^\.+|\.+$/g, '')
      .slice(0, 255);
  }

  private static async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({ width: img.width, height: img.height });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Invalid image file'));
      };

      img.src = url;
    });
  }

  private static async checkFileSignature(file: File): Promise<boolean> {
    // Read first few bytes to check file signature
    const buffer = await file.slice(0, 8).arrayBuffer();
    const bytes = new Uint8Array(buffer);

    // Check for executable file signatures
    const executableSignatures = [
      [0x4D, 0x5A], // PE executable (Windows)
      [0x7F, 0x45, 0x4C, 0x46], // ELF executable (Linux)
      [0xFE, 0xED, 0xFA, 0xCE], // Mach-O executable (macOS)
      [0xCA, 0xFE, 0xBA, 0xBE], // Java class file
    ];

    return executableSignatures.some(signature => 
      signature.every((byte, index) => bytes[index] === byte)
    );
  }

  private static async validatePDF(file: File): Promise<boolean> {
    try {
      // Check PDF header
      const buffer = await file.slice(0, 8).arrayBuffer();
      const header = new TextDecoder().decode(buffer);
      
      if (!header.startsWith('%PDF-')) {
        return false;
      }

      // Basic structure validation - look for suspicious content
      const fullBuffer = await file.arrayBuffer();
      const content = new TextDecoder().decode(fullBuffer);
      
      // Check for suspicious PDF content
      const suspiciousPatterns = [
        /\/JavaScript/i,
        /\/JS/i,
        /\/OpenAction/i,
        /\/Launch/i,
        /\/EmbeddedFile/i
      ];

      return !suspiciousPatterns.some(pattern => pattern.test(content));
    } catch {
      return false;
    }
  }

  static validateMultipleFiles(
    files: FileList | File[],
    options: FileValidationOptions = {}
  ): Promise<Array<FileValidationResult & { file: File }>> {
    const fileArray = Array.from(files);
    
    return Promise.all(
      fileArray.map(async (file) => {
        const result = await this.validateFile(file, options);
        return { ...result, file };
      })
    );
  }
}
