
export class SecurityLogging {
  static logSecurityEvent(event: {
    type: 'rate_limit' | 'validation_error' | 'unauthorized_access' | 'suspicious_activity' | 'csrf_violation';
    userId?: string;
    details: string;
    metadata?: Record<string, any>;
  }): void {
    const logEntry = {
      timestamp: new Date().toISOString(),
      type: event.type,
      userId: event.userId ?? 'unknown',
      details: event.details,
      metadata: event.metadata ?? {},
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown'
    };

    // In production, send to security monitoring service
    console.warn('🔒 Security Event:', logEntry);
    
    // Send to analytics or monitoring service
    if (typeof window !== 'undefined' && 'gtag' in window) {
      (window as any).gtag('event', 'security_event', {
        event_category: 'Security',
        event_label: event.type,
        custom_parameter_1: event.details
      });
    }
  }
}
