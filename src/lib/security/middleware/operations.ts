
import { SecurityCore } from './core';
import { RATE_LIMITS } from '../rateLimiter';
import type { ApiResponse } from '../../api/types';

export class SecurityOperations {
  static async secureOperation<T>(
    operation: () => Promise<ApiResponse<T>>,
    operationType: keyof typeof RATE_LIMITS,
    options: {
      requireAuth?: boolean;
      validateCSRF?: boolean;
      csrfToken?: string;
    } = {}
  ): Promise<ApiResponse<T>> {
    try {
      await SecurityCore.applyRateLimit(operationType);

      // CSRF validation if required
      if (options.validateCSRF && options.csrfToken) {
        const isValidCSRF = await SecurityCore.validateCSRFToken(options.csrfToken);
        if (!isValidCSRF) {
          throw new Error('Invalid CSRF token');
        }
      }

      return await operation();
    } catch (error) {
      return SecurityCore.handleSecurityError(error);
    }
  }
}
