
import { z } from 'zod';
import { InputSanitizer } from '../inputSanitizer';
import { validateInput } from '../../validation/base-schemas';

export class SecurityValidation {
  static sanitizeAndValidate<T>(
    data: unknown,
    schema: z.ZodSchema<T>
  ): { success: true; data: T } | { success: false; errors: Record<string, string> } {
    const sanitized = InputSanitizer.sanitizeProductionData(data);
    return validateInput(schema, sanitized);
  }
}
