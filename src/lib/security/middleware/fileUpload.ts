
import { FileValidator, FileValidationOptions } from '../fileValidation';
import { SecurityLogging } from './logging';

export class SecurityFileUpload {
  static async validateFileUpload(
    file: File,
    options: FileValidationOptions = {}
  ): Promise<{ valid: boolean; error?: string; sanitizedName?: string }> {
    const result = await FileValidator.validateFile(file, options);
    return {
      valid: result.valid,
      error: result.error,
      sanitizedName: result.sanitizedName
    };
  }

  static validateFileUploadSecurity(
    file: File,
    options: FileValidationOptions & {
      requireImageOptimization?: boolean;
      allowExecutables?: boolean;
    } = {}
  ): Promise<{ valid: boolean; error?: string; warnings?: string[] }> {
    const enhancedOptions = {
      scanForMalware: true,
      checkDimensions: true,
      ...options
    };

    // Block executables by default
    if (!options.allowExecutables) {
      const dangerousExts = ['.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js'];
      const fileExt = '.' + file.name.split('.').pop()?.toLowerCase();
      
      if (dangerousExts.includes(fileExt)) {
        SecurityLogging.logSecurityEvent({
          type: 'suspicious_activity',
          details: `Attempted upload of executable file: ${file.name}`,
          metadata: { fileType: file.type, fileName: file.name }
        });
        
        return Promise.resolve({
          valid: false,
          error: 'Executable files are not allowed for security reasons'
        });
      }
    }

    return FileValidator.validateFile(file, enhancedOptions);
  }
}
