
import { z } from 'zod';
import { SecurityCore } from './core';
import { SecurityValidation } from './validation';
import { SecurityLogging } from './logging';
import { SecurityFileUpload } from './fileUpload';
import { SecurityOperations } from './operations';
import { RATE_LIMITS } from '../rateLimiter';
import { FileValidationOptions } from '../fileValidation';
import type { ApiResponse } from '../../api/types';

export const securityHelpers = {
  withSecurity: <T extends (...args: any[]) => Promise<ApiResponse<any>>>(
    fn: T,
    operationType: keyof typeof RATE_LIMITS,
    options: {
      requireAuth?: boolean;
      validateCSRF?: boolean;
    } = {}
  ) => {
    return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
      // Extract CSRF token from arguments if present
      const csrfToken = args.find(arg => 
        typeof arg === 'object' && arg !== null && 'csrf_token' in arg
      )?.csrf_token;

      return SecurityOperations.secureOperation(
        () => fn(...args),
        operationType,
        { ...options, csrfToken }
      ) as ReturnType<T>;
    };
  },

  validateInput: <T>(data: unknown, schema: z.ZodSchema<T>) => {
    const result = SecurityValidation.sanitizeAndValidate(data, schema);
    if (!result.success && 'errors' in result) {
      SecurityLogging.logSecurityEvent({
        type: 'validation_error',
        details: `Validation failed: ${Object.values(result.errors).join(', ')}`,
        metadata: { inputData: data }
      });
    }
    return result;
  },

  logEvent: SecurityLogging.logSecurityEvent,
  
  validateFile: SecurityFileUpload.validateFileUploadSecurity
};
