
import { z } from 'zod';
import { RateLimitError, checkUserRateLimit, RATE_LIMITS } from '../rateLimiter';
import { CSRFProtection } from '../csrfProtection';
import type { ApiResponse } from '../../api/types';

export class SecurityCore {
  static async applyRateLimit(operationType: keyof typeof RATE_LIMITS): Promise<void> {
    const config = RATE_LIMITS[operationType];
    const result = await checkUserRateLimit(config);

    if (!result.allowed) {
      throw new RateLimitError(
        result.remaining,
        result.resetTime,
        `Rate limit exceeded for ${operationType}. Try again later.`
      );
    }
  }

  static async validateCSRFToken(token: string): Promise<boolean> {
    return CSRFProtection.validateToken(token);
  }

  static handleSecurityError(error: unknown): ApiResponse<any> {
    console.error('Security error:', error);

    if (error instanceof RateLimitError) {
      return {
        success: false,
        data: null,
        error: `Rate limit exceeded. Try again after ${new Date(error.resetTime).toLocaleString()}`,
      };
    }

    if (error instanceof z.ZodError) {
      const validationErrors = error.errors.map(err =>
        `${err.path.join('.')}: ${err.message}`
      ).join(', ');

      return {
        success: false,
        data: null,
        error: `Validation failed: ${validationErrors}`,
      };
    }

    return {
      success: false,
      data: null,
      error: 'Operation failed due to security restrictions',
    };
  }
}
