
import { supabase } from '@/integrations/supabase/client';
import { env } from '@/lib/config/environment';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  burstSize?: number; // Maximum burst requests
  keyGenerator?: (identifier: string) => string;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  burstCount: number;
  lastRequest: number;
}

class RateLimiter {
  private cache = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.resetTime) {
        this.cache.delete(key);
      }
    }
  }

  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number; retryAfter?: number }> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier;
    const now = Date.now();
    const resetTime = now + config.windowMs;
    const burstSize = config.burstSize || 5;

    let entry = this.cache.get(key);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime,
        burstCount: 1,
        lastRequest: now,
      };
      this.cache.set(key, entry);
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime,
      };
    }

    // Check burst limit (requests within 1 second)
    const timeSinceLastRequest = now - entry.lastRequest;
    if (timeSinceLastRequest < 1000) {
      entry.burstCount++;
      if (entry.burstCount > burstSize) {
        return {
          allowed: false,
          remaining: config.maxRequests - entry.count,
          resetTime: entry.resetTime,
          retryAfter: 1000 - timeSinceLastRequest, // Wait until burst window resets
        };
      }
    } else {
      entry.burstCount = 1; // Reset burst count
    }

    // Check rate limit
    if (entry.count >= config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: entry.resetTime - now,
      };
    }

    // Allow the request
    entry.count++;
    entry.lastRequest = now;
    this.cache.set(key, entry);

    return {
      allowed: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime,
    };
  }

  // Get current user identifier for rate limiting
  async getUserIdentifier(): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.id || 'anonymous';
  }

  destroy() {
    clearInterval(this.cleanupInterval);
    this.cache.clear();
  }
}

// Rate limit configurations for different operations
export const RATE_LIMITS = {
  // General API operations
  API_GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
  },
  
  // Create operations (more restrictive)
  CREATE_OPERATIONS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 creates per minute
  },
  
  // File upload operations
  FILE_UPLOAD: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Search operations
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 searches per minute
  },
  
  // Authentication operations
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 auth attempts per 15 minutes
  },
  
  // Export operations (very restrictive)
  EXPORT: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5, // 5 exports per hour
  },
} as const;

// Singleton instance
export const rateLimiter = new RateLimiter();

// Helper function to check rate limits with automatic user identification
export async function checkUserRateLimit(config: RateLimitConfig): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: number;
}> {
  const identifier = await rateLimiter.getUserIdentifier();
  return rateLimiter.checkRateLimit(identifier, config);
}

// Rate limit error class
export class RateLimitError extends Error {
  constructor(
    public remaining: number,
    public resetTime: number,
    message = 'Rate limit exceeded'
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// Higher-order function to wrap API calls with rate limiting
export function withRateLimit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config: RateLimitConfig
): T {
  return (async (...args: Parameters<T>) => {
    const result = await checkUserRateLimit(config);

    if (!result.allowed) {
      throw new RateLimitError(
        result.remaining,
        result.resetTime,
        `Rate limit exceeded. Try again after ${new Date(result.resetTime).toLocaleString()}`
      );
    }

    return fn(...args);
  }) as T;
}

/**
 * Predefined rate limiting configurations for different API endpoints
 */
export const rateLimitConfigs = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    burstSize: 3,
  },

  // Search endpoints - moderate limits
  search: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    burstSize: 5,
  },

  // File upload endpoints - very strict limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,
    burstSize: 2,
  },

  // AI endpoints - strict limits due to cost
  ai: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20,
    burstSize: 3,
  },

  // General API endpoints - standard limits
  api: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: env.rateLimiting.requestsPerMinute,
    burstSize: env.rateLimiting.burstSize,
  },

  // Real-time subscriptions - moderate limits
  realtime: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    burstSize: 10,
  },
} as const;

/**
 * Rate limiting middleware for fetch requests
 */
export function createRateLimitedFetch(
  config: RateLimitConfig
): typeof fetch {
  return async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    const result = await checkUserRateLimit(config);

    if (!result.allowed) {
      // Return a rate limit response
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          retryAfter: result.retryAfter,
          remaining: result.remaining,
          resetTime: result.resetTime,
        }),
        {
          status: 429,
          statusText: 'Too Many Requests',
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': Math.ceil((result.retryAfter || 0) / 1000).toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
          },
        }
      );
    }

    // Execute the actual fetch
    return fetch(input, init);
  };
}
