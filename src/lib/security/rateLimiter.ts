
import { supabase } from '@/integrations/supabase/client';

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (identifier: string) => string;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

class RateLimiter {
  private cache = new Map<string, RateLimitEntry>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.resetTime) {
        this.cache.delete(key);
      }
    }
  }

  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier;
    const now = Date.now();
    const resetTime = now + config.windowMs;

    let entry = this.cache.get(key);

    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime,
      };
      this.cache.set(key, entry);
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime,
      };
    }

    if (entry.count >= config.maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.resetTime,
      };
    }

    entry.count++;
    this.cache.set(key, entry);

    return {
      allowed: true,
      remaining: config.maxRequests - entry.count,
      resetTime: entry.resetTime,
    };
  }

  // Get current user identifier for rate limiting
  async getUserIdentifier(): Promise<string> {
    const { data: { user } } = await supabase.auth.getUser();
    return user?.id || 'anonymous';
  }

  destroy() {
    clearInterval(this.cleanupInterval);
    this.cache.clear();
  }
}

// Rate limit configurations for different operations
export const RATE_LIMITS = {
  // General API operations
  API_GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000, // 1000 requests per 15 minutes
  },
  
  // Create operations (more restrictive)
  CREATE_OPERATIONS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 creates per minute
  },
  
  // File upload operations
  FILE_UPLOAD: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Search operations
  SEARCH: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 searches per minute
  },
  
  // Authentication operations
  AUTH: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10, // 10 auth attempts per 15 minutes
  },
  
  // Export operations (very restrictive)
  EXPORT: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5, // 5 exports per hour
  },
} as const;

// Singleton instance
export const rateLimiter = new RateLimiter();

// Helper function to check rate limits with automatic user identification
export async function checkUserRateLimit(config: RateLimitConfig): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: number;
}> {
  const identifier = await rateLimiter.getUserIdentifier();
  return rateLimiter.checkRateLimit(identifier, config);
}

// Rate limit error class
export class RateLimitError extends Error {
  constructor(
    public remaining: number,
    public resetTime: number,
    message = 'Rate limit exceeded'
  ) {
    super(message);
    this.name = 'RateLimitError';
  }
}

// Higher-order function to wrap API calls with rate limiting
export function withRateLimit<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  config: RateLimitConfig
): T {
  return (async (...args: Parameters<T>) => {
    const result = await checkUserRateLimit(config);
    
    if (!result.allowed) {
      throw new RateLimitError(
        result.remaining,
        result.resetTime,
        `Rate limit exceeded. Try again after ${new Date(result.resetTime).toLocaleString()}`
      );
    }
    
    return fn(...args);
  }) as T;
}
