
export interface SecurityHeadersConfig {
  enableCSP?: boolean;
  enableHSTS?: boolean;
  enableFrameOptions?: boolean;
  cspDirectives?: {
    defaultSrc?: string[];
    scriptSrc?: string[];
    styleSrc?: string[];
    imgSrc?: string[];
    connectSrc?: string[];
    fontSrc?: string[];
    mediaSrc?: string[];
    frameSrc?: string[];
  };
}

export class SecurityHeaders {
  private static defaultConfig: SecurityHeadersConfig = {
    enableCSP: true,
    enableHSTS: true,
    enableFrameOptions: true,
    cspDirectives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'", "https://js.stripe.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      connectSrc: ["'self'", "https://api.stripe.com", "wss://"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      mediaSrc: ["'self'", "blob:", "data:"],
      frameSrc: ["'self'", "https://js.stripe.com", "https://hooks.stripe.com"]
    }
  };

  static generateHeaders(config: SecurityHeadersConfig = this.defaultConfig): Record<string, string> {
    const headers: Record<string, string> = {};

    // Content Security Policy
    if (config.enableCSP && config.cspDirectives) {
      const cspParts: string[] = [];
      
      Object.entries(config.cspDirectives).forEach(([directive, sources]) => {
        if (sources && sources.length > 0) {
          const directiveName = directive.replace(/([A-Z])/g, '-$1').toLowerCase();
          cspParts.push(`${directiveName} ${sources.join(' ')}`);
        }
      });
      
      headers['Content-Security-Policy'] = cspParts.join('; ');
    }

    // HTTP Strict Transport Security
    if (config.enableHSTS) {
      headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
    }

    // Frame Options
    if (config.enableFrameOptions) {
      headers['X-Frame-Options'] = 'DENY';
    }

    // Additional security headers
    headers['X-Content-Type-Options'] = 'nosniff';
    headers['X-XSS-Protection'] = '1; mode=block';
    headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';
    headers['Permissions-Policy'] = 'camera=(), microphone=(), geolocation=(), payment=()';

    return headers;
  }

  static applyToResponse(response: Response, config?: SecurityHeadersConfig): Response {
    const headers = this.generateHeaders(config);
    
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;
  }

  static injectMetaTags(config?: SecurityHeadersConfig): void {
    const headers = this.generateHeaders(config);
    
    // Only inject CSP as meta tag since others need to be HTTP headers
    if (headers['Content-Security-Policy']) {
      let metaTag = document.querySelector('meta[http-equiv="Content-Security-Policy"]') as HTMLMetaElement;
      
      if (!metaTag) {
        metaTag = document.createElement('meta');
        metaTag.httpEquiv = 'Content-Security-Policy';
        document.head.appendChild(metaTag);
      }
      
      metaTag.content = headers['Content-Security-Policy'];
    }
  }
}
