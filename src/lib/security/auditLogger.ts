
import { supabase } from '@/integrations/supabase/client';

export interface AuditLogEntry {
  eventType: 'access' | 'modification' | 'deletion' | 'authentication' | 'authorization' | 'system';
  resource: string;
  action: string;
  userId?: string;
  organizationId?: string;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  compliance?: {
    regulation: string; // e.g., 'GDPR', 'CCPA', 'SOX'
    category: string;
  };
}

export class AuditLogger {
  private static instance: AuditLogger;
  private buffer: AuditLogEntry[] = [];
  private batchSize = 10;
  private flushInterval = 5000; // 5 seconds

  constructor() {
    if (AuditLogger.instance) {
      return AuditLogger.instance;
    }
    
    AuditLogger.instance = this;
    this.startBatchProcessor();
  }

  static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger();
    }
    return AuditLogger.instance;
  }

  async log(entry: AuditLogEntry): Promise<void> {
    const enrichedEntry = await this.enrichEntry(entry);
    this.buffer.push(enrichedEntry);

    // For now, just log to console since audit_logs table doesn't exist
    console.log('[AUDIT]', enrichedEntry);

    // Immediate flush for critical events (just console log for now)
    if (entry.severity === 'critical') {
      await this.flush();
    } else if (this.buffer.length >= this.batchSize) {
      await this.flush();
    }
  }

  private async enrichEntry(entry: AuditLogEntry): Promise<AuditLogEntry> {
    const { data: { user } } = await supabase.auth.getUser();
    
    return {
      ...entry,
      userId: entry.userId || user?.id,
      ipAddress: entry.ipAddress || await this.getClientIP(),
      userAgent: entry.userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : 'server'),
      metadata: {
        ...entry.metadata,
        timestamp: new Date().toISOString(),
        sessionId: this.getSessionId()
      }
    };
  }

  private async flush(): Promise<void> {
    if (this.buffer.length === 0) return;

    const entries = [...this.buffer];
    this.buffer = [];

    try {
      // Since audit_logs table doesn't exist, we'll just log to console
      // In a real implementation, this would insert into the audit_logs table
      console.log('[AUDIT BATCH]', entries);
      
      // Future implementation when audit_logs table is created:
      // const { error } = await supabase
      //   .from('audit_logs')
      //   .insert(entries.map(entry => ({ ... })));
      
    } catch (error) {
      console.error('Audit logging error:', error);
      // Re-add failed entries to buffer for retry
      this.buffer.unshift(...entries);
    }
  }

  private startBatchProcessor(): void {
    setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  private async getClientIP(): Promise<string> {
    try {
      // In a real implementation, this would get the actual client IP
      // For now, return a placeholder
      return '0.0.0.0';
    } catch {
      return 'unknown';
    }
  }

  private getSessionId(): string {
    // Generate or retrieve session ID
    let sessionId = sessionStorage.getItem('audit_session_id');
    if (!sessionId) {
      sessionId = crypto.randomUUID();
      sessionStorage.setItem('audit_session_id', sessionId);
    }
    return sessionId;
  }

  // Convenience methods for common audit events
  static async logAccess(resource: string, action: string, metadata?: Record<string, any>): Promise<void> {
    const logger = AuditLogger.getInstance();
    await logger.log({
      eventType: 'access',
      resource,
      action,
      metadata,
      severity: 'low'
    });
  }

  static async logModification(resource: string, action: string, metadata?: Record<string, any>): Promise<void> {
    const logger = AuditLogger.getInstance();
    await logger.log({
      eventType: 'modification',
      resource,
      action,
      metadata,
      severity: 'medium'
    });
  }

  static async logSecurityEvent(resource: string, action: string, metadata?: Record<string, any>): Promise<void> {
    const logger = AuditLogger.getInstance();
    await logger.log({
      eventType: 'authentication',
      resource,
      action,
      metadata,
      severity: 'high'
    });
  }

  static async logComplianceEvent(
    resource: string, 
    action: string, 
    regulation: string, 
    category: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const logger = AuditLogger.getInstance();
    await logger.log({
      eventType: 'access',
      resource,
      action,
      metadata,
      severity: 'medium',
      compliance: { regulation, category }
    });
  }
}

// Export singleton instance
export const auditLogger = AuditLogger.getInstance();

// Hook for React components
export const useAuditLogging = () => {
  const logAccess = (resource: string, action: string, metadata?: Record<string, any>) => {
    return AuditLogger.logAccess(resource, action, metadata);
  };

  const logModification = (resource: string, action: string, metadata?: Record<string, any>) => {
    return AuditLogger.logModification(resource, action, metadata);
  };

  const logSecurityEvent = (resource: string, action: string, metadata?: Record<string, any>) => {
    return AuditLogger.logSecurityEvent(resource, action, metadata);
  };

  const logComplianceEvent = (
    resource: string, 
    action: string, 
    regulation: string, 
    category: string,
    metadata?: Record<string, any>
  ) => {
    return AuditLogger.logComplianceEvent(resource, action, regulation, category, metadata);
  };

  return {
    logAccess,
    logModification,
    logSecurityEvent,
    logComplianceEvent
  };
};
