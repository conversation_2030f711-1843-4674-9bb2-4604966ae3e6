
// Re-export all sanitization utilities

export { TextSanitizer, sanitizeText, sanitizeNumber } from './textSanitizer';
export { HtmlSanitizer, sanitizeHtml } from './htmlSanitizer';
export { FormatSanitizer, sanitizeJson, sanitizeEmail, sanitizePhone, sanitizeUrl } from './formatSanitizer';
export { FileSanitizer, sanitizeFileName } from './fileSanitizer';
export { ProductionSanitizer, sanitizeProductionData } from './productionSanitizer';

// Import classes for the combined sanitizer
import { HtmlSanitizer } from './htmlSanitizer';
import { TextSanitizer } from './textSanitizer';
import { FormatSanitizer } from './formatSanitizer';
import { FileSanitizer } from './fileSanitizer';
import { ProductionSanitizer } from './productionSanitizer';

// Combined sanitizer class for backward compatibility
export class InputSanitizer {
  static sanitizeHtml = HtmlSanitizer.sanitizeHtml;
  static sanitizeText = TextSanitizer.sanitizeText;
  static sanitizeNumber = TextSanitizer.sanitizeNumber;
  static sanitizeJson = FormatSanitizer.sanitizeJson;
  static sanitizeFileName = FileSanitizer.sanitizeFileName;
  static sanitizeEmail = FormatSanitizer.sanitizeEmail;
  static sanitizePhone = FormatSanitizer.sanitizePhone;
  static sanitizeUrl = FormatSanitizer.sanitizeUrl;
  static sanitizeProductionData = ProductionSanitizer.sanitizeProductionData;
}
