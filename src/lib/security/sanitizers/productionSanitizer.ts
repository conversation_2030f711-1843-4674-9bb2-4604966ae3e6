
// Production data sanitization utilities

import { TextSanitizer } from './textSanitizer';
import { HtmlSanitizer } from './htmlSanitizer';
import { FormatSanitizer } from './formatSanitizer';

export class ProductionSanitizer {
  // Comprehensive sanitization for production data
  static sanitizeProductionData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized: any = {};

    for (const [key, value] of Object.entries(data)) {
      const sanitizedKey = TextSanitizer.sanitizeText(key, 100);
      
      if (!sanitizedKey) continue;

      if (typeof value === 'string') {
        if (key.toLowerCase().includes('email')) {
          sanitized[sanitizedKey] = FormatSanitizer.sanitizeEmail(value);
        } else if (key.toLowerCase().includes('phone')) {
          sanitized[sanitizedKey] = FormatSanitizer.sanitizePhone(value);
        } else if (key.toLowerCase().includes('url')) {
          sanitized[sanitizedKey] = FormatSanitizer.sanitizeUrl(value);
        } else if (key.toLowerCase().includes('html') || key.toLowerCase().includes('content')) {
          sanitized[sanitizedKey] = HtmlSanitizer.sanitizeHtml(value);
        } else {
          sanitized[sanitizedKey] = TextSanitizer.sanitizeText(value);
        }
      } else if (typeof value === 'number') {
        sanitized[sanitizedKey] = TextSanitizer.sanitizeNumber(value);
      } else if (typeof value === 'object') {
        sanitized[sanitizedKey] = FormatSanitizer.sanitizeJson(value);
      } else {
        sanitized[sanitizedKey] = value;
      }
    }

    return sanitized;
  }
}

// Export utility functions
export const { sanitizeProductionData } = ProductionSanitizer;
