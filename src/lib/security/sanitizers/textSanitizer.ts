
// Text sanitization utilities

export class TextSanitizer {
  // Sanitize text input for database storage
  static sanitizeText(input: string, maxLength = 10000): string {
    if (!input) return '';
    
    return input
      .trim()
      .slice(0, maxLength)
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\s+/g, ' '); // Normalize whitespace
  }

  // Sanitize and validate numeric input
  static sanitizeNumber(input: any, options: {
    min?: number;
    max?: number;
    decimals?: number;
  } = {}): number | null {
    const num = parseFloat(input);
    
    if (isNaN(num) || !isFinite(num)) {
      return null;
    }
    
    let result = num;
    
    if (options.min !== undefined && result < options.min) {
      result = options.min;
    }
    
    if (options.max !== undefined && result > options.max) {
      result = options.max;
    }
    
    if (options.decimals !== undefined) {
      result = parseFloat(result.toFixed(options.decimals));
    }
    
    return result;
  }
}

// Export utility functions
export const { sanitizeText, sanitizeNumber } = TextSanitizer;
