
// File sanitization utilities

export class FileSanitizer {
  // Sanitize file names
  static sanitizeFileName(fileName: string): string {
    if (!fileName) return '';
    
    return fileName
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace unsafe characters
      .replace(/\.{2,}/g, '.') // Replace multiple dots
      .replace(/^\.+|\.+$/g, '') // Remove leading/trailing dots
      .slice(0, 255); // Limit length
  }
}

// Export utility functions
export const { sanitizeFileName } = FileSanitizer;
