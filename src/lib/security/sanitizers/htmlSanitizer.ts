
// HTML sanitization utilities

export class HtmlSanitizer {
  // Remove potentially dangerous HTML tags and attributes
  static sanitizeHtml(input: string): string {
    if (!input) return '';
    
    // Allow only basic formatting tags
    const allowedTags = ['b', 'i', 'u', 'strong', 'em', 'br', 'p'];
    const allowedTagsRegex = new RegExp(`<(?!\/?(${allowedTags.join('|')})\s*\/?>)[^>]+>`, 'gi');
    
    return input
      .replace(allowedTagsRegex, '') // Remove disallowed tags
      .replace(/javascript:/gi, '') // Remove javascript: protocols
      .replace(/on\w+\s*=/gi, '') // Remove event handlers
      .trim();
  }
}

// Export utility functions
export const { sanitizeHtml } = HtmlSanitizer;
