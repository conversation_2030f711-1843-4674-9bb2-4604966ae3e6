
// Format-specific sanitization utilities (JSON, email, phone, URL)

import { TextSanitizer } from './textSanitizer';

export class FormatSanitizer {
  // Sanitize JSON input
  static sanitizeJson(input: any, maxDepth = 5): any {
    if (maxDepth <= 0) return null;
    
    if (input === null || input === undefined) {
      return null;
    }
    
    if (typeof input === 'string') {
      return TextSanitizer.sanitizeText(input, 1000);
    }
    
    if (typeof input === 'number') {
      return TextSanitizer.sanitizeNumber(input);
    }
    
    if (typeof input === 'boolean') {
      return input;
    }
    
    if (Array.isArray(input)) {
      return input
        .slice(0, 100) // Limit array size
        .map(item => this.sanitizeJson(item, maxDepth - 1))
        .filter(item => item !== null);
    }
    
    if (typeof input === 'object') {
      const sanitized: any = {};
      const keys = Object.keys(input).slice(0, 50); // Limit object keys
      
      for (const key of keys) {
        const sanitizedKey = TextSanitizer.sanitizeText(key, 100);
        if (sanitizedKey) {
          const sanitizedValue = this.sanitizeJson(input[key], maxDepth - 1);
          if (sanitizedValue !== null) {
            sanitized[sanitizedKey] = sanitizedValue;
          }
        }
      }
      
      return sanitized;
    }
    
    return null;
  }

  // Validate and sanitize email addresses
  static sanitizeEmail(email: string): string | null {
    if (!email) return null;
    
    const sanitized = email.trim().toLowerCase();
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    
    if (!emailRegex.test(sanitized) || sanitized.length > 254) {
      return null;
    }
    
    return sanitized;
  }

  // Validate and sanitize phone numbers
  static sanitizePhone(phone: string): string | null {
    if (!phone) return null;
    
    const cleaned = phone.replace(/[^\d+()-\s]/g, '');
    
    if (cleaned.length < 7 || cleaned.length > 20) {
      return null;
    }
    
    return cleaned;
  }

  // Sanitize URL inputs
  static sanitizeUrl(url: string): string | null {
    if (!url) return null;
    
    try {
      const parsed = new URL(url);
      
      // Only allow http and https protocols
      if (!['http:', 'https:'].includes(parsed.protocol)) {
        return null;
      }
      
      return parsed.toString();
    } catch {
      return null;
    }
  }
}

// Export utility functions
export const { sanitizeJson, sanitizeEmail, sanitizePhone, sanitizeUrl } = FormatSanitizer;
