
export interface PDFProcessingResult {
  success: boolean;
  text?: string;
  error?: string;
  metadata?: {
    pageCount: number;
    title?: string;
    author?: string;
  };
}

export interface ScreenplayElement {
  type: 'scene_heading' | 'action' | 'character' | 'dialogue' | 'parenthetical' | 'transition';
  content: string;
  character?: string;
}

export class PDFProcessor {
  static async extractText(file: File): Promise<PDFProcessingResult> {
    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', file);

      // Use PDF.js for client-side processing
      const arrayBuffer = await file.arrayBuffer();
      const pdfjsLib = await import('pdfjs-dist');
      
      // Set worker source
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      let fullText = '';

      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        fullText += pageText + '\n';
      }

      return {
        success: true,
        text: fullText,
        metadata: {
          pageCount: pdf.numPages,
          title: file.name
        }
      };
    } catch (error) {
      console.error('PDF processing error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error processing PDF'
      };
    }
  }

  static parseScreenplayFormat(text: string): ScreenplayElement[] {
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    const elements: ScreenplayElement[] = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      
      if (!line) continue;

      // Scene headings (INT./EXT.)
      if (/^(INT\.|EXT\.|FADE IN:|FADE OUT:)/i.test(line)) {
        elements.push({
          type: 'scene_heading',
          content: line.toUpperCase()
        });
      }
      // Character names (all caps, centered)
      else if (/^[A-Z\s]+$/.test(line) && line.length < 30 && !line.includes('.')) {
        // Check if next line might be dialogue
        const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
        if (nextLine && !/^[A-Z\s]+$/.test(nextLine)) {
          elements.push({
            type: 'character',
            content: line,
            character: line
          });
        } else {
          elements.push({
            type: 'action',
            content: line
          });
        }
      }
      // Parentheticals
      else if (/^\(.+\)$/.test(line)) {
        elements.push({
          type: 'parenthetical',
          content: line
        });
      }
      // Transitions (FADE TO:, CUT TO:, etc.)
      else if (/^(FADE TO:|CUT TO:|DISSOLVE TO:|SMASH CUT TO:)/i.test(line)) {
        elements.push({
          type: 'transition',
          content: line.toUpperCase()
        });
      }
      // Check if this might be dialogue (following a character)
      else if (elements.length > 0 && elements[elements.length - 1].type === 'character') {
        elements.push({
          type: 'dialogue',
          content: line,
          character: elements[elements.length - 1].character
        });
      }
      // Default to action
      else {
        elements.push({
          type: 'action',
          content: line
        });
      }
    }

    return elements;
  }

  static convertToScriptFormat(elements: ScreenplayElement[]): string {
    return elements.map(element => {
      switch (element.type) {
        case 'scene_heading':
          return element.content.toUpperCase();
        case 'character':
          return `\n${element.content.toUpperCase()}\n`;
        case 'dialogue':
          return element.content;
        case 'parenthetical':
          return `\n${element.content}\n`;
        case 'transition':
          return `\n${element.content.toUpperCase()}\n`;
        case 'action':
        default:
          return element.content;
      }
    }).join('\n');
  }
}
