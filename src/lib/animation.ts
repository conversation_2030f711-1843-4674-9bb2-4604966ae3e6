
// Animation utilities for smooth UI transitions
export interface AnimationConfig {
  duration?: number;
  easing?: string;
  delay?: number;
}

export class AnimationManager {
  private static instance: AnimationManager;
  private activeAnimations = new Set<number>();

  static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager();
    }
    return AnimationManager.instance;
  }

  animate(
    element: HTMLElement,
    keyframes: Keyframe[],
    options: KeyframeAnimationOptions = {}
  ): Animation {
    return element.animate(keyframes, {
      duration: 300,
      easing: 'ease-out',
      fill: 'forwards',
      ...options
    });
  }

  fadeIn(element: HTMLElement, config: AnimationConfig = {}): Animation {
    return this.animate(element, [
      { opacity: 0, transform: 'translateY(10px)' },
      { opacity: 1, transform: 'translateY(0)' }
    ], config);
  }

  fadeOut(element: HTMLElement, config: AnimationConfig = {}): Animation {
    return this.animate(element, [
      { opacity: 1, transform: 'translateY(0)' },
      { opacity: 0, transform: 'translateY(10px)' }
    ], config);
  }

  slideIn(element: HTMLElement, direction: 'left' | 'right' | 'up' | 'down' = 'right', config: AnimationConfig = {}): Animation {
    const transforms = {
      left: ['translateX(-100%)', 'translateX(0)'],
      right: ['translateX(100%)', 'translateX(0)'],
      up: ['translateY(-100%)', 'translateY(0)'],
      down: ['translateY(100%)', 'translateY(0)']
    };

    return this.animate(element, [
      { transform: transforms[direction][0] },
      { transform: transforms[direction][1] }
    ], config);
  }

  scale(element: HTMLElement, from: number = 0.95, to: number = 1, config: AnimationConfig = {}): Animation {
    return this.animate(element, [
      { transform: `scale(${from})`, opacity: 0 },
      { transform: `scale(${to})`, opacity: 1 }
    ], config);
  }

  requestAnimationFrame(callback: FrameRequestCallback): number {
    const id = requestAnimationFrame(callback);
    this.activeAnimations.add(id);
    return id;
  }

  cancelAnimation(id: number): void {
    cancelAnimationFrame(id);
    this.activeAnimations.delete(id);
  }

  cancelAllAnimations(): void {
    this.activeAnimations.forEach(id => cancelAnimationFrame(id));
    this.activeAnimations.clear();
  }
}

export const animationManager = AnimationManager.getInstance();
