interface Translation {
  [key: string]: string | Translation;
}

interface I18nOptions {
  defaultLocale: string;
  fallbackLocale: string;
  translations: Record<string, Translation>;
}

class I18n {
  private static instance: I18n;
  private options: I18nOptions;
  private currentLocale: string;
  private listeners: Set<() => void> = new Set();

  private constructor(options: I18nOptions) {
    this.options = options;
    this.currentLocale = options.defaultLocale;
  }

  static initialize(options: I18nOptions): I18n {
    if (!I18n.instance) {
      I18n.instance = new I18n(options);
    }
    return I18n.instance;
  }

  static getInstance(): I18n {
    if (!I18n.instance) {
      throw new Error('I18n not initialized. Call initialize first.');
    }
    return I18n.instance;
  }

  t(key: string, params?: Record<string, string | number>): string {
    const translation = this.getTranslation(key);
    if (!translation) {
      console.warn(`Translation missing for key: ${key}`);
      return key;
    }

    if (params) {
      return this.interpolate(translation, params);
    }

    return translation;
  }

  setLocale(locale: string) {
    if (this.currentLocale === locale) return;
    
    if (!this.options.translations[locale]) {
      console.warn(`Locale ${locale} not found, falling back to ${this.options.fallbackLocale}`);
      this.currentLocale = this.options.fallbackLocale;
    } else {
      this.currentLocale = locale;
    }

    document.documentElement.lang = this.currentLocale;
    this.notifyListeners();
  }

  getLocale(): string {
    return this.currentLocale;
  }

  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private getTranslation(key: string): string | undefined {
    const keys = key.split('.');
    let translation: any = this.options.translations[this.currentLocale];

    for (const k of keys) {
      if (!translation || typeof translation !== 'object') {
        return undefined;
      }
      translation = translation[k];
    }

    return typeof translation === 'string' ? translation : undefined;
  }

  private interpolate(text: string, params: Record<string, string | number>): string {
    return text.replace(/\{(\w+)\}/g, (_, key) => {
      const value = params[key];
      return value !== undefined ? String(value) : `{${key}}`;
    });
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener());
  }
}

// Example usage:
/*
const i18n = I18n.initialize({
  defaultLocale: 'en',
  fallbackLocale: 'en',
  translations: {
    en: {
      common: {
        welcome: 'Welcome, {name}!',
        buttons: {
          save: 'Save',
          cancel: 'Cancel'
        }
      }
    },
    es: {
      common: {
        welcome: '¡Bienvenido, {name}!',
        buttons: {
          save: 'Guardar',
          cancel: 'Cancelar'
        }
      }
    }
  }
});

// Use in components:
const message = i18n.t('common.welcome', { name: 'John' });
*/ 