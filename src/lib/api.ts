// Re-export everything from the main API index file
export * from './api/index';

// Keep the ApiClient for backward compatibility
interface RequestConfig extends Omit<RequestInit, 'cache'> {
  cache?: boolean;
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

class ApiClient {
  private baseURL: string;
  private defaultHeaders: Record<string, string>;
  private requestInterceptors: ((config: RequestConfig) => RequestConfig)[] = [];
  private responseInterceptors: ((response: Response) => Response)[] = [];

  constructor(baseURL = '', defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...defaultHeaders
    };
  }

  addRequestInterceptor(interceptor: (config: RequestConfig) => RequestConfig): void {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(interceptor: (response: Response) => Response): void {
    this.responseInterceptors.push(interceptor);
  }

  private async request<T>(url: string, config: RequestConfig = {}): Promise<T> {
    const { cache: useCache, timeout = 30000, retries = 3, retryDelay = 1000, ...fetchConfig } = config;
    
    // Apply request interceptors
    let finalConfig = { ...fetchConfig };
    for (const interceptor of this.requestInterceptors) {
      finalConfig = interceptor(finalConfig);
    }

    const fullUrl = `${this.baseURL}${url}`;
    const requestConfig: RequestInit = {
      headers: {
        ...this.defaultHeaders,
        ...finalConfig.headers
      },
      ...finalConfig
    };

    // Add cache control if specified
    if (useCache === false) {
      requestConfig.cache = 'no-store';
    }

    let lastError: Error;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        const response = await fetch(fullUrl, {
          ...requestConfig,
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // Apply response interceptors
        let finalResponse = response;
        for (const interceptor of this.responseInterceptors) {
          finalResponse = interceptor(finalResponse);
        }
        
        if (!finalResponse.ok) {
          throw new Error(`HTTP ${finalResponse.status}: ${finalResponse.statusText}`);
        }
        
        return await finalResponse.json() as T;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
        }
      }
    }
    
    throw lastError!;
  }

  async get<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, { ...config, method: 'GET' });
  }

  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async delete<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, { ...config, method: 'DELETE' });
  }
}

export const apiClient = new ApiClient();
