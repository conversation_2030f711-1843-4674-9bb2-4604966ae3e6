import { enhancedLogger } from '../logging/enhancedLogger';
import { advancedCache } from '../cache/advancedCache';

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private observers: Map<string, PerformanceObserver> = new Map();
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  // Initialize performance monitoring
  initialize() {
    this.setupCoreWebVitalsMonitoring();
    this.setupResourceMonitoring();
    this.setupUserTimingMonitoring();
    this.setupMemoryMonitoring();
  }

  // Measure and optimize function execution
  async optimizeFunction<T>(
    name: string,
    fn: () => Promise<T> | T,
    options: {
      cache?: boolean;
      cacheKey?: string;
      cacheTTL?: number;
      timeout?: number;
      retries?: number;
    } = {}
  ): Promise<T> {
    const { cache = false, cacheKey, cacheTTL = 300000, timeout = 10000, retries = 0 } = options;
    
    // Try cache first
    if (cache && cacheKey) {
      const cached = advancedCache.get<T>(cacheKey);
      if (cached !== null) {
        enhancedLogger.debug(`Cache hit for ${name}`, { action: 'cache-hit', metadata: { cacheKey } });
        return cached;
      }
    }

    return enhancedLogger.trace(name, async () => {
      let lastError: Error;
      
      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          // Ensure we're working with a Promise
          const result = await Promise.resolve(fn());
          
          if (timeout > 0) {
            const timeoutPromise = new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error(`Operation timed out after ${timeout}ms`)), timeout)
            );
            const finalResult = await Promise.race([Promise.resolve(result), timeoutPromise]);
            
            // Cache result if requested
            if (cache && cacheKey) {
              advancedCache.set(cacheKey, finalResult, { ttl: cacheTTL });
            }
            
            return finalResult;
          } else {
            // Cache result if requested
            if (cache && cacheKey) {
              advancedCache.set(cacheKey, result, { ttl: cacheTTL });
            }
            
            return result;
          }
        } catch (error) {
          lastError = error as Error;
          
          if (attempt < retries) {
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
            enhancedLogger.warn(`Retrying ${name} (attempt ${attempt + 2})`, {
              action: 'function-retry',
              metadata: { attempt: attempt + 2, delay }
            });
          }
        }
      }
      
      throw lastError!;
    });
  }

  // Optimize component rendering
  memoizeComponent<T extends (...args: any[]) => any>(
    Component: T,
    isEqual?: (prevProps: Parameters<T>[0], nextProps: Parameters<T>[0]) => boolean
  ): T {
    const memoCache = new Map();
    
    return ((...args: Parameters<T>) => {
      const props = args[0];
      const cacheKey = JSON.stringify(props);
      
      if (memoCache.has(cacheKey)) {
        const cached = memoCache.get(cacheKey);
        
        if (!isEqual || isEqual(cached.props, props)) {
          enhancedLogger.debug('Component memo cache hit', {
            component: Component.name,
            action: 'memo-cache-hit'
          });
          return cached.result;
        }
      }
      
      const result = Component(...args);
      memoCache.set(cacheKey, { props, result });
      
      // Limit cache size
      if (memoCache.size > 100) {
        const firstKey = memoCache.keys().next().value;
        memoCache.delete(firstKey);
      }
      
      return result;
    }) as T;
  }

  // Debounce expensive operations
  debounce<T extends (...args: any[]) => any>(
    fn: T,
    delay: number,
    options: { leading?: boolean; trailing?: boolean } = { trailing: true }
  ): T {
    let timeoutId: NodeJS.Timeout;
    let lastCallTime = 0;
    let lastResult: ReturnType<T>;

    return ((...args: Parameters<T>) => {
      const now = Date.now();
      const { leading = false, trailing = true } = options;

      const callFunction = () => {
        lastCallTime = now;
        lastResult = fn(...args);
        enhancedLogger.debug('Debounced function executed', {
          action: 'debounce-execute',
          metadata: { functionName: fn.name, delay }
        });
        return lastResult;
      };

      if (leading && now - lastCallTime >= delay) {
        return callFunction();
      }

      clearTimeout(timeoutId);
      
      if (trailing) {
        timeoutId = setTimeout(callFunction, delay);
      }

      return lastResult;
    }) as T;
  }

  // Throttle high-frequency operations
  throttle<T extends (...args: any[]) => any>(fn: T, limit: number): T {
    let inThrottle = false;
    let lastResult: ReturnType<T>;

    return ((...args: Parameters<T>) => {
      if (!inThrottle) {
        lastResult = fn(...args);
        inThrottle = true;
        
        setTimeout(() => {
          inThrottle = false;
        }, limit);
        
        enhancedLogger.debug('Throttled function executed', {
          action: 'throttle-execute',
          metadata: { functionName: fn.name, limit }
        });
      }
      
      return lastResult;
    }) as T;
  }

  // Image optimization
  optimizeImage(
    src: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'webp' | 'avif' | 'auto';
      lazy?: boolean;
    } = {}
  ): { src: string; srcSet?: string; loading?: 'lazy' | 'eager' } {
    const { width, height, quality = 80, format = 'auto', lazy = true } = options;
    
    // For now, return optimized attributes
    // In a real implementation, you'd integrate with an image optimization service
    const optimizedSrc = src;
    
    return {
      src: optimizedSrc,
      loading: lazy ? 'lazy' : 'eager'
    };
  }

  // Bundle splitting helper
  async loadModule<T>(importFn: () => Promise<{ default: T }>, fallback?: T): Promise<T> {
    try {
      const startTime = performance.now();
      const module = await importFn();
      const loadTime = performance.now() - startTime;
      
      enhancedLogger.info('Dynamic module loaded', {
        action: 'module-load',
        metadata: { loadTime: Math.round(loadTime) }
      });
      
      return module.default;
    } catch (error) {
      enhancedLogger.error('Failed to load dynamic module', error as Error, {
        action: 'module-load-failed'
      });
      
      if (fallback !== undefined) {
        return fallback;
      }
      
      throw error;
    }
  }

  // Get performance metrics
  getMetrics(): Record<string, any> {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    return {
      pageLoad: {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
        loadComplete: navigation.loadEventEnd - navigation.fetchStart,
        firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
        firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0
      },
      memory: this.getMemoryInfo(),
      customMetrics: Object.fromEntries(
        Array.from(this.metrics.entries()).map(([key, values]) => [
          key,
          {
            count: values.length,
            average: values.reduce((a, b) => a + b, 0) / values.length,
            min: Math.min(...values),
            max: Math.max(...values)
          }
        ])
      )
    };
  }

  private async withTimeout<T>(promise: Promise<T>, ms: number): Promise<T> {
    const timeout = new Promise<never>((_, reject) =>
      setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms)
    );
    
    return Promise.race([promise, timeout]);
  }

  private setupCoreWebVitalsMonitoring() {
    // Largest Contentful Paint
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      enhancedLogger.info('LCP measured', {
        action: 'core-web-vital',
        metadata: {
          metric: 'LCP',
          value: lastEntry.startTime,
          rating: this.getRating(lastEntry.startTime, { good: 2500, needsImprovement: 4000 })
        }
      });
    });
    
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.set('lcp', lcpObserver);

    // First Input Delay
    const fidObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        const fid = entry.processingStart - entry.startTime;
        
        enhancedLogger.info('FID measured', {
          action: 'core-web-vital',
          metadata: {
            metric: 'FID',
            value: fid,
            rating: this.getRating(fid, { good: 100, needsImprovement: 300 })
          }
        });
      });
    });
    
    fidObserver.observe({ entryTypes: ['first-input'] });
    this.observers.set('fid', fidObserver);

    // Cumulative Layout Shift
    let cumulativeScore = 0;
    const clsObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          cumulativeScore += entry.value;
        }
      });
      
      enhancedLogger.info('CLS measured', {
        action: 'core-web-vital',
        metadata: {
          metric: 'CLS',
          value: cumulativeScore,
          rating: this.getRating(cumulativeScore, { good: 0.1, needsImprovement: 0.25 })
        }
      });
    });
    
    clsObserver.observe({ entryTypes: ['layout-shift'] });
    this.observers.set('cls', clsObserver);
  }

  private setupResourceMonitoring() {
    const resourceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry: PerformanceResourceTiming) => {
        const duration = entry.responseEnd - entry.startTime;
        
        if (duration > 1000) { // Log slow resources
          enhancedLogger.warn('Slow resource detected', {
            action: 'slow-resource',
            metadata: {
              name: entry.name,
              duration: Math.round(duration),
              size: entry.transferSize || 0,
              type: entry.initiatorType
            }
          });
        }
      });
    });
    
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.set('resource', resourceObserver);
  }

  private setupUserTimingMonitoring() {
    const userTimingObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        this.recordMetric(entry.name, entry.duration || entry.startTime);
        
        enhancedLogger.debug('User timing measured', {
          action: 'user-timing',
          metadata: {
            name: entry.name,
            duration: entry.duration || entry.startTime,
            type: entry.entryType
          }
        });
      });
    });
    
    userTimingObserver.observe({ entryTypes: ['mark', 'measure'] });
    this.observers.set('userTiming', userTimingObserver);
  }

  private setupMemoryMonitoring() {
    setInterval(() => {
      const memInfo = this.getMemoryInfo();
      if (memInfo.usedJSHeapSize && memInfo.usedJSHeapSize > 50 * 1024 * 1024) { // 50MB threshold
        enhancedLogger.warn('High memory usage detected', {
          action: 'memory-warning',
          metadata: memInfo
        });
      }
    }, 30000); // Check every 30 seconds
  }

  private getMemoryInfo() {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      };
    }
    return {};
  }

  private recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  private getRating(value: number, thresholds: { good: number; needsImprovement: number }): string {
    if (value <= thresholds.good) return 'good';
    if (value <= thresholds.needsImprovement) return 'needs-improvement';
    return 'poor';
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.metrics.clear();
  }
}

export const performanceOptimizer = PerformanceOptimizer.getInstance();
