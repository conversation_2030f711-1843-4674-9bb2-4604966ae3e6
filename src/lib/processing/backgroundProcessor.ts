
export interface ProcessingJob {
  id: string;
  sceneId: string;
  fidelityLevel: string;
  content: string;
  orgId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: any;
  error?: string;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
}

export type ProgressCallback = (progress: number, status: string) => void;

export class BackgroundProcessor {
  private jobs = new Map<string, ProcessingJob>();
  private progressCallbacks = new Map<string, ProgressCallback>();
  private isProcessing = false;

  createJob(
    sceneId: string,
    fidelityLevel: string,
    content: string,
    orgId: string
  ): string {
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const job: ProcessingJob = {
      id: jobId,
      sceneId,
      fidelityLevel,
      content,
      orgId,
      status: 'pending',
      progress: 0,
      createdAt: Date.now(),
    };

    this.jobs.set(jobId, job);
    this.processNextJob();
    
    return jobId;
  }

  getJob(jobId: string): ProcessingJob | null {
    return this.jobs.get(jobId) || null;
  }

  onProgress(jobId: string, callback: ProgressCallback): void {
    this.progressCallbacks.set(jobId, callback);
  }

  private async processNextJob(): Promise<void> {
    if (this.isProcessing) return;

    const pendingJob = Array.from(this.jobs.values())
      .find(job => job.status === 'pending');

    if (!pendingJob) return;

    this.isProcessing = true;
    await this.processJob(pendingJob);
    this.isProcessing = false;

    // Process next job if any
    setTimeout(() => this.processNextJob(), 100);
  }

  private async processJob(job: ProcessingJob): Promise<void> {
    try {
      job.status = 'processing';
      job.startedAt = Date.now();
      this.updateProgress(job.id, 0, 'Starting analysis...');

      // Simulate processing steps with progress updates
      await this.simulateProcessingSteps(job);

      job.status = 'completed';
      job.completedAt = Date.now();
      job.progress = 100;
      this.updateProgress(job.id, 100, 'Analysis complete!');

    } catch (error) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'Unknown error';
      job.completedAt = Date.now();
      this.updateProgress(job.id, 0, `Failed: ${job.error}`);
    }
  }

  private async simulateProcessingSteps(job: ProcessingJob): Promise<void> {
    const steps = [
      { progress: 10, message: 'Validating screenplay content...' },
      { progress: 25, message: 'Analyzing structure and format...' },
      { progress: 40, message: 'Processing character development...' },
      { progress: 60, message: 'Evaluating dialogue and pacing...' },
      { progress: 80, message: 'Generating comprehensive analysis...' },
      { progress: 95, message: 'Finalizing coverage report...' },
    ];

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      job.progress = step.progress;
      this.updateProgress(job.id, step.progress, step.message);
    }
  }

  private updateProgress(jobId: string, progress: number, status: string): void {
    const callback = this.progressCallbacks.get(jobId);
    if (callback) {
      callback(progress, status);
    }
  }

  cleanup(): void {
    // Remove completed jobs older than 1 hour
    const cutoff = Date.now() - (60 * 60 * 1000);
    
    for (const [jobId, job] of this.jobs.entries()) {
      if (job.completedAt && job.completedAt < cutoff) {
        this.jobs.delete(jobId);
        this.progressCallbacks.delete(jobId);
      }
    }
  }
}

export const backgroundProcessor = new BackgroundProcessor();

// Cleanup old jobs every 30 minutes
setInterval(() => backgroundProcessor.cleanup(), 30 * 60 * 1000);
