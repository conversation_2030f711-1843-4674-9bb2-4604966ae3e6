// Add type declarations for gtag
declare global {
  interface Window {
    gtag: (
      command: 'config' | 'event' | 'set' | 'consent',
      targetId: string,
      config?: {
        [key: string]: any;
      }
    ) => void;
  }
}

// Analytics event types
export type AnalyticsEvent =
  | 'page_view'
  | 'user_action'
  | 'performance'
  | 'error';

// Analytics properties interface
export interface AnalyticsProperties {
  [key: string]: any;
}

// Analytics options interface
export interface AnalyticsOptions {
  debug?: boolean;
  userId?: string;
  deviceId?: string;
  sessionId?: string;
}

// Analytics context interface
export interface AnalyticsContext {
  page: {
    path: string;
    title: string;
    url: string;
    referrer: string;
  };
  user: {
    id?: string;
    email?: string;
    name?: string;
  };
  device: {
    id?: string;
    type: 'mobile' | 'desktop' | 'tablet';
    os: string;
    viewport: {
      width: number;
      height: number;
    };
  };
  session: {
    id?: string;
    startedAt: string;
  };
  app: {
    name: string;
    version: string;
    build: string;
  };
}

// Analytics service for tracking user interactions and performance
class AnalyticsService {
  private isInitialized = false;
  private queue: Array<{ event: string; properties?: Record<string, any> }> = [];

  async initialize(): Promise<void> {
    if (this.isInitialized) return;
    
    this.isInitialized = true;
    
    // Process queued events
    const queuedEvents = [...this.queue];
    this.queue = [];
    
    for (const { event, properties } of queuedEvents) {
      await this.track(event, properties);
    }
  }

  async track(event: string, properties?: Record<string, any>): Promise<void> {
    if (!this.isInitialized) {
      this.queue.push({ event, properties });
      return;
    }

    try {
      // Send to analytics service
      console.log('Analytics Event:', event, properties);
    } catch (error) {
      console.error('Analytics error:', error);
    }
  }

  async trackPageView(path: string): Promise<void> {
    await this.track('page_view', { path });
  }

  async trackUserAction(action: string, details?: Record<string, any>): Promise<void> {
    await this.track('user_action', { action, ...details });
  }

  async trackPerformance(metric: string, value: number): Promise<void> {
    await this.track('performance', { metric, value });
  }

  async trackError(error: Error, context?: Record<string, any>): Promise<void> {
    await this.track('error', {
      message: error.message,
      stack: error.stack,
      ...context
    });
  }
}

// Create singleton instance
export const analytics = new AnalyticsService();

// Enhanced conversion tracking functions
export const trackConversion = async (eventName: string, data: Record<string, any> = {}) => {
  try {
    // Google Analytics 4
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        ...data,
        timestamp: new Date().toISOString()
      });
    }

    // Track in our analytics service
    await analytics.track(eventName, data);
  } catch (error) {
    console.error('Conversion tracking error:', error);
  }
};

export const trackPlanSelection = async (planId: string, billingCycle: string, source: string = 'pricing_page') => {
  await trackConversion('plan_selected', {
    plan_id: planId,
    billing_cycle: billingCycle,
    source,
    event_category: 'subscription',
    event_label: `${planId}_${billingCycle}`
  });
};

export const trackSignupStart = async (source: string = 'organic') => {
  await trackConversion('sign_up_start', {
    source,
    event_category: 'authentication',
    event_label: 'signup_initiated'
  });
};

export const trackSignupComplete = async (source: string = 'organic', planIntent?: string) => {
  await trackConversion('sign_up_complete', {
    source,
    plan_intent: planIntent,
    event_category: 'authentication',
    event_label: 'signup_completed'
  });
};

export const trackCheckoutStart = async (planId: string, billingCycle: string, promoCode?: string) => {
  await trackConversion('begin_checkout', {
    plan_id: planId,
    billing_cycle: billingCycle,
    promo_code: promoCode,
    event_category: 'ecommerce',
    event_label: 'checkout_initiated'
  });
};
