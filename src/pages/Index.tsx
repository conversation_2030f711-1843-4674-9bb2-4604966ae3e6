
import React, { memo, Suspense, lazy, useEffect } from "react";
import LoadingSpinner from "@/components/LoadingSpinner";
import { MobileErrorBoundary } from "@/components/mobile/MobileErrorBoundary";
import { NetworkAwareWrapper } from "@/components/NetworkAwareWrapper";
import { useAuth } from "@/contexts/AuthContext";
import { usePlanRedirect } from "@/hooks/usePlanRedirect";
import { useIsMobile } from "@/hooks/use-mobile";
import SEO from "@/components/SEO";

// Lazy load with mobile-specific chunking
const Dashboard = lazy(() => import("@/components/Dashboard"));
const LandingPage = lazy(() => import("@/components/LandingPage"));

const MobileFallback = () => (
  <div className="min-h-screen flex items-center justify-center bg-background px-4">
    <div className="text-center space-y-4 max-w-sm">
      <LoadingSpinner size="lg" />
      <div className="space-y-2">
        <p className="text-lg font-medium">Loading...</p>
        <p className="text-sm text-muted-foreground">Optimizing for mobile</p>
      </div>
    </div>
  </div>
);

const Index = memo(() => {
  const { user, loading } = useAuth();
  const isMobile = useIsMobile();
  
  usePlanRedirect();

  // Preload critical resources for mobile
  useEffect(() => {
    if (isMobile) {
      // Preload critical CSS
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'style';
      link.href = '/src/index.css';
      document.head.appendChild(link);
    }
  }, [isMobile]);

  if (loading) {
    return <MobileFallback />;
  }

  return (
    <>
      <SEO 
        title={user ? "Dashboard" : "ScriptGenius - Professional Screenwriting Tools"}
        description={user ? "Your ScriptGenius dashboard. Manage your projects, collaborate with your team, and create amazing screenplays." : "Transform your ideas into professional screenplays with AI-powered writing tools. Collaborate, create, and bring your stories to life with ScriptGenius."}
        noIndex={!!user}
      />
      <NetworkAwareWrapper>
        <MobileErrorBoundary>
          {user ? (
            <Suspense fallback={<MobileFallback />}>
              <Dashboard />
            </Suspense>
          ) : (
            <Suspense fallback={<MobileFallback />}>
              <LandingPage />
            </Suspense>
          )}
        </MobileErrorBoundary>
      </NetworkAwareWrapper>
    </>
  );
});

Index.displayName = 'Index';

export default Index;
