
import React from 'react';
import AuthCallback from '@/components/AuthCallback';

/**
 * Auth callback page for handling Supabase authentication callbacks
 * This page is used for email verification, password reset, invitations, etc.
 */
const AuthCallbackPage: React.FC = () => {
  return (
    <AuthCallback
      successRedirect="/dashboard"
      errorRedirect="/auth"
      showUI={true}
    />
  );
};

export default AuthCallbackPage;
