
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, Users, ArrowRight } from 'lucide-react';

const Careers = () => {
  const jobOpenings = [
    {
      title: "Senior Frontend Developer",
      department: "Engineering",
      location: "Remote",
      type: "Full-time",
      description: "Join our team to build the next generation of creative writing tools using React, TypeScript, and modern web technologies."
    },
    {
      title: "AI/ML Engineer",
      department: "AI Research",
      location: "Remote",
      type: "Full-time", 
      description: "Help us develop cutting-edge AI features that assist screenwriters in crafting better stories and characters."
    },
    {
      title: "Product Designer",
      department: "Design",
      location: "Remote",
      type: "Full-time",
      description: "Design intuitive and beautiful interfaces that empower writers to bring their creative visions to life."
    },
    {
      title: "Community Manager",
      department: "Marketing",
      location: "Remote",
      type: "Part-time",
      description: "Build and nurture our growing community of screenwriters, fostering engagement and supporting user success."
    }
  ];

  const benefits = [
    "Competitive salary and equity",
    "Comprehensive health coverage",
    "Flexible remote work",
    "Professional development budget",
    "Unlimited PTO policy",
    "Latest tech equipment"
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              💼 Join Our Team
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Shape the Future of <span className="gold-gradient">Storytelling</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Join a passionate team of creators, engineers, and innovators building the tools 
              that will define the next generation of screenwriting.
            </p>
          </div>
        </div>
      </section>

      {/* Culture Section */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Why <span className="gold-gradient">ScriptGenius</span>?
            </h2>
            <p className="text-xl text-muted-foreground">
              We're not just building software—we're empowering the next generation of storytellers.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <h3 className="text-2xl font-semibold mb-4 font-playfair">Remote-First Culture</h3>
              <p className="text-muted-foreground leading-relaxed">
                Work from anywhere in the world. We believe great talent knows no boundaries, 
                and we've built our entire company around supporting distributed teams.
              </p>
            </Card>
            
            <Card className="cinema-card p-8">
              <h3 className="text-2xl font-semibold mb-4 font-playfair">Creative Impact</h3>
              <p className="text-muted-foreground leading-relaxed">
                Your work directly impacts thousands of writers worldwide, helping them 
                bring their stories to life and advance their careers in entertainment.
              </p>
            </Card>
            
            <Card className="cinema-card p-8">
              <h3 className="text-2xl font-semibold mb-4 font-playfair">Growth Opportunities</h3>
              <p className="text-muted-foreground leading-relaxed">
                We invest heavily in our team's professional development with conferences, 
                courses, and mentorship programs to help you reach your full potential.
              </p>
            </Card>
            
            <Card className="cinema-card p-8">
              <h3 className="text-2xl font-semibold mb-4 font-playfair">Innovation Focus</h3>
              <p className="text-muted-foreground leading-relaxed">
                Work with cutting-edge AI and machine learning technologies, pushing the 
                boundaries of what's possible in creative software development.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Job Openings */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Open <span className="gold-gradient">Positions</span>
            </h2>
            
            <div className="space-y-6">
              {jobOpenings.map((job, index) => (
                <Card key={index} className="cinema-card p-8 hover:scale-105 transition-all duration-300 group">
                  <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                    <div className="mb-4 md:mb-0">
                      <h3 className="text-2xl font-semibold mb-2 font-playfair">{job.title}</h3>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="outline">{job.department}</Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {job.location}
                        </Badge>
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {job.type}
                        </Badge>
                      </div>
                    </div>
                    <Button className="group-hover:bg-primary group-hover:text-primary-foreground">
                      Apply Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-muted-foreground leading-relaxed">{job.description}</p>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12">
              Benefits & <span className="gold-gradient">Perks</span>
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {benefits.map((benefit, index) => (
                <Card key={index} className="p-6 text-center border-primary/20">
                  <p className="font-medium">{benefit}</p>
                </Card>
              ))}
            </div>
            
            <div className="mt-12">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <Users className="mr-2 h-5 w-5" />
                View All Open Positions
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Careers;
