
import React from 'react';
import { DatabaseMonitoringDashboard } from '@/components/monitoring/DatabaseMonitoringDashboard';
import { AccessibilityEnhancements } from '@/components/monitoring/AccessibilityEnhancements';
import { MobileOptimizedDashboard } from '@/components/monitoring/MobileOptimizedDashboard';
import { Database, Activity, GitBranch, HardDrive } from 'lucide-react';

export default function DatabaseMonitoringPage() {
  const navigationItems = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <Database className="h-4 w-4" />,
      onClick: () => {
        const element = document.querySelector('[data-tab="overview"]');
        element?.scrollIntoView({ behavior: 'smooth' });
      }
    },
    {
      id: 'backups',
      label: 'Backups',
      icon: <HardDrive className="h-4 w-4" />,
      onClick: () => {
        const element = document.querySelector('[data-tab="backups"]');
        element?.scrollIntoView({ behavior: 'smooth' });
      }
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: <Activity className="h-4 w-4" />,
      onClick: () => {
        const element = document.querySelector('[data-tab="performance"]');
        element?.scrollIntoView({ behavior: 'smooth' });
      }
    },
    {
      id: 'migrations',
      label: 'Migrations',
      icon: <GitBranch className="h-4 w-4" />,
      onClick: () => {
        const element = document.querySelector('[data-tab="migrations"]');
        element?.scrollIntoView({ behavior: 'smooth' });
      }
    }
  ];

  return (
    <AccessibilityEnhancements>
      <MobileOptimizedDashboard navigationItems={navigationItems}>
        <div className="container mx-auto px-4 py-8">
          <DatabaseMonitoringDashboard />
        </div>
      </MobileOptimizedDashboard>
    </AccessibilityEnhancements>
  );
}
