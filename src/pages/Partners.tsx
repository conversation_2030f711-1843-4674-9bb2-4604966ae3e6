
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Handshake, Users, Target, ArrowRight, Check } from 'lucide-react';

const Partners = () => {
  const partnerTypes = [
    {
      title: "Studios & Production Companies",
      description: "Partner with us to provide your writers with cutting-edge tools and streamline your development process.",
      benefits: [
        "Bulk licensing discounts",
        "Custom branding options", 
        "Priority technical support",
        "Usage analytics and reporting"
      ],
      icon: <Target className="h-8 w-8 text-blue-500" />
    },
    {
      title: "Educational Institutions",
      description: "Bring industry-standard screenwriting tools to your film and media programs with special academic pricing.",
      benefits: [
        "Educational pricing tiers",
        "Curriculum integration support",
        "Student progress tracking",
        "Instructor training resources"
      ],
      icon: <Users className="h-8 w-8 text-green-500" />
    },
    {
      title: "Technology Partners",
      description: "Integrate with ScriptGenius through our API or explore co-development opportunities.",
      benefits: [
        "API access and documentation",
        "Technical partnership support",
        "Co-marketing opportunities",
        "Revenue sharing models"
      ],
      icon: <Handshake className="h-8 w-8 text-purple-500" />
    }
  ];

  const currentPartners = [
    { name: "Hollywood Creative Alliance", category: "Industry Association" },
    { name: "Film Academy International", category: "Education" },
    { name: "Writers Guild Foundation", category: "Professional Organization" },
    { name: "Creative Cloud Studios", category: "Production Company" },
    { name: "Digital Media Institute", category: "Technology" },
    { name: "Screencraft University", category: "Education" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🤝 Partnerships
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Partner with <span className="gold-gradient">ScriptGenius</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Join forces with us to revolutionize the entertainment industry and empower the next generation of storytellers.
            </p>
            
            <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group">
              Become a Partner
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>
      </section>

      {/* Partnership Types */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Partnership <span className="gold-gradient">Opportunities</span>
            </h2>
            
            <div className="grid lg:grid-cols-3 gap-8">
              {partnerTypes.map((partner, index) => (
                <Card key={index} className="cinema-card p-8 hover:scale-105 transition-all duration-300 group">
                  <div className="mb-6 group-hover:scale-110 transition-transform">
                    {partner.icon}
                  </div>
                  
                  <h3 className="text-2xl font-semibold mb-4 font-playfair">{partner.title}</h3>
                  <p className="text-muted-foreground mb-6 leading-relaxed">
                    {partner.description}
                  </p>
                  
                  <div className="space-y-3 mb-6">
                    {partner.benefits.map((benefit, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm">{benefit}</span>
                      </div>
                    ))}
                  </div>
                  
                  <Button variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                    Learn More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Current Partners */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Our <span className="gold-gradient">Partners</span>
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {currentPartners.map((partner, index) => (
                <Card key={index} className="p-6 text-center border-primary/20 hover:border-primary/40 transition-colors">
                  <h3 className="font-semibold mb-2">{partner.name}</h3>
                  <Badge variant="outline" className="text-xs">
                    {partner.category}
                  </Badge>
                </Card>
              ))}
            </div>
            
            <div className="text-center mt-12">
              <p className="text-muted-foreground mb-6">
                Join these industry leaders in shaping the future of screenwriting.
              </p>
              <Button size="lg" variant="outline">
                View All Partners
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Partnership Benefits */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-8">
              Why Partner with <span className="gold-gradient">Us</span>?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-12 leading-relaxed">
              ScriptGenius partners gain access to cutting-edge technology, dedicated support, 
              and the opportunity to shape the future of creative writing tools.
            </p>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Innovation Leadership</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Stay ahead of the curve with early access to new features and technologies 
                  that are reshaping the entertainment industry.
                </p>
              </Card>
              
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Market Expansion</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Leverage our growing user base and industry connections to expand your reach 
                  and discover new business opportunities.
                </p>
              </Card>
              
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Dedicated Support</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Our partnership team provides personalized support, training, and resources 
                  to ensure your success with our platform.
                </p>
              </Card>
              
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Co-Innovation</h3>
                <p className="text-muted-foreground leading-relaxed">
                  Collaborate with our development team to create custom solutions and features 
                  that meet your specific industry needs.
                </p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Ready to <span className="gold-gradient">Collaborate</span>?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Let's discuss how a partnership with ScriptGenius can benefit your organization 
              and the creative community.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4">
                Schedule Partnership Call
              </Button>
              <Button size="lg" variant="outline" className="font-medium text-lg px-8 py-4">
                Download Partnership Guide
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Partners;
