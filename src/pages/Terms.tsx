import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { FileText, Shield, Users, Gavel, Calendar, AlertTriangle } from 'lucide-react';
const Terms = () => {
  return <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📄 Terms of Service
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Terms of <span className="gold-gradient">Service</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Clear, fair terms that protect both you and ScriptGenius while fostering a creative community.
            </p>
            
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Calendar className="h-5 w-5" />
              <span>Last updated: March 15, 2024</span>
            </div>
          </div>
        </div>
      </section>

      {/* Key Points */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Key <span className="gold-gradient">Points</span>
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="p-6 text-center border-green-500/20">
                <FileText className="h-8 w-8 mx-auto mb-3 text-green-500" />
                <h3 className="font-semibold mb-2">Your Content Rights</h3>
                <p className="text-sm text-muted-foreground">You retain full ownership of all creative work</p>
              </Card>
              
              <Card className="p-6 text-center border-blue-500/20">
                <Shield className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <h3 className="font-semibold mb-2">Fair Usage</h3>
                <p className="text-sm text-muted-foreground">Reasonable limits to ensure quality service</p>
              </Card>
              
              <Card className="p-6 text-center border-purple-500/20">
                <Users className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                <h3 className="font-semibold mb-2">Community Standards</h3>
                <p className="text-sm text-muted-foreground">Respectful environment for all creators</p>
              </Card>
              
              <Card className="p-6 text-center border-orange-500/20">
                <Gavel className="h-8 w-8 mx-auto mb-3 text-orange-500" />
                <h3 className="font-semibold mb-2">Dispute Resolution</h3>
                <p className="text-sm text-muted-foreground">Fair process for resolving any issues</p>
              </Card>
              
              <Card className="p-6 text-center border-red-500/20">
                <AlertTriangle className="h-8 w-8 mx-auto mb-3 text-red-500" />
                <h3 className="font-semibold mb-2">Service Limits</h3>
                <p className="text-sm text-muted-foreground">Clear boundaries and expectations</p>
              </Card>
              
              <Card className="p-6 text-center border-primary/20">
                <Shield className="h-8 w-8 mx-auto mb-3 text-primary" />
                <h3 className="font-semibold mb-2">Privacy Protection</h3>
                <p className="text-sm text-muted-foreground">Your data is protected and secure</p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Terms */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">1. Acceptance of Terms</h2>
              
              <p className="text-muted-foreground leading-relaxed mb-4">
                By accessing and using ScriptGenius, you accept and agree to be bound by these Terms of Service. 
                If you do not agree to these terms, please do not use our service.
              </p>
              
              <p className="text-muted-foreground leading-relaxed">
                These terms may be updated from time to time. We will notify you of significant changes via email 
                or through our platform. Continued use after changes constitutes acceptance of the new terms.
              </p>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">2. Your Content and Intellectual Property</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Ownership</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    You retain complete ownership of all scripts, characters, stories, and other creative content 
                    you create using ScriptGenius. We do not claim any ownership rights to your intellectual property.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">License to Operate</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    You grant us a limited license to store, process, and display your content solely for the purpose 
                    of providing our services. This includes generating AI suggestions, enabling collaboration features, 
                    and backing up your work.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">AI Training</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Your content is never used to train our AI models without explicit consent. We maintain strict 
                    separation between user content and AI training data to protect your creative work.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">3. Acceptable Use</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Permitted Uses</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Create, edit, and store original screenplays and creative content</li>
                    <li>Collaborate with other users on projects</li>
                    <li>Use AI assistance for writing and development</li>
                    <li>Export and share your completed work</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Prohibited Activities</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Uploading copyrighted material without permission</li>
                    <li>Creating content that violates laws or infringes rights</li>
                    <li>Attempting to breach security or reverse engineer our platform</li>
                    <li>Using the service for spam, harassment, or illegal activities</li>
                    <li>Sharing account credentials or circumventing usage limits</li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">4. Service Availability and Modifications</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Service Availability</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    We strive to maintain 99.9% uptime but cannot guarantee uninterrupted service. 
                    Scheduled maintenance will be announced in advance, and emergency maintenance 
                    will be communicated as soon as possible.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Feature Changes</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    We regularly update and improve ScriptGenius. While we aim to enhance existing features, 
                    we may occasionally need to modify or discontinue certain functionalities. 
                    Significant changes will be communicated with reasonable notice.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">5. Payment and Refunds</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Subscription Terms</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Subscriptions are billed in advance and automatically renew unless cancelled. 
                    You can cancel anytime through your account settings. Cancellation takes effect 
                    at the end of your current billing period.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Refund Policy</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    We offer a 30-day money-back guarantee for new subscriptions. Refunds for other 
                    situations will be considered on a case-by-case basis. Contact our support team 
                    to discuss any payment concerns.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">6. Limitation of Liability</h2>
              
              <p className="text-muted-foreground leading-relaxed mb-6">
                ScriptGenius provides tools to assist with creative writing but cannot guarantee specific outcomes. 
                We are not liable for any creative, commercial, or professional results from using our platform.
              </p>
              
              <p className="text-muted-foreground leading-relaxed">
                Our total liability for any claims related to our service will not exceed the amount you paid 
                for your subscription in the 12 months preceding the claim.
              </p>
            </Card>

            <Card className="cinema-card p-8 border-primary/20">
              <h2 className="text-3xl font-playfair font-bold mb-6">Questions About These Terms?</h2>
              
              <p className="text-muted-foreground leading-relaxed mb-6">
                If you have any questions about these Terms of Service, please don't hesitate to contact us. 
                We're committed to maintaining fair and transparent terms that work for everyone.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <span className="font-medium"><EMAIL></span>
                <span className="text-muted-foreground">•</span>
                <span className="text-muted-foreground">Response within 48 hours</span>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>;
};
export default Terms;