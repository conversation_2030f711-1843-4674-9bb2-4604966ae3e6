
import React from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

const AdminDashboard: React.FC = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-4">Admin Dashboard</h1>
          <p className="text-gray-300">Welcome to the ScriptGenius admin panel</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">Prompt Management</h3>
            <p className="text-gray-300 mb-4">Manage AI prompts and templates for the platform</p>
            <a 
              href="/super_admin/prompts" 
              className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
            >
              Manage Prompts
            </a>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">Blog Management</h3>
            <p className="text-gray-300 mb-4">Create and manage blog posts and articles</p>
            <a 
              href="/super_admin/blog" 
              className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
            >
              Manage Blog
            </a>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">User Analytics</h3>
            <p className="text-gray-300 mb-4">View user engagement and platform statistics</p>
            <a 
              href="/super_admin/analytics" 
              className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
            >
              View Analytics
            </a>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">System Health</h3>
            <p className="text-gray-300 mb-4">Monitor system performance and uptime</p>
            <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded opacity-50 cursor-not-allowed">
              Coming Soon
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
