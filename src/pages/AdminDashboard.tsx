
import React, { Suspense } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

// Lazy load admin components for better performance
const LazyAdminComponents = {
  PromptManagement: React.lazy(() => import('@/components/admin/PromptManagement')),
  BlogManagement: React.lazy(() => import('@/components/admin/BlogManagement')),
  BetaTestingDashboard: React.lazy(() => import('@/components/admin/BetaTestingDashboard')),
  UserManagement: React.lazy(() => import('@/components/admin/UserManagement'))
};

// Loading component for lazy-loaded admin sections
const AdminSectionLoader: React.FC = () => (
  <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700 animate-pulse">
    <div className="h-6 bg-cinema-700 rounded mb-2"></div>
    <div className="h-4 bg-cinema-700 rounded mb-4 w-3/4"></div>
    <div className="h-10 bg-cinema-700 rounded w-32"></div>
  </div>
);

const AdminDashboard: React.FC = () => {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-white mb-4">Admin Dashboard</h1>
          <p className="text-gray-300">Welcome to the ScriptGenius admin panel</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">Prompt Management</h3>
            <p className="text-gray-300 mb-4">Manage AI prompts and templates for the platform</p>
            <a 
              href="/super_admin/prompts" 
              className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
            >
              Manage Prompts
            </a>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">Blog Management</h3>
            <p className="text-gray-300 mb-4">Create and manage blog posts, categories, and content</p>
            <div className="flex flex-col space-y-2">
              <a
                href="/super_admin/blog"
                className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                Manage Blog
              </a>
              <p className="text-xs text-gray-400">
                Super Admin only - Create, edit, and publish blog posts
              </p>
            </div>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">User Analytics</h3>
            <p className="text-gray-300 mb-4">View user engagement and platform statistics</p>
            <a 
              href="/super_admin/analytics" 
              className="inline-flex items-center px-4 py-2 bg-gold-600 text-white rounded hover:bg-gold-700 transition-colors"
            >
              View Analytics
            </a>
          </div>
          
          <div className="bg-cinema-800 p-6 rounded-lg border border-cinema-700">
            <h3 className="text-xl font-semibold text-white mb-2">System Health</h3>
            <p className="text-gray-300 mb-4">Monitor system performance and uptime</p>
            <button className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded opacity-50 cursor-not-allowed">
              Coming Soon
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
