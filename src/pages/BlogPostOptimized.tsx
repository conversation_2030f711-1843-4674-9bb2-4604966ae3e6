
import React, { useEffect, useState, Suspense } from 'react';
import { useParams } from 'react-router-dom';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BlogPostHeader from '@/components/blog/BlogPostHeader';
import BlogPostMeta from '@/components/blog/BlogPostMeta';
import BlogPostContent from '@/components/blog/BlogPostContent';
import BlogPostTags from '@/components/blog/BlogPostTags';
import BlogPostLoading from '@/components/blog/BlogPostLoading';
import BlogPostNotFound from '@/components/blog/BlogPostNotFound';
import RelatedPostsSection from '@/components/blog/RelatedPostsSection';
import { getBlogPostBySlug, getRelatedPosts, type BlogPost } from '@/lib/blog/optimizedBlogData';
import { blogCache } from '@/utils/blogCache';

// Safe SEO import with fallback
let seo: any = {
  setMetadata: () => {},
  generateArticleData: () => ({}),
  addStructuredData: () => {},
  clearStructuredData: () => {}
};

// Load SEO module asynchronously with error handling
(async () => {
  try {
    const seoModule = await import('@/utils/seo');
    seo = seoModule.seo;
  } catch (error) {
    console.warn('SEO module failed to load, using fallback:', error);
  }
})();

const BlogPostOptimized = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (!slug) return;

    const loadPost = async () => {
      setLoading(true);
      
      try {
        // Check cache first
        const cacheKey = `blog-post-${slug}`;
        let cachedPost = blogCache.get<BlogPost>(cacheKey);
        
        if (!cachedPost) {
          cachedPost = await getBlogPostBySlug(slug);
          if (cachedPost) {
            blogCache.set(cacheKey, cachedPost, 10 * 60 * 1000); // Cache for 10 minutes
          }
        }
        
        setPost(cachedPost || null);
      } catch (error) {
        console.error('Failed to load blog post:', error);
        setPost(null);
      } finally {
        setLoading(false);
      }
    };

    loadPost();
  }, [slug]);
  
  useEffect(() => {
    if (post) {
      try {
        // Update SEO metadata for the blog post
        seo.setMetadata({
          title: post.seo.metaTitle || post.title,
          description: post.seo.metaDescription || post.excerpt,
          keywords: post.seo.keywords,
          author: post.author.name,
          type: 'article',
          canonical: `/blog/${post.slug}`,
          robots: 'index, follow'
        });

        // Add structured data for article
        const articleData = seo.generateArticleData({
          headline: post.title,
          description: post.excerpt,
          image: `${window.location.origin}/images/blog/${post.slug}.jpg`,
          datePublished: post.publishedAt,
          dateModified: post.updatedAt,
          author: {
            name: post.author.name,
            url: `${window.location.origin}/author/${post.author.name.toLowerCase().replace(' ', '-')}`
          },
          publisher: {
            name: 'ScriptGenius',
            logo: {
              url: `${window.location.origin}/images/logo.png`,
              width: 200,
              height: 60
            }
          }
        });
        
        seo.addStructuredData(articleData);
      } catch (error) {
        console.warn('Failed to set SEO data:', error);
      }
    }

    return () => {
      try {
        seo.clearStructuredData();
      } catch (error) {
        console.warn('Failed to clear SEO data:', error);
      }
    };
  }, [post]);

  if (loading) {
    return <BlogPostLoading />;
  }

  if (!post) {
    return <BlogPostNotFound />;
  }

  const relatedPosts = getRelatedPosts(post);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Article Content */}
      <article className="pt-24 pb-12">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <BlogPostHeader post={post} />
            <BlogPostMeta post={post} />
            <BlogPostContent post={post} />
            <BlogPostTags post={post} />
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <Suspense fallback={<div className="h-64 bg-muted/10 animate-pulse" />}>
          <RelatedPostsSection relatedPosts={relatedPosts} />
        </Suspense>
      )}

      <Footer />
    </div>
  );
};

export default BlogPostOptimized;
