
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ProductionToolsHero from '@/components/production-tools/ProductionToolsHero';
import WorkflowsSection from '@/components/production-tools/WorkflowsSection';
import ToolsShowcase from '@/components/production-tools/ToolsShowcase';
import BenefitsSection from '@/components/production-tools/BenefitsSection';
import ProductionToolsCTA from '@/components/production-tools/ProductionToolsCTA';

const ProductionTools = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard/production');
    } else {
      navigate('/auth');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <ProductionToolsHero user={user} onGetStarted={handleGetStarted} />
      <WorkflowsSection />
      <ToolsShowcase />
      <BenefitsSection />
      <ProductionToolsCTA user={user} onGetStarted={handleGetStarted} />

      <Footer />
    </div>
  );
};

export default ProductionTools;
