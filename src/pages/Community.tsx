
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CommunityHero from '@/components/community/CommunityHero';
import CommunityStats from '@/components/community/CommunityStats';
import CommunityFeatures from '@/components/community/CommunityFeatures';
import UpcomingEvents from '@/components/community/UpcomingEvents';
import FeaturedMembers from '@/components/community/FeaturedMembers';
import CommunityCTA from '@/components/community/CommunityCTA';

const Community = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleJoinCommunity = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/auth');
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <CommunityHero user={user} onJoinCommunity={handleJoinCommunity} />
      <CommunityStats />
      <CommunityFeatures />
      <UpcomingEvents />
      <FeaturedMembers />
      <CommunityCTA user={user} onJoinCommunity={handleJoinCommunity} />

      <Footer />
    </div>
  );
};

export default Community;
