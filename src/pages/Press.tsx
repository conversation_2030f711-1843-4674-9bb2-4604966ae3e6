
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, ExternalLink, Download, Mail } from 'lucide-react';

const Press = () => {
  const pressStories = [
    {
      title: "ScriptGenius Raises $5M Series A to Revolutionize Screenwriting with AI",
      publication: "TechCrunch",
      date: "March 15, 2024",
      excerpt: "Former journalist turned entrepreneur <PERSON> leads the charge in democratizing screenplay development with cutting-edge artificial intelligence tools.",
      link: "#",
      featured: true
    },
    {
      title: "How AI is Transforming Hollywood's Creative Process",
      publication: "The Hollywood Reporter",
      date: "February 28, 2024", 
      excerpt: "ScriptGenius emerges as a game-changer for screenwriters, offering intelligent assistance without replacing human creativity.",
      link: "#"
    },
    {
      title: "The Future of Screenwriting: An Interview with <PERSON>",
      publication: "Variety",
      date: "January 22, 2024",
      excerpt: "In an exclusive interview, <PERSON>riptGenius founder discusses how his journalism background shaped his vision for the future of storytelling technology.",
      link: "#"
    },
    {
      title: "ScriptGenius Partners with Major Studios for Writer Development",
      publication: "Deadline",
      date: "December 10, 2023",
      excerpt: "Platform announces partnerships with leading entertainment companies to provide professional development tools for emerging screenwriters.",
      link: "#"
    },
    {
      title: "From Newsroom to Writers' Room: The ScriptGenius Story",
      publication: "Forbes",
      date: "November 5, 2023",
      excerpt: "How a former news journalist's understanding of deadline-driven storytelling led to a breakthrough in creative writing technology.",
      link: "#"
    }
  ];

  const mediaKit = [
    { name: "Company Logo (PNG)", size: "2.3 MB" },
    { name: "Founder Headshot", size: "1.8 MB" },
    { name: "Product Screenshots", size: "4.2 MB" },
    { name: "Brand Guidelines", size: "1.1 MB" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📰 Press & Media
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              ScriptGenius in the <span className="gold-gradient">News</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Discover how ScriptGenius is making headlines and transforming the entertainment industry.
            </p>
          </div>
        </div>
      </section>

      {/* Featured Story */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-6 text-sm">Featured Story</Badge>
            
            <Card className="cinema-card p-12">
              <div className="grid md:grid-cols-3 gap-8">
                <div className="md:col-span-2">
                  <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
                    {pressStories[0].title}
                  </h2>
                  <div className="flex items-center gap-4 mb-6 text-muted-foreground">
                    <span className="font-medium">{pressStories[0].publication}</span>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {pressStories[0].date}
                    </div>
                  </div>
                  <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                    {pressStories[0].excerpt}
                  </p>
                  <Button variant="outline" className="group">
                    Read Full Article
                    <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-primary/5 p-6 rounded-lg">
                    <h3 className="font-semibold mb-2">Media Contact</h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      For press inquiries and interviews
                    </p>
                    <Button variant="outline" size="sm" className="w-full">
                      <Mail className="mr-2 h-4 w-4" />
                      Contact Press Team
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Press Coverage */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Recent <span className="gold-gradient">Coverage</span>
            </h2>
            
            <div className="space-y-6">
              {pressStories.slice(1).map((story, index) => (
                <Card key={index} className="cinema-card p-8 hover:scale-105 transition-all duration-300 group">
                  <div className="flex flex-col md:flex-row md:items-center justify-between">
                    <div className="flex-1 mb-4 md:mb-0">
                      <h3 className="text-xl md:text-2xl font-semibold mb-2 font-playfair group-hover:text-primary transition-colors">
                        {story.title}
                      </h3>
                      <div className="flex items-center gap-4 mb-3 text-muted-foreground">
                        <span className="font-medium">{story.publication}</span>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {story.date}
                        </div>
                      </div>
                      <p className="text-muted-foreground leading-relaxed">
                        {story.excerpt}
                      </p>
                    </div>
                    <div className="md:ml-8">
                      <Button variant="outline" size="sm" className="group-hover:bg-primary group-hover:text-primary-foreground">
                        Read More
                        <ExternalLink className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Media Kit */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Media <span className="gold-gradient">Kit</span>
            </h2>
            
            <Card className="cinema-card p-8">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Download Press Resources</h3>
                <p className="text-muted-foreground">
                  High-resolution logos, images, and brand guidelines for media use.
                </p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-4 mb-8">
                {mediaKit.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
                    <div>
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-muted-foreground">{item.size}</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              
              <div className="text-center">
                <Button size="lg" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  <Download className="mr-2 h-5 w-5" />
                  Download Complete Media Kit
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Press;
