
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { BetaTestingDashboard } from '@/components/admin/BetaTestingDashboard';
import { LoadingSpinner } from '@/components/LoadingSpinner';

const BetaAdmin: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Note: In a real implementation, you'd check if user is super admin
  // For now, we'll allow access to all authenticated users for demo purposes

  return (
    <div className="container mx-auto py-6">
      <BetaTestingDashboard />
    </div>
  );
};

export default BetaAdmin;
