
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

export default function Auth() {
  const [isLogin, setIsLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [resetMode, setResetMode] = useState(false);
  const { signIn, signUp, resetPassword } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || (!resetMode && !password)) {
      toast.error('Please fill in all fields');
      return;
    }

    setLoading(true);
    try {
      if (resetMode) {
        await resetPassword(email);
        setResetMode(false);
        toast.success('Password reset email sent! Check your inbox.');
      } else if (isLogin) {
        await signIn(email, password);
      } else {
        await signUp(email, password);
      }
    } catch (error: any) {
      // Additional error details for debugging
      console.error('Authentication error details:', error);
      if (error.message?.includes('Invalid login credentials')) {
        toast.error('Invalid email or password. Please check your credentials.');
      } else if (error.message?.includes('Email not confirmed')) {
        toast.error('Please check your email and click the confirmation link before signing in.');
      } else {
        toast.error(error.message || 'Authentication failed');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleModeSwitch = () => {
    setResetMode(false);
    setIsLogin(!isLogin);
    setEmail('');
    setPassword('');
  };

  const handleResetMode = () => {
    setResetMode(true);
    setPassword('');
  };

  const handleBackToLogin = () => {
    setResetMode(false);
    setEmail('');
    setPassword('');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>
            {resetMode ? 'Reset Password' : (isLogin ? 'Sign In' : 'Sign Up')}
          </CardTitle>
          <CardDescription>
            {resetMode 
              ? 'Enter your email to receive a password reset link.' 
              : (isLogin 
                ? 'Welcome back! Please sign in to your account.' 
                : 'Create a new account to get started.'
              )
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                type="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>
            {!resetMode && (
              <div>
                <Input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
            )}
            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? 'Loading...' : (
                resetMode ? 'Send Reset Email' : (isLogin ? 'Sign In' : 'Sign Up')
              )}
            </Button>
          </form>
          
          <div className="mt-4 space-y-2 text-center">
            {!resetMode ? (
              <>
                {isLogin && (
                  <Button
                    variant="link"
                    onClick={handleResetMode}
                    className="text-sm p-0"
                  >
                    Forgot your password?
                  </Button>
                )}
                <div>
                  <Button
                    variant="link"
                    onClick={handleModeSwitch}
                    className="text-sm"
                  >
                    {isLogin ? "Don't have an account? Sign up" : 'Already have an account? Sign in'}
                  </Button>
                </div>
              </>
            ) : (
              <Button
                variant="link"
                onClick={handleBackToLogin}
                className="text-sm"
              >
                Back to Sign In
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
