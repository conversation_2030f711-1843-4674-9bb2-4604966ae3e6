
import React, { useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BlogPostHeader from '@/components/blog/BlogPostHeader';
import BlogPostMeta from '@/components/blog/BlogPostMeta';
import BlogPostContent from '@/components/blog/BlogPostContent';
import BlogPostTags from '@/components/blog/BlogPostTags';
import RelatedPostsSection from '@/components/blog/RelatedPostsSection';
import { getBlogPostBySlug, getRelatedPosts } from '@/lib/blog/blogData';
import { seo } from '@/utils/seo';

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  
  const post = slug ? getBlogPostBySlug(slug) : undefined;
  
  useEffect(() => {
    if (post) {
      // Update SEO metadata for the blog post
      seo.setMetadata({
        title: post.seo.metaTitle || post.title,
        description: post.seo.metaDescription || post.excerpt,
        keywords: post.seo.keywords,
        author: post.author.name,
        type: 'article',
        canonical: `/blog/${post.slug}`,
        robots: 'index, follow'
      });

      // Add structured data for article
      const articleData = seo.generateArticleData({
        headline: post.title,
        description: post.excerpt,
        image: `${window.location.origin}/images/blog/${post.slug}.jpg`,
        datePublished: post.publishedAt,
        dateModified: post.updatedAt,
        author: {
          name: post.author.name,
          url: `${window.location.origin}/author/${post.author.name.toLowerCase().replace(' ', '-')}`
        },
        publisher: {
          name: 'ScriptGenius',
          logo: {
            url: `${window.location.origin}/images/logo.png`,
            width: 200,
            height: 60
          }
        }
      });
      
      seo.addStructuredData(articleData);
    }

    return () => {
      // Clean up structured data when component unmounts
      seo.clearStructuredData();
    };
  }, [post]);

  if (!post) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="pt-32 pb-16 text-center">
          <div className="container mx-auto px-6">
            <h1 className="text-4xl font-playfair font-bold mb-6">Post Not Found</h1>
            <p className="text-muted-foreground mb-8">The blog post you're looking for doesn't exist.</p>
            <Button onClick={() => navigate('/blog')}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Blog
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  const relatedPosts = getRelatedPosts(post);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Article */}
      <article className="pt-32 pb-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <BlogPostHeader post={post} />
            <BlogPostMeta post={post} />
            <BlogPostContent post={post} />
            <BlogPostTags post={post} />
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <RelatedPostsSection relatedPosts={relatedPosts} />
      )}

      <Footer />
    </div>
  );
};

export default BlogPost;
