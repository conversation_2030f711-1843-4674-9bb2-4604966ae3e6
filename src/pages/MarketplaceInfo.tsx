
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Star, DollarSign, Shield, Users, TrendingUp, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const MarketplaceInfo = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/auth');
    }
  };

  const features = [
    {
      icon: <Star className="h-6 w-6 text-yellow-500" />,
      title: "Curated Quality",
      description: "Every script goes through our professional review process to ensure industry standards."
    },
    {
      icon: <DollarSign className="h-6 w-6 text-green-500" />,
      title: "Fair Revenue Split",
      description: "Writers keep 90% of all sales. We only take 10% to maintain the platform."
    },
    {
      icon: <Shield className="h-6 w-6 text-blue-500" />,
      title: "Rights Protection",
      description: "Built-in copyright protection and secure transaction processing for all deals."
    },
    {
      icon: <Users className="h-6 w-6 text-purple-500" />,
      title: "Direct Producer Access",
      description: "Connect directly with verified producers, studios, and industry professionals."
    },
    {
      icon: <TrendingUp className="h-6 w-6 text-orange-500" />,
      title: "Market Insights",
      description: "Real-time analytics on what genres and themes are trending in the industry."
    },
    {
      icon: <CheckCircle className="h-6 w-6 text-teal-500" />,
      title: "Verified Buyers",
      description: "All producers and buyers are verified industry professionals with track records."
    }
  ];

  const genres = [
    "Drama", "Thriller", "Comedy", "Action", "Horror", "Sci-Fi", "Romance", "Documentary", "Animation", "Fantasy"
  ];

  const stats = [
    { number: "2,500+", label: "Scripts Available" },
    { number: "500+", label: "Verified Producers" },
    { number: "$2.5M+", label: "Total Sales Volume" },
    { number: "95%", label: "Writer Satisfaction" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🎬 Screenplay Marketplace
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Where <span className="gold-gradient">Stories</span> Meet
              <br />
              <span className="gold-gradient">Storytellers</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
              The premier destination for buying and selling screenplays. Connect directly with industry professionals, 
              showcase your work, and turn your scripts into productions.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group"
                onClick={handleGetStarted}
              >
                {user ? 'Browse Marketplace' : 'Get Started'}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4">
                Learn More
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-muted/20">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold gold-gradient mb-2">{stat.number}</div>
                <div className="text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Why Choose Our <span className="gold-gradient">Marketplace</span>?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Built by industry professionals, for industry professionals. Every feature designed to protect writers and facilitate genuine connections.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card 
                key={index} 
                className="cinema-card p-8 hover:scale-105 transition-all duration-300 group animate-fade-scale"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="mb-4 group-hover:scale-110 transition-transform">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 font-playfair">{feature.title}</h3>
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Genres Section */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Popular <span className="gold-gradient">Genres</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Discover scripts across all genres, from blockbuster action to intimate character studies.
            </p>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
            {genres.map((genre, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="text-lg px-6 py-3 hover:bg-primary hover:text-primary-foreground transition-colors cursor-pointer"
              >
                {genre}
              </Badge>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-accent rounded-full blur-3xl animate-float delay-200"></div>
        </div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-6xl font-playfair font-bold mb-6 animate-slide-up">
              Ready to <span className="gold-gradient">Showcase</span> Your Work?
            </h2>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-slide-up delay-100">
              Join thousands of writers who have already found success through our marketplace.
            </p>
            
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xl px-12 py-6 group animate-glow"
              onClick={handleGetStarted}
            >
              {user ? 'Access Marketplace' : 'Join Now'}
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default MarketplaceInfo;
