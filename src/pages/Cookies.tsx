
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, Settings, Shield, BarChart, Users, Zap, Calendar } from 'lucide-react';

const Cookies = () => {
  const cookieTypes = [
    {
      icon: <Shield className="h-6 w-6 text-green-500" />,
      type: "Essential Cookies",
      purpose: "Required for basic website functionality",
      description: "These cookies are necessary for the website to function and cannot be switched off. They include authentication, security, and basic functionality cookies.",
      examples: ["Session management", "Security tokens", "Load balancing"],
      canDisable: false,
      retention: "Session or up to 1 year"
    },
    {
      icon: <BarChart className="h-6 w-6 text-blue-500" />,
      type: "Analytics Cookies",
      purpose: "Help us understand how you use our website",
      description: "These cookies allow us to count visits and traffic sources to improve our website performance and user experience.",
      examples: ["Page views", "User journeys", "Feature usage"],
      canDisable: true,
      retention: "Up to 2 years"
    },
    {
      icon: <Users className="h-6 w-6 text-purple-500" />,
      type: "Marketing Cookies",
      purpose: "Used to deliver relevant advertisements",
      description: "These cookies track your activity across websites to deliver more relevant advertising and marketing content.",
      examples: ["Ad targeting", "Campaign tracking", "Social media integration"],
      canDisable: true,
      retention: "Up to 1 year"
    },
    {
      icon: <Zap className="h-6 w-6 text-orange-500" />,
      type: "Preference Cookies",
      purpose: "Remember your choices and settings",
      description: "These cookies remember your preferences and settings to provide a more personalized experience.",
      examples: ["Theme preferences", "Language settings", "Display options"],
      canDisable: true,
      retention: "Up to 1 year"
    }
  ];

  const thirdPartyServices = [
    {
      name: "Google Analytics",
      purpose: "Website analytics and performance monitoring",
      cookies: ["_ga", "_ga_*", "_gid"],
      retention: "2 years",
      link: "https://policies.google.com/privacy"
    },
    {
      name: "Stripe",
      purpose: "Payment processing and fraud prevention",
      cookies: ["__stripe_mid", "__stripe_sid"],
      retention: "1 year",
      link: "https://stripe.com/privacy"
    },
    {
      name: "Supabase",
      purpose: "Authentication and database services",
      cookies: ["sb-*"],
      retention: "Session",
      link: "https://supabase.com/privacy"
    },
    {
      name: "Intercom",
      purpose: "Customer support and messaging",
      cookies: ["intercom-*"],
      retention: "9 months",
      link: "https://www.intercom.com/legal/privacy"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🍪 Cookie Policy
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Cookie <span className="gold-gradient">Policy</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Learn about how we use cookies and similar technologies to enhance your ScriptGenius experience.
            </p>
            
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Calendar className="h-5 w-5" />
              <span>Last updated: March 15, 2024</span>
            </div>
          </div>
        </div>
      </section>

      {/* What are Cookies */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <div className="text-center mb-8">
                <Cookie className="h-16 w-16 mx-auto mb-4 text-primary" />
                <h2 className="text-3xl font-playfair font-bold mb-4">
                  What Are <span className="gold-gradient">Cookies</span>?
                </h2>
              </div>
              
              <p className="text-lg text-muted-foreground leading-relaxed mb-6">
                Cookies are small text files stored on your device when you visit websites. They help websites 
                remember information about your visit, which can both make it easier to visit the site again 
                and make the site more useful to you.
              </p>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Session Cookies</h3>
                  <p className="text-sm text-muted-foreground">Temporary cookies that expire when you close your browser</p>
                </div>
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Persistent Cookies</h3>
                  <p className="text-sm text-muted-foreground">Remain on your device for a set period or until deleted</p>
                </div>
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Third-Party Cookies</h3>
                  <p className="text-sm text-muted-foreground">Set by external services we use on our website</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Cookie Types */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Types of <span className="gold-gradient">Cookies</span> We Use
            </h2>
            
            <div className="space-y-6">
              {cookieTypes.map((cookie, index) => (
                <Card key={index} className="cinema-card p-8">
                  <div className="flex flex-col md:flex-row md:items-start gap-6">
                    <div className="flex items-center gap-4 md:w-1/3">
                      <div className="flex-shrink-0">
                        {cookie.icon}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold mb-1">{cookie.type}</h3>
                        <p className="text-sm text-muted-foreground">{cookie.purpose}</p>
                      </div>
                    </div>
                    
                    <div className="md:w-2/3">
                      <p className="text-muted-foreground mb-4">{cookie.description}</p>
                      
                      <div className="grid md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="font-medium">Examples:</span>
                          <ul className="list-disc list-inside text-muted-foreground mt-1">
                            {cookie.examples.map((example, i) => (
                              <li key={i}>{example}</li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <span className="font-medium">Control:</span>
                          <p className="text-muted-foreground mt-1">
                            {cookie.canDisable ? "Can be disabled" : "Always active"}
                          </p>
                        </div>
                        
                        <div>
                          <span className="font-medium">Retention:</span>
                          <p className="text-muted-foreground mt-1">{cookie.retention}</p>
                        </div>
                      </div>
                      
                      {cookie.canDisable && (
                        <div className="flex items-center justify-between mt-4 p-3 bg-muted/20 rounded-lg">
                          <span className="text-sm font-medium">Enable {cookie.type}</span>
                          <Switch defaultChecked />
                        </div>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Third-Party Services */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Third-Party <span className="gold-gradient">Services</span>
            </h2>
            
            <p className="text-lg text-muted-foreground text-center mb-12">
              We use carefully selected third-party services to enhance your experience. 
              Here's what cookies they may set:
            </p>
            
            <div className="space-y-6">
              {thirdPartyServices.map((service, index) => (
                <Card key={index} className="cinema-card p-6">
                  <div className="grid md:grid-cols-4 gap-4">
                    <div>
                      <h3 className="font-semibold mb-1">{service.name}</h3>
                      <p className="text-sm text-muted-foreground">{service.purpose}</p>
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium">Cookies:</span>
                      <div className="text-xs text-muted-foreground mt-1">
                        {service.cookies.map((cookie, i) => (
                          <div key={i} className="font-mono bg-muted/50 px-2 py-1 rounded mt-1">
                            {cookie}
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium">Retention:</span>
                      <p className="text-sm text-muted-foreground mt-1">{service.retention}</p>
                    </div>
                    
                    <div>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => window.open(service.link, '_blank')}
                      >
                        Privacy Policy
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Cookie Management */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Managing Your <span className="gold-gradient">Cookies</span>
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="cinema-card p-8">
                <div className="text-center mb-6">
                  <Settings className="h-12 w-12 mx-auto mb-4 text-primary" />
                  <h3 className="text-2xl font-semibold mb-2">Cookie Preferences</h3>
                  <p className="text-muted-foreground">
                    Control which cookies you accept through our preference center
                  </p>
                </div>
                
                <Button className="w-full mb-4">
                  Open Cookie Preferences
                </Button>
                
                <p className="text-sm text-muted-foreground text-center">
                  You can change your preferences at any time
                </p>
              </Card>
              
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-6">Browser Settings</h3>
                
                <p className="text-muted-foreground mb-4">
                  You can also control cookies through your browser settings:
                </p>
                
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Block all cookies (may affect functionality)</li>
                  <li>• Allow only first-party cookies</li>
                  <li>• Delete existing cookies</li>
                  <li>• Set cookies to be deleted when browser closes</li>
                </ul>
                
                <div className="mt-6 p-4 bg-amber-500/10 border border-amber-500/20 rounded-lg">
                  <p className="text-sm text-amber-700 dark:text-amber-300">
                    <strong>Note:</strong> Disabling essential cookies may prevent you from using core features of ScriptGenius.
                  </p>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Updates and Contact */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6 text-center">
                Updates and <span className="gold-gradient">Contact</span>
              </h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Policy Updates</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    We may update this Cookie Policy from time to time to reflect changes in our practices 
                    or for other operational, legal, or regulatory reasons. We will notify you of any 
                    material changes by posting the new policy on our website.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-4">Questions?</h3>
                  <p className="text-muted-foreground mb-4">
                    If you have any questions about our use of cookies or this Cookie Policy, 
                    please contact us:
                  </p>
                  <p className="font-medium"><EMAIL></p>
                </div>
              </div>
              
              <div className="mt-8 text-center">
                <Button variant="outline">
                  View Full Privacy Policy
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Cookies;
