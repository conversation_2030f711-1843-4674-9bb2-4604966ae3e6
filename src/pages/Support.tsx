import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import ContactForm from '@/components/ContactForm';
import { ArrowRight, MessageCircle, Mail, Phone, Clock, Search, ChevronDown, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Support = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const handleGetHelp = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/auth');
    }
  };

  const supportChannels = [
    {
      icon: <MessageCircle className="h-6 w-6 text-blue-500" />,
      title: "Live Chat",
      description: "Get instant help from our support team",
      availability: "24/7",
      responseTime: "< 2 minutes",
      action: "Start Chat"
    },
    {
      icon: <Mail className="h-6 w-6 text-green-500" />,
      title: "Email Support",
      description: "Send us detailed questions and get comprehensive answers",
      availability: "Business hours",
      responseTime: "< 4 hours",
      action: "Send Email",
      link: "mailto:<EMAIL>"
    },
    {
      icon: <Phone className="h-6 w-6 text-purple-500" />,
      title: "Phone Support",
      description: "Speak directly with our technical experts",
      availability: "Mon-Fri 9AM-6PM EST",
      responseTime: "Immediate",
      action: "Call Now"
    }
  ];

  const faqCategories = [
    {
      title: "Getting Started",
      questions: [
        {
          question: "How do I create my first screenplay?",
          answer: "Start by navigating to your dashboard and clicking 'New Project'. Our AI-powered setup wizard will guide you through choosing a template, setting up your characters, and beginning your first scene."
        },
        {
          question: "What screenplay formats are supported?",
          answer: "ScriptGenius supports all industry-standard formats including Feature Film, TV Episode, Short Film, and Documentary. Our formatting engine automatically applies the correct style guidelines."
        },
        {
          question: "Can I import existing scripts?",
          answer: "Yes! You can import scripts in .fdx (Final Draft), .fountain, .pdf, and .txt formats. Our AI will analyze and preserve your formatting while making it compatible with our tools."
        }
      ]
    },
    {
      title: "AI Collaboration",
      questions: [
        {
          question: "How does the AI writing assistant work?",
          answer: "Our AI assistant analyzes your script's context, character development, and story structure to provide intelligent suggestions for dialogue, scene development, and plot progression while maintaining your unique voice."
        },
        {
          question: "Will AI replace my creativity?",
          answer: "Absolutely not! Our AI is designed to enhance and support your creativity, not replace it. Think of it as an intelligent writing partner that helps you overcome writer's block and explore new creative possibilities."
        },
        {
          question: "Can I control how much AI assistance I receive?",
          answer: "Yes, you have complete control. You can adjust AI assistance levels from minimal suggestions to more comprehensive support, or turn it off entirely and use traditional writing tools."
        }
      ]
    },
    {
      title: "Account & Billing",
      questions: [
        {
          question: "What's included in the free plan?",
          answer: "The free plan includes basic screenplay writing tools, limited AI assistance, and access to our community forums. You can create up to 3 projects with standard formatting features."
        },
        {
          question: "How do I upgrade my subscription?",
          answer: "Go to your account settings and click 'Upgrade Plan'. You can switch between plans at any time, and any unused time from your current plan will be credited to your new subscription."
        },
        {
          question: "Can I cancel my subscription anytime?",
          answer: "Yes, you can cancel your subscription at any time. Your account will remain active until the end of your current billing period, and you'll retain access to all your projects."
        }
      ]
    }
  ];

  const helpResources = [
    {
      title: "Video Tutorials",
      description: "Step-by-step video guides for all features",
      count: "50+ videos",
      icon: "🎥"
    },
    {
      title: "Knowledge Base",
      description: "Comprehensive articles and how-to guides",
      count: "200+ articles",
      icon: "📚"
    },
    {
      title: "Community Forum",
      description: "Get help from other writers and experts",
      count: "25k+ members",
      icon: "👥"
    },
    {
      title: "Webinars",
      description: "Live training sessions and Q&A",
      count: "Weekly sessions",
      icon: "📡"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🤝 Support Center
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              We're Here to <span className="gold-gradient">Help</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
              Get the support you need to make the most of ScriptGenius. Our team of experts 
              is ready to help you succeed in your screenwriting journey.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
              <div className="relative max-w-md mx-auto">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <input 
                  type="text" 
                  placeholder="Search for help..."
                  className="w-full pl-10 pr-4 py-3 border border-border rounded-lg bg-background"
                />
              </div>
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-8 py-3"
                onClick={handleGetHelp}
              >
                {user ? 'Get Help Now' : 'Sign In for Support'}
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Support Channels */}
      <section className="py-16 bg-muted/20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
              Choose Your <span className="gold-gradient">Support Channel</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Multiple ways to get the help you need, when you need it.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {supportChannels.map((channel, index) => (
              <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300 group">
                <div className="mb-6 group-hover:scale-110 transition-transform">
                  {channel.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 font-playfair">{channel.title}</h3>
                <p className="text-muted-foreground mb-4">{channel.description}</p>
                
                <div className="space-y-2 mb-6 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Availability:</span>
                    <Badge variant="outline">{channel.availability}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Response time:</span>
                    <span className="font-medium">{channel.responseTime}</span>
                  </div>
                </div>
                
                <Button 
                  className="w-full group-hover:bg-primary group-hover:text-primary-foreground"
                  asChild={!!channel.link}
                >
                  {channel.link ? (
                    <a href={channel.link}>
                      {channel.action}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </a>
                  ) : (
                    <>
                      {channel.action}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Frequently Asked <span className="gold-gradient">Questions</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Quick answers to the most common questions about ScriptGenius.
            </p>
          </div>
          
          <div className="max-w-4xl mx-auto space-y-8">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex}>
                <h3 className="text-2xl font-semibold mb-6 font-playfair">{category.title}</h3>
                <div className="space-y-4">
                  {category.questions.map((faq, faqIndex) => {
                    const globalIndex = categoryIndex * 10 + faqIndex;
                    const isExpanded = expandedFaq === globalIndex;
                    
                    return (
                      <Card key={faqIndex} className="cinema-card overflow-hidden">
                        <button
                          className="w-full p-6 text-left flex items-center justify-between hover:bg-muted/50 transition-colors"
                          onClick={() => setExpandedFaq(isExpanded ? null : globalIndex)}
                        >
                          <span className="font-medium pr-4">{faq.question}</span>
                          {isExpanded ? (
                            <ChevronDown className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                          ) : (
                            <ChevronRight className="h-5 w-5 text-muted-foreground flex-shrink-0" />
                          )}
                        </button>
                        {isExpanded && (
                          <div className="px-6 pb-6 pt-0">
                            <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                          </div>
                        )}
                      </Card>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Help Resources */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Self-Help <span className="gold-gradient">Resources</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Explore our comprehensive collection of learning materials and community resources.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {helpResources.map((resource, index) => (
              <Card 
                key={index} 
                className="cinema-card p-6 text-center hover:scale-105 transition-all duration-300 group cursor-pointer"
                onClick={() => {
                  if (resource.title === 'Community Forum') {
                    navigate('/community');
                  } else if (resource.title === 'Video Tutorials') {
                    navigate('/tutorials');
                  } else if (resource.title === 'Knowledge Base') {
                    navigate('/documentation');
                  }
                }}
              >
                <div className="text-4xl mb-4">{resource.icon}</div>
                <h3 className="text-lg font-semibold mb-2 font-playfair">{resource.title}</h3>
                <p className="text-muted-foreground text-sm mb-3">{resource.description}</p>
                <Badge variant="outline" className="mb-4">{resource.count}</Badge>
                <Button variant="ghost" size="sm" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                  Explore
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
                Still Need <span className="gold-gradient">Help?</span>
              </h2>
              <p className="text-xl text-muted-foreground">
                Send us a message and we'll get back to you as soon as possible.
              </p>
            </div>
            
            <ContactForm />
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Support;
