
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Lock, Server, Eye, AlertTriangle, CheckCircle, Users, Zap } from 'lucide-react';

const Security = () => {
  const securityFeatures = [
    {
      icon: <Lock className="h-8 w-8 text-blue-500" />,
      title: "End-to-End Encryption",
      description: "All data is encrypted in transit and at rest using AES-256 encryption"
    },
    {
      icon: <Server className="h-8 w-8 text-green-500" />,
      title: "Secure Infrastructure",
      description: "Hosted on enterprise-grade cloud infrastructure with 24/7 monitoring"
    },
    {
      icon: <Eye className="h-8 w-8 text-purple-500" />,
      title: "Privacy by Design",
      description: "Built with privacy principles from the ground up, not as an afterthought"
    },
    {
      icon: <Users className="h-8 w-8 text-orange-500" />,
      title: "Access Controls",
      description: "Granular permissions and multi-factor authentication support"
    },
    {
      icon: <Shield className="h-8 w-8 text-red-500" />,
      title: "Regular Audits",
      description: "Quarterly security assessments and penetration testing"
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-500" />,
      title: "Incident Response",
      description: "24/7 security team with rapid incident response protocols"
    }
  ];

  const certifications = [
    { name: "SOC 2 Type II", status: "Certified", description: "Annual third-party security audit" },
    { name: "ISO 27001", status: "In Progress", description: "International security management standard" },
    { name: "GDPR Compliant", status: "Certified", description: "European data protection compliance" },
    { name: "CCPA Compliant", status: "Certified", description: "California privacy rights compliance" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🔐 Security
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Enterprise-Grade <span className="gold-gradient">Security</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Your creative work is protected by industry-leading security measures and best practices.
            </p>
          </div>
        </div>
      </section>

      {/* Security Features */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Security <span className="gold-gradient">Features</span>
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {securityFeatures.map((feature, index) => (
                <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300 group">
                  <div className="mb-6 group-hover:scale-110 transition-transform">
                    {feature.icon}
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3 font-playfair">{feature.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    {feature.description}
                  </p>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Data Protection */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              How We Protect Your <span className="gold-gradient">Data</span>
            </h2>
            
            <div className="space-y-8">
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Encryption at Every Level</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-2">Data in Transit</h4>
                    <p className="text-muted-foreground">All communication between your device and our servers uses TLS 1.3 encryption, the latest and most secure protocol available.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Data at Rest</h4>
                    <p className="text-muted-foreground">Your files are encrypted using AES-256 encryption before being stored in our secure databases and file systems.</p>
                  </div>
                </div>
              </Card>

              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Infrastructure Security</h3>
                <div className="space-y-4">
                  <div className="grid md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                      <h4 className="font-semibold">Secure Data Centers</h4>
                      <p className="text-sm text-muted-foreground">SOC 2 certified facilities with biometric access controls</p>
                    </div>
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                      <h4 className="font-semibold">Network Security</h4>
                      <p className="text-sm text-muted-foreground">Advanced firewalls and intrusion detection systems</p>
                    </div>
                    <div className="text-center">
                      <CheckCircle className="h-12 w-12 mx-auto mb-2 text-green-500" />
                      <h4 className="font-semibold">Backup & Recovery</h4>
                      <p className="text-sm text-muted-foreground">Automated daily backups with rapid disaster recovery</p>
                    </div>
                  </div>
                </div>
              </Card>

              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Access Management</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold mb-3">User Controls</h4>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1">
                      <li>Two-factor authentication (2FA)</li>
                      <li>Single sign-on (SSO) support</li>
                      <li>Session management and timeout</li>
                      <li>Device and location monitoring</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-3">Internal Security</h4>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1">
                      <li>Principle of least privilege</li>
                      <li>Role-based access controls</li>
                      <li>Regular access reviews</li>
                      <li>Employee security training</li>
                    </ul>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Compliance */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Compliance & <span className="gold-gradient">Certifications</span>
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              {certifications.map((cert, index) => (
                <Card key={index} className="cinema-card p-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-xl font-semibold">{cert.name}</h3>
                    <Badge 
                      variant={cert.status === 'Certified' ? 'default' : 'outline'}
                      className={cert.status === 'Certified' ? 'bg-green-500' : ''}
                    >
                      {cert.status}
                    </Badge>
                  </div>
                  <p className="text-muted-foreground">{cert.description}</p>
                </Card>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <p className="text-muted-foreground mb-4">
                We maintain the highest security standards and regularly update our certifications.
              </p>
              <Button variant="outline">
                View Security Documentation
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Incident Response */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8 border-orange-500/20">
              <div className="text-center mb-8">
                <AlertTriangle className="h-16 w-16 mx-auto mb-4 text-orange-500" />
                <h2 className="text-3xl font-playfair font-bold mb-4">
                  Security Incident Response
                </h2>
                <p className="text-muted-foreground">
                  In the unlikely event of a security incident, here's what we do:
                </p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4">Immediate Response</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Incident detected within minutes</li>
                    <li>Automatic containment measures activated</li>
                    <li>24/7 security team mobilized</li>
                    <li>Affected systems isolated if necessary</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-4">Communication</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Affected users notified within 2 hours</li>
                    <li>Regular updates provided during resolution</li>
                    <li>Detailed post-incident report published</li>
                    <li>Preventive measures implemented</li>
                  </ul>
                </div>
              </div>
              
              <div className="mt-8 text-center">
                <Button variant="outline">
                  View Our Security Status Page
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Security Contact */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-8">
              Security <span className="gold-gradient">Questions</span>?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8">
              Have concerns about security or found a potential vulnerability? We want to hear from you.
            </p>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="p-8">
                <h3 className="text-xl font-semibold mb-4">General Security Questions</h3>
                <p className="text-muted-foreground mb-4">
                  For questions about our security practices, compliance, or policies.
                </p>
                <Button variant="outline" className="w-full">
                  Contact Security Team
                </Button>
              </Card>
              
              <Card className="p-8 border-red-500/20">
                <h3 className="text-xl font-semibold mb-4">Report a Vulnerability</h3>
                <p className="text-muted-foreground mb-4">
                  Found a security issue? We have a responsible disclosure program.
                </p>
                <Button className="w-full bg-red-500 hover:bg-red-600">
                  Report Vulnerability
                </Button>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Security;
