
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Play, Clock, Users, Star, BookOpen } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Tutorials = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/auth');
    }
  };

  const tutorialCategories = [
    {
      title: "Getting Started",
      description: "Essential tutorials for new users",
      color: "bg-blue-500",
      count: 8
    },
    {
      title: "Writing Techniques",
      description: "Master the craft of screenwriting",
      color: "bg-green-500",
      count: 12
    },
    {
      title: "AI Collaboration",
      description: "Work effectively with AI tools",
      color: "bg-purple-500",
      count: 6
    },
    {
      title: "Production Tools",
      description: "Manage your production workflow",
      color: "bg-orange-500",
      count: 10
    }
  ];

  const featuredTutorials = [
    {
      title: "Your First Screenplay in 30 Minutes",
      description: "Learn the basics of formatting and structure with our AI-powered tools",
      duration: "32 min",
      level: "Beginner",
      views: "15.2k",
      rating: 4.9,
      thumbnail: "🎬",
      category: "Getting Started"
    },
    {
      title: "Advanced Character Development with AI",
      description: "Create compelling characters using our AI character development tools",
      duration: "45 min",
      level: "Intermediate",
      views: "8.7k",
      rating: 4.8,
      thumbnail: "👥",
      category: "Writing Techniques"
    },
    {
      title: "Production Scheduling Masterclass",
      description: "Optimize your production schedule using our advanced scheduling tools",
      duration: "1h 15min",
      level: "Advanced",
      views: "5.3k",
      rating: 4.9,
      thumbnail: "📅",
      category: "Production Tools"
    },
    {
      title: "AI Writing Assistant Deep Dive",
      description: "Maximize your creative potential with AI collaboration techniques",
      duration: "38 min",
      level: "Intermediate",
      views: "12.1k",
      rating: 4.7,
      thumbnail: "🤖",
      category: "AI Collaboration"
    },
    {
      title: "From Script to Screen: Complete Workflow",
      description: "End-to-end tutorial covering the entire filmmaking process",
      duration: "2h 30min",
      level: "Advanced",
      views: "3.9k",
      rating: 5.0,
      thumbnail: "🎭",
      category: "Production Tools"
    },
    {
      title: "Collaborative Writing with Teams",
      description: "Learn how to collaborate effectively with writing teams and AI",
      duration: "28 min",
      level: "Beginner",
      views: "9.4k",
      rating: 4.6,
      thumbnail: "✍️",
      category: "AI Collaboration"
    }
  ];

  const learningPaths = [
    {
      title: "Complete Beginner",
      description: "Start your screenwriting journey",
      tutorials: 15,
      duration: "6 hours",
      badge: "Fundamentals"
    },
    {
      title: "AI-Powered Writer",
      description: "Master AI collaboration techniques",
      tutorials: 12,
      duration: "4.5 hours",
      badge: "Innovation"
    },
    {
      title: "Production Manager",
      description: "Learn production workflows",
      tutorials: 18,
      duration: "8 hours",
      badge: "Professional"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🎓 Video Tutorials
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Learn by <span className="gold-gradient">Watching</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
              Step-by-step video tutorials covering everything from basic screenwriting to advanced 
              production management and AI collaboration.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group"
                onClick={handleGetStarted}
              >
                Start Learning
                <Play className="ml-2 h-5 w-5 group-hover:scale-110 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4">
                Browse All Tutorials
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-16 bg-muted/20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
              Tutorial <span className="gold-gradient">Categories</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Organized learning paths to help you master specific skills.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {tutorialCategories.map((category, index) => (
              <Card key={index} className="cinema-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                <div className={`w-16 h-16 ${category.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <BookOpen className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-2 font-playfair">{category.title}</h3>
                <p className="text-muted-foreground mb-4">{category.description}</p>
                <Badge variant="outline" className="mb-4">{category.count} tutorials</Badge>
                <Button variant="ghost" size="sm" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                  Explore
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Tutorials */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Featured <span className="gold-gradient">Tutorials</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Our most popular and highly-rated tutorials to accelerate your learning.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredTutorials.map((tutorial, index) => (
              <Card 
                key={index} 
                className="cinema-card overflow-hidden hover:scale-105 transition-all duration-300 group animate-fade-scale"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="aspect-video bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center relative overflow-hidden">
                  <span className="text-6xl">{tutorial.thumbnail}</span>
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button size="lg" className="rounded-full w-16 h-16 p-0">
                      <Play className="h-6 w-6" />
                    </Button>
                  </div>
                </div>
                
                <div className="p-6">
                  <Badge variant="outline" className="mb-3">{tutorial.category}</Badge>
                  <h3 className="text-xl font-semibold mb-2 font-playfair">{tutorial.title}</h3>
                  <p className="text-muted-foreground mb-4 text-sm leading-relaxed">
                    {tutorial.description}
                  </p>
                  
                  <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {tutorial.duration}
                      </span>
                      <span className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {tutorial.views}
                      </span>
                    </div>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 mr-1 fill-yellow-400 text-yellow-400" />
                      {tutorial.rating}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Badge variant={tutorial.level === 'Beginner' ? 'default' : tutorial.level === 'Intermediate' ? 'secondary' : 'outline'}>
                      {tutorial.level}
                    </Badge>
                    <Button variant="ghost" size="sm">
                      Watch Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Learning Paths */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Structured <span className="gold-gradient">Learning Paths</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Follow curated learning paths designed to take you from beginner to expert.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {learningPaths.map((path, index) => (
              <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300 group">
                <Badge className="mb-4">{path.badge}</Badge>
                <h3 className="text-2xl font-semibold mb-3 font-playfair">{path.title}</h3>
                <p className="text-muted-foreground mb-6">{path.description}</p>
                
                <div className="space-y-2 mb-6 text-sm">
                  <div className="flex justify-between">
                    <span>Tutorials:</span>
                    <span className="font-medium">{path.tutorials}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duration:</span>
                    <span className="font-medium">{path.duration}</span>
                  </div>
                </div>
                
                <Button className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                  Start Path
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Tutorials;
