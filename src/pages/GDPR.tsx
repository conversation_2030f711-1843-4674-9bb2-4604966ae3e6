
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Shield, Download, Eye, UserCheck, Lock, Globe, Calendar, Mail } from 'lucide-react';

const GDPR = () => {
  const gdprRights = [
    {
      icon: <Eye className="h-6 w-6 text-blue-500" />,
      title: "Right to Access",
      description: "Request a copy of all personal data we hold about you",
      action: "Download your data anytime from account settings"
    },
    {
      icon: <UserCheck className="h-6 w-6 text-green-500" />,
      title: "Right to Rectification",
      description: "Correct any inaccurate or incomplete personal data",
      action: "Update information directly in your profile"
    },
    {
      icon: <Download className="h-6 w-6 text-purple-500" />,
      title: "Right to Portability",
      description: "Export your data in a structured, machine-readable format",
      action: "Export feature available in account settings"
    },
    {
      icon: <Lock className="h-6 w-6 text-orange-500" />,
      title: "Right to Erasure",
      description: "Request deletion of your personal data",
      action: "Delete account option with complete data removal"
    },
    {
      icon: <Shield className="h-6 w-6 text-red-500" />,
      title: "Right to Restrict Processing",
      description: "Limit how we process your personal data",
      action: "Contact support to discuss processing limitations"
    },
    {
      icon: <Globe className="h-6 w-6 text-teal-500" />,
      title: "Right to Object",
      description: "Object to processing for specific purposes",
      action: "Manage preferences in privacy settings"
    }
  ];

  const dataProcessingBases = [
    {
      basis: "Contractual Necessity",
      description: "Processing required to provide ScriptGenius services",
      examples: ["Account management", "Content storage", "Feature delivery"]
    },
    {
      basis: "Legitimate Interest",
      description: "Processing for legitimate business interests",
      examples: ["Service improvement", "Security monitoring", "Bug fixes"]
    },
    {
      basis: "Consent",
      description: "Processing based on your explicit consent",
      examples: ["Marketing communications", "Optional features", "Data sharing"]
    },
    {
      basis: "Legal Obligation",
      description: "Processing required by law",
      examples: ["Tax compliance", "Fraud prevention", "Legal requests"]
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🇪🇺 GDPR Compliance
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              GDPR <span className="gold-gradient">Compliance</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              We fully comply with the General Data Protection Regulation, ensuring your privacy rights are protected under European law.
            </p>
            
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Calendar className="h-5 w-5" />
              <span>GDPR compliance since May 25, 2018</span>
            </div>
          </div>
        </div>
      </section>

      {/* Your Rights */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Your <span className="gold-gradient">Rights</span> Under GDPR
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {gdprRights.map((right, index) => (
                <Card key={index} className="cinema-card p-6 hover:scale-105 transition-all duration-300 group">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="flex-shrink-0 group-hover:scale-110 transition-transform">
                      {right.icon}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold mb-2">{right.title}</h3>
                      <p className="text-muted-foreground text-sm mb-3">{right.description}</p>
                      <p className="text-xs font-medium text-primary">{right.action}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Legal Basis */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Legal Basis for <span className="gold-gradient">Processing</span>
            </h2>
            
            <p className="text-lg text-muted-foreground text-center mb-12">
              Under GDPR, we must have a legal basis for processing your personal data. Here are the bases we rely on:
            </p>
            
            <div className="space-y-6">
              {dataProcessingBases.map((basis, index) => (
                <Card key={index} className="cinema-card p-6">
                  <div className="grid md:grid-cols-3 gap-6">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">{basis.basis}</h3>
                      <p className="text-muted-foreground">{basis.description}</p>
                    </div>
                    <div className="md:col-span-2">
                      <h4 className="font-semibold mb-2">Examples:</h4>
                      <ul className="list-disc list-inside text-muted-foreground space-y-1">
                        {basis.examples.map((example, i) => (
                          <li key={i}>{example}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Data Protection */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              How We <span className="gold-gradient">Protect</span> Your Data
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-6">Technical Measures</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>End-to-end encryption for all sensitive data</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>Regular security audits and penetration testing</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>Automated backup and disaster recovery systems</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                    <span>Access controls and monitoring systems</span>
                  </li>
                </ul>
              </Card>
              
              <Card className="cinema-card p-8">
                <h3 className="text-2xl font-semibold mb-6">Organizational Measures</h3>
                <ul className="space-y-3 text-muted-foreground">
                  <li className="flex items-start gap-3">
                    <UserCheck className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <span>Regular staff training on data protection</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <UserCheck className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <span>Strict data access policies and procedures</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <UserCheck className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <span>Data Protection Impact Assessments (DPIAs)</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <UserCheck className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
                    <span>Incident response and breach notification procedures</span>
                  </li>
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Data Retention */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6 text-center">
                Data <span className="gold-gradient">Retention</span>
              </h2>
              
              <p className="text-muted-foreground text-center mb-8">
                We only keep your personal data for as long as necessary to provide our services or as required by law.
              </p>
              
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Account Data</h3>
                  <p className="text-muted-foreground text-sm">Kept while your account is active, plus 30 days after deletion</p>
                </div>
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Creative Content</h3>
                  <p className="text-muted-foreground text-sm">Kept according to your settings, with full control over deletion</p>
                </div>
                <div className="text-center">
                  <h3 className="font-semibold mb-2">Analytics Data</h3>
                  <p className="text-muted-foreground text-sm">Anonymized after 24 months, aggregated insights may be kept longer</p>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* International Transfers */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">
                International <span className="gold-gradient">Data Transfers</span>
              </h2>
              
              <p className="text-muted-foreground leading-relaxed mb-6">
                ScriptGenius primarily stores and processes data within the European Economic Area (EEA). 
                When we do transfer data outside the EEA, we ensure appropriate safeguards are in place:
              </p>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-3">Transfer Mechanisms</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Standard Contractual Clauses (SCCs)</li>
                    <li>Adequacy decisions by the European Commission</li>
                    <li>Binding Corporate Rules where applicable</li>
                    <li>Explicit consent for specific transfers</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold mb-3">Current Locations</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Primary servers: Ireland (EU)</li>
                    <li>Backup servers: Germany (EU)</li>
                    <li>CDN endpoints: Global (with safeguards)</li>
                    <li>Support services: EU and UK only</li>
                  </ul>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Exercise Your Rights */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-8">
              Exercise Your <span className="gold-gradient">Rights</span>
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8">
              You can exercise most of your GDPR rights directly through your account settings. 
              For other requests, contact our Data Protection Officer.
            </p>
            
            <div className="grid md:grid-cols-2 gap-6">
              <Card className="p-8">
                <h3 className="text-xl font-semibold mb-4">Self-Service Options</h3>
                <p className="text-muted-foreground mb-4">
                  Access, update, export, or delete your data directly from your account.
                </p>
                <Button className="w-full">
                  Go to Account Settings
                </Button>
              </Card>
              
              <Card className="p-8 border-primary/20">
                <h3 className="text-xl font-semibold mb-4">Contact Our DPO</h3>
                <p className="text-muted-foreground mb-4">
                  For complex requests or questions about your rights under GDPR.
                </p>
                <div className="flex items-center justify-center gap-2 mb-4">
                  <Mail className="h-5 w-5 text-primary" />
                  <span className="font-medium"><EMAIL></span>
                </div>
                <Button variant="outline" className="w-full">
                  Contact DPO
                </Button>
              </Card>
            </div>
            
            <p className="text-sm text-muted-foreground mt-6">
              We will respond to all GDPR requests within 30 days, or 60 days for complex requests.
            </p>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default GDPR;
