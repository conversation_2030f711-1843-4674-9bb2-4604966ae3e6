
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Twitter, Film, Newspaper, Code } from 'lucide-react';

const About = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🎬 About ScriptGenius
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Revolutionizing <span className="gold-gradient">Screenwriting</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
              Born from a passion for storytelling and powered by cutting-edge technology, 
              ScriptGenius is transforming how stories come to life.
            </p>
          </div>
        </div>
      </section>

      {/* Founder Section */}
      <section className="py-16 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-12">
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-4xl font-playfair font-bold mb-6">
                    Meet the <span className="gold-gradient">Founder</span>
                  </h2>
                  
                  <div className="space-y-4 text-lg text-muted-foreground">
                    <p>
                      <strong className="text-foreground">Ryan Fortune</strong> is the visionary behind ScriptGenius, 
                      bringing together decades of experience across journalism, entertainment, and technology.
                    </p>
                    
                    <p>
                      As a former news journalist, Ryan honed his ability to craft compelling narratives under tight deadlines. 
                      His transition into film and television as a screenwriter and producer gave him firsthand insight into 
                      the challenges writers face in bringing their stories to life.
                    </p>
                    
                    <p>
                      Now, as a software developer and consultant, Ryan combines his storytelling expertise with 
                      cutting-edge technology to create tools that empower writers at every stage of their journey.
                    </p>
                  </div>
                  
                  <div className="mt-8">
                    <Button 
                      variant="outline" 
                      className="group"
                      onClick={() => window.open('https://twitter.com/ryanfortune_sa', '_blank')}
                    >
                      <Twitter className="h-5 w-5 mr-2 group-hover:text-blue-500 transition-colors" />
                      Follow @ryanfortune_sa
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-6">
                  {/* Ryan Fortune Profile Picture */}
                  <div className="mb-6">
                    <img 
                      src="/lovable-uploads/c2f32224-154f-42ea-a06d-b68439b9cb81.png"
                      alt="Ryan Fortune, Founder of ScriptGenius"
                      className="w-80 h-64 object-cover rounded-lg shadow-lg mx-auto"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="p-6 text-center border-primary/20">
                      <Newspaper className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                      <h3 className="font-semibold mb-2">Journalist</h3>
                      <p className="text-sm text-muted-foreground">News reporting & storytelling</p>
                    </Card>
                    
                    <Card className="p-6 text-center border-primary/20">
                      <Film className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                      <h3 className="font-semibold mb-2">Screenwriter</h3>
                      <p className="text-sm text-muted-foreground">Film & TV producer</p>
                    </Card>
                  </div>
                  
                  <Card className="p-6 text-center border-primary/20">
                    <Code className="h-8 w-8 mx-auto mb-3 text-green-500" />
                    <h3 className="font-semibold mb-2">Developer</h3>
                    <p className="text-sm text-muted-foreground">Software consultant & innovator</p>
                  </Card>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-8">
              Our <span className="gold-gradient">Mission</span>
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              We believe every great story deserves to be told. Our mission is to democratize screenwriting 
              by providing powerful, intuitive tools that help writers focus on what they do best—creating 
              compelling characters and unforgettable stories.
            </p>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card className="p-8 text-center border-primary/20">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Innovation</h3>
                <p className="text-muted-foreground">
                  Pushing the boundaries of what's possible in creative writing technology.
                </p>
              </Card>
              
              <Card className="p-8 text-center border-accent/20">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Community</h3>
                <p className="text-muted-foreground">
                  Building a supportive ecosystem where writers can learn, grow, and succeed together.
                </p>
              </Card>
              
              <Card className="p-8 text-center border-primary/20">
                <h3 className="text-2xl font-semibold mb-4 font-playfair">Excellence</h3>
                <p className="text-muted-foreground">
                  Delivering professional-grade tools that meet the highest industry standards.
                </p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default About;
