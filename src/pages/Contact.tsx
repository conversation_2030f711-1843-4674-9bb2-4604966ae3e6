import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactForm from '@/components/ContactForm';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Mail, MessageCircle, Phone, MapPin, Clock } from 'lucide-react';

const Contact = () => {
  const contactMethods = [
    {
      icon: <Mail className="h-6 w-6 text-blue-500" />,
      title: "Email Support",
      description: "Get help with your account or technical issues",
      contact: "<EMAIL>",
      response: "Within 24 hours"
    },
    {
      icon: <MessageCircle className="h-6 w-6 text-green-500" />,
      title: "Live Chat",
      description: "Chat with our support team in real-time",
      contact: "Available in-app",
      response: "Immediate"
    },
    {
      icon: <Phone className="h-6 w-6 text-purple-500" />,
      title: "Phone Support",
      description: "Speak directly with our technical team",
      contact: "+****************",
      response: "Business hours"
    }
  ];

  const officeInfo = [
    {
      location: "San Francisco, CA",
      address: "123 Creative Avenue, Suite 100\nSan Francisco, CA 94102",
      hours: "Mon-Fri: 9:00 AM - 6:00 PM PST"
    },
    {
      location: "Austin, TX", 
      address: "456 Innovation Drive, Floor 3\nAustin, TX 78701",
      hours: "Mon-Fri: 9:00 AM - 6:00 PM CST"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📞 Get in Touch
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Contact <span className="gold-gradient">ScriptGenius</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Have questions, feedback, or need support? We're here to help you succeed 
              in your screenwriting journey.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              How Can We <span className="gold-gradient">Help</span>?
            </h2>
            
            <div className="grid lg:grid-cols-3 gap-8 mb-16">
              {contactMethods.map((method, index) => (
                <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300 group">
                  <div className="mb-6 group-hover:scale-110 transition-transform">
                    {method.icon}
                  </div>
                  
                  <h3 className="text-2xl font-semibold mb-3 font-playfair">{method.title}</h3>
                  <p className="text-muted-foreground mb-4 leading-relaxed">
                    {method.description}
                  </p>
                  
                  <div className="space-y-2 mb-6">
                    <p className="font-medium">{method.contact}</p>
                    <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      {method.response}
                    </div>
                  </div>
                  
                  <Button variant="outline" className="group-hover:bg-primary group-hover:text-primary-foreground">
                    Contact Now
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
                Send Us a <span className="gold-gradient">Message</span>
              </h2>
              <p className="text-xl text-muted-foreground">
                Fill out the form below and we'll get back to you within 24 hours.
              </p>
            </div>
            
            <ContactForm />
          </div>
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Our <span className="gold-gradient">Offices</span>
            </h2>
            
            <div className="grid md:grid-cols-2 gap-8">
              {officeInfo.map((office, index) => (
                <Card key={index} className="cinema-card p-8">
                  <div className="flex items-start gap-4 mb-6">
                    <MapPin className="h-6 w-6 text-primary flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="text-2xl font-semibold mb-2 font-playfair">{office.location}</h3>
                      <p className="text-muted-foreground leading-relaxed whitespace-pre-line">
                        {office.address}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Clock className="h-4 w-4" />
                    {office.hours}
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Preview */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-8">
              Looking for Quick <span className="gold-gradient">Answers</span>?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-8">
              Check out our comprehensive support documentation and frequently asked questions.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="outline">
                Browse Support Docs
              </Button>
              <Button size="lg" variant="outline" asChild>
                <a href="/faq">View FAQ</a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;
