
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Book, FileText, Video, Code, Download, ExternalLink } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const Documentation = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  const handleGetStarted = () => {
    if (user) {
      navigate('/dashboard');
    } else {
      navigate('/auth');
    }
  };

  const sections = [
    {
      icon: <Book className="h-6 w-6 text-blue-500" />,
      title: "Getting Started",
      description: "Learn the basics of ScriptGenius and start writing your first screenplay",
      items: ["Quick Start Guide", "Setting Up Your Account", "Creating Your First Project", "Understanding the Interface"]
    },
    {
      icon: <FileText className="h-6 w-6 text-green-500" />,
      title: "Writing Tools",
      description: "Master our AI-powered writing features and screenplay formatting",
      items: ["AI Writing Assistant", "Character Development", "Scene Planning", "Dialogue Enhancement"]
    },
    {
      icon: <Video className="h-6 w-6 text-purple-500" />,
      title: "Production Features",
      description: "Explore our comprehensive production management capabilities",
      items: ["Scheduling Tools", "Budget Tracking", "Crew Management", "Call Sheets"]
    },
    {
      icon: <Code className="h-6 w-6 text-orange-500" />,
      title: "API Reference",
      description: "Integrate ScriptGenius with your existing workflow and tools",
      items: ["Authentication", "Endpoints", "Webhooks", "Rate Limits"]
    }
  ];

  const quickLinks = [
    { title: "Screenplay Formatting Guide", type: "PDF", size: "2.1 MB" },
    { title: "Keyboard Shortcuts", type: "Web", size: "Interactive" },
    { title: "Video Tutorials", type: "Video", size: "45 min" },
    { title: "API Examples", type: "Code", size: "GitHub" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📚 Documentation
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Learn <span className="gold-gradient">ScriptGenius</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
              Comprehensive guides, tutorials, and resources to help you master every feature 
              of our creative suite for screenwriters.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group"
                onClick={handleGetStarted}
              >
                {user ? 'Go to Dashboard' : 'Get Started'}
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4">
                Download PDF Guide
                <Download className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Links */}
      <section className="py-16 bg-muted/20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
              Quick <span className="gold-gradient">Reference</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Essential resources to get you up and running quickly.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
            {quickLinks.map((link, index) => (
              <Card key={index} className="cinema-card p-6 text-center hover:scale-105 transition-all duration-300 group">
                <div className="mb-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <ExternalLink className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-semibold mb-2">{link.title}</h3>
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{link.type}</span>
                    <span>{link.size}</span>
                  </div>
                </div>
                <Button variant="ghost" size="sm" className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                  Access
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Documentation Sections */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Complete <span className="gold-gradient">Documentation</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Everything you need to know about using ScriptGenius effectively.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {sections.map((section, index) => (
              <Card 
                key={index} 
                className="cinema-card p-8 hover:scale-105 transition-all duration-300 group animate-fade-scale"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="mb-6 group-hover:scale-110 transition-transform">
                  {section.icon}
                </div>
                <h3 className="text-2xl font-semibold mb-3 font-playfair">{section.title}</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {section.description}
                </p>
                <ul className="space-y-3 mb-6">
                  {section.items.map((item, i) => (
                    <li key={i} className="flex items-center text-sm">
                      <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                      {item}
                    </li>
                  ))}
                </ul>
                <Button variant="outline" className="w-full group-hover:bg-primary group-hover:text-primary-foreground group-hover:border-primary">
                  Explore Section
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Search and Support */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Can't Find What You're <span className="gold-gradient">Looking For?</span>
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Our support team is here to help. Search our knowledge base or contact us directly.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                variant="outline"
                className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4"
                onClick={() => navigate('/support')}
              >
                Contact Support
              </Button>
              <Button 
                size="lg" 
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4"
                onClick={() => navigate('/community')}
              >
                Join Community
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Documentation;
