
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Eye, Lock, Users, Mail, Calendar } from 'lucide-react';

const Privacy = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🔒 Privacy Policy
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Your Privacy <span className="gold-gradient">Matters</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              We're committed to protecting your personal information and being transparent about how we collect, use, and safeguard your data.
            </p>
            
            <div className="flex items-center justify-center gap-2 text-muted-foreground">
              <Calendar className="h-5 w-5" />
              <span>Last updated: March 15, 2024</span>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Overview */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Privacy at a <span className="gold-gradient">Glance</span>
            </h2>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="p-6 text-center border-primary/20">
                <Shield className="h-8 w-8 mx-auto mb-3 text-green-500" />
                <h3 className="font-semibold mb-2">Data Protection</h3>
                <p className="text-sm text-muted-foreground">Enterprise-grade security for all your creative work</p>
              </Card>
              
              <Card className="p-6 text-center border-primary/20">
                <Eye className="h-8 w-8 mx-auto mb-3 text-blue-500" />
                <h3 className="font-semibold mb-2">Transparency</h3>
                <p className="text-sm text-muted-foreground">Clear information about data collection and use</p>
              </Card>
              
              <Card className="p-6 text-center border-primary/20">
                <Lock className="h-8 w-8 mx-auto mb-3 text-purple-500" />
                <h3 className="font-semibold mb-2">Your Control</h3>
                <p className="text-sm text-muted-foreground">Full control over your data and privacy settings</p>
              </Card>
              
              <Card className="p-6 text-center border-primary/20">
                <Users className="h-8 w-8 mx-auto mb-3 text-orange-500" />
                <h3 className="font-semibold mb-2">No Selling</h3>
                <p className="text-sm text-muted-foreground">We never sell your personal data to third parties</p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Detailed Policy */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto space-y-12">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">Information We Collect</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Account Information</h3>
                  <p className="text-muted-foreground leading-relaxed mb-3">
                    When you create an account, we collect:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1 ml-4">
                    <li>Name and email address</li>
                    <li>Professional information (industry role, experience level)</li>
                    <li>Account preferences and settings</li>
                    <li>Profile picture (if provided)</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Creative Content</h3>
                  <p className="text-muted-foreground leading-relaxed mb-3">
                    Your creative work is yours. We store your scripts, characters, and projects to provide our services:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1 ml-4">
                    <li>Screenplay content and revisions</li>
                    <li>Character development notes</li>
                    <li>Project collaboration data</li>
                    <li>AI-generated suggestions and feedback</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Usage Information</h3>
                  <p className="text-muted-foreground leading-relaxed mb-3">
                    To improve our services, we collect:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1 ml-4">
                    <li>Feature usage patterns</li>
                    <li>Performance and error data</li>
                    <li>Device and browser information</li>
                    <li>IP address and general location</li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">How We Use Your Information</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Service Delivery</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Provide and maintain ScriptGenius features</li>
                    <li>Process your creative content</li>
                    <li>Enable collaboration and sharing</li>
                    <li>Deliver AI-powered writing assistance</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Communication</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Send service updates and notifications</li>
                    <li>Respond to support requests</li>
                    <li>Share product news (with consent)</li>
                    <li>Provide educational content</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Improvement</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Analyze usage patterns</li>
                    <li>Develop new features</li>
                    <li>Fix bugs and improve performance</li>
                    <li>Enhance AI recommendations</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Legal Compliance</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2">
                    <li>Comply with legal obligations</li>
                    <li>Protect against fraud and abuse</li>
                    <li>Enforce our terms of service</li>
                    <li>Respond to legal requests</li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">Your Rights and Controls</h2>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold mb-3">Access and Portability</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    You can access, download, and export all your data at any time through your account settings. 
                    We provide tools to easily move your content to other platforms if you choose to leave ScriptGenius.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Correction and Deletion</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Update your personal information anytime or request complete account deletion. 
                    When you delete your account, we permanently remove your personal data within 30 days.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-3">Communication Preferences</h3>
                  <p className="text-muted-foreground leading-relaxed">
                    Control what communications you receive from us. Easily unsubscribe from marketing emails 
                    while continuing to receive important service notifications.
                  </p>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">Data Security</h2>
              
              <p className="text-muted-foreground leading-relaxed mb-6">
                We implement industry-leading security measures to protect your information:
              </p>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">Technical Safeguards</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>End-to-end encryption for sensitive data</li>
                    <li>Secure data centers with 24/7 monitoring</li>
                    <li>Regular security audits and penetration testing</li>
                    <li>Multi-factor authentication support</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-3">Operational Security</h3>
                  <ul className="list-disc list-inside text-muted-foreground space-y-1">
                    <li>Limited employee access on need-to-know basis</li>
                    <li>Comprehensive background checks</li>
                    <li>Regular security training for all staff</li>
                    <li>Incident response and breach notification protocols</li>
                  </ul>
                </div>
              </div>
            </Card>

            <Card className="cinema-card p-8 border-primary/20">
              <h2 className="text-3xl font-playfair font-bold mb-6">Contact Us About Privacy</h2>
              
              <p className="text-muted-foreground leading-relaxed mb-6">
                Have questions about this privacy policy or how we handle your data? We're here to help.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-primary" />
                  <span className="font-medium"><EMAIL></span>
                </div>
                <div className="text-muted-foreground">
                  We respond to all privacy inquiries within 48 hours
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Privacy;
