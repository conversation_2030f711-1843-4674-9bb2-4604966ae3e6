
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import PromoAdminDashboard from '@/components/admin/PromoAdminDashboard';
import { LoadingSpinner } from '@/components/LoadingSpinner';

const PromoAdmin: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  // Check if user is super admin - you might want to implement this check
  // For now, we'll allow access to all authenticated users
  // In production, you should check user role against profiles table

  return (
    <div className="container mx-auto py-6">
      <PromoAdminDashboard />
    </div>
  );
};

export default PromoAdmin;
