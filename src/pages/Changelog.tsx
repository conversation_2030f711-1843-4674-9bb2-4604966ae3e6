
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Plus, Zap, Bug, Shield, Sparkles } from 'lucide-react';

const Changelog = () => {
  const releases = [
    {
      version: "2.4.0",
      date: "March 15, 2024",
      type: "major",
      title: "Enhanced AI Writing Assistant",
      changes: [
        {
          type: "feature",
          description: "New character development AI that suggests detailed backstories and motivations"
        },
        {
          type: "feature", 
          description: "Improved dialogue generation with style matching for different characters"
        },
        {
          type: "improvement",
          description: "Faster response times for AI suggestions (40% improvement)"
        },
        {
          type: "fix",
          description: "Fixed issue where AI suggestions would sometimes repeat content"
        }
      ]
    },
    {
      version: "2.3.2",
      date: "March 8, 2024",
      type: "patch",
      title: "Performance Improvements",
      changes: [
        {
          type: "improvement",
          description: "Optimized script editor loading time by 60%"
        },
        {
          type: "improvement",
          description: "Enhanced real-time collaboration performance"
        },
        {
          type: "fix",
          description: "Resolved issue with auto-save in large scripts"
        },
        {
          type: "fix",
          description: "Fixed formatting inconsistencies in PDF exports"
        }
      ]
    },
    {
      version: "2.3.1",
      date: "March 1, 2024",
      type: "patch",
      title: "Bug Fixes & Stability",
      changes: [
        {
          type: "fix",
          description: "Fixed crash when importing scripts with special characters"
        },
        {
          type: "fix",
          description: "Resolved collaboration sync issues in poor network conditions"
        },
        {
          type: "security",
          description: "Enhanced encryption for data transmission"
        }
      ]
    },
    {
      version: "2.3.0",
      date: "February 28, 2024",
      type: "minor",
      title: "Storyboard Studio & Templates",
      changes: [
        {
          type: "feature",
          description: "New Storyboard Studio with drag-and-drop visual planning"
        },
        {
          type: "feature",
          description: "Professional screenplay templates for different genres"
        },
        {
          type: "feature",
          description: "Export storyboards as PDF or image sequences"
        },
        {
          type: "improvement",
          description: "Redesigned project dashboard with better organization"
        }
      ]
    },
    {
      version: "2.2.1",
      date: "February 20, 2024",
      type: "patch",
      title: "Marketplace Enhancements",
      changes: [
        {
          type: "improvement",
          description: "Improved search functionality in script marketplace"
        },
        {
          type: "improvement",
          description: "Better filtering options for marketplace listings"
        },
        {
          type: "fix",
          description: "Fixed payment processing delays for script purchases"
        }
      ]
    },
    {
      version: "2.2.0",
      date: "February 15, 2024", 
      type: "minor",
      title: "Coverage Generator",
      changes: [
        {
          type: "feature",
          description: "AI-powered script coverage generator for industry professionals"
        },
        {
          type: "feature",
          description: "Customizable coverage templates and criteria"
        },
        {
          type: "feature",
          description: "Export coverage reports as professional PDFs"
        }
      ]
    }
  ];

  const getChangeIcon = (type: string) => {
    switch (type) {
      case 'feature': return <Plus className="h-4 w-4 text-green-500" />;
      case 'improvement': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'fix': return <Bug className="h-4 w-4 text-orange-500" />;
      case 'security': return <Shield className="h-4 w-4 text-purple-500" />;
      default: return <Sparkles className="h-4 w-4 text-primary" />;
    }
  };

  const getVersionColor = (type: string) => {
    switch (type) {
      case 'major': return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'minor': return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'patch': return 'bg-green-500/10 text-green-500 border-green-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📝 Product Updates
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              What's <span className="gold-gradient">New</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Stay up to date with the latest features, improvements, and fixes in ScriptGenius.
            </p>
          </div>
        </div>
      </section>

      {/* Changelog */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="space-y-12">
              {releases.map((release, index) => (
                <Card key={index} className="cinema-card p-8">
                  <div className="flex items-start justify-between mb-6">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <Badge variant="outline" className={getVersionColor(release.type)}>
                          v{release.version}
                        </Badge>
                        <div className="flex items-center gap-2 text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span>{release.date}</span>
                        </div>
                      </div>
                      <h2 className="text-2xl font-playfair font-bold">{release.title}</h2>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    {release.changes.map((change, changeIndex) => (
                      <div key={changeIndex} className="flex items-start gap-3 p-3 rounded-lg bg-muted/30">
                        {getChangeIcon(change.type)}
                        <div className="flex-1">
                          <span className="text-sm font-medium capitalize text-muted-foreground">
                            {change.type}
                          </span>
                          <p className="mt-1">{change.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
            
            {/* Load More */}
            <div className="text-center mt-12">
              <p className="text-muted-foreground mb-4">
                Want to see older releases?
              </p>
              <button className="px-6 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
                Load More Releases
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe to Updates */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-playfair font-bold mb-6">
              Stay <span className="gold-gradient">Informed</span>
            </h2>
            
            <p className="text-muted-foreground mb-8">
              Get notified about new features and updates as soon as they're released.
            </p>
            
            <Card className="cinema-card p-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <input 
                  type="email" 
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 border border-border rounded-lg bg-background"
                />
                <button className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
                  Subscribe
                </button>
              </div>
              <p className="text-xs text-muted-foreground mt-4">
                You can unsubscribe at any time. We respect your privacy.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Release Notes Archive */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-playfair font-bold mb-6">
              Release Notes <span className="gold-gradient">Archive</span>
            </h2>
            
            <p className="text-muted-foreground mb-8">
              Browse our complete release history and detailed technical documentation.
            </p>
            
            <div className="grid md:grid-cols-3 gap-6">
              <Card className="p-6 text-center">
                <h3 className="font-semibold mb-2">2024 Releases</h3>
                <p className="text-muted-foreground text-sm mb-4">Q1 releases and updates</p>
                <button className="text-primary hover:underline">View All</button>
              </Card>
              
              <Card className="p-6 text-center">
                <h3 className="font-semibold mb-2">2023 Archive</h3>
                <p className="text-muted-foreground text-sm mb-4">Complete 2023 changelog</p>
                <button className="text-primary hover:underline">View Archive</button>
              </Card>
              
              <Card className="p-6 text-center">
                <h3 className="font-semibold mb-2">API Changes</h3>
                <p className="text-muted-foreground text-sm mb-4">Developer-focused updates</p>
                <button className="text-primary hover:underline">View API Docs</button>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Changelog;
