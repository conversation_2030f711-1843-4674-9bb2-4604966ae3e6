
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle, Clock, Activity, Globe, Database, Zap } from 'lucide-react';

const Status = () => {
  const services = [
    {
      name: "API Gateway",
      status: "operational",
      uptime: "99.9%",
      responseTime: "45ms",
      icon: <Globe className="h-5 w-5" />
    },
    {
      name: "Database",
      status: "operational", 
      uptime: "99.8%",
      responseTime: "12ms",
      icon: <Database className="h-5 w-5" />
    },
    {
      name: "AI Services",
      status: "operational",
      uptime: "99.7%",
      responseTime: "230ms",
      icon: <Zap className="h-5 w-5" />
    },
    {
      name: "File Storage",
      status: "operational",
      uptime: "99.9%",
      responseTime: "89ms",
      icon: <Activity className="h-5 w-5" />
    }
  ];

  const incidents = [
    {
      title: "Scheduled Maintenance",
      description: "Routine database optimization and security updates",
      date: "March 12, 2024",
      duration: "30 minutes",
      status: "resolved"
    },
    {
      title: "API Rate Limiting Issue",
      description: "Temporary increase in response times for AI generation requests",
      date: "March 8, 2024", 
      duration: "15 minutes",
      status: "resolved"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'bg-green-500';
      case 'degraded': return 'bg-yellow-500';
      case 'outage': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'outage': return <AlertCircle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              📊 System Status
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              All Systems <span className="gold-gradient">Operational</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Real-time status updates for all ScriptGenius services and infrastructure.
            </p>
            
            <div className="flex items-center justify-center gap-4">
              <CheckCircle className="h-6 w-6 text-green-500" />
              <span className="text-lg font-semibold">All Services Operational</span>
            </div>
          </div>
        </div>
      </section>

      {/* Current Status */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Service <span className="gold-gradient">Status</span>
            </h2>
            
            <div className="space-y-4">
              {services.map((service, index) => (
                <Card key={index} className="cinema-card p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="p-2 bg-muted rounded-lg">
                        {service.icon}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{service.name}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          {getStatusIcon(service.status)}
                          <span className="text-sm text-muted-foreground capitalize">
                            {service.status}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-8 text-right">
                      <div>
                        <div className="text-sm text-muted-foreground">Uptime</div>
                        <div className="font-semibold">{service.uptime}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Response Time</div>
                        <div className="font-semibold">{service.responseTime}</div>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Recent Incidents */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Recent <span className="gold-gradient">Incidents</span>
            </h2>
            
            <div className="space-y-6">
              {incidents.map((incident, index) => (
                <Card key={index} className="cinema-card p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="font-semibold text-lg mb-2">{incident.title}</h3>
                      <p className="text-muted-foreground">{incident.description}</p>
                    </div>
                    <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                      Resolved
                    </Badge>
                  </div>
                  
                  <div className="flex gap-6 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span>{incident.date}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      <span>Duration: {incident.duration}</span>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Subscribe to Updates */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-playfair font-bold mb-6">
              Stay <span className="gold-gradient">Updated</span>
            </h2>
            
            <p className="text-muted-foreground mb-8">
              Subscribe to receive notifications about service incidents and scheduled maintenance.
            </p>
            
            <Card className="cinema-card p-8">
              <div className="flex flex-col sm:flex-row gap-4">
                <input 
                  type="email" 
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 border border-border rounded-lg bg-background"
                />
                <button className="px-6 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
                  Subscribe
                </button>
              </div>
            </Card>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Status;
