
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Code, Key, Book, Zap, Shield, Globe, Download, ExternalLink } from 'lucide-react';

const API = () => {
  const endpoints = [
    {
      method: "POST",
      path: "/api/v1/screenplays",
      description: "Create a new screenplay",
      category: "Content Management"
    },
    {
      method: "GET",
      path: "/api/v1/screenplays/{id}",
      description: "Retrieve a specific screenplay",
      category: "Content Management"
    },
    {
      method: "POST",
      path: "/api/v1/ai/generate",
      description: "Generate AI-powered writing suggestions",
      category: "AI Services"
    },
    {
      method: "POST",
      path: "/api/v1/coverage/generate",
      description: "Generate script coverage analysis",
      category: "AI Services"
    },
    {
      method: "GET",
      path: "/api/v1/marketplace/listings",
      description: "Browse marketplace listings",
      category: "Marketplace"
    }
  ];

  const sdks = [
    {
      name: "JavaScript SDK",
      description: "Official JavaScript/TypeScript SDK for web and Node.js applications",
      language: "JavaScript",
      icon: "🟨"
    },
    {
      name: "Python SDK",
      description: "Python library for integrating ScriptGenius into your Python applications",
      language: "Python", 
      icon: "🐍"
    },
    {
      name: "REST API",
      description: "Direct HTTP REST API access for any programming language",
      language: "HTTP",
      icon: "🌐"
    }
  ];

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'POST': return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'PUT': return 'bg-orange-500/10 text-orange-500 border-orange-500/20';
      case 'DELETE': return 'bg-red-500/10 text-red-500 border-red-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
        </div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
              🔌 API Documentation
            </Badge>
            
            <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
              Build with <span className="gold-gradient">ScriptGenius API</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto animate-slide-up delay-200">
              Integrate powerful screenwriting and AI capabilities into your applications with our comprehensive API.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8 py-3">
                <Key className="h-5 w-5 mr-2" />
                Get API Key
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                <Book className="h-5 w-5 mr-2" />
                View Docs
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Quick Start */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              Quick <span className="gold-gradient">Start</span>
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              <Card className="cinema-card p-8 text-center">
                <Key className="h-12 w-12 mx-auto mb-4 text-primary" />
                <h3 className="text-xl font-semibold mb-3">1. Get API Key</h3>
                <p className="text-muted-foreground">
                  Sign up and generate your API key from the developer dashboard.
                </p>
              </Card>
              
              <Card className="cinema-card p-8 text-center">
                <Code className="h-12 w-12 mx-auto mb-4 text-primary" />
                <h3 className="text-xl font-semibold mb-3">2. Make Your First Call</h3>
                <p className="text-muted-foreground">
                  Use our SDKs or REST API to integrate with your application.
                </p>
              </Card>
              
              <Card className="cinema-card p-8 text-center">
                <Zap className="h-12 w-12 mx-auto mb-4 text-primary" />
                <h3 className="text-xl font-semibold mb-3">3. Build Amazing Apps</h3>
                <p className="text-muted-foreground">
                  Create powerful screenwriting tools with AI capabilities.
                </p>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* API Endpoints */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              API <span className="gold-gradient">Endpoints</span>
            </h2>
            
            <div className="space-y-4">
              {endpoints.map((endpoint, index) => (
                <Card key={index} className="cinema-card p-6 hover:scale-[1.02] transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline" className={getMethodColor(endpoint.method)}>
                        {endpoint.method}
                      </Badge>
                      <div>
                        <code className="text-lg font-mono">{endpoint.path}</code>
                        <p className="text-muted-foreground mt-1">{endpoint.description}</p>
                      </div>
                    </div>
                    <Badge variant="outline">{endpoint.category}</Badge>
                  </div>
                </Card>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <Button variant="outline" size="lg">
                <ExternalLink className="h-5 w-5 mr-2" />
                View Complete API Reference
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* SDKs and Libraries */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-12 text-center">
              SDKs & <span className="gold-gradient">Libraries</span>
            </h2>
            
            <div className="grid md:grid-cols-3 gap-8">
              {sdks.map((sdk, index) => (
                <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300">
                  <div className="text-4xl mb-4">{sdk.icon}</div>
                  <h3 className="text-xl font-semibold mb-3">{sdk.name}</h3>
                  <p className="text-muted-foreground mb-6">{sdk.description}</p>
                  <Button variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Install
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Authentication */}
      <section className="py-24">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card className="cinema-card p-8">
              <h2 className="text-3xl font-playfair font-bold mb-6">
                Authentication & <span className="gold-gradient">Security</span>
              </h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    API Key Authentication
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Secure your API requests using API keys. Include your key in the Authorization header.
                  </p>
                  <Card className="bg-muted p-4">
                    <code className="text-sm">
                      Authorization: Bearer your-api-key-here
                    </code>
                  </Card>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    Rate Limiting
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    API requests are rate-limited to ensure fair usage and optimal performance.
                  </p>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Free: 100 requests/hour</li>
                    <li>• Pro: 1,000 requests/hour</li>
                    <li>• Enterprise: Custom limits</li>
                  </ul>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Support */}
      <section className="py-24 bg-muted/10">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="text-3xl font-playfair font-bold mb-6">
              Need <span className="gold-gradient">Help</span>?
            </h2>
            
            <p className="text-muted-foreground mb-8">
              Our developer support team is here to help you integrate successfully.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline">
                <ExternalLink className="h-4 w-4 mr-2" />
                Developer Discord
              </Button>
              <Button variant="outline">
                <Book className="h-4 w-4 mr-2" />
                API Documentation
              </Button>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default API;
