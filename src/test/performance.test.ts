
import { describe, it, expect } from 'vitest';
import { performanceMonitoring } from '../lib/performance';

describe('PerformanceMonitoring', () => {
  it('should track and report metrics', () => {
    performanceMonitoring.trackCustomMetric('load', 100);
    performanceMonitoring.trackCustomMetric('paint', 50);
    const report = performanceMonitoring.getReport();
    expect(report.metrics.load.length).toBeGreaterThan(0);
    expect(report.metrics.paint.length).toBeGreaterThan(0);
  });

  it('should get metrics by name', () => {
    performanceMonitoring.trackCustomMetric('test', 200);
    const metrics = performanceMonitoring.getMetrics();
    expect(Array.isArray(metrics)).toBe(true);
    expect(metrics.length).toBeGreaterThan(0);
  });

  it('should clean up metrics', () => {
    performanceMonitoring.trackCustomMetric('cleanup', 300);
    performanceMonitoring.cleanup();
    const metrics = performanceMonitoring.getMetrics();
    expect(Array.isArray(metrics)).toBe(true);
  });
}); 
