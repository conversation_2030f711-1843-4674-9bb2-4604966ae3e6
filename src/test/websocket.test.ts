import { describe, it, expect, beforeEach, vi } from 'vitest';
import { websocketManager } from '../utils/websocket';

describe('WebSocket Manager', () => {
  beforeEach(() => {
    websocketManager.reset();
    vi.clearAllMocks();
  });

  it('should connect to websocket', () => {
    const mockWebSocket = vi.fn();
    global.WebSocket = mockWebSocket as any;

    websocketManager.connect('ws://test.com');
    expect(mockWebSocket).toHaveBeenCalledWith('ws://test.com');
  });

  it('should handle connection errors', () => {
    const mockWebSocket = vi.fn().mockImplementation(() => {
      throw new Error('Connection failed');
    });
    global.WebSocket = mockWebSocket as any;

    expect(() => websocketManager.connect('ws://test.com')).toThrow('Connection failed');
  });

  it('should send messages', () => {
    const mockSend = vi.fn();
    const mockWebSocket = vi.fn().mockImplementation(() => ({
      send: mockSend
    }));
    global.WebSocket = mockWebSocket as any;

    websocketManager.connect('ws://test.com');
    websocketManager.send('test message');
    expect(mockSend).toHaveBeenCalledWith('test message');
  });

  it('should handle incoming messages', () => {
    const mockWebSocket = vi.fn().mockImplementation(() => ({
      addEventListener: vi.fn((event, callback) => {
        if (event === 'message') {
          callback({ data: 'test message' });
        }
      })
    }));
    global.WebSocket = mockWebSocket as any;

    let receivedMessage = '';
    websocketManager.onMessage((message) => {
      receivedMessage = message;
    });

    websocketManager.connect('ws://test.com');
    expect(receivedMessage).toBe('test message');
  });

  it('should handle disconnection', () => {
    const mockClose = vi.fn();
    const mockWebSocket = vi.fn().mockImplementation(() => ({
      close: mockClose
    }));
    global.WebSocket = mockWebSocket as any;

    websocketManager.connect('ws://test.com');
    websocketManager.disconnect();
    expect(mockClose).toHaveBeenCalled();
  });
}); 