import { describe, it, expect, beforeEach } from 'vitest';
import { uploadManager } from '../utils/upload';

describe('Upload Manager', () => {
  beforeEach(() => {
    uploadManager.reset();
  });

  it('should validate file size', () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result = uploadManager.validateFileSize(file, 5);
    expect(result).toBe(true);
  });

  it('should validate file type', () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result = uploadManager.validateFileType(file, ['text/plain']);
    expect(result).toBe(true);
  });

  it('should validate file name', () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result = uploadManager.validateFileName(file, /^[a-zA-Z0-9.-]+$/);
    expect(result).toBe(true);
  });

  it('should handle file upload', async () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result = await uploadManager.upload(file);
    expect(result.success).toBe(true);
  });

  it('should handle upload errors', async () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result = await uploadManager.upload(file, { maxSize: 1 });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('should track upload progress', async () => {
    const file = new File(['test'], 'test.txt', { type: 'text/plain' });
    let progress = 0;
    
    uploadManager.onProgress((p) => {
      progress = p;
    });

    await uploadManager.upload(file);
    expect(progress).toBe(100);
  });
}); 