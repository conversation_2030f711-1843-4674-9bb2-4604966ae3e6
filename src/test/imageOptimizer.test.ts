import { describe, it, expect } from 'vitest';
import { ImageOptimizer } from '../utils/imageOptimizer';

describe('ImageOptimizer', () => {
  it('should generate optimized image URLs and cache them', async () => {
    const optimizer = ImageOptimizer.getInstance();
    const url = await optimizer.getOptimizedImageUrl('test.jpg', { width: 100, height: 100, format: 'webp' });
    expect(url).toBeDefined();
    const cached = optimizer['imageCache'].get(optimizer['getCacheKey']('test.jpg', { width: 100, height: 100, format: 'webp' }));
    expect(cached).toBeDefined();
  });
}); 