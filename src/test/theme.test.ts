import { describe, it, expect, beforeEach } from 'vitest';
import { themeManager } from '../utils/theme';

describe('Theme Manager', () => {
  beforeEach(() => {
    themeManager.reset();
  });

  it('should initialize with default theme', () => {
    expect(themeManager.getCurrentTheme()).toBe('light');
  });

  it('should change theme', () => {
    themeManager.setTheme('dark');
    expect(themeManager.getCurrentTheme()).toBe('dark');
  });

  it('should persist theme preference', () => {
    themeManager.setTheme('dark');
    const storedTheme = localStorage.getItem('theme');
    expect(storedTheme).toBe('dark');
  });

  it('should handle invalid theme gracefully', () => {
    themeManager.setTheme('invalid' as any);
    expect(themeManager.getCurrentTheme()).toBe('light');
  });

  it('should notify subscribers of theme changes', () => {
    let lastTheme: string | null = null;
    const unsubscribe = themeManager.subscribe(theme => {
      lastTheme = theme;
    });

    themeManager.setTheme('dark');
    expect(lastTheme).toBe('dark');

    unsubscribe();
    themeManager.setTheme('light');
    expect(lastTheme).toBe('dark');
  });
}); 