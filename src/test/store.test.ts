
import { describe, it, expect, beforeEach } from 'vitest';
import { Store } from '../utils/store';
import { Action } from '../utils/types';

interface TestState {
  count: number;
  text: string;
}

type TestAction = 
  | { type: 'INCREMENT' }
  | { type: 'DECREMENT' }
  | { type: 'SET_TEXT'; payload: string };

const initialState: TestState = {
  count: 0,
  text: ''
};

const reducer = (state: TestState, action: TestAction): TestState => {
  switch (action.type) {
    case 'INCREMENT':
      return { ...state, count: state.count + 1 };
    case 'DECREMENT':
      return { ...state, count: state.count - 1 };
    case 'SET_TEXT':
      return { ...state, text: action.payload };
    default:
      return state;
  }
};

describe('Store', () => {
  let store: Store<TestState, TestAction>;

  beforeEach(() => {
    store = new Store<TestState, TestAction>(reducer, { initialState });
  });

  it('should initialize with initial state', () => {
    expect(store.getState()).toEqual(initialState);
  });

  it('should update state when dispatching actions', () => {
    store.dispatch({ type: 'INCREMENT' });
    expect(store.getState().count).toBe(1);

    store.dispatch({ type: 'DECREMENT' });
    expect(store.getState().count).toBe(0);

    store.dispatch({ type: 'SET_TEXT', payload: 'test' });
    expect(store.getState().text).toBe('test');
  });

  it('should notify subscribers of state changes', () => {
    let lastState: TestState | null = null;
    const unsubscribe = store.subscribe(state => {
      lastState = state;
    });

    store.dispatch({ type: 'INCREMENT' });
    expect(lastState).toEqual({ count: 1, text: '' });

    unsubscribe();
    store.dispatch({ type: 'DECREMENT' });
    expect(lastState).toEqual({ count: 1, text: '' });
  });

  it('should support undo/redo', () => {
    store.dispatch({ type: 'INCREMENT' });
    store.dispatch({ type: 'SET_TEXT', payload: 'test' });

    store.undo();
    expect(store.getState()).toEqual({ count: 1, text: '' });

    store.undo();
    expect(store.getState()).toEqual(initialState);

    store.redo();
    expect(store.getState()).toEqual({ count: 1, text: '' });

    store.redo();
    expect(store.getState()).toEqual({ count: 1, text: 'test' });
  });
}); 
