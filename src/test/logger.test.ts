import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { logger } from '../utils/logger';

describe('Logger', () => {
  beforeEach(() => {
    logger.clear();
  });

  it('should log messages with different levels', () => {
    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warning message');
    logger.error('Error message');

    const logs = logger.getLogs();
    expect(logs).toHaveLength(4);
    expect(logs[0].level).toBe('debug');
    expect(logs[1].level).toBe('info');
    expect(logs[2].level).toBe('warn');
    expect(logs[3].level).toBe('error');
  });

  it('should clear logs', () => {
    logger.info('Test message');
    expect(logger.getLogs()).toHaveLength(1);
    
    logger.clear();
    expect(logger.getLogs()).toHaveLength(0);
  });

  it('should format log messages correctly', () => {
    const message = 'Test message';
    const context = { userId: '123' };
    
    logger.info(message, context);
    const logs = logger.getLogs();
    
    expect(logs[0].message).toBe(message);
    expect(logs[0].context).toEqual(context);
  });

  let consoleSpy: {
    debug: ReturnType<typeof vi.spyOn>;
    info: ReturnType<typeof vi.spyOn>;
    warn: ReturnType<typeof vi.spyOn>;
    error: ReturnType<typeof vi.spyOn>;
  };

  beforeEach(() => {
    consoleSpy = {
      debug: vi.spyOn(console, 'debug'),
      info: vi.spyOn(console, 'info'),
      warn: vi.spyOn(console, 'warn'),
      error: vi.spyOn(console, 'error')
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
    logger.clearLogs();
  });

  describe('Log Levels', () => {
    it('should respect log level settings', () => {
      logger.setLogLevel('warn');
      
      logger.debug('debug message');
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');

      expect(consoleSpy.debug).not.toHaveBeenCalled();
      expect(consoleSpy.info).not.toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalled();
    });

    it('should log all levels when set to debug', () => {
      logger.setLogLevel('debug');
      
      logger.debug('debug message');
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');

      expect(consoleSpy.debug).toHaveBeenCalled();
      expect(consoleSpy.info).toHaveBeenCalled();
      expect(consoleSpy.warn).toHaveBeenCalled();
      expect(consoleSpy.error).toHaveBeenCalled();
    });
  });

  describe('Log Formatting', () => {
    it('should format log messages with timestamp', () => {
      logger.setLogLevel('info');
      logger.info('test message');

      const logCall = consoleSpy.info.mock.calls[0][0];
      expect(logCall).toMatch(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z\] INFO: test message/);
    });

    it('should include data in log messages', () => {
      logger.setLogLevel('info');
      const testData = { key: 'value' };
      logger.info('test message', testData);

      expect(consoleSpy.info).toHaveBeenCalledWith(
        expect.stringMatching(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z\] INFO: test message/),
        testData
      );
    });
  });

  describe('Log Storage', () => {
    it('should store logs in memory', () => {
      logger.setLogLevel('debug');
      
      logger.debug('debug message');
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');

      const logs = logger.getLogs();
      expect(logs).toHaveLength(4);
      expect(logs[0].level).toBe('debug');
      expect(logs[1].level).toBe('info');
      expect(logs[2].level).toBe('warn');
      expect(logs[3].level).toBe('error');
    });

    it('should filter logs by level', () => {
      logger.setLogLevel('debug');
      
      logger.debug('debug message');
      logger.info('info message');
      logger.warn('warn message');
      logger.error('error message');

      const errorLogs = logger.getLogs('error');
      expect(errorLogs).toHaveLength(1);
      expect(errorLogs[0].level).toBe('error');
    });

    it('should clear logs', () => {
      logger.setLogLevel('debug');
      logger.debug('test message');
      
      expect(logger.getLogs()).toHaveLength(1);
      logger.clearLogs();
      expect(logger.getLogs()).toHaveLength(0);
    });
  });

  describe('Log Export', () => {
    it('should export logs as JSON', async () => {
      logger.setLogLevel('debug');
      logger.debug('debug message');
      logger.info('info message');

      const exportedLogs = await logger.exportLogs();
      const parsedLogs = JSON.parse(exportedLogs);

      expect(parsedLogs).toHaveLength(2);
      expect(parsedLogs[0].level).toBe('debug');
      expect(parsedLogs[1].level).toBe('info');
      expect(parsedLogs[0]).toHaveProperty('timestamp');
      expect(parsedLogs[0]).toHaveProperty('message');
    });
  });

  describe('Log Rotation', () => {
    it('should limit the number of stored logs', () => {
      logger.setLogLevel('debug');
      const maxLogs = 1000;

      // Add more logs than the maximum
      for (let i = 0; i < maxLogs + 10; i++) {
        logger.debug(`message ${i}`);
      }

      const logs = logger.getLogs();
      expect(logs).toHaveLength(maxLogs);
      expect(logs[0].message).toBe('message 10'); // First message should be removed
      expect(logs[logs.length - 1].message).toBe(`message ${maxLogs + 9}`); // Last message should be the most recent
    });
  });
}); 