
import { describe, it, expect } from 'vitest';
import { mapToProductionSchedule, mapToProductionScheduleItem } from '@/lib/api/production/scheduleMappers';
import type { ProductionSchedule, ProductionScheduleItem } from '@/lib/api/production/types';

describe('Schedule Mappers', () => {
  describe('mapToProductionSchedule', () => {
    it('should map raw database data to ProductionSchedule', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        org_id: '123e4567-e89b-12d3-a456-426614174001',
        user_id: '123e4567-e89b-12d3-a456-426614174002',
        title: 'Production Schedule',
        description: 'Main production schedule',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionSchedule(rawData);

      expect(result).toEqual({
        id: '123e4567-e89b-12d3-a456-426614174000',
        org_id: '123e4567-e89b-12d3-a456-426614174001',
        user_id: '123e4567-e89b-12d3-a456-426614174002',
        title: 'Production Schedule',
        description: 'Main production schedule',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      });
    });

    it('should handle null description', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        org_id: '123e4567-e89b-12d3-a456-426614174001',
        user_id: '123e4567-e89b-12d3-a456-426614174002',
        title: 'Production Schedule',
        description: null,
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'draft',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionSchedule(rawData);

      expect(result.description).toBeNull();
    });

    it('should coerce invalid status to default', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        org_id: '123e4567-e89b-12d3-a456-426614174001',
        user_id: '123e4567-e89b-12d3-a456-426614174002',
        title: 'Production Schedule',
        description: 'Test description',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'invalid_status',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionSchedule(rawData);

      expect(result.status).toBe('draft'); // fallback default
    });
  });

  describe('mapToProductionScheduleItem', () => {
    it('should map raw database data to ProductionScheduleItem', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        schedule_id: '123e4567-e89b-12d3-a456-426614174001',
        scene_id: '123e4567-e89b-12d3-a456-426614174002',
        location_id: '123e4567-e89b-12d3-a456-426614174003',
        title: 'Scene 1 Shoot',
        description: 'Morning scene',
        scheduled_date: '2024-01-15',
        start_time: '09:00',
        end_time: '17:00',
        estimated_duration: 480,
        status: 'in_progress',
        notes: 'Bring extra lighting',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionScheduleItem(rawData);

      expect(result).toEqual({
        id: '123e4567-e89b-12d3-a456-426614174000',
        schedule_id: '123e4567-e89b-12d3-a456-426614174001',
        scene_id: '123e4567-e89b-12d3-a456-426614174002',
        location_id: '123e4567-e89b-12d3-a456-426614174003',
        title: 'Scene 1 Shoot',
        description: 'Morning scene',
        scheduled_date: '2024-01-15',
        start_time: '09:00',
        end_time: '17:00',
        estimated_duration: 480,
        status: 'in_progress',
        notes: 'Bring extra lighting',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      });
    });

    it('should handle null optional fields', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        schedule_id: '123e4567-e89b-12d3-a456-426614174001',
        scene_id: null,
        location_id: null,
        title: 'Scene 1 Shoot',
        description: null,
        scheduled_date: '2024-01-15',
        start_time: null,
        end_time: null,
        estimated_duration: null,
        status: 'scheduled',
        notes: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionScheduleItem(rawData);

      expect(result.scene_id).toBeNull();
      expect(result.location_id).toBeNull();
      expect(result.description).toBeNull();
      expect(result.start_time).toBeNull();
      expect(result.end_time).toBeNull();
      expect(result.estimated_duration).toBeNull();
      expect(result.notes).toBeNull();
    });

    it('should coerce invalid status to default', () => {
      const rawData = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        schedule_id: '123e4567-e89b-12d3-a456-426614174001',
        scene_id: null,
        location_id: null,
        title: 'Scene 1 Shoot',
        description: null,
        scheduled_date: '2024-01-15',
        start_time: null,
        end_time: null,
        estimated_duration: null,
        status: 'invalid_status',
        notes: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const result = mapToProductionScheduleItem(rawData);

      expect(result.status).toBe('scheduled'); // fallback default
    });
  });
});
