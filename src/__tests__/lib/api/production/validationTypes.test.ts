
import { describe, it, expect } from 'vitest';
import type { ValidationResult } from '@/lib/api/production/validationUtils';

describe('Validation Types', () => {
  describe('ValidationResult type handling', () => {
    it('should properly discriminate success and error types', () => {
      const successResult: ValidationResult<{ test: string }> = {
        success: true,
        data: { test: 'value' }
      };

      const errorResult: ValidationResult<{ test: string }> = {
        success: false,
        errors: { general: 'Validation failed' }
      };

      // Type checking - these should compile without errors
      if (successResult.success) {
        expect(successResult.data.test).toBe('value');
        // successResult.errors should not be accessible here
      }

      if (!errorResult.success) {
        expect(errorResult.errors.general).toBe('Validation failed');
        // errorResult.data should not be accessible here
      }
    });
  });
});
