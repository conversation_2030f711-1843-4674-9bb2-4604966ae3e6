
import { describe, it, expect } from 'vitest';
import { 
  validateScheduleItemData,
  isValidationSuccess,
  isValidationError,
  type ValidationResult 
} from '@/lib/api/production/validationUtils';

describe('Schedule Item Validation', () => {
  describe('validateScheduleItemData', () => {
    it('should validate correct schedule item data', () => {
      const validItemData = {
        schedule_id: '123e4567-e89b-12d3-a456-************',
        title: 'Scene 1 Shoot',
        description: 'Morning scene at location A',
        scheduled_date: '2024-01-15',
        start_time: '09:00',
        end_time: '17:00',
        estimated_duration: 480,
        status: 'scheduled'
      };

      const result = validateScheduleItemData(validItemData);

      expect(result.success).toBe(true);
      if (isValidationSuccess(result)) {
        expect(result.data.title).toBe('Scene 1 Shoot');
        expect(result.data.schedule_id).toBe('123e4567-e89b-12d3-a456-************');
      }
    });

    it('should reject schedule item with missing required fields', () => {
      const invalidItemData = {
        description: 'Missing schedule_id and title'
      };

      const result = validateScheduleItemData(invalidItemData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('required');
      }
    });

    it('should reject schedule item with invalid UUID', () => {
      const invalidItemData = {
        schedule_id: 'invalid-uuid',
        title: 'Scene 1',
        scheduled_date: '2024-01-15'
      };

      const result = validateScheduleItemData(invalidItemData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('uuid');
      }
    });

    it('should reject schedule item with end time before start time', () => {
      const invalidItemData = {
        schedule_id: '123e4567-e89b-12d3-a456-************',
        title: 'Scene 1',
        scheduled_date: '2024-01-15',
        start_time: '17:00',
        end_time: '09:00'
      };

      const result = validateScheduleItemData(invalidItemData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('End time must be after start time');
      }
    });

    it('should validate schedule item with optional fields', () => {
      const minimalItemData = {
        schedule_id: '123e4567-e89b-12d3-a456-************',
        title: 'Minimal Scene',
        scheduled_date: '2024-01-15'
      };

      const result = validateScheduleItemData(minimalItemData);

      expect(result.success).toBe(true);
      if (isValidationSuccess(result)) {
        expect(result.data.status).toBe('scheduled'); // default value
      }
    });

    it('should handle duration validation correctly', () => {
      const itemWithLongDuration = {
        schedule_id: '123e4567-e89b-12d3-a456-************',
        title: 'Long Scene',
        scheduled_date: '2024-01-15',
        estimated_duration: 1500 // More than 24 hours
      };

      const result = validateScheduleItemData(itemWithLongDuration);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('Duration cannot exceed 24 hours');
      }
    });
  });
});
