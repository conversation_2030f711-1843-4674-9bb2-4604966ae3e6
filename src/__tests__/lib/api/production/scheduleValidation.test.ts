
import { describe, it, expect } from 'vitest';
import { 
  validateScheduleData, 
  validateScheduleUpdateData,
  isValidationSuccess,
  isValidationError,
  type ValidationResult 
} from '@/lib/api/production/validationUtils';

describe('Schedule Validation', () => {
  describe('validateScheduleData', () => {
    it('should validate correct schedule data', () => {
      const validScheduleData = {
        title: 'Production Schedule',
        description: 'Main production schedule',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'draft'
      };

      const result = validateScheduleData(validScheduleData);

      expect(result.success).toBe(true);
      if (isValidationSuccess(result)) {
        expect(result.data.title).toBe('Production Schedule');
        expect(result.data.start_date).toBe('2024-01-01');
        expect(result.data.end_date).toBe('2024-01-31');
      }
    });

    it('should reject schedule with missing required fields', () => {
      const invalidScheduleData = {
        description: 'Missing required fields'
      };

      const result = validateScheduleData(invalidScheduleData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('required');
      }
    });

    it('should reject schedule with end date before start date', () => {
      const invalidScheduleData = {
        title: 'Invalid Schedule',
        start_date: '2024-01-31',
        end_date: '2024-01-01',
        status: 'draft'
      };

      const result = validateScheduleData(invalidScheduleData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(Object.values(result.errors).join(', ')).toContain('End date must be after start date');
      }
    });

    it('should handle invalid data types gracefully', () => {
      const invalidScheduleData = {
        title: 123, // should be string
        start_date: 'invalid-date',
        end_date: '2024-01-31'
      };

      const result = validateScheduleData(invalidScheduleData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(typeof Object.values(result.errors).join(', ')).toBe('string');
      }
    });
  });

  describe('validateScheduleUpdateData', () => {
    it('should validate partial schedule update data', () => {
      const updateData = {
        title: 'Updated Schedule Title',
        status: 'active'
      };

      const result = validateScheduleUpdateData(updateData);

      expect(result.success).toBe(true);
      if (isValidationSuccess(result)) {
        expect(result.data.title).toBe('Updated Schedule Title');
        expect(result.data.status).toBe('active');
      }
    });

    it('should validate empty update data', () => {
      const result = validateScheduleUpdateData({});

      expect(result.success).toBe(true);
      if (isValidationSuccess(result)) {
        expect(Object.keys(result.data)).toHaveLength(0);
      }
    });

    it('should reject invalid partial update data', () => {
      const invalidUpdateData = {
        start_date: '2024-01-31',
        end_date: '2024-01-01' // End before start
      };

      const result = validateScheduleUpdateData(invalidUpdateData);

      expect(result.success).toBe(false);
    });
  });
});
