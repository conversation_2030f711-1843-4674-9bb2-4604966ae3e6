
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { errorReporting } from '@/lib/monitoring/errorReporting';

// Mock fetch
global.fetch = vi.fn();

describe('ErrorReporting', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should capture errors with context', () => {
    const error = new Error('Test error');
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    errorReporting.captureError(error, {
      tags: { component: 'test' }
    });

    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });

  it('should respect enabled state', () => {
    errorReporting.setEnabled(false);
    
    const error = new Error('Test error');
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    errorReporting.captureError(error);

    expect(consoleSpy).not.toHaveBeenCalled();
    
    errorReporting.setEnabled(true);
    consoleSpy.mockRestore();
  });
});
