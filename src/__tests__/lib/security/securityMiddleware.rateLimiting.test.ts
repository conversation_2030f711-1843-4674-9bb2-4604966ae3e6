
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SecurityMiddleware } from '@/lib/security/securityMiddleware';
import { RateLimitError } from '@/lib/security/rateLimiter';

// Mock the rate limiter
vi.mock('@/lib/security/rateLimiter', () => ({
  checkUserRateLimit: vi.fn(),
  RateLimitError: class extends Error {
    constructor(
      public remaining: number,
      public resetTime: number,
      message: string
    ) {
      super(message);
      this.name = 'RateLimitError';
    }
  },
  RATE_LIMITS: {
    API_GENERAL: { requests: 100, windowMs: 60000 },
    CREATE_OPERATIONS: { requests: 20, windowMs: 60000 }
  }
}));

describe('SecurityMiddleware - Rate Limiting', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('applyRateLimit', () => {
    it('should pass when rate limit is not exceeded', async () => {
      const { checkUserRateLimit } = await import('@/lib/security/rateLimiter');
      const mockCheckUserRateLimit = vi.mocked(checkUserRateLimit);
      mockCheckUserRateLimit.mockResolvedValue({
        allowed: true,
        remaining: 99,
        resetTime: Date.now() + 60000
      });

      await expect(SecurityMiddleware.applyRateLimit('API_GENERAL')).resolves.not.toThrow();
    });

    it('should throw RateLimitError when rate limit is exceeded', async () => {
      const { checkUserRateLimit } = await import('@/lib/security/rateLimiter');
      const mockCheckUserRateLimit = vi.mocked(checkUserRateLimit);
      mockCheckUserRateLimit.mockResolvedValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() + 60000
      });

      await expect(SecurityMiddleware.applyRateLimit('API_GENERAL'))
        .rejects.toThrow(RateLimitError);
    });
  });

  describe('secureOperation', () => {
    const mockOperation = vi.fn();

    beforeEach(() => {
      mockOperation.mockResolvedValue({
        success: true,
        data: 'test data',
        error: null
      });
    });

    it('should execute operation successfully when rate limit allows', async () => {
      const { checkUserRateLimit } = await import('@/lib/security/rateLimiter');
      const mockCheckUserRateLimit = vi.mocked(checkUserRateLimit);
      mockCheckUserRateLimit.mockResolvedValue({
        allowed: true,
        remaining: 99,
        resetTime: Date.now() + 60000
      });

      const result = await SecurityMiddleware.secureOperation(
        mockOperation,
        'API_GENERAL'
      );

      expect(result.success).toBe(true);
      expect(mockOperation).toHaveBeenCalledOnce();
    });

    it('should handle operation errors', async () => {
      mockOperation.mockRejectedValue(new Error('Operation failed'));

      const result = await SecurityMiddleware.secureOperation(
        mockOperation,
        'API_GENERAL'
      );

      expect(result.success).toBe(false);
      expect(result.error).toBe('Operation failed due to security restrictions');
    });
  });
});
