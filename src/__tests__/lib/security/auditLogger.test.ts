
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AuditLogger, AuditLogEntry } from '@/lib/security/auditLogger';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } },
        error: null
      })
    },
    from: vi.fn(() => ({
      insert: vi.fn().mockResolvedValue({ error: null })
    }))
  }
}));

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('AuditLogger', () => {
  let auditLogger: AuditLogger;

  beforeEach(() => {
    vi.clearAllMocks();
    auditLogger = new AuditLogger();
    mockSessionStorage.getItem.mockReturnValue('test-session-id');
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('log', () => {
    it('should add entry to buffer', async () => {
      const entry: AuditLogEntry = {
        eventType: 'access',
        resource: 'test-resource',
        action: 'read',
        severity: 'low'
      };

      await auditLogger.log(entry);
      
      // Check that the entry was processed (buffer should be empty after flush for critical)
      expect(auditLogger['buffer'].length).toBeGreaterThanOrEqual(0);
    });

    it('should immediately flush critical events', async () => {
      const criticalEntry: AuditLogEntry = {
        eventType: 'authentication',
        resource: 'user-access',
        action: 'failed-login',
        severity: 'critical'
      };

      const flushSpy = vi.spyOn(auditLogger as any, 'flush');
      await auditLogger.log(criticalEntry);
      
      expect(flushSpy).toHaveBeenCalled();
    });

    it('should enrich entry with metadata', async () => {
      const entry: AuditLogEntry = {
        eventType: 'modification',
        resource: 'test-resource',
        action: 'update',
        severity: 'medium'
      };

      await auditLogger.log(entry);
      
      // The entry should be enriched with timestamp, sessionId, etc.
      expect(mockSessionStorage.getItem).toHaveBeenCalledWith('audit_session_id');
    });
  });

  describe('static convenience methods', () => {
    it('should log access events', async () => {
      const logSpy = vi.spyOn(AuditLogger.prototype, 'log');
      
      await AuditLogger.logAccess('test-resource', 'read');
      
      expect(logSpy).toHaveBeenCalledWith({
        eventType: 'access',
        resource: 'test-resource',
        action: 'read',
        metadata: undefined,
        severity: 'low'
      });
    });

    it('should log modification events', async () => {
      const logSpy = vi.spyOn(AuditLogger.prototype, 'log');
      
      await AuditLogger.logModification('test-resource', 'update', { field: 'value' });
      
      expect(logSpy).toHaveBeenCalledWith({
        eventType: 'modification',
        resource: 'test-resource',
        action: 'update',
        metadata: { field: 'value' },
        severity: 'medium'
      });
    });

    it('should log compliance events', async () => {
      const logSpy = vi.spyOn(AuditLogger.prototype, 'log');
      
      await AuditLogger.logComplianceEvent(
        'user-data', 
        'export', 
        'GDPR', 
        'data-portability'
      );
      
      expect(logSpy).toHaveBeenCalledWith({
        eventType: 'access',
        resource: 'user-data',
        action: 'export',
        metadata: undefined,
        severity: 'medium',
        compliance: {
          regulation: 'GDPR',
          category: 'data-portability'
        }
      });
    });
  });

  describe('singleton pattern', () => {
    it('should return the same instance', () => {
      const instance1 = AuditLogger.getInstance();
      const instance2 = AuditLogger.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
});
