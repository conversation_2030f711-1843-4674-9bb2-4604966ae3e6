
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { z } from 'zod';
import { SecurityMiddleware } from '@/lib/security/securityMiddleware';

// Mock the input sanitizer
vi.mock('@/lib/security/inputSanitizer', () => ({
  InputSanitizer: {
    sanitizeProductionData: vi.fn((data) => data),
    sanitizeFileName: vi.fn((name) => name)
  }
}));

// Type guard function for validation results
function isValidationError<T>(result: any): result is { success: false; errors: Record<string, string> } {
  return result.success === false;
}

describe('SecurityMiddleware - Validation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('sanitizeAndValidate', () => {
    const testSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Invalid email')
    });

    it('should return success with valid data', () => {
      const validData = { name: 'John', email: '<EMAIL>' };
      const result = SecurityMiddleware.sanitizeAndValidate(validData, testSchema);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should return errors with invalid data', () => {
      const invalidData = { name: '', email: 'invalid' };
      const result = SecurityMiddleware.sanitizeAndValidate(invalidData, testSchema);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(result.errors).toHaveProperty('name');
        expect(result.errors).toHaveProperty('email');
      }
    });
  });

  describe('validateFileUpload', () => {
    const createMockFile = (name: string, type: string, size: number): File => {
      const file = new File(['content'], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('should validate acceptable file', async () => {
      const file = createMockFile('test.pdf', 'application/pdf', 1024 * 1024); // 1MB
      
      const result = await SecurityMiddleware.validateFileUpload(file);

      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject oversized file', async () => {
      const file = createMockFile('large.pdf', 'application/pdf', 15 * 1024 * 1024); // 15MB
      
      const result = await SecurityMiddleware.validateFileUpload(file, { maxSize: 10 * 1024 * 1024 });

      expect(result.valid).toBe(false);
      expect(result.error).toContain('File size exceeds');
    });

    it('should reject disallowed file type', async () => {
      const file = createMockFile('script.exe', 'application/x-msdownload', 1024);
      
      const result = await SecurityMiddleware.validateFileUpload(file);

      expect(result.valid).toBe(false);
      expect(result.error).toContain('File type');
    });

    it('should reject disallowed file extension', async () => {
      const file = createMockFile('script.sh', 'text/plain', 1024);
      
      const result = await SecurityMiddleware.validateFileUpload(file);

      expect(result.valid).toBe(false);
      expect(result.error).toContain('File extension');
    });
  });
});
