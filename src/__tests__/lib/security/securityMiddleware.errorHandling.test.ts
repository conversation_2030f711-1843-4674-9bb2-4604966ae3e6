
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { z } from 'zod';
import { SecurityMiddleware, securityHelpers } from '@/lib/security/securityMiddleware';
import { RateLimitError } from '@/lib/security/rateLimiter';

// Mock dependencies
vi.mock('@/lib/security/rateLimiter', () => ({
  RateLimitError: class extends Error {
    constructor(
      public remaining: number,
      public resetTime: number,
      message: string
    ) {
      super(message);
      this.name = 'RateLimitError';
    }
  }
}));

describe('SecurityMiddleware - Error Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('handleSecurityError', () => {
    it('should handle RateLimitError correctly', () => {
      const resetTime = Date.now() + 60000;
      const error = new RateLimitError(0, resetTime, 'Rate limit exceeded');
      
      const result = SecurityMiddleware.handleSecurityError(error);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });

    it('should handle ZodError correctly', () => {
      const schema = z.object({ name: z.string().min(1) });
      let zodError: z.ZodError;
      
      try {
        schema.parse({ name: '' });
      } catch (error) {
        zodError = error as z.ZodError;
      }

      const result = SecurityMiddleware.handleSecurityError(zodError!);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Validation failed');
    });

    it('should handle unknown errors securely', () => {
      const unknownError = new Error('Internal server error');
      
      const result = SecurityMiddleware.handleSecurityError(unknownError);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Operation failed due to security restrictions');
    });
  });

  describe('logSecurityEvent', () => {
    it('should log security event with proper structure', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      SecurityMiddleware.logSecurityEvent({
        type: 'validation_error',
        userId: 'user123',
        details: 'Invalid input detected',
        metadata: { input: 'malicious_data' }
      });

      expect(consoleSpy).toHaveBeenCalledWith(
        'Security Event:',
        expect.objectContaining({
          type: 'validation_error',
          userId: 'user123',
          details: 'Invalid input detected',
          metadata: { input: 'malicious_data' },
          timestamp: expect.any(String),
          userAgent: expect.any(String)
        })
      );
    });
  });
});
