
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { z } from 'zod';
import { SecurityMiddleware, securityHelpers } from '@/lib/security/securityMiddleware';

// Mock dependencies
vi.mock('@/lib/security/rateLimiter', () => ({
  checkUserRateLimit: vi.fn(),
  RATE_LIMITS: {
    API_GENERAL: { requests: 100, windowMs: 60000 }
  }
}));

vi.mock('@/lib/security/inputSanitizer', () => ({
  InputSanitizer: {
    sanitizeProductionData: vi.fn((data) => data)
  }
}));

describe('Security Helpers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  describe('withSecurity', () => {
    it('should wrap function with security middleware', async () => {
      const mockFn = vi.fn().mockResolvedValue({
        success: true,
        data: 'test',
        error: null
      });

      const { checkUserRateLimit } = await import('@/lib/security/rateLimiter');
      const mockedCheckUserRateLimit = vi.mocked(checkUserRateLimit);
      mockedCheckUserRateLimit.mockResolvedValue({
        allowed: true,
        remaining: 99,
        resetTime: Date.now() + 60000
      });

      const securedFn = securityHelpers.withSecurity(mockFn, 'API_GENERAL');
      const result = await securedFn('arg1', 'arg2');

      expect(result.success).toBe(true);
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });
  });

  describe('validateInput', () => {
    const testSchema = z.object({
      name: z.string().min(1, 'Name required')
    });

    it('should validate input and log errors on failure', () => {
      const logSpy = vi.spyOn(SecurityMiddleware, 'logSecurityEvent').mockImplementation(() => {});
      
      const result = securityHelpers.validateInput({ name: '' }, testSchema);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(logSpy).toHaveBeenCalledWith({
          type: 'validation_error',
          details: expect.stringContaining('Validation failed'),
          metadata: { inputData: { name: '' } }
        });
      }
    });
  });
});
