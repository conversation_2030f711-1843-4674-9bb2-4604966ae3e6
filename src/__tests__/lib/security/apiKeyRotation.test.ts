
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ApiKeyRotationManager } from '@/lib/security/apiKeyRotation';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn().mockResolvedValue({ error: null }),
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ 
        data: {
          id: 'test-id',
          key_name: 'test-key',
          created_at: new Date().toISOString(),
          old_key_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        }, 
        error: null 
      })
    }))
  }
}));

// Mock SecurityMiddleware
vi.mock('@/lib/security/securityMiddleware', () => ({
  SecurityMiddleware: {
    logSecurityEvent: vi.fn()
  }
}));

describe('ApiKeyRotationManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('rotateApiKey', () => {
    it('should successfully rotate an API key', async () => {
      const config = {
        keyName: 'test-api-key',
        rotationIntervalDays: 30,
        gracePeriodDays: 7
      };

      const result = await ApiKeyRotationManager.rotateApiKey(config);

      expect(result.success).toBe(true);
      expect(result.newKeyId).toBeDefined();
      expect(result.error).toBeUndefined();
    });

    it('should handle rotation errors gracefully', async () => {
      // Mock database error
      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.from).mockReturnValueOnce({
        insert: vi.fn().mockResolvedValue({ 
          error: { message: 'Database connection failed' } 
        })
      } as any);

      const config = {
        keyName: 'test-api-key',
        rotationIntervalDays: 30,
        gracePeriodDays: 7
      };

      const result = await ApiKeyRotationManager.rotateApiKey(config);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Database connection failed');
    });
  });

  describe('checkKeyExpiration', () => {
    it('should identify keys that need rotation', async () => {
      // Mock key that expires soon
      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.from).mockReturnValueOnce({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: {
            id: 'test-id',
            key_name: 'test-key',
            created_at: new Date().toISOString(),
            old_key_expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days
          },
          error: null
        })
      } as any);

      const result = await ApiKeyRotationManager.checkKeyExpiration('test-key');

      expect(result.needsRotation).toBe(true);
      expect(result.daysUntilExpiration).toBeLessThanOrEqual(7);
      expect(result.keyInfo).toBeDefined();
    });

    it('should handle missing key records', async () => {
      // Mock no key found
      const { supabase } = await import('@/integrations/supabase/client');
      vi.mocked(supabase.from).mockReturnValueOnce({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockReturnThis(),
        limit: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { code: 'PGRST116' } // Not found
        })
      } as any);

      const result = await ApiKeyRotationManager.checkKeyExpiration('non-existent-key');

      expect(result.needsRotation).toBe(true);
      expect(result.daysUntilExpiration).toBe(0);
      expect(result.keyInfo).toBeUndefined();
    });
  });

  describe('scheduleRotation', () => {
    it('should schedule rotation successfully', async () => {
      const result = await ApiKeyRotationManager.scheduleRotation('test-key', 30);

      expect(result).toBe(true);
    });
  });
});
