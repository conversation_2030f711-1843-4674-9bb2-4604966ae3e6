
import { describe, it, expect } from 'vitest';
import { createReportSchema } from '@/features/production/validation/report-validation';

describe('Report Validation Schema', () => {
  describe('createReportSchema', () => {
    it('should validate correct report data', () => {
      const validReport = {
        title: 'Daily Report',
        report_type: 'daily' as const,
        content: {
          summary: 'Good progress today',
          details: 'Completed 3 scenes',
          issues: 'Minor lighting issues',
          next_steps: 'Continue with scene 4'
        },
        date: '2024-01-15',
        status: 'draft' as const
      };

      const result = createReportSchema.safeParse(validReport);
      expect(result.success).toBe(true);
    });

    it('should reject report with invalid date', () => {
      const invalidReport = {
        title: 'Invalid Report',
        report_type: 'daily' as const,
        content: {},
        date: 'invalid-date'
      };

      const result = createReportSchema.safeParse(invalidReport);
      expect(result.success).toBe(false);
    });

    it('should reject report with invalid report type', () => {
      const invalidReport = {
        title: 'Invalid Report',
        report_type: 'invalid_type',
        content: {},
        date: '2024-01-15'
      };

      const result = createReportSchema.safeParse(invalidReport);
      expect(result.success).toBe(false);
    });
  });
});
