
import { describe, it, expect, beforeEach } from 'vitest';
import { Validator } from '@/lib/validation/validator';
import { rules } from '@/lib/validation/rules';

describe('Validator', () => {
  let validator: Validator;

  beforeEach(() => {
    validator = Validator.getInstance();
    validator.setRules({});
  });

  describe('field validation', () => {
    it('should validate required fields', async () => {
      validator.setRules({
        email: [rules.required(), rules.email()]
      });

      const errors = await validator.validateField('email', '');
      expect(errors).toContain('This field is required');
    });

    it('should validate email format', async () => {
      validator.setRules({
        email: [rules.email()]
      });

      const errors = await validator.validateField('email', 'invalid-email');
      expect(errors).toContain('Invalid email address');
    });

    it('should pass valid input', async () => {
      validator.setRules({
        email: [rules.required(), rules.email()]
      });

      const errors = await validator.validateField('email', '<EMAIL>');
      expect(errors).toHaveLength(0);
    });
  });

  describe('form validation', () => {
    it('should validate entire form', async () => {
      validator.setRules({
        email: [rules.required(), rules.email()],
        password: [rules.required(), rules.minLength(8)]
      });

      const result = await validator.validateForm({
        email: 'invalid',
        password: '123'
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.email).toBeDefined();
      expect(result.errors.password).toBeDefined();
    });

    it('should return valid for correct form', async () => {
      validator.setRules({
        email: [rules.required(), rules.email()],
        password: [rules.required(), rules.minLength(8)]
      });

      const result = await validator.validateForm({
        email: '<EMAIL>',
        password: 'password123'
      });

      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });
  });
});
