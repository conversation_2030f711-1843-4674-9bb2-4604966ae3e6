
import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { validateFormData, formatValidationErrors } from '@/features/production/validation/validation-helpers';

describe('Production Validation Helpers', () => {
  describe('validateFormData', () => {
    const simpleSchema = z.object({
      title: z.string().min(1, 'Title is required'),
      count: z.number().min(0, 'Count must be non-negative')
    });

    it('should return success with valid data', () => {
      const validData = { title: 'Test Title', count: 5 };
      const result = validateFormData(simpleSchema, validData);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should return error with invalid data', () => {
      const invalidData = { title: '', count: -1 };
      const result = validateFormData(simpleSchema, invalidData);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBeInstanceOf(z.ZodError);
        expect(result.error.issues).toHaveLength(2);
      }
    });

    it('should handle non-ZodError exceptions', () => {
      const faultySchema = {
        parse: () => {
          throw new Error('Custom error');
        }
      } as unknown as z.ZodSchema<any>;

      expect(() => validateFormData(faultySchema, {})).toThrow('Custom error');
    });
  });

  describe('formatValidationErrors', () => {
    it('should format simple validation errors', () => {
      const schema = z.object({
        name: z.string().min(1, 'Name is required'),
        email: z.string().email('Invalid email')
      });

      try {
        schema.parse({ name: '', email: 'bad-email' });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const formatted = formatValidationErrors(error);
          expect(formatted).toEqual({
            name: 'Name is required',
            email: 'Invalid email'
          });
        }
      }
    });

    it('should handle nested path validation errors', () => {
      const schema = z.object({
        user: z.object({
          contact: z.object({
            phone: z.string().min(10, 'Phone must be at least 10 digits')
          })
        })
      });

      try {
        schema.parse({ user: { contact: { phone: '123' } } });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const formatted = formatValidationErrors(error);
          expect(formatted).toHaveProperty('user.contact.phone');
          expect(formatted['user.contact.phone']).toBe('Phone must be at least 10 digits');
        }
      }
    });
  });
});
