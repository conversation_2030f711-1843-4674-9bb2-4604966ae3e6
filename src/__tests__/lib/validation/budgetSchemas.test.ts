
import { describe, it, expect } from 'vitest';
import { createBudgetSchema } from '@/features/production/validation/budget-validation';

describe('Budget Validation Schema', () => {
  describe('createBudgetSchema', () => {
    it('should validate correct budget data', () => {
      const validBudget = {
        title: 'Production Budget',
        description: 'Main budget for film',
        total_budget: 100000,
        currency: 'USD',
        status: 'draft' as const
      };

      const result = createBudgetSchema.safeParse(validBudget);
      expect(result.success).toBe(true);
    });

    it('should reject budget with negative total', () => {
      const invalidBudget = {
        title: 'Production Budget',
        total_budget: -1000,
        currency: 'USD'
      };

      const result = createBudgetSchema.safeParse(invalidBudget);
      expect(result.success).toBe(false);
    });

    it('should use default values for optional fields', () => {
      const minimalBudget = {
        title: 'Minimal Budget',
        total_budget: 50000
      };

      const result = createBudgetSchema.safeParse(minimalBudget);
      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.currency).toBe('USD');
        expect(result.data.status).toBe('draft');
      }
    });
  });
});
