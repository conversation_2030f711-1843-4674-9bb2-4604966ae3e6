
import { describe, it, expect } from 'vitest';
import { createScheduleSchema } from '@/lib/validation/schedule-schemas';

describe('Schedule Validation Schema', () => {
  describe('createScheduleSchema', () => {
    it('should validate correct schedule data', () => {
      const validSchedule = {
        title: 'Production Schedule',
        description: 'Main production schedule',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'draft' as const
      };

      const result = createScheduleSchema.safeParse(validSchedule);
      expect(result.success).toBe(true);
    });

    it('should reject schedule with end date before start date', () => {
      const invalidSchedule = {
        title: 'Production Schedule',
        start_date: '2024-01-31',
        end_date: '2024-01-01',
        status: 'draft' as const
      };

      const result = createScheduleSchema.safeParse(invalidSchedule);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.some(issue => 
          issue.path.includes('end_date') && 
          issue.message.includes('End date must be after start date')
        )).toBe(true);
      }
    });

    it('should reject schedule with missing required fields', () => {
      const invalidSchedule = {
        description: 'Missing title and dates'
      };

      const result = createScheduleSchema.safeParse(invalidSchedule);
      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0);
      }
    });
  });
});
