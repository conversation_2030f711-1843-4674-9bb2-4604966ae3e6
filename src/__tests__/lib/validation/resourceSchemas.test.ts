
import { describe, it, expect } from 'vitest';
import { createResourceSchema } from '@/features/production/validation/resource-validation';

describe('Resource Validation Schema', () => {
  describe('createResourceSchema', () => {
    it('should validate correct resource data', () => {
      const validResource = {
        name: 'Camera Equipment',
        type: 'equipment' as const,
        description: 'Professional camera setup',
        availability_status: 'available' as const,
        cost_per_day: 500
      };

      const result = createResourceSchema.safeParse(validResource);
      expect(result.success).toBe(true);
    });

    it('should reject resource with invalid type', () => {
      const invalidResource = {
        name: 'Invalid Resource',
        type: 'invalid_type',
        cost_per_day: 100
      };

      const result = createResourceSchema.safeParse(invalidResource);
      expect(result.success).toBe(false);
    });

    it('should reject resource with negative cost', () => {
      const invalidResource = {
        name: 'Expensive Resource',
        type: 'equipment' as const,
        cost_per_day: -100
      };

      const result = createResourceSchema.safeParse(invalidResource);
      expect(result.success).toBe(false);
    });
  });
});
