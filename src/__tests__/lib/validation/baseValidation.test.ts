
import { describe, it, expect } from 'vitest';
import { z } from 'zod';
import { validateInput } from '@/lib/validation/base-schemas';

// Type guard function for validation results
function isValidationError<T>(result: any): result is { success: false; errors: Record<string, string> } {
  return result.success === false;
}

describe('Base Validation Utilities', () => {
  describe('validateInput', () => {
    const testSchema = z.object({
      name: z.string().min(1, 'Name is required'),
      email: z.string().email('Invalid email'),
      age: z.number().positive('Age must be positive')
    });

    it('should return success with valid data', () => {
      const validData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        age: 30
      };

      const result = validateInput(testSchema, validData);

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(validData);
      }
    });

    it('should return errors with invalid data', () => {
      const invalidData = {
        name: '',
        email: 'invalid-email',
        age: -5
      };

      const result = validateInput(testSchema, invalidData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(result.errors).toHaveProperty('name');
        expect(result.errors).toHaveProperty('email');
        expect(result.errors).toHaveProperty('age');
        expect(result.errors.name).toBe('Name is required');
        expect(result.errors.email).toBe('Invalid email');
        expect(result.errors.age).toBe('Age must be positive');
      }
    });

    it('should handle nested object validation', () => {
      const nestedSchema = z.object({
        user: z.object({
          profile: z.object({
            firstName: z.string().min(1, 'First name required')
          })
        })
      });

      const invalidNestedData = {
        user: {
          profile: {
            firstName: ''
          }
        }
      };

      const result = validateInput(nestedSchema, invalidNestedData);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(result.errors).toHaveProperty('user.profile.firstName');
      }
    });

    it('should handle unexpected validation errors gracefully', () => {
      const result = validateInput(testSchema, null);

      expect(result.success).toBe(false);
      if (isValidationError(result)) {
        expect(result.errors).toHaveProperty('general');
      }
    });
  });
});
