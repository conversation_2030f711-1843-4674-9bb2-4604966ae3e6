
import { describe, it, expect, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSchedule, useBudget, useResource, useReport } from '@/hooks';
import React from 'react';

// Mock the context providers
vi.mock('@/contexts/OrganizationContext', () => ({
  useOrganization: () => ({
    currentOrganization: {
      id: 'test-org-id',
      name: 'Test Organization'
    }
  })
}));

vi.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      id: 'test-user-id',
      email: '<EMAIL>'
    }
  })
}));

// Mock the API modules
vi.mock('@/lib/api/production', () => ({
  schedulesApi: {
    getSchedules: vi.fn(() => Promise.resolve({
      success: true,
      data: [],
      error: null
    })),
    createSchedule: vi.fn(() => Promise.resolve({
      success: true,
      data: { id: '1', title: 'Test Schedule' },
      error: null
    }))
  },
  budgetsApi: {
    getBudgets: vi.fn(() => Promise.resolve({
      success: true,
      data: [],
      error: null
    })),
    createBudget: vi.fn(() => Promise.resolve({
      success: true,
      data: { id: '1', title: 'Test Budget' },
      error: null
    }))
  },
  resourcesApi: {
    getResources: vi.fn(() => Promise.resolve({
      success: true,
      data: [],
      error: null
    })),
    createResource: vi.fn(() => Promise.resolve({
      success: true,
      data: { id: '1', name: 'Test Resource' },
      error: null
    }))
  },
  reportsApi: {
    getReports: vi.fn(() => Promise.resolve({
      success: true,
      data: [],
      error: null
    })),
    createReport: vi.fn(() => Promise.resolve({
      success: true,
      data: { id: '1', title: 'Test Report' },
      error: null
    }))
  }
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Production Hooks', () => {
  describe('useSchedule', () => {
    it('should provide schedule data and operations', async () => {
      const { result } = renderHook(() => useSchedule(), {
        wrapper: createWrapper(),
      });

      expect(result.current.schedules).toEqual([]);
      expect(result.current.loading).toBe(true);
      expect(typeof result.current.createSchedule).toBe('function');
      expect(typeof result.current.refetch).toBe('function');
    });
  });

  describe('useBudget', () => {
    it('should provide budget data and operations', async () => {
      const { result } = renderHook(() => useBudget(), {
        wrapper: createWrapper(),
      });

      expect(result.current.budgets).toEqual([]);
      expect(result.current.totalBudgetAmount).toBe(0);
      expect(typeof result.current.createBudget).toBe('function');
      expect(typeof result.current.refetch).toBe('function');
    });
  });

  describe('useResource', () => {
    it('should provide resource data and operations', async () => {
      const { result } = renderHook(() => useResource(), {
        wrapper: createWrapper(),
      });

      expect(result.current.resources).toEqual([]);
      expect(result.current.availableResources).toEqual([]);
      expect(typeof result.current.createResource).toBe('function');
      expect(typeof result.current.refetch).toBe('function');
    });
  });

  describe('useReport', () => {
    it('should provide report data and operations', async () => {
      const { result } = renderHook(() => useReport(), {
        wrapper: createWrapper(),
      });

      expect(result.current.reports).toEqual([]);
      expect(result.current.recentReports).toEqual([]);
      expect(typeof result.current.createReport).toBe('function');
      expect(typeof result.current.refetch).toBe('function');
    });
  });
});
