
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { schedulesApi, budgetsApi, resourcesApi, reportsApi } from '@/lib/api/production';
import type { 
  ProductionSchedule, 
  ProductionBudget, 
  ProductionResource, 
  ProductionReport 
} from '@/lib/api/production/types';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => Promise.resolve({
            data: [],
            error: null
          }))
        }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({
            data: null,
            error: null
          }))
        }))
      }))
    }))
  }
}));

describe('Production API', () => {
  const mockOrgId = 'test-org-id';
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Schedules API', () => {
    it('should fetch schedules successfully', async () => {
      const mockSchedules: ProductionSchedule[] = [
        {
          id: '1',
          org_id: mockOrgId,
          user_id: 'user-1',
          title: 'Test Schedule',
          description: 'Test Description',
          start_date: '2024-01-01',
          end_date: '2024-01-31',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ];

      // Mock successful response
      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockSchedules,
              error: null
            }))
          }))
        }))
      } as any);

      const result = await schedulesApi.getSchedules(mockOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockSchedules);
      expect(result.error).toBe(null);
    });

    it('should handle schedule creation', async () => {
      const newSchedule = {
        org_id: mockOrgId,
        user_id: 'user-1',
        title: 'New Schedule',
        description: 'New Description',
        start_date: '2024-02-01',
        end_date: '2024-02-28',
        status: 'draft' as const
      };

      const createdSchedule: ProductionSchedule = {
        ...newSchedule,
        id: '2',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        insert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({
              data: createdSchedule,
              error: null
            }))
          }))
        }))
      } as any);

      const result = await schedulesApi.createSchedule(newSchedule);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(createdSchedule);
    });
  });

  describe('Budgets API', () => {
    it('should fetch budgets successfully', async () => {
      const mockBudgets: ProductionBudget[] = [
        {
          id: '1',
          org_id: mockOrgId,
          user_id: 'user-1',
          title: 'Test Budget',
          description: 'Test Description',
          total_budget: 10000,
          currency: 'USD',
          status: 'active',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockBudgets,
              error: null
            }))
          }))
        }))
      } as any);

      const result = await budgetsApi.getBudgets(mockOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockBudgets);
    });
  });

  describe('Resources API', () => {
    it('should fetch resources successfully', async () => {
      const mockResources: ProductionResource[] = [
        {
          id: '1',
          org_id: mockOrgId,
          user_id: 'user-1',
          name: 'Test Resource',
          type: 'equipment',
          description: 'Test Description',
          availability_status: 'available',
          cost_per_day: 100,
          contact_info: {},
          specifications: {},
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockResources,
              error: null
            }))
          }))
        }))
      } as any);

      const result = await resourcesApi.getResources(mockOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResources);
    });
  });

  describe('Reports API', () => {
    it('should fetch reports successfully', async () => {
      const mockReports: ProductionReport[] = [
        {
          id: '1',
          org_id: mockOrgId,
          user_id: 'user-1',
          schedule_item_id: null,
          title: 'Test Report',
          report_type: 'daily',
          content: { summary: 'Test content' },
          date: '2024-01-01',
          status: 'draft',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        }
      ];

      const mockSupabase = await import('@/integrations/supabase/client');
      vi.mocked(mockSupabase.supabase.from).mockReturnValue({
        select: vi.fn(() => ({
          eq: vi.fn(() => ({
            order: vi.fn(() => Promise.resolve({
              data: mockReports,
              error: null
            }))
          }))
        }))
      } as any);

      const result = await reportsApi.getReports(mockOrgId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockReports);
    });
  });
});
