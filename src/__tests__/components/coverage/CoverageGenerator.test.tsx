
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/dom';
import { renderWithProviders, mockUser, mockOrganization } from '@/lib/testing/testUtils';
import CoverageGenerator from '@/features/coverage/CoverageGenerator';

// Mock the coverage hooks
vi.mock('@/features/coverage/hooks/useCoverageOperations', () => ({
  useCoverageOperations: vi.fn(() => ({
    generating: false,
    processingJobs: new Map(),
    generateCoverage: vi.fn(),
    deleteCoverageReport: vi.fn(),
    copyReportToClipboard: vi.fn(),
    getCacheStats: vi.fn(() => ({ 
      size: 10, 
      maxSize: 100, 
      totalAccesses: 50, 
      averageAge: 300 
    })),
    clearCache: vi.fn(),
    dismissJob: vi.fn()
  }))
}));

vi.mock('@/hooks/useCoverage', () => ({
  useCoverage: vi.fn(() => ({
    reports: [],
    isLoading: false,
    error: null,
    refetch: vi.fn()
  }))
}));

vi.mock('@/hooks/useScenes', () => ({
  useScenes: vi.fn(() => ({
    scenes: [
      { 
        id: 'scene-1', 
        title: 'Test Scene', 
        content: 'This is a test scene content.',
        user_id: mockUser.id,
        org_id: mockOrganization.id
      }
    ],
    isLoading: false,
    error: null
  }))
}));

describe('CoverageGenerator', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render coverage generator interface', () => {
    renderWithProviders(<CoverageGenerator />);
    
    expect(screen.getByText('AI Coverage Generator')).toBeInTheDocument();
    expect(screen.getByText('Select Scene')).toBeInTheDocument();
    expect(screen.getByText('Fidelity Level')).toBeInTheDocument();
  });

  it('should show scene selection dropdown', () => {
    renderWithProviders(<CoverageGenerator />);
    
    expect(screen.getByRole('combobox')).toBeInTheDocument();
  });

  it('should display fidelity level options', () => {
    renderWithProviders(<CoverageGenerator />);
    
    expect(screen.getByText('Standard')).toBeInTheDocument();
    expect(screen.getByText('Premium')).toBeInTheDocument();
    expect(screen.getByText('Professional')).toBeInTheDocument();
  });

  it('should handle coverage generation', async () => {
    const mockGenerateCoverage = vi.fn();
    const { useCoverageOperations } = await import('@/features/coverage/hooks/useCoverageOperations');
    vi.mocked(useCoverageOperations).mockReturnValue({
      generating: false,
      processingJobs: new Map(),
      generateCoverage: mockGenerateCoverage,
      deleteCoverageReport: vi.fn(),
      copyReportToClipboard: vi.fn(),
      getCacheStats: vi.fn(() => ({ 
        size: 10, 
        maxSize: 100, 
        totalAccesses: 50, 
        averageAge: 300 
      })),
      clearCache: vi.fn(),
      dismissJob: vi.fn()
    });

    renderWithProviders(<CoverageGenerator />);
    
    const generateButton = screen.getByRole('button', { name: /generate coverage/i });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(mockGenerateCoverage).toHaveBeenCalled();
    });
  });

  it('should show loading state during generation', () => {
    const { useCoverageOperations } = require('@/features/coverage/hooks/useCoverageOperations');
    vi.mocked(useCoverageOperations).mockReturnValue({
      generating: true,
      processingJobs: new Map(),
      generateCoverage: vi.fn(),
      deleteCoverageReport: vi.fn(),
      copyReportToClipboard: vi.fn(),
      getCacheStats: vi.fn(() => ({ 
        size: 10, 
        maxSize: 100, 
        totalAccesses: 50, 
        averageAge: 300 
      })),
      clearCache: vi.fn(),
      dismissJob: vi.fn()
    });

    renderWithProviders(<CoverageGenerator />);
    
    expect(screen.getByText('Generating...')).toBeInTheDocument();
  });

  it('should handle access restrictions for starter plans', () => {
    const restrictedAuthContext = {
      ...require('@/lib/testing/testUtils').mockAuthContext,
      user: mockUser
    };

    const restrictedOrgContext = {
      ...require('@/lib/testing/testUtils').mockOrganizationContext,
      currentOrganization: {
        ...mockOrganization,
        plan: 'starter'
      }
    };

    renderWithProviders(
      <CoverageGenerator />, 
      { 
        authContext: restrictedAuthContext,
        organizationContext: restrictedOrgContext 
      }
    );
    
    expect(screen.getByText(/Coverage Generator requires a Pro subscription/)).toBeInTheDocument();
  });
});
