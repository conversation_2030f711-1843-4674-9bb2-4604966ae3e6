
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/dom';
import { renderWithProviders, mockUser, mockOrganization, generateMockScene, generateMockCoverageReport } from '@/lib/testing/testUtils';
import CoverageGenerator from '@/features/coverage/CoverageGenerator';

// Mock all the required modules
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: mockUser },
        error: null
      })
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: generateMockCoverageReport(), error: null })
    })),
    functions: {
      invoke: vi.fn().mockResolvedValue({
        data: { coverage: 'Generated coverage report' },
        error: null
      })
    }
  }
}));

describe('Coverage Generation Workflow Integration', () => {
  const mockScene = generateMockScene({
    title: 'Integration Test Scene',
    content: 'This is a comprehensive scene for testing the full coverage generation workflow.'
  });

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock the hooks with realistic data
    vi.doMock('@/hooks/useScenes', () => ({
      useScenes: vi.fn(() => ({
        scenes: [mockScene],
        isLoading: false,
        error: null
      }))
    }));

    vi.doMock('@/hooks/useCoverage', () => ({
      useCoverage: vi.fn(() => ({
        reports: [],
        isLoading: false,
        error: null,
        refetch: vi.fn()
      }))
    }));

    vi.doMock('@/features/coverage/hooks/useCoverageOperations', () => ({
      useCoverageOperations: vi.fn(() => ({
        generating: false,
        processingJobs: new Map(),
        generateCoverage: vi.fn().mockResolvedValue(undefined),
        deleteCoverageReport: vi.fn(),
        copyReportToClipboard: vi.fn(),
        getCacheStats: vi.fn(() => ({ 
          size: 10, 
          maxSize: 100, 
          totalAccesses: 50, 
          averageAge: 300 
        })),
        clearCache: vi.fn(),
        dismissJob: vi.fn()
      }))
    }));
  });

  it('should complete full coverage generation workflow', async () => {
    const mockGenerateCoverage = vi.fn().mockImplementation((sceneId, fidelity, onSuccess) => {
      // Simulate successful generation
      setTimeout(() => {
        onSuccess(generateMockCoverageReport({ scene_id: sceneId }));
      }, 100);
    });

    const { useCoverageOperations } = await import('@/features/coverage/hooks/useCoverageOperations');
    vi.mocked(useCoverageOperations).mockReturnValue({
      generating: false,
      processingJobs: new Map(),
      generateCoverage: mockGenerateCoverage,
      deleteCoverageReport: vi.fn(),
      copyReportToClipboard: vi.fn(),
      getCacheStats: vi.fn(() => ({ 
        size: 10, 
        maxSize: 100, 
        totalAccesses: 50, 
        averageAge: 300 
      })),
      clearCache: vi.fn(),
      dismissJob: vi.fn()
    });

    renderWithProviders(<CoverageGenerator />);

    // Step 1: Select a scene
    const sceneSelect = screen.getByRole('combobox');
    fireEvent.click(sceneSelect);
    
    await waitFor(() => {
      expect(screen.getByText('Integration Test Scene')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Integration Test Scene'));

    // Step 2: Select fidelity level (default should be Standard)
    expect(screen.getByText('Standard')).toBeInTheDocument();

    // Step 3: Generate coverage
    const generateButton = screen.getByRole('button', { name: /generate coverage/i });
    expect(generateButton).not.toBeDisabled();
    
    fireEvent.click(generateButton);

    // Step 4: Verify generation process started
    await waitFor(() => {
      expect(mockGenerateCoverage).toHaveBeenCalledWith(
        mockScene.id,
        'Standard',
        expect.any(Function)
      );
    });
  });

  it('should handle errors gracefully during workflow', async () => {
    const mockGenerateCoverage = vi.fn().mockRejectedValue(new Error('Generation failed'));

    const { useCoverageOperations } = await import('@/features/coverage/hooks/useCoverageOperations');
    vi.mocked(useCoverageOperations).mockReturnValue({
      generating: false,
      processingJobs: new Map(),
      generateCoverage: mockGenerateCoverage,
      deleteCoverageReport: vi.fn(),
      copyReportToClipboard: vi.fn(),
      getCacheStats: vi.fn(() => ({ 
        size: 10, 
        maxSize: 100, 
        totalAccesses: 50, 
        averageAge: 300 
      })),
      clearCache: vi.fn(),
      dismissJob: vi.fn()
    });

    renderWithProviders(<CoverageGenerator />);

    // Select scene and attempt generation
    const sceneSelect = screen.getByRole('combobox');
    fireEvent.click(sceneSelect);
    fireEvent.click(screen.getByText('Integration Test Scene'));

    const generateButton = screen.getByRole('button', { name: /generate coverage/i });
    fireEvent.click(generateButton);

    await waitFor(() => {
      expect(mockGenerateCoverage).toHaveBeenCalled();
    });

    // The error should be handled gracefully without crashing the component
    expect(screen.getByText('AI Coverage Generator')).toBeInTheDocument();
  });

  it('should respect subscription limits', () => {
    const starterOrgContext = {
      ...require('@/lib/testing/testUtils').mockOrganizationContext,
      currentOrganization: {
        ...mockOrganization,
        plan: 'starter'
      }
    };

    renderWithProviders(
      <CoverageGenerator />, 
      { organizationContext: starterOrgContext }
    );

    // Should show access restriction for starter plan
    expect(screen.getByText(/Coverage Generator requires a Pro subscription/)).toBeInTheDocument();
  });

  it('should handle long content with background processing', async () => {
    const longContentScene = generateMockScene({
      title: 'Long Content Scene',
      content: 'A'.repeat(60000) // Simulate very long content (>50KB)
    });

    vi.doMock('@/hooks/useScenes', () => ({
      useScenes: vi.fn(() => ({
        scenes: [longContentScene],
        isLoading: false,
        error: null
      }))
    }));

    const mockGenerateCoverage = vi.fn();
    const processingJobsMap = new Map();
    processingJobsMap.set('job-1', { id: 'job-1', sceneId: longContentScene.id, progress: 50 });

    const { useCoverageOperations } = await import('@/features/coverage/hooks/useCoverageOperations');
    vi.mocked(useCoverageOperations).mockReturnValue({
      generating: false,
      processingJobs: processingJobsMap,
      generateCoverage: mockGenerateCoverage,
      deleteCoverageReport: vi.fn(),
      copyReportToClipboard: vi.fn(),
      getCacheStats: vi.fn(() => ({ 
        size: 10, 
        maxSize: 100, 
        totalAccesses: 50, 
        averageAge: 300 
      })),
      clearCache: vi.fn(),
      dismissJob: vi.fn()
    });

    renderWithProviders(<CoverageGenerator />);

    // Should show background processing indicator
    expect(screen.getByText(/Processing/)).toBeInTheDocument();
  });
});
