
import { describe, it, expect, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { useStoryboardData } from '@/hooks/storyboard/useStoryboardData';
import type { Storyboard } from '@/lib/api/storyboards';

// Mock the API
vi.mock('@/lib/api', () => ({
  storyboardsApi: {
    getStoryboards: vi.fn()
  }
}));

describe('useStoryboardData', () => {
  it('should fetch storyboards on mount', async () => {
    const { storyboardsApi } = await import('@/lib/api');
    const mockStoryboard: Storyboard = {
      id: '1',
      title: 'Test Storyboard',
      org_id: 'org-1',
      user_id: 'user-1',
      fidelity: 'medium',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z',
      description: 'Test description'
    };

    vi.mocked(storyboardsApi.getStoryboards).mockResolvedValue({
      success: true,
      data: [mockStoryboard],
      error: null
    });

    const { result } = renderHook(() => useStoryboardData('org-1'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.storyboards).toHaveLength(1);
    expect(storyboardsApi.getStoryboards).toHaveBeenCalledWith();
  });

  it('should handle selection', () => {
    const { result } = renderHook(() => useStoryboardData('org-1'));
    
    const storyboard: Storyboard = {
      id: '1',
      title: 'Test',
      org_id: 'org-1',
      user_id: 'user-1',
      fidelity: 'medium',
      created_at: '2023-01-01T00:00:00Z',
      updated_at: '2023-01-01T00:00:00Z'
    };
    
    result.current.selectStoryboard(storyboard);

    expect(result.current.selectedStoryboard).toEqual(storyboard);
  });
});
