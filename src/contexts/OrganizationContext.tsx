
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { organizationApi } from '@/lib/api';
import type { Organization } from '@/types/api';

interface OrganizationMembership {
  id: string;
  org_id: string;
  user_id: string;
  role: string;
  joined_at: string;
  profiles?: {
    username: string | null;
    full_name: string | null;
    avatar_url: string | null;
  } | null;
}

interface OrganizationContextType {
  currentOrganization: Organization | null;
  organizations: Organization[];
  memberships: OrganizationMembership[];
  members: OrganizationMembership[];
  isAdmin: boolean;
  loading: boolean;
  error: string | null;
  setCurrentOrganization: (org: Organization | null) => void;
  switchOrganization: (orgId: string) => void;
  refreshOrganizations: () => Promise<void>;
  createOrganization: (name: string) => Promise<void>;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export const useOrganization = () => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

export const OrganizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [currentOrganization, setCurrentOrganization] = useState<Organization | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [memberships, setMemberships] = useState<OrganizationMembership[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const isAdmin = currentOrganization ? 
    memberships.find(m => m.org_id === currentOrganization.id)?.role === 'admin' || 
    currentOrganization.owner_id === user?.id : false;

  // Get members for current organization
  const members = currentOrganization 
    ? memberships.filter(m => m.org_id === currentOrganization.id)
    : [];

  const switchOrganization = (orgId: string) => {
    const org = organizations.find(o => o.id === orgId);
    if (org) {
      setCurrentOrganization(org);
    }
  };

  const refreshOrganizations = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const [orgsResult, membershipsResult] = await Promise.all([
        organizationApi.getOrganizations(),
        organizationApi.getMemberships()
      ]);

      if (orgsResult.success && orgsResult.data) {
        const orgsWithOwnerIds = orgsResult.data.map(org => ({
          ...org,
          owner_id: org.created_by || user.id,
          settings: {}
        }));
        setOrganizations(orgsWithOwnerIds);
        if (!currentOrganization && orgsWithOwnerIds.length > 0) {
          setCurrentOrganization(orgsWithOwnerIds[0]);
        }
      }

      if (membershipsResult.success && membershipsResult.data) {
        // Transform the data to match our interface, handling cases where profiles might be null
        const transformedMemberships: OrganizationMembership[] = membershipsResult.data.map(item => {
          // Extract profiles safely - if it exists and has the right structure, use it
          const profiles = item.profiles && 
            typeof item.profiles === 'object' && 
            item.profiles !== null ? item.profiles : null;

          return {
            id: item.id,
            org_id: item.org_id,
            user_id: item.user_id,
            role: item.role,
            joined_at: item.joined_at || new Date().toISOString(),
            profiles: profiles ? {
              username: (profiles as any)?.username || null,
              full_name: (profiles as any)?.full_name || null,
              avatar_url: (profiles as any)?.avatar_url || null
            } : null
          };
        });
        setMemberships(transformedMemberships);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load organizations');
    } finally {
      setLoading(false);
    }
  };

  const createOrganization = async (name: string) => {
    if (!user) return;

    try {
      const result = await organizationApi.createOrganization({ name });
      if (result.success && result.data) {
        const orgWithOwnerData = {
          ...result.data,
          owner_id: result.data.created_by || user.id,
          settings: {}
        };
        await refreshOrganizations();
        setCurrentOrganization(orgWithOwnerData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create organization');
    }
  };

  useEffect(() => {
    if (user) {
      refreshOrganizations();
    } else {
      setCurrentOrganization(null);
      setOrganizations([]);
      setMemberships([]);
      setLoading(false);
    }
  }, [user]);

  const value: OrganizationContextType = {
    currentOrganization,
    organizations,
    memberships,
    members,
    isAdmin,
    loading,
    error,
    setCurrentOrganization,
    switchOrganization,
    refreshOrganizations,
    createOrganization
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};
