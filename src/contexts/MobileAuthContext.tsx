
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { mobileSupabase } from '@/integrations/supabase/mobile-client';
import type { User, Session } from '@supabase/supabase-js';

interface MobileAuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const MobileAuthContext = createContext<MobileAuthContextType>({
  user: null,
  session: null,
  loading: true,
  signOut: async () => {}
});

export const useMobileAuth = () => {
  const context = useContext(MobileAuthContext);
  if (!context) {
    throw new Error('useMobileAuth must be used within MobileAuthProvider');
  }
  return context;
};

interface MobileAuthProviderProps {
  children: ReactNode;
}

export const MobileAuthProvider: React.FC<MobileAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let mounted = true;

    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session: initialSession } } = await mobileSupabase.auth.getSession();
        if (mounted) {
          setSession(initialSession);
          setUser(initialSession?.user ?? null);
          setLoading(false);
        }
      } catch (error) {
        console.warn('Failed to get initial session:', error);
        if (mounted) {
          setLoading(false);
        }
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = mobileSupabase.auth.onAuthStateChange(
      async (event, session) => {
        if (mounted) {
          setSession(session);
          setUser(session?.user ?? null);
          setLoading(false);
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signOut = async () => {
    try {
      await mobileSupabase.auth.signOut();
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  return (
    <MobileAuthContext.Provider value={{
      user,
      session,
      loading,
      signOut
    }}>
      {children}
    </MobileAuthContext.Provider>
  );
};
