import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { env } from '@/lib/config/environment';

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(env.supabase.url, env.supabase.anonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  },
  global: {
    headers: {
      'x-application-name': 'scriptgenius',
      'x-application-version': env.app.version,
      'x-environment': env.app.environment,
    },
  },
  db: {
    schema: 'public',
  },
});