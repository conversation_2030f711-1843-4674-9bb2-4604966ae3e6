// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dygcfpcndgivnuimdgqv.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR5Z2NmcGNuZGdpdm51aW1kZ3F2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NTU2NjMsImV4cCI6MjA2NTAzMTY2M30.rTipcyif1-ERYZDbq5RC3fJl_KUiJJMqPtQvDdFDlFQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);