
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

// Mobile-optimized Supabase client with reduced timeouts and retry logic
export const mobileSupabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Disable for mobile to prevent issues
    flowType: 'pkce'
  },
  global: {
    headers: {
      'x-client-info': 'scriptgenius-mobile@1.0.0',
    },
    fetch: (url, options: RequestInit = {}) => {
      // Mobile-optimized fetch with shorter timeouts
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10s timeout

      return fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          ...options.headers,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        }
      }).finally(() => {
        clearTimeout(timeoutId);
      });
    }
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 2 // Reduce realtime events for mobile
    }
  }
});

// Simplified error handling for mobile
mobileSupabase.auth.onAuthStateChange((event, session) => {
  if (event === 'SIGNED_OUT' && !session) {
    // Clear any cached data
    localStorage.removeItem('supabase.auth.token');
    sessionStorage.clear();
  }
});
