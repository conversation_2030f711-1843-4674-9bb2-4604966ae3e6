export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      backup_schedules: {
        Row: {
          backup_type: string
          created_at: string | null
          id: string
          is_active: boolean | null
          last_run_at: string | null
          next_run_at: string | null
          retention_days: number
          schedule_expression: string
          updated_at: string | null
        }
        Insert: {
          backup_type: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          last_run_at?: string | null
          next_run_at?: string | null
          retention_days?: number
          schedule_expression: string
          updated_at?: string | null
        }
        Update: {
          backup_type?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          last_run_at?: string | null
          next_run_at?: string | null
          retention_days?: number
          schedule_expression?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      beta_analytics: {
        Row: {
          device_info: Json | null
          duration_ms: number | null
          event_name: string
          event_type: string
          feature_name: string | null
          id: string
          metadata: Json | null
          org_id: string | null
          page_path: string | null
          session_id: string
          timestamp: string
          user_agent: string | null
          user_id: string
        }
        Insert: {
          device_info?: Json | null
          duration_ms?: number | null
          event_name: string
          event_type: string
          feature_name?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          page_path?: string | null
          session_id: string
          timestamp?: string
          user_agent?: string | null
          user_id: string
        }
        Update: {
          device_info?: Json | null
          duration_ms?: number | null
          event_name?: string
          event_type?: string
          feature_name?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          page_path?: string | null
          session_id?: string
          timestamp?: string
          user_agent?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "beta_analytics_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "beta_analytics_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      beta_cohorts: {
        Row: {
          cohort_name: string
          feature_flags: Json | null
          id: string
          is_active: boolean | null
          joined_at: string
          last_activity_at: string | null
          notes: string | null
          onboarding_completed: boolean | null
          user_id: string
        }
        Insert: {
          cohort_name: string
          feature_flags?: Json | null
          id?: string
          is_active?: boolean | null
          joined_at?: string
          last_activity_at?: string | null
          notes?: string | null
          onboarding_completed?: boolean | null
          user_id: string
        }
        Update: {
          cohort_name?: string
          feature_flags?: Json | null
          id?: string
          is_active?: boolean | null
          joined_at?: string
          last_activity_at?: string | null
          notes?: string | null
          onboarding_completed?: boolean | null
          user_id?: string
        }
        Relationships: []
      }
      beta_feedback: {
        Row: {
          actual_behavior: string | null
          assigned_to: string | null
          browser_info: Json | null
          category: string
          created_at: string
          description: string
          expected_behavior: string | null
          feedback_type: string
          id: string
          org_id: string | null
          page_url: string | null
          priority: number | null
          resolved_at: string | null
          screenshot_url: string | null
          severity: string
          status: string
          steps_to_reproduce: string | null
          tags: string[] | null
          title: string
          updated_at: string
          user_agent: string | null
          user_id: string
          votes: number | null
        }
        Insert: {
          actual_behavior?: string | null
          assigned_to?: string | null
          browser_info?: Json | null
          category: string
          created_at?: string
          description: string
          expected_behavior?: string | null
          feedback_type: string
          id?: string
          org_id?: string | null
          page_url?: string | null
          priority?: number | null
          resolved_at?: string | null
          screenshot_url?: string | null
          severity?: string
          status?: string
          steps_to_reproduce?: string | null
          tags?: string[] | null
          title: string
          updated_at?: string
          user_agent?: string | null
          user_id: string
          votes?: number | null
        }
        Update: {
          actual_behavior?: string | null
          assigned_to?: string | null
          browser_info?: Json | null
          category?: string
          created_at?: string
          description?: string
          expected_behavior?: string | null
          feedback_type?: string
          id?: string
          org_id?: string | null
          page_url?: string | null
          priority?: number | null
          resolved_at?: string | null
          screenshot_url?: string | null
          severity?: string
          status?: string
          steps_to_reproduce?: string | null
          tags?: string[] | null
          title?: string
          updated_at?: string
          user_agent?: string | null
          user_id?: string
          votes?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "beta_feedback_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "beta_feedback_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      beta_invitations: {
        Row: {
          accepted_at: string | null
          email: string
          expires_at: string
          id: string
          invitation_code: string
          invited_at: string
          invited_by: string | null
          metadata: Json | null
          org_id: string | null
          phase: number
          status: string
        }
        Insert: {
          accepted_at?: string | null
          email: string
          expires_at?: string
          id?: string
          invitation_code: string
          invited_at?: string
          invited_by?: string | null
          metadata?: Json | null
          org_id?: string | null
          phase?: number
          status?: string
        }
        Update: {
          accepted_at?: string | null
          email?: string
          expires_at?: string
          id?: string
          invitation_code?: string
          invited_at?: string
          invited_by?: string | null
          metadata?: Json | null
          org_id?: string | null
          phase?: number
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "beta_invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "beta_invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      beta_metrics_summary: {
        Row: {
          accepted_invitations: number | null
          active_users: number | null
          bug_reports: number | null
          conversion_rate: number | null
          created_at: string
          daily_active_users: number | null
          date: string
          feature_usage: Json | null
          feedback_count: number | null
          id: string
          nps_score: number | null
          phase: number
          retention_rate: number | null
          total_invitations: number | null
        }
        Insert: {
          accepted_invitations?: number | null
          active_users?: number | null
          bug_reports?: number | null
          conversion_rate?: number | null
          created_at?: string
          daily_active_users?: number | null
          date?: string
          feature_usage?: Json | null
          feedback_count?: number | null
          id?: string
          nps_score?: number | null
          phase: number
          retention_rate?: number | null
          total_invitations?: number | null
        }
        Update: {
          accepted_invitations?: number | null
          active_users?: number | null
          bug_reports?: number | null
          conversion_rate?: number | null
          created_at?: string
          daily_active_users?: number | null
          date?: string
          feature_usage?: Json | null
          feedback_count?: number | null
          id?: string
          nps_score?: number | null
          phase?: number
          retention_rate?: number | null
          total_invitations?: number | null
        }
        Relationships: []
      }
      budget_approvals: {
        Row: {
          approval_level: number
          approved_amount: number | null
          approved_at: string | null
          approver_id: string
          budget_id: string
          comments: string | null
          created_at: string
          id: string
          org_id: string
          status: Database["public"]["Enums"]["approval_status"]
          updated_at: string
        }
        Insert: {
          approval_level?: number
          approved_amount?: number | null
          approved_at?: string | null
          approver_id: string
          budget_id: string
          comments?: string | null
          created_at?: string
          id?: string
          org_id: string
          status?: Database["public"]["Enums"]["approval_status"]
          updated_at?: string
        }
        Update: {
          approval_level?: number
          approved_amount?: number | null
          approved_at?: string | null
          approver_id?: string
          budget_id?: string
          comments?: string | null
          created_at?: string
          id?: string
          org_id?: string
          status?: Database["public"]["Enums"]["approval_status"]
          updated_at?: string
        }
        Relationships: []
      }
      budget_line_items: {
        Row: {
          actual_cost: number | null
          budget_id: string
          category: string
          created_at: string
          description: string
          estimated_cost: number
          id: string
          notes: string | null
          quantity: number | null
          status: string
          subcategory: string | null
          unit_cost: number | null
          updated_at: string
          vendor: string | null
        }
        Insert: {
          actual_cost?: number | null
          budget_id: string
          category: string
          created_at?: string
          description: string
          estimated_cost?: number
          id?: string
          notes?: string | null
          quantity?: number | null
          status?: string
          subcategory?: string | null
          unit_cost?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Update: {
          actual_cost?: number | null
          budget_id?: string
          category?: string
          created_at?: string
          description?: string
          estimated_cost?: number
          id?: string
          notes?: string | null
          quantity?: number | null
          status?: string
          subcategory?: string | null
          unit_cost?: number | null
          updated_at?: string
          vendor?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "budget_line_items_budget_id_fkey"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "production_budgets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_budget_line_items_budgets"
            columns: ["budget_id"]
            isOneToOne: false
            referencedRelation: "production_budgets"
            referencedColumns: ["id"]
          },
        ]
      }
      call_sheet_items: {
        Row: {
          call_sheet_id: string
          call_time: string | null
          created_at: string
          description: string | null
          estimated_duration: number | null
          id: string
          item_type: string
          location: string | null
          order_index: number | null
          schedule_item_id: string | null
          special_requirements: string | null
          title: string
          updated_at: string
        }
        Insert: {
          call_sheet_id: string
          call_time?: string | null
          created_at?: string
          description?: string | null
          estimated_duration?: number | null
          id?: string
          item_type?: string
          location?: string | null
          order_index?: number | null
          schedule_item_id?: string | null
          special_requirements?: string | null
          title: string
          updated_at?: string
        }
        Update: {
          call_sheet_id?: string
          call_time?: string | null
          created_at?: string
          description?: string | null
          estimated_duration?: number | null
          id?: string
          item_type?: string
          location?: string | null
          order_index?: number | null
          schedule_item_id?: string | null
          special_requirements?: string | null
          title?: string
          updated_at?: string
        }
        Relationships: []
      }
      characters: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          notes: string | null
          org_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          notes?: string | null
          org_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          notes?: string | null
          org_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "characters_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "characters_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      collection_items: {
        Row: {
          added_at: string | null
          collection_id: string
          id: string
          screenplay_id: string
        }
        Insert: {
          added_at?: string | null
          collection_id: string
          id?: string
          screenplay_id: string
        }
        Update: {
          added_at?: string | null
          collection_id?: string
          id?: string
          screenplay_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "collection_items_collection_id_fkey"
            columns: ["collection_id"]
            isOneToOne: false
            referencedRelation: "collections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "collection_items_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      collections: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      comments: {
        Row: {
          content: string
          created_at: string
          id: string
          line_number: number | null
          resolved: boolean | null
          scene_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          id?: string
          line_number?: number | null
          resolved?: boolean | null
          scene_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          id?: string
          line_number?: number | null
          resolved?: boolean | null
          scene_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      connection_pool_metrics: {
        Row: {
          active_connections: number
          avg_connection_time: number | null
          connection_errors: number | null
          id: string
          idle_connections: number
          max_connections: number
          metadata: Json | null
          pool_exhausted_count: number | null
          pool_name: string
          timestamp: string
          total_connections: number
          waiting_connections: number
        }
        Insert: {
          active_connections: number
          avg_connection_time?: number | null
          connection_errors?: number | null
          id?: string
          idle_connections: number
          max_connections: number
          metadata?: Json | null
          pool_exhausted_count?: number | null
          pool_name: string
          timestamp?: string
          total_connections: number
          waiting_connections: number
        }
        Update: {
          active_connections?: number
          avg_connection_time?: number | null
          connection_errors?: number | null
          id?: string
          idle_connections?: number
          max_connections?: number
          metadata?: Json | null
          pool_exhausted_count?: number | null
          pool_name?: string
          timestamp?: string
          total_connections?: number
          waiting_connections?: number
        }
        Relationships: []
      }
      coverage_reports: {
        Row: {
          coverage_report: string | null
          created_at: string | null
          fidelity_level: string | null
          id: string
          org_id: string | null
          scene_id: string | null
          strengths: string | null
          synopsis: string | null
          updated_at: string | null
          user_id: string
          verdict: string | null
          weaknesses: string | null
        }
        Insert: {
          coverage_report?: string | null
          created_at?: string | null
          fidelity_level?: string | null
          id?: string
          org_id?: string | null
          scene_id?: string | null
          strengths?: string | null
          synopsis?: string | null
          updated_at?: string | null
          user_id: string
          verdict?: string | null
          weaknesses?: string | null
        }
        Update: {
          coverage_report?: string | null
          created_at?: string | null
          fidelity_level?: string | null
          id?: string
          org_id?: string | null
          scene_id?: string | null
          strengths?: string | null
          synopsis?: string | null
          updated_at?: string | null
          user_id?: string
          verdict?: string | null
          weaknesses?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "coverage_reports_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coverage_reports_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "coverage_reports_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      coverage_usage_tracking: {
        Row: {
          created_at: string
          generation_count: number
          id: string
          last_generation_at: string | null
          org_id: string
          updated_at: string
          usage_date: string
          user_id: string
        }
        Insert: {
          created_at?: string
          generation_count?: number
          id?: string
          last_generation_at?: string | null
          org_id: string
          updated_at?: string
          usage_date?: string
          user_id: string
        }
        Update: {
          created_at?: string
          generation_count?: number
          id?: string
          last_generation_at?: string | null
          org_id?: string
          updated_at?: string
          usage_date?: string
          user_id?: string
        }
        Relationships: []
      }
      crew_assignments: {
        Row: {
          created_at: string
          id: string
          notes: string | null
          rate_per_day: number | null
          role: string
          schedule_item_id: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          notes?: string | null
          rate_per_day?: number | null
          role: string
          schedule_item_id: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          notes?: string | null
          rate_per_day?: number | null
          role?: string
          schedule_item_id?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "crew_assignments_schedule_item_id_fkey"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_crew_assignments_schedule_items"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
        ]
      }
      crew_members: {
        Row: {
          availability_status: Database["public"]["Enums"]["crew_availability_status"]
          created_at: string
          daily_rate: number | null
          email: string | null
          emergency_contact: Json | null
          id: string
          name: string
          notes: string | null
          org_id: string
          overtime_rate: number | null
          phone: string | null
          role: string
          skills: Json | null
          union_status: string | null
          updated_at: string
        }
        Insert: {
          availability_status?: Database["public"]["Enums"]["crew_availability_status"]
          created_at?: string
          daily_rate?: number | null
          email?: string | null
          emergency_contact?: Json | null
          id?: string
          name: string
          notes?: string | null
          org_id: string
          overtime_rate?: number | null
          phone?: string | null
          role: string
          skills?: Json | null
          union_status?: string | null
          updated_at?: string
        }
        Update: {
          availability_status?: Database["public"]["Enums"]["crew_availability_status"]
          created_at?: string
          daily_rate?: number | null
          email?: string | null
          emergency_contact?: Json | null
          id?: string
          name?: string
          notes?: string | null
          org_id?: string
          overtime_rate?: number | null
          phone?: string | null
          role?: string
          skills?: Json | null
          union_status?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      crew_schedules: {
        Row: {
          call_time: string | null
          created_at: string
          crew_member_id: string
          id: string
          notes: string | null
          org_id: string
          overtime_hours: number | null
          rate_for_day: number | null
          role: string
          schedule_item_id: string
          status: string
          updated_at: string
          wrap_time: string | null
        }
        Insert: {
          call_time?: string | null
          created_at?: string
          crew_member_id: string
          id?: string
          notes?: string | null
          org_id: string
          overtime_hours?: number | null
          rate_for_day?: number | null
          role: string
          schedule_item_id: string
          status?: string
          updated_at?: string
          wrap_time?: string | null
        }
        Update: {
          call_time?: string | null
          created_at?: string
          crew_member_id?: string
          id?: string
          notes?: string | null
          org_id?: string
          overtime_hours?: number | null
          rate_for_day?: number | null
          role?: string
          schedule_item_id?: string
          status?: string
          updated_at?: string
          wrap_time?: string | null
        }
        Relationships: []
      }
      database_backups: {
        Row: {
          backup_location: string
          backup_size: number | null
          backup_status: string
          backup_type: string
          checksum: string | null
          completed_at: string | null
          created_by: string | null
          error_message: string | null
          id: string
          metadata: Json | null
          org_id: string | null
          retention_until: string
          started_at: string
        }
        Insert: {
          backup_location: string
          backup_size?: number | null
          backup_status?: string
          backup_type: string
          checksum?: string | null
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          retention_until: string
          started_at?: string
        }
        Update: {
          backup_location?: string
          backup_size?: number | null
          backup_status?: string
          backup_type?: string
          checksum?: string | null
          completed_at?: string | null
          created_by?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          retention_until?: string
          started_at?: string
        }
        Relationships: []
      }
      database_performance_metrics: {
        Row: {
          active_connections: number
          buffer_stats: Json | null
          cache_hit_ratio: number | null
          connection_count: number
          database_size: number | null
          deadlocks: number | null
          id: string
          idle_connections: number
          index_usage: Json | null
          lock_waits: number | null
          max_connections: number
          query_performance: Json
          slow_queries: Json | null
          table_stats: Json | null
          temp_bytes: number | null
          temp_files_created: number | null
          timestamp: string
        }
        Insert: {
          active_connections: number
          buffer_stats?: Json | null
          cache_hit_ratio?: number | null
          connection_count: number
          database_size?: number | null
          deadlocks?: number | null
          id?: string
          idle_connections: number
          index_usage?: Json | null
          lock_waits?: number | null
          max_connections: number
          query_performance?: Json
          slow_queries?: Json | null
          table_stats?: Json | null
          temp_bytes?: number | null
          temp_files_created?: number | null
          timestamp?: string
        }
        Update: {
          active_connections?: number
          buffer_stats?: Json | null
          cache_hit_ratio?: number | null
          connection_count?: number
          database_size?: number | null
          deadlocks?: number | null
          id?: string
          idle_connections?: number
          index_usage?: Json | null
          lock_waits?: number | null
          max_connections?: number
          query_performance?: Json
          slow_queries?: Json | null
          table_stats?: Json | null
          temp_bytes?: number | null
          temp_files_created?: number | null
          timestamp?: string
        }
        Relationships: []
      }
      disaster_recovery_plans: {
        Row: {
          created_at: string | null
          created_by: string | null
          estimated_rpo_minutes: number | null
          estimated_rto_minutes: number | null
          id: string
          is_active: boolean | null
          last_tested_at: string | null
          plan_name: string
          plan_type: string
          recovery_steps: Json
          test_results: Json | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          estimated_rpo_minutes?: number | null
          estimated_rto_minutes?: number | null
          id?: string
          is_active?: boolean | null
          last_tested_at?: string | null
          plan_name: string
          plan_type: string
          recovery_steps: Json
          test_results?: Json | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          estimated_rpo_minutes?: number | null
          estimated_rto_minutes?: number | null
          id?: string
          is_active?: boolean | null
          last_tested_at?: string | null
          plan_name?: string
          plan_type?: string
          recovery_steps?: Json
          test_results?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      earnings_summary: {
        Row: {
          id: string
          last_updated: string
          pending_earnings: number
          total_earnings: number
          total_sales: number
          user_id: string
        }
        Insert: {
          id?: string
          last_updated?: string
          pending_earnings?: number
          total_earnings?: number
          total_sales?: number
          user_id: string
        }
        Update: {
          id?: string
          last_updated?: string
          pending_earnings?: number
          total_earnings?: number
          total_sales?: number
          user_id?: string
        }
        Relationships: []
      }
      equipment_checkouts: {
        Row: {
          checked_in_at: string | null
          checked_in_by: string | null
          checked_out_at: string
          checked_out_by: string
          condition_in: string | null
          condition_out: string | null
          created_at: string
          equipment_id: string
          expected_return_date: string | null
          id: string
          notes: string | null
          org_id: string
          schedule_item_id: string | null
          updated_at: string
        }
        Insert: {
          checked_in_at?: string | null
          checked_in_by?: string | null
          checked_out_at?: string
          checked_out_by: string
          condition_in?: string | null
          condition_out?: string | null
          created_at?: string
          equipment_id: string
          expected_return_date?: string | null
          id?: string
          notes?: string | null
          org_id: string
          schedule_item_id?: string | null
          updated_at?: string
        }
        Update: {
          checked_in_at?: string | null
          checked_in_by?: string | null
          checked_out_at?: string
          checked_out_by?: string
          condition_in?: string | null
          condition_out?: string | null
          created_at?: string
          equipment_id?: string
          expected_return_date?: string | null
          id?: string
          notes?: string | null
          org_id?: string
          schedule_item_id?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      error_logs: {
        Row: {
          component: string | null
          created_at: string | null
          error_level: string
          error_message: string
          error_stack: string | null
          id: string
          metadata: Json | null
          org_id: string | null
          resolved: boolean | null
          resolved_at: string | null
          session_id: string | null
          url: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          component?: string | null
          created_at?: string | null
          error_level: string
          error_message: string
          error_stack?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          resolved?: boolean | null
          resolved_at?: string | null
          session_id?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          component?: string | null
          created_at?: string | null
          error_level?: string
          error_message?: string
          error_stack?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string | null
          resolved?: boolean | null
          resolved_at?: string | null
          session_id?: string | null
          url?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      favorites: {
        Row: {
          created_at: string | null
          id: string
          screenplay_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          screenplay_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          screenplay_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "favorites_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      invitations: {
        Row: {
          created_at: string | null
          email: string
          expires_at: string | null
          id: string
          invited_by: string | null
          org_id: string | null
          role: string | null
          status: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          expires_at?: string | null
          id?: string
          invited_by?: string | null
          org_id?: string | null
          role?: string | null
          status?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          expires_at?: string | null
          id?: string
          invited_by?: string | null
          org_id?: string | null
          role?: string | null
          status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invitations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      location_scouts: {
        Row: {
          accessibility_notes: string | null
          address: string | null
          availability_notes: string | null
          contact_email: string | null
          contact_person: string | null
          contact_phone: string | null
          coordinates: unknown | null
          cost_per_day: number | null
          created_at: string
          description: string | null
          id: string
          location_name: string
          org_id: string
          parking_info: string | null
          permits_required: boolean | null
          photos: Json | null
          scouted_date: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          accessibility_notes?: string | null
          address?: string | null
          availability_notes?: string | null
          contact_email?: string | null
          contact_person?: string | null
          contact_phone?: string | null
          coordinates?: unknown | null
          cost_per_day?: number | null
          created_at?: string
          description?: string | null
          id?: string
          location_name: string
          org_id: string
          parking_info?: string | null
          permits_required?: boolean | null
          photos?: Json | null
          scouted_date?: string | null
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          accessibility_notes?: string | null
          address?: string | null
          availability_notes?: string | null
          contact_email?: string | null
          contact_person?: string | null
          contact_phone?: string | null
          coordinates?: unknown | null
          cost_per_day?: number | null
          created_at?: string
          description?: string | null
          id?: string
          location_name?: string
          org_id?: string
          parking_info?: string | null
          permits_required?: boolean | null
          photos?: Json | null
          scouted_date?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      locations: {
        Row: {
          created_at: string
          description: string | null
          id: string
          name: string
          notes: string | null
          org_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          name: string
          notes?: string | null
          org_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          notes?: string | null
          org_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "locations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "locations_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      marketplace_analytics: {
        Row: {
          created_at: string
          event_data: Json | null
          event_type: string
          id: string
          ip_address: unknown | null
          screenplay_id: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          event_data?: Json | null
          event_type: string
          id?: string
          ip_address?: unknown | null
          screenplay_id: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          event_data?: Json | null
          event_type?: string
          id?: string
          ip_address?: unknown | null
          screenplay_id?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      migration_history: {
        Row: {
          applied_at: string
          applied_by: string | null
          environment: string
          id: string
          migration_checksum: string | null
          migration_name: string
          migration_version: string
          rollback_available: boolean
          rollback_sql: string | null
          rollback_tested: boolean
        }
        Insert: {
          applied_at?: string
          applied_by?: string | null
          environment?: string
          id?: string
          migration_checksum?: string | null
          migration_name: string
          migration_version: string
          rollback_available?: boolean
          rollback_sql?: string | null
          rollback_tested?: boolean
        }
        Update: {
          applied_at?: string
          applied_by?: string | null
          environment?: string
          id?: string
          migration_checksum?: string | null
          migration_name?: string
          migration_version?: string
          rollback_available?: boolean
          rollback_sql?: string | null
          rollback_tested?: boolean
        }
        Relationships: []
      }
      notifications: {
        Row: {
          created_at: string
          data: Json | null
          id: string
          message: string
          read: boolean
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string
          data?: Json | null
          id?: string
          message: string
          read?: boolean
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string
          data?: Json | null
          id?: string
          message?: string
          read?: boolean
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: []
      }
      organization_addons: {
        Row: {
          activated_at: string | null
          addon_key: string | null
          canceled_at: string | null
          created_at: string | null
          id: string
          org_id: string | null
          status: string | null
          stripe_subscription_id: string | null
        }
        Insert: {
          activated_at?: string | null
          addon_key?: string | null
          canceled_at?: string | null
          created_at?: string | null
          id?: string
          org_id?: string | null
          status?: string | null
          stripe_subscription_id?: string | null
        }
        Update: {
          activated_at?: string | null
          addon_key?: string | null
          canceled_at?: string | null
          created_at?: string | null
          id?: string
          org_id?: string | null
          status?: string | null
          stripe_subscription_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_addons_addon_key_fkey"
            columns: ["addon_key"]
            isOneToOne: false
            referencedRelation: "subscription_addons"
            referencedColumns: ["addon_key"]
          },
          {
            foreignKeyName: "organization_addons_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_addons_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      organization_members: {
        Row: {
          id: string
          joined_at: string | null
          org_id: string | null
          role: string
          user_id: string | null
        }
        Insert: {
          id?: string
          joined_at?: string | null
          org_id?: string | null
          role: string
          user_id?: string | null
        }
        Update: {
          id?: string
          joined_at?: string | null
          org_id?: string | null
          role?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_members_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      organization_subscription_plans: {
        Row: {
          addon_features: Json | null
          billing_cycle: string | null
          cancel_at_period_end: boolean | null
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          custom_limits: Json | null
          id: string
          org_id: string | null
          plan_id: string | null
          seats_included: number | null
          seats_used: number | null
          status: string | null
          stripe_subscription_id: string | null
          trial_end: string | null
          trial_start: string | null
          updated_at: string | null
        }
        Insert: {
          addon_features?: Json | null
          billing_cycle?: string | null
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          custom_limits?: Json | null
          id?: string
          org_id?: string | null
          plan_id?: string | null
          seats_included?: number | null
          seats_used?: number | null
          status?: string | null
          stripe_subscription_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
        }
        Update: {
          addon_features?: Json | null
          billing_cycle?: string | null
          cancel_at_period_end?: boolean | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          custom_limits?: Json | null
          id?: string
          org_id?: string | null
          plan_id?: string | null
          seats_included?: number | null
          seats_used?: number | null
          status?: string | null
          stripe_subscription_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_subscription_plans_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_subscription_plans_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "organization_subscription_plans_plan_id_fkey"
            columns: ["plan_id"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["plan_id"]
          },
        ]
      }
      organization_tools: {
        Row: {
          created_at: string | null
          enabled: boolean | null
          id: string
          org_id: string | null
          tool_id: string | null
        }
        Insert: {
          created_at?: string | null
          enabled?: boolean | null
          id?: string
          org_id?: string | null
          tool_id?: string | null
        }
        Update: {
          created_at?: string | null
          enabled?: boolean | null
          id?: string
          org_id?: string | null
          tool_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_tools_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_tools_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "organization_tools_tool_id_fkey"
            columns: ["tool_id"]
            isOneToOne: false
            referencedRelation: "tools"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: string
          name: string
          plan: string | null
          updated_at: string | null
          user_limit: number | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name: string
          plan?: string | null
          updated_at?: string | null
          user_limit?: number | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: string
          name?: string
          plan?: string | null
          updated_at?: string | null
          user_limit?: number | null
        }
        Relationships: []
      }
      performance_metrics: {
        Row: {
          id: string
          metric_name: string
          metric_unit: string | null
          metric_value: number
          org_id: string | null
          recorded_at: string | null
          tags: Json | null
          user_id: string | null
        }
        Insert: {
          id?: string
          metric_name: string
          metric_unit?: string | null
          metric_value: number
          org_id?: string | null
          recorded_at?: string | null
          tags?: Json | null
          user_id?: string | null
        }
        Update: {
          id?: string
          metric_name?: string
          metric_unit?: string | null
          metric_value?: number
          org_id?: string | null
          recorded_at?: string | null
          tags?: Json | null
          user_id?: string | null
        }
        Relationships: []
      }
      posts: {
        Row: {
          content: string | null
          created_at: string
          id: string
          org_id: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content?: string | null
          created_at?: string
          id?: string
          org_id?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string | null
          created_at?: string
          id?: string
          org_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "posts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "posts_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "posts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      production_audit_logs: {
        Row: {
          action: string
          changes_summary: string | null
          created_at: string
          entity_id: string
          entity_type: string
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          org_id: string
          session_id: string | null
          user_agent: string | null
          user_id: string
        }
        Insert: {
          action: string
          changes_summary?: string | null
          created_at?: string
          entity_id: string
          entity_type: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          org_id: string
          session_id?: string | null
          user_agent?: string | null
          user_id: string
        }
        Update: {
          action?: string
          changes_summary?: string | null
          created_at?: string
          entity_id?: string
          entity_type?: string
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          org_id?: string
          session_id?: string | null
          user_agent?: string | null
          user_id?: string
        }
        Relationships: []
      }
      production_budgets: {
        Row: {
          created_at: string
          currency: string
          description: string | null
          id: string
          org_id: string
          search_vector: unknown | null
          status: string
          title: string
          total_budget: number
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          currency?: string
          description?: string | null
          id?: string
          org_id: string
          search_vector?: unknown | null
          status?: string
          title: string
          total_budget?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          currency?: string
          description?: string | null
          id?: string
          org_id?: string
          search_vector?: unknown | null
          status?: string
          title?: string
          total_budget?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      production_call_sheets: {
        Row: {
          call_date: string
          created_at: string
          emergency_contacts: Json | null
          general_notes: string | null
          id: string
          org_id: string
          schedule_id: string
          status: string
          title: string
          updated_at: string
          user_id: string
          weather_info: Json | null
        }
        Insert: {
          call_date: string
          created_at?: string
          emergency_contacts?: Json | null
          general_notes?: string | null
          id?: string
          org_id: string
          schedule_id: string
          status?: string
          title: string
          updated_at?: string
          user_id: string
          weather_info?: Json | null
        }
        Update: {
          call_date?: string
          created_at?: string
          emergency_contacts?: Json | null
          general_notes?: string | null
          id?: string
          org_id?: string
          schedule_id?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
          weather_info?: Json | null
        }
        Relationships: []
      }
      production_equipment: {
        Row: {
          category: string
          condition: string | null
          created_at: string
          daily_rate: number | null
          id: string
          location: string | null
          maintenance_notes: string | null
          name: string
          org_id: string
          purchase_date: string | null
          serial_number: string | null
          specifications: Json | null
          status: Database["public"]["Enums"]["equipment_status"]
          updated_at: string
        }
        Insert: {
          category: string
          condition?: string | null
          created_at?: string
          daily_rate?: number | null
          id?: string
          location?: string | null
          maintenance_notes?: string | null
          name: string
          org_id: string
          purchase_date?: string | null
          serial_number?: string | null
          specifications?: Json | null
          status?: Database["public"]["Enums"]["equipment_status"]
          updated_at?: string
        }
        Update: {
          category?: string
          condition?: string | null
          created_at?: string
          daily_rate?: number | null
          id?: string
          location?: string | null
          maintenance_notes?: string | null
          name?: string
          org_id?: string
          purchase_date?: string | null
          serial_number?: string | null
          specifications?: Json | null
          status?: Database["public"]["Enums"]["equipment_status"]
          updated_at?: string
        }
        Relationships: []
      }
      production_file_attachments: {
        Row: {
          created_at: string
          description: string | null
          entity_id: string
          entity_type: string
          file_name: string
          file_size: number
          file_type: string
          id: string
          org_id: string
          storage_path: string
          uploaded_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          entity_id: string
          entity_type: string
          file_name: string
          file_size: number
          file_type: string
          id?: string
          org_id: string
          storage_path: string
          uploaded_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          entity_id?: string
          entity_type?: string
          file_name?: string
          file_size?: number
          file_type?: string
          id?: string
          org_id?: string
          storage_path?: string
          uploaded_at?: string
          user_id?: string
        }
        Relationships: []
      }
      production_integrations: {
        Row: {
          api_credentials: Json | null
          created_at: string
          created_by: string
          id: string
          integration_type: string
          is_active: boolean
          name: string
          org_id: string
          settings: Json
          updated_at: string
          webhook_url: string | null
        }
        Insert: {
          api_credentials?: Json | null
          created_at?: string
          created_by: string
          id?: string
          integration_type: string
          is_active?: boolean
          name: string
          org_id: string
          settings?: Json
          updated_at?: string
          webhook_url?: string | null
        }
        Update: {
          api_credentials?: Json | null
          created_at?: string
          created_by?: string
          id?: string
          integration_type?: string
          is_active?: boolean
          name?: string
          org_id?: string
          settings?: Json
          updated_at?: string
          webhook_url?: string | null
        }
        Relationships: []
      }
      production_reports: {
        Row: {
          content: Json
          created_at: string
          date: string
          id: string
          org_id: string
          report_type: string
          schedule_item_id: string | null
          search_vector: unknown | null
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: Json
          created_at?: string
          date: string
          id?: string
          org_id: string
          report_type: string
          schedule_item_id?: string | null
          search_vector?: unknown | null
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: Json
          created_at?: string
          date?: string
          id?: string
          org_id?: string
          report_type?: string
          schedule_item_id?: string | null
          search_vector?: unknown | null
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_production_reports_schedule_items"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_reports_schedule_item_id_fkey"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
        ]
      }
      production_resources: {
        Row: {
          availability_status: string
          contact_info: Json | null
          cost_per_day: number | null
          created_at: string
          description: string | null
          id: string
          name: string
          org_id: string
          search_vector: unknown | null
          specifications: Json | null
          type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          availability_status?: string
          contact_info?: Json | null
          cost_per_day?: number | null
          created_at?: string
          description?: string | null
          id?: string
          name: string
          org_id: string
          search_vector?: unknown | null
          specifications?: Json | null
          type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          availability_status?: string
          contact_info?: Json | null
          cost_per_day?: number | null
          created_at?: string
          description?: string | null
          id?: string
          name?: string
          org_id?: string
          search_vector?: unknown | null
          specifications?: Json | null
          type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      production_schedule_items: {
        Row: {
          created_at: string
          description: string | null
          end_time: string | null
          estimated_duration: number | null
          id: string
          location_id: string | null
          notes: string | null
          scene_id: string | null
          schedule_id: string
          scheduled_date: string
          start_time: string | null
          status: string
          title: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_time?: string | null
          estimated_duration?: number | null
          id?: string
          location_id?: string | null
          notes?: string | null
          scene_id?: string | null
          schedule_id: string
          scheduled_date: string
          start_time?: string | null
          status?: string
          title: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          description?: string | null
          end_time?: string | null
          estimated_duration?: number | null
          id?: string
          location_id?: string | null
          notes?: string | null
          scene_id?: string | null
          schedule_id?: string
          scheduled_date?: string
          start_time?: string | null
          status?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_schedule_items_locations"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_schedule_items_scenes"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_schedule_items_schedules"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "production_schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_schedule_items_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_schedule_items_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "production_schedule_items_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "production_schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      production_schedules: {
        Row: {
          created_at: string
          description: string | null
          end_date: string
          id: string
          org_id: string
          search_vector: unknown | null
          start_date: string
          status: string
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          end_date: string
          id?: string
          org_id: string
          search_vector?: unknown | null
          start_date: string
          status?: string
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          end_date?: string
          id?: string
          org_id?: string
          search_vector?: unknown | null
          start_date?: string
          status?: string
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      production_templates: {
        Row: {
          created_at: string
          created_by: string
          description: string | null
          id: string
          is_public: boolean
          name: string
          org_id: string
          template_data: Json
          template_type: string
          updated_at: string
          usage_count: number
        }
        Insert: {
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          is_public?: boolean
          name: string
          org_id: string
          template_data?: Json
          template_type: string
          updated_at?: string
          usage_count?: number
        }
        Update: {
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          is_public?: boolean
          name?: string
          org_id?: string
          template_data?: Json
          template_type?: string
          updated_at?: string
          usage_count?: number
        }
        Relationships: []
      }
      production_user_roles: {
        Row: {
          assigned_at: string
          assigned_by: string
          id: string
          org_id: string
          permissions: Json
          role: Database["public"]["Enums"]["production_role"]
          user_id: string
        }
        Insert: {
          assigned_at?: string
          assigned_by: string
          id?: string
          org_id: string
          permissions?: Json
          role?: Database["public"]["Enums"]["production_role"]
          user_id: string
        }
        Update: {
          assigned_at?: string
          assigned_by?: string
          id?: string
          org_id?: string
          permissions?: Json
          role?: Database["public"]["Enums"]["production_role"]
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          full_name: string | null
          id: string
          role: string | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          full_name?: string | null
          id: string
          role?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          full_name?: string | null
          id?: string
          role?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      project_feature_flags: {
        Row: {
          coverage_enabled: boolean | null
          created_at: string | null
          id: string
          project_id: string | null
          storyboard_enabled: boolean | null
          updated_at: string | null
        }
        Insert: {
          coverage_enabled?: boolean | null
          created_at?: string | null
          id?: string
          project_id?: string | null
          storyboard_enabled?: boolean | null
          updated_at?: string | null
        }
        Update: {
          coverage_enabled?: boolean | null
          created_at?: string | null
          id?: string
          project_id?: string | null
          storyboard_enabled?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "project_feature_flags_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_feature_flags_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      projects: {
        Row: {
          collaborator_count: number
          created_at: string
          description: string | null
          id: string
          is_archived: boolean
          org_id: string
          screenplay_id: string | null
          status: Database["public"]["Enums"]["project_status"]
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          collaborator_count?: number
          created_at?: string
          description?: string | null
          id?: string
          is_archived?: boolean
          org_id: string
          screenplay_id?: string | null
          status?: Database["public"]["Enums"]["project_status"]
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          collaborator_count?: number
          created_at?: string
          description?: string | null
          id?: string
          is_archived?: boolean
          org_id?: string
          screenplay_id?: string | null
          status?: Database["public"]["Enums"]["project_status"]
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "projects_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      promo_campaign_analytics: {
        Row: {
          applications: number
          campaign_id: string
          conversions: number
          created_at: string
          date: string
          id: string
          total_discount_given: number
          total_revenue: number
          unique_users: number
          updated_at: string
          views: number
        }
        Insert: {
          applications?: number
          campaign_id: string
          conversions?: number
          created_at?: string
          date?: string
          id?: string
          total_discount_given?: number
          total_revenue?: number
          unique_users?: number
          updated_at?: string
          views?: number
        }
        Update: {
          applications?: number
          campaign_id?: string
          conversions?: number
          created_at?: string
          date?: string
          id?: string
          total_discount_given?: number
          total_revenue?: number
          unique_users?: number
          updated_at?: string
          views?: number
        }
        Relationships: [
          {
            foreignKeyName: "promo_campaign_analytics_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "promo_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      promo_campaign_usage: {
        Row: {
          applied_at: string
          campaign_id: string
          discount_amount: number
          final_amount: number
          id: string
          metadata: Json | null
          original_amount: number
          payment_intent_id: string | null
          subscription_id: string | null
          user_id: string
        }
        Insert: {
          applied_at?: string
          campaign_id: string
          discount_amount: number
          final_amount: number
          id?: string
          metadata?: Json | null
          original_amount: number
          payment_intent_id?: string | null
          subscription_id?: string | null
          user_id: string
        }
        Update: {
          applied_at?: string
          campaign_id?: string
          discount_amount?: number
          final_amount?: number
          id?: string
          metadata?: Json | null
          original_amount?: number
          payment_intent_id?: string | null
          subscription_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "promo_campaign_usage_campaign_id_fkey"
            columns: ["campaign_id"]
            isOneToOne: false
            referencedRelation: "promo_campaigns"
            referencedColumns: ["id"]
          },
        ]
      }
      promo_campaigns: {
        Row: {
          campaign_code: string
          created_at: string
          created_by: string
          custom_criteria: Json | null
          description: string | null
          discount_type: Database["public"]["Enums"]["discount_type"]
          discount_value: number
          end_date: string | null
          id: string
          is_active: boolean
          name: string
          start_date: string
          status: Database["public"]["Enums"]["campaign_status"]
          target_audience: Database["public"]["Enums"]["target_audience"]
          target_plans: string[] | null
          updated_at: string
          usage_limit: number | null
          usage_limit_per_user: number | null
        }
        Insert: {
          campaign_code: string
          created_at?: string
          created_by: string
          custom_criteria?: Json | null
          description?: string | null
          discount_type: Database["public"]["Enums"]["discount_type"]
          discount_value: number
          end_date?: string | null
          id?: string
          is_active?: boolean
          name: string
          start_date: string
          status?: Database["public"]["Enums"]["campaign_status"]
          target_audience?: Database["public"]["Enums"]["target_audience"]
          target_plans?: string[] | null
          updated_at?: string
          usage_limit?: number | null
          usage_limit_per_user?: number | null
        }
        Update: {
          campaign_code?: string
          created_at?: string
          created_by?: string
          custom_criteria?: Json | null
          description?: string | null
          discount_type?: Database["public"]["Enums"]["discount_type"]
          discount_value?: number
          end_date?: string | null
          id?: string
          is_active?: boolean
          name?: string
          start_date?: string
          status?: Database["public"]["Enums"]["campaign_status"]
          target_audience?: Database["public"]["Enums"]["target_audience"]
          target_plans?: string[] | null
          updated_at?: string
          usage_limit?: number | null
          usage_limit_per_user?: number | null
        }
        Relationships: []
      }
      prompt_library: {
        Row: {
          cot_enabled: boolean
          created_at: string
          created_by: string | null
          id: string
          last_updated: string
          output_format: string
          prompt_content: string | null
          role: string
          tool_name: string
          version: string
        }
        Insert: {
          cot_enabled?: boolean
          created_at?: string
          created_by?: string | null
          id?: string
          last_updated?: string
          output_format?: string
          prompt_content?: string | null
          role: string
          tool_name: string
          version?: string
        }
        Update: {
          cot_enabled?: boolean
          created_at?: string
          created_by?: string | null
          id?: string
          last_updated?: string
          output_format?: string
          prompt_content?: string | null
          role?: string
          tool_name?: string
          version?: string
        }
        Relationships: []
      }
      resource_bookings: {
        Row: {
          cost: number | null
          created_at: string
          end_date: string
          id: string
          notes: string | null
          resource_id: string
          schedule_item_id: string
          start_date: string
          status: string
          updated_at: string
        }
        Insert: {
          cost?: number | null
          created_at?: string
          end_date: string
          id?: string
          notes?: string | null
          resource_id: string
          schedule_item_id: string
          start_date: string
          status?: string
          updated_at?: string
        }
        Update: {
          cost?: number | null
          created_at?: string
          end_date?: string
          id?: string
          notes?: string | null
          resource_id?: string
          schedule_item_id?: string
          start_date?: string
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_resource_bookings_resources"
            columns: ["resource_id"]
            isOneToOne: false
            referencedRelation: "production_resources"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_resource_bookings_schedule_items"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "resource_bookings_resource_id_fkey"
            columns: ["resource_id"]
            isOneToOne: false
            referencedRelation: "production_resources"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "resource_bookings_schedule_item_id_fkey"
            columns: ["schedule_item_id"]
            isOneToOne: false
            referencedRelation: "production_schedule_items"
            referencedColumns: ["id"]
          },
        ]
      }
      revisions: {
        Row: {
          change_summary: string | null
          content: string
          created_at: string
          id: string
          scene_id: string | null
          user_id: string
          version_number: number
        }
        Insert: {
          change_summary?: string | null
          content: string
          created_at?: string
          id?: string
          scene_id?: string | null
          user_id: string
          version_number: number
        }
        Update: {
          change_summary?: string | null
          content?: string
          created_at?: string
          id?: string
          scene_id?: string | null
          user_id?: string
          version_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "revisions_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      scene_characters: {
        Row: {
          character_id: string | null
          created_at: string
          id: string
          is_main_character: boolean | null
          scene_id: string | null
        }
        Insert: {
          character_id?: string | null
          created_at?: string
          id?: string
          is_main_character?: boolean | null
          scene_id?: string | null
        }
        Update: {
          character_id?: string | null
          created_at?: string
          id?: string
          is_main_character?: boolean | null
          scene_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scene_characters_character_id_fkey"
            columns: ["character_id"]
            isOneToOne: false
            referencedRelation: "characters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scene_characters_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      scene_dependencies: {
        Row: {
          created_at: string
          dependency_type: string | null
          depends_on_scene_id: string | null
          id: string
          notes: string | null
          scene_id: string | null
        }
        Insert: {
          created_at?: string
          dependency_type?: string | null
          depends_on_scene_id?: string | null
          id?: string
          notes?: string | null
          scene_id?: string | null
        }
        Update: {
          created_at?: string
          dependency_type?: string | null
          depends_on_scene_id?: string | null
          id?: string
          notes?: string | null
          scene_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scene_dependencies_depends_on_scene_id_fkey"
            columns: ["depends_on_scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scene_dependencies_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      scenes: {
        Row: {
          act: number | null
          content: string | null
          created_at: string
          description: string | null
          duration_minutes: number | null
          id: string
          location_id: string | null
          notes: string | null
          order_index: number
          org_id: string | null
          title: string
          updated_at: string
          user_id: string
        }
        Insert: {
          act?: number | null
          content?: string | null
          created_at?: string
          description?: string | null
          duration_minutes?: number | null
          id?: string
          location_id?: string | null
          notes?: string | null
          order_index?: number
          org_id?: string | null
          title: string
          updated_at?: string
          user_id: string
        }
        Update: {
          act?: number | null
          content?: string | null
          created_at?: string
          description?: string | null
          duration_minutes?: number | null
          id?: string
          location_id?: string | null
          notes?: string | null
          order_index?: number
          org_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "scenes_location_id_fkey"
            columns: ["location_id"]
            isOneToOne: false
            referencedRelation: "locations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scenes_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scenes_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      screenplay_analytics_summary: {
        Row: {
          avg_offer_amount: number | null
          conversion_rate: number | null
          id: string
          last_updated: string
          screenplay_id: string
          total_offers: number
          total_purchases: number
          total_revenue: number
          total_views: number
          unique_viewers: number
        }
        Insert: {
          avg_offer_amount?: number | null
          conversion_rate?: number | null
          id?: string
          last_updated?: string
          screenplay_id: string
          total_offers?: number
          total_purchases?: number
          total_revenue?: number
          total_views?: number
          unique_viewers?: number
        }
        Update: {
          avg_offer_amount?: number | null
          conversion_rate?: number | null
          id?: string
          last_updated?: string
          screenplay_id?: string
          total_offers?: number
          total_purchases?: number
          total_revenue?: number
          total_views?: number
          unique_viewers?: number
        }
        Relationships: [
          {
            foreignKeyName: "screenplay_analytics_summary_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      screenplay_assessments: {
        Row: {
          assessed_by: string | null
          assessment_type: string
          character_score: number | null
          completed_at: string | null
          created_at: string
          detailed_feedback: Json | null
          dialogue_score: number | null
          id: string
          market_analysis: Json | null
          marketability_score: number | null
          overall_score: number | null
          recommendations: string[] | null
          screenplay_id: string
          status: string
          strengths: string[] | null
          structure_score: number | null
          weaknesses: string[] | null
        }
        Insert: {
          assessed_by?: string | null
          assessment_type?: string
          character_score?: number | null
          completed_at?: string | null
          created_at?: string
          detailed_feedback?: Json | null
          dialogue_score?: number | null
          id?: string
          market_analysis?: Json | null
          marketability_score?: number | null
          overall_score?: number | null
          recommendations?: string[] | null
          screenplay_id: string
          status?: string
          strengths?: string[] | null
          structure_score?: number | null
          weaknesses?: string[] | null
        }
        Update: {
          assessed_by?: string | null
          assessment_type?: string
          character_score?: number | null
          completed_at?: string | null
          created_at?: string
          detailed_feedback?: Json | null
          dialogue_score?: number | null
          id?: string
          market_analysis?: Json | null
          marketability_score?: number | null
          overall_score?: number | null
          recommendations?: string[] | null
          screenplay_id?: string
          status?: string
          strengths?: string[] | null
          structure_score?: number | null
          weaknesses?: string[] | null
        }
        Relationships: [
          {
            foreignKeyName: "screenplay_assessments_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      screenplay_contracts: {
        Row: {
          buyer_id: string
          buyer_signed_at: string | null
          contract_type: string
          created_at: string
          executed_at: string | null
          expires_at: string | null
          id: string
          offer_id: string | null
          screenplay_id: string
          seller_id: string
          seller_signed_at: string | null
          status: string
          terms: Json
          updated_at: string
        }
        Insert: {
          buyer_id: string
          buyer_signed_at?: string | null
          contract_type?: string
          created_at?: string
          executed_at?: string | null
          expires_at?: string | null
          id?: string
          offer_id?: string | null
          screenplay_id: string
          seller_id: string
          seller_signed_at?: string | null
          status?: string
          terms: Json
          updated_at?: string
        }
        Update: {
          buyer_id?: string
          buyer_signed_at?: string | null
          contract_type?: string
          created_at?: string
          executed_at?: string | null
          expires_at?: string | null
          id?: string
          offer_id?: string | null
          screenplay_id?: string
          seller_id?: string
          seller_signed_at?: string | null
          status?: string
          terms?: Json
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "screenplay_contracts_offer_id_fkey"
            columns: ["offer_id"]
            isOneToOne: false
            referencedRelation: "screenplay_offers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "screenplay_contracts_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      screenplay_files: {
        Row: {
          file_name: string
          file_size: number
          file_type: string
          id: string
          is_preview: boolean
          screenplay_id: string
          storage_path: string
          uploaded_at: string
          uploaded_by: string
        }
        Insert: {
          file_name: string
          file_size: number
          file_type: string
          id?: string
          is_preview?: boolean
          screenplay_id: string
          storage_path: string
          uploaded_at?: string
          uploaded_by: string
        }
        Update: {
          file_name?: string
          file_size?: number
          file_type?: string
          id?: string
          is_preview?: boolean
          screenplay_id?: string
          storage_path?: string
          uploaded_at?: string
          uploaded_by?: string
        }
        Relationships: []
      }
      screenplay_offers: {
        Row: {
          buyer_id: string
          created_at: string
          expires_at: string
          id: string
          message: string | null
          offer_amount: number
          responded_at: string | null
          screenplay_id: string
          seller_id: string
          status: string
          updated_at: string
        }
        Insert: {
          buyer_id: string
          created_at?: string
          expires_at?: string
          id?: string
          message?: string | null
          offer_amount: number
          responded_at?: string | null
          screenplay_id: string
          seller_id: string
          status?: string
          updated_at?: string
        }
        Update: {
          buyer_id?: string
          created_at?: string
          expires_at?: string
          id?: string
          message?: string | null
          offer_amount?: number
          responded_at?: string | null
          screenplay_id?: string
          seller_id?: string
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      screenplay_purchases: {
        Row: {
          buyer_id: string
          id: string
          payment_intent_id: string | null
          purchase_price: number
          purchased_at: string
          screenplay_id: string
        }
        Insert: {
          buyer_id: string
          id?: string
          payment_intent_id?: string | null
          purchase_price: number
          purchased_at?: string
          screenplay_id: string
        }
        Update: {
          buyer_id?: string
          id?: string
          payment_intent_id?: string | null
          purchase_price?: number
          purchased_at?: string
          screenplay_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "screenplay_purchases_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      screenplay_reviews: {
        Row: {
          created_at: string
          id: string
          purchase_id: string
          rating: number
          review_text: string | null
          reviewer_id: string
          screenplay_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          purchase_id: string
          rating: number
          review_text?: string | null
          reviewer_id: string
          screenplay_id: string
        }
        Update: {
          created_at?: string
          id?: string
          purchase_id?: string
          rating?: number
          review_text?: string | null
          reviewer_id?: string
          screenplay_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "screenplay_reviews_purchase_id_fkey"
            columns: ["purchase_id"]
            isOneToOne: false
            referencedRelation: "screenplay_purchases"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "screenplay_reviews_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      screenplays: {
        Row: {
          cover_image_url: string | null
          created_at: string
          genre: string
          id: string
          logline: string | null
          org_id: string | null
          page_count: number | null
          price: number
          rejection_reason: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          script_content: string
          status: string
          synopsis: string | null
          title: string
          updated_at: string
          writer_id: string
        }
        Insert: {
          cover_image_url?: string | null
          created_at?: string
          genre: string
          id?: string
          logline?: string | null
          org_id?: string | null
          page_count?: number | null
          price?: number
          rejection_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          script_content: string
          status?: string
          synopsis?: string | null
          title: string
          updated_at?: string
          writer_id: string
        }
        Update: {
          cover_image_url?: string | null
          created_at?: string
          genre?: string
          id?: string
          logline?: string | null
          org_id?: string | null
          page_count?: number | null
          price?: number
          rejection_reason?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          script_content?: string
          status?: string
          synopsis?: string | null
          title?: string
          updated_at?: string
          writer_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "screenplays_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "screenplays_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      seller_accounts: {
        Row: {
          account_status: string
          business_type: string | null
          capabilities: Json | null
          charges_enabled: boolean
          country: string | null
          created_at: string
          default_currency: string | null
          details_submitted: boolean
          id: string
          onboarding_completed: boolean
          payouts_enabled: boolean
          requirements: Json | null
          stripe_account_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          account_status?: string
          business_type?: string | null
          capabilities?: Json | null
          charges_enabled?: boolean
          country?: string | null
          created_at?: string
          default_currency?: string | null
          details_submitted?: boolean
          id?: string
          onboarding_completed?: boolean
          payouts_enabled?: boolean
          requirements?: Json | null
          stripe_account_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          account_status?: string
          business_type?: string | null
          capabilities?: Json | null
          charges_enabled?: boolean
          country?: string | null
          created_at?: string
          default_currency?: string | null
          details_submitted?: boolean
          id?: string
          onboarding_completed?: boolean
          payouts_enabled?: boolean
          requirements?: Json | null
          stripe_account_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      seller_onboarding_sessions: {
        Row: {
          completed: boolean
          created_at: string
          expires_at: string
          id: string
          refresh_url: string
          return_url: string
          seller_account_id: string
          stripe_account_link_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          completed?: boolean
          created_at?: string
          expires_at: string
          id?: string
          refresh_url: string
          return_url: string
          seller_account_id: string
          stripe_account_link_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          completed?: boolean
          created_at?: string
          expires_at?: string
          id?: string
          refresh_url?: string
          return_url?: string
          seller_account_id?: string
          stripe_account_link_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "seller_onboarding_sessions_seller_account_id_fkey"
            columns: ["seller_account_id"]
            isOneToOne: false
            referencedRelation: "seller_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      storyboard_panel_comments: {
        Row: {
          content: string
          created_at: string | null
          id: string
          org_id: string
          panel_id: string
          resolved: boolean | null
          timestamp_position: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          org_id: string
          panel_id: string
          resolved?: boolean | null
          timestamp_position?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          org_id?: string
          panel_id?: string
          resolved?: boolean | null
          timestamp_position?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "storyboard_panel_comments_panel_id_fkey"
            columns: ["panel_id"]
            isOneToOne: false
            referencedRelation: "storyboard_panels"
            referencedColumns: ["id"]
          },
        ]
      }
      storyboard_panels: {
        Row: {
          created_at: string | null
          dialogue: string | null
          feedback: string | null
          id: string
          image_url: string | null
          order_index: number | null
          org_id: string | null
          scene_id: string | null
          search_vector: unknown | null
          storyboard_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          dialogue?: string | null
          feedback?: string | null
          id?: string
          image_url?: string | null
          order_index?: number | null
          org_id?: string | null
          scene_id?: string | null
          search_vector?: unknown | null
          storyboard_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          dialogue?: string | null
          feedback?: string | null
          id?: string
          image_url?: string | null
          order_index?: number | null
          org_id?: string | null
          scene_id?: string | null
          search_vector?: unknown | null
          storyboard_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "storyboard_panels_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "storyboard_panels_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
          {
            foreignKeyName: "storyboard_panels_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "storyboard_panels_storyboard_id_fkey"
            columns: ["storyboard_id"]
            isOneToOne: false
            referencedRelation: "storyboards"
            referencedColumns: ["id"]
          },
        ]
      }
      storyboard_prompt_styles: {
        Row: {
          cost_multiplier: number | null
          created_at: string | null
          description: string | null
          id: string
          label: string
          prompt_suffix: string | null
        }
        Insert: {
          cost_multiplier?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          label: string
          prompt_suffix?: string | null
        }
        Update: {
          cost_multiplier?: number | null
          created_at?: string | null
          description?: string | null
          id?: string
          label?: string
          prompt_suffix?: string | null
        }
        Relationships: []
      }
      storyboard_templates: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          name: string
          org_id: string
          template_data: Json
          thumbnail_url: string | null
          updated_at: string | null
          usage_count: number | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name: string
          org_id: string
          template_data?: Json
          thumbnail_url?: string | null
          updated_at?: string | null
          usage_count?: number | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name?: string
          org_id?: string
          template_data?: Json
          thumbnail_url?: string | null
          updated_at?: string | null
          usage_count?: number | null
          user_id?: string
        }
        Relationships: []
      }
      storyboard_usage_tracking: {
        Row: {
          created_at: string
          generation_count: number
          id: string
          last_generation_at: string
          org_id: string
          updated_at: string
          usage_date: string
          user_id: string
        }
        Insert: {
          created_at?: string
          generation_count?: number
          id?: string
          last_generation_at?: string
          org_id: string
          updated_at?: string
          usage_date?: string
          user_id: string
        }
        Update: {
          created_at?: string
          generation_count?: number
          id?: string
          last_generation_at?: string
          org_id?: string
          updated_at?: string
          usage_date?: string
          user_id?: string
        }
        Relationships: []
      }
      storyboards: {
        Row: {
          created_at: string | null
          description: string | null
          export_settings: Json | null
          fidelity: string | null
          id: string
          last_exported_at: string | null
          org_id: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          export_settings?: Json | null
          fidelity?: string | null
          id?: string
          last_exported_at?: string | null
          org_id?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          export_settings?: Json | null
          fidelity?: string | null
          id?: string
          last_exported_at?: string | null
          org_id?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "storyboards_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "storyboards_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      subscribers: {
        Row: {
          cancel_at_period_end: boolean | null
          created_at: string
          current_period_end: string | null
          current_period_start: string | null
          email: string
          id: string
          metadata: Json | null
          plan_name: string | null
          status: string | null
          stripe_customer_id: string | null
          subscribed: boolean
          subscription_end: string | null
          subscription_tier: string | null
          trial_end: string | null
          trial_start: string | null
          updated_at: string
          user_id: string | null
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          email: string
          id?: string
          metadata?: Json | null
          plan_name?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          cancel_at_period_end?: boolean | null
          created_at?: string
          current_period_end?: string | null
          current_period_start?: string | null
          email?: string
          id?: string
          metadata?: Json | null
          plan_name?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          subscribed?: boolean
          subscription_end?: string | null
          subscription_tier?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      subscription_addons: {
        Row: {
          addon_key: string
          created_at: string | null
          description: string | null
          features: Json | null
          id: string
          is_active: boolean | null
          limits: Json | null
          name: string
          price_monthly: number | null
          price_yearly: number | null
          updated_at: string | null
        }
        Insert: {
          addon_key: string
          created_at?: string | null
          description?: string | null
          features?: Json | null
          id?: string
          is_active?: boolean | null
          limits?: Json | null
          name: string
          price_monthly?: number | null
          price_yearly?: number | null
          updated_at?: string | null
        }
        Update: {
          addon_key?: string
          created_at?: string | null
          description?: string | null
          features?: Json | null
          id?: string
          is_active?: boolean | null
          limits?: Json | null
          name?: string
          price_monthly?: number | null
          price_yearly?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subscription_feature_access: {
        Row: {
          access_level: string | null
          created_at: string | null
          expires_at: string | null
          feature_key: string
          id: string
          last_reset: string | null
          reset_period: string | null
          updated_at: string | null
          usage_count: number | null
          usage_limit: number | null
          user_id: string | null
        }
        Insert: {
          access_level?: string | null
          created_at?: string | null
          expires_at?: string | null
          feature_key: string
          id?: string
          last_reset?: string | null
          reset_period?: string | null
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
          user_id?: string | null
        }
        Update: {
          access_level?: string | null
          created_at?: string | null
          expires_at?: string | null
          feature_key?: string
          id?: string
          last_reset?: string | null
          reset_period?: string | null
          updated_at?: string | null
          usage_count?: number | null
          usage_limit?: number | null
          user_id?: string | null
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          created_at: string | null
          currency: string | null
          description: string | null
          display_name: string
          features: Json | null
          id: string
          is_active: boolean | null
          is_popular: boolean | null
          limits: Json | null
          name: string
          plan_id: string
          price_monthly: number
          price_yearly: number | null
          sort_order: number | null
          stripe_price_id_monthly: string | null
          stripe_price_id_yearly: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          currency?: string | null
          description?: string | null
          display_name: string
          features?: Json | null
          id?: string
          is_active?: boolean | null
          is_popular?: boolean | null
          limits?: Json | null
          name: string
          plan_id: string
          price_monthly?: number
          price_yearly?: number | null
          sort_order?: number | null
          stripe_price_id_monthly?: string | null
          stripe_price_id_yearly?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          currency?: string | null
          description?: string | null
          display_name?: string
          features?: Json | null
          id?: string
          is_active?: boolean | null
          is_popular?: boolean | null
          limits?: Json | null
          name?: string
          plan_id?: string
          price_monthly?: number
          price_yearly?: number | null
          sort_order?: number | null
          stripe_price_id_monthly?: string | null
          stripe_price_id_yearly?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      subscription_usage_tracking: {
        Row: {
          created_at: string | null
          feature_key: string
          id: string
          metadata: Json | null
          org_id: string | null
          reset_period: string | null
          usage_amount: number | null
          usage_date: string | null
          usage_type: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          feature_key: string
          id?: string
          metadata?: Json | null
          org_id?: string | null
          reset_period?: string | null
          usage_amount?: number | null
          usage_date?: string | null
          usage_type?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          feature_key?: string
          id?: string
          metadata?: Json | null
          org_id?: string | null
          reset_period?: string | null
          usage_amount?: number | null
          usage_date?: string | null
          usage_type?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscription_usage_tracking_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_usage_tracking_org_id_fkey"
            columns: ["org_id"]
            isOneToOne: false
            referencedRelation: "production_dashboard_analytics"
            referencedColumns: ["org_id"]
          },
        ]
      }
      subscriptions: {
        Row: {
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          id: string
          plan_name: string
          status: string
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_name?: string
          status?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          plan_name?: string
          status?: string
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      system_alerts: {
        Row: {
          acknowledged: boolean | null
          acknowledged_at: string | null
          acknowledged_by: string | null
          alert_type: string
          created_at: string | null
          id: string
          message: string
          metadata: Json | null
          resolved: boolean | null
          resolved_at: string | null
          severity: string
          source: string
          title: string
        }
        Insert: {
          acknowledged?: boolean | null
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          alert_type: string
          created_at?: string | null
          id?: string
          message: string
          metadata?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          severity: string
          source: string
          title: string
        }
        Update: {
          acknowledged?: boolean | null
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          alert_type?: string
          created_at?: string | null
          id?: string
          message?: string
          metadata?: Json | null
          resolved?: boolean | null
          resolved_at?: string | null
          severity?: string
          source?: string
          title?: string
        }
        Relationships: []
      }
      system_health_checks: {
        Row: {
          check_name: string
          checked_at: string | null
          created_at: string | null
          details: Json | null
          error_message: string | null
          id: string
          response_time_ms: number | null
          status: string
        }
        Insert: {
          check_name: string
          checked_at?: string | null
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          response_time_ms?: number | null
          status: string
        }
        Update: {
          check_name?: string
          checked_at?: string | null
          created_at?: string | null
          details?: Json | null
          error_message?: string | null
          id?: string
          response_time_ms?: number | null
          status?: string
        }
        Relationships: []
      }
      team_activities: {
        Row: {
          activity_type: string
          created_at: string
          description: string
          entity_id: string | null
          entity_type: string | null
          id: string
          metadata: Json | null
          org_id: string
          user_id: string
        }
        Insert: {
          activity_type: string
          created_at?: string
          description: string
          entity_id?: string | null
          entity_type?: string | null
          id?: string
          metadata?: Json | null
          org_id: string
          user_id: string
        }
        Update: {
          activity_type?: string
          created_at?: string
          description?: string
          entity_id?: string | null
          entity_type?: string | null
          id?: string
          metadata?: Json | null
          org_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_discussion_replies: {
        Row: {
          content: string
          created_at: string
          discussion_id: string
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string
          discussion_id: string
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string
          discussion_id?: string
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_discussion_replies_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_discussions: {
        Row: {
          content: string
          created_at: string
          created_by: string
          discussion_type: string
          id: string
          is_announcement: boolean | null
          is_pinned: boolean | null
          org_id: string
          title: string
          updated_at: string
        }
        Insert: {
          content: string
          created_at?: string
          created_by: string
          discussion_type?: string
          id?: string
          is_announcement?: boolean | null
          is_pinned?: boolean | null
          org_id: string
          title: string
          updated_at?: string
        }
        Update: {
          content?: string
          created_at?: string
          created_by?: string
          discussion_type?: string
          id?: string
          is_announcement?: boolean | null
          is_pinned?: boolean | null
          org_id?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_discussions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_memberships: {
        Row: {
          created_at: string
          id: string
          invited_by: string | null
          joined_at: string
          permissions: Json | null
          project_id: string
          role: string
          status: string
          team_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          invited_by?: string | null
          joined_at?: string
          permissions?: Json | null
          project_id: string
          role?: string
          status?: string
          team_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          invited_by?: string | null
          joined_at?: string
          permissions?: Json | null
          project_id?: string
          role?: string
          status?: string
          team_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_memberships_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      tier_limits: {
        Row: {
          created_at: string
          id: string
          max_active_projects: number
          max_collaborators_per_project: number
          max_members_per_team: number
          max_teams: number
          tier_name: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          id?: string
          max_active_projects?: number
          max_collaborators_per_project?: number
          max_members_per_team?: number
          max_teams?: number
          tier_name: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          id?: string
          max_active_projects?: number
          max_collaborators_per_project?: number
          max_members_per_team?: number
          max_teams?: number
          tier_name?: string
          updated_at?: string
        }
        Relationships: []
      }
      tools: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          slug: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          slug: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          slug?: string
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          buyer_id: string
          completed_at: string | null
          created_at: string
          id: string
          offer_id: string | null
          platform_fee: number
          screenplay_id: string
          seller_earnings: number
          seller_id: string
          status: string
          stripe_payment_intent_id: string | null
          stripe_transfer_id: string | null
          transaction_type: string
        }
        Insert: {
          amount: number
          buyer_id: string
          completed_at?: string | null
          created_at?: string
          id?: string
          offer_id?: string | null
          platform_fee?: number
          screenplay_id: string
          seller_earnings: number
          seller_id: string
          status?: string
          stripe_payment_intent_id?: string | null
          stripe_transfer_id?: string | null
          transaction_type?: string
        }
        Update: {
          amount?: number
          buyer_id?: string
          completed_at?: string | null
          created_at?: string
          id?: string
          offer_id?: string | null
          platform_fee?: number
          screenplay_id?: string
          seller_earnings?: number
          seller_id?: string
          status?: string
          stripe_payment_intent_id?: string | null
          stripe_transfer_id?: string | null
          transaction_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "transactions_offer_id_fkey"
            columns: ["offer_id"]
            isOneToOne: false
            referencedRelation: "screenplay_offers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_screenplay_id_fkey"
            columns: ["screenplay_id"]
            isOneToOne: false
            referencedRelation: "screenplays"
            referencedColumns: ["id"]
          },
        ]
      }
      user_analytics: {
        Row: {
          created_at: string
          event_data: Json | null
          event_type: string
          id: string
          org_id: string
          session_id: string | null
          tool_name: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          event_data?: Json | null
          event_type: string
          id?: string
          org_id: string
          session_id?: string | null
          tool_name?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          event_data?: Json | null
          event_type?: string
          id?: string
          org_id?: string
          session_id?: string | null
          tool_name?: string | null
          user_id?: string
        }
        Relationships: []
      }
      user_favorites: {
        Row: {
          created_at: string
          favorite_id: string
          favorite_type: string
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          favorite_id: string
          favorite_type: string
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string
          favorite_id?: string
          favorite_type?: string
          id?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      production_dashboard_analytics: {
        Row: {
          active_schedules: number | null
          approved_budgets: number | null
          available_resources: number | null
          last_activity: string | null
          org_id: string | null
          total_budget_amount: number | null
          total_budgets: number | null
          total_reports: number | null
          total_resources: number | null
          total_schedules: number | null
        }
        Relationships: []
      }
      public_storyboard_prompt_styles: {
        Row: {
          cost_multiplier: number | null
          description: string | null
          id: string | null
          label: string | null
        }
        Insert: {
          cost_multiplier?: number | null
          description?: string | null
          id?: string | null
          label?: string | null
        }
        Update: {
          cost_multiplier?: number | null
          description?: string | null
          id?: string | null
          label?: string | null
        }
        Relationships: []
      }
      subscription_analytics: {
        Row: {
          active_subscribers: number | null
          avg_days_subscribed: number | null
          signup_month: string | null
          subscriber_count: number | null
          subscription_tier: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      accept_beta_invitation: {
        Args: { invitation_code_param: string }
        Returns: {
          success: boolean
          message: string
        }[]
      }
      apply_promo_code: {
        Args: {
          code: string
          user_id_param: string
          original_amount_param: number
          subscription_id_param?: string
          payment_intent_id_param?: string
        }
        Returns: {
          success: boolean
          discount_amount: number
          final_amount: number
          error_message: string
        }[]
      }
      can_create_project: {
        Args: { target_org_id: string }
        Returns: boolean
      }
      check_feature_access: {
        Args: { target_user_id: string; feature_key: string }
        Returns: {
          has_access: boolean
          access_level: string
          usage_count: number
          usage_limit: number
          remaining_usage: number
        }[]
      }
      collect_database_metrics: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      create_system_alert: {
        Args: {
          alert_type_param: string
          severity_param: string
          title_param: string
          message_param: string
          source_param: string
          metadata_param?: Json
        }
        Returns: string
      }
      export_production_data: {
        Args: { org_id_param: string }
        Returns: {
          data_type: string
          export_data: Json
          record_count: number
        }[]
      }
      generate_script_coverage: {
        Args: {
          scene_id_param: string
          fidelity_level_param: string
          org_id_param: string
        }
        Returns: {
          id: string
          coverage_report: string
          synopsis: string
          strengths: string
          weaknesses: string
          verdict: string
        }[]
      }
      get_current_user_orgs: {
        Args: Record<PropertyKey, never>
        Returns: {
          org_id: string
        }[]
      }
      get_or_create_daily_usage: {
        Args: { target_org_id: string; target_date?: string }
        Returns: {
          id: string
          generation_count: number
          daily_limit: number
          remaining_generations: number
        }[]
      }
      get_or_increment_storyboard_usage: {
        Args: { target_org_id: string; increment?: boolean }
        Returns: {
          usage_record_id: string
          generation_count: number
          daily_limit: number
          remaining_generations: number
          tier_name: string
        }[]
      }
      get_organization_subscription_with_features: {
        Args: { target_org_id: string }
        Returns: {
          has_subscription: boolean
          plan_id: string
          plan_name: string
          status: string
          current_period_end: string
          trial_end: string
          cancel_at_period_end: boolean
          features: Json
          limits: Json
          addons: Json
          usage_summary: Json
          seats_info: Json
        }[]
      }
      get_user_org_ids: {
        Args: Record<PropertyKey, never>
        Returns: {
          org_id: string
        }[]
      }
      get_user_production_role: {
        Args: { _org_id: string }
        Returns: Database["public"]["Enums"]["production_role"]
      }
      get_user_subscription_with_features: {
        Args: { target_user_id?: string }
        Returns: {
          subscribed: boolean
          plan_id: string
          plan_name: string
          status: string
          current_period_end: string
          trial_end: string
          cancel_at_period_end: boolean
          features: Json
          limits: Json
        }[]
      }
      get_user_team_access: {
        Args: { target_org_id: string }
        Returns: {
          can_manage_teams: boolean
          max_teams: number
          max_members_per_team: number
          can_access_production: boolean
          can_create_custom_roles: boolean
        }[]
      }
      get_user_tier_limits: {
        Args: { target_org_id: string }
        Returns: {
          tier_name: string
          max_active_projects: number
          max_teams: number
          max_members_per_team: number
          max_collaborators_per_project: number
        }[]
      }
      get_user_usage_stats: {
        Args: { target_org_id: string }
        Returns: {
          active_projects: number
          total_projects: number
          team_count: number
          total_collaborators: number
          tier_name: string
          max_active_projects: number
          max_teams: number
          max_collaborators_per_project: number
        }[]
      }
      has_production_role: {
        Args: {
          _org_id: string
          _role: Database["public"]["Enums"]["production_role"]
        }
        Returns: boolean
      }
      increment_coverage_usage: {
        Args: { target_org_id: string }
        Returns: {
          success: boolean
          remaining_generations: number
          error_message: string
        }[]
      }
      initiate_database_backup: {
        Args: { backup_type_param?: string; retention_days?: number }
        Returns: string
      }
      is_org_admin: {
        Args: { target_org_id: string }
        Returns: boolean
      }
      is_org_member: {
        Args: { target_org_id: string }
        Returns: boolean
      }
      is_super_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      log_error: {
        Args: {
          error_level_param: string
          error_message_param: string
          error_stack_param?: string
          component_param?: string
          url_param?: string
          metadata_param?: Json
        }
        Returns: string
      }
      record_health_check: {
        Args: {
          check_name_param: string
          status_param: string
          response_time_param?: number
          details_param?: Json
          error_message_param?: string
        }
        Returns: string
      }
      record_migration: {
        Args: {
          migration_name_param: string
          migration_version_param: string
          rollback_sql_param?: string
          migration_checksum_param?: string
        }
        Returns: string
      }
      record_performance_metric: {
        Args: {
          metric_name_param: string
          metric_value_param: number
          metric_unit_param?: string
          tags_param?: Json
        }
        Returns: string
      }
      refresh_analytics_views: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      search_production_data: {
        Args: {
          org_id_param: string
          search_query?: string
          entity_type_filter?: string
          page_num?: number
          page_size?: number
        }
        Returns: {
          entity_type: string
          entity_id: string
          title: string
          description: string
          status: string
          created_at: string
          relevance_rank: number
        }[]
      }
      send_beta_invitation: {
        Args: {
          email_param: string
          phase_param?: number
          org_id_param?: string
        }
        Returns: string
      }
      storyboard_tier_limit: {
        Args: { tier_name: string }
        Returns: number
      }
      track_beta_event: {
        Args: {
          event_type_param: string
          event_name_param: string
          metadata_param?: Json
        }
        Returns: string
      }
      track_feature_usage: {
        Args: {
          target_org_id: string
          feature_key: string
          usage_amount?: number
          usage_type?: string
        }
        Returns: {
          success: boolean
          remaining_usage: number
          limit_exceeded: boolean
          error_message: string
        }[]
      }
      track_team_activity: {
        Args: {
          _org_id: string
          _activity_type: string
          _description: string
          _entity_type?: string
          _entity_id?: string
          _metadata?: Json
        }
        Returns: string
      }
      validate_promo_code: {
        Args: { code: string; user_id_param?: string }
        Returns: {
          valid: boolean
          campaign_id: string
          discount_type: Database["public"]["Enums"]["discount_type"]
          discount_value: number
          error_message: string
        }[]
      }
    }
    Enums: {
      approval_status:
        | "pending"
        | "approved"
        | "rejected"
        | "revision_requested"
      campaign_status: "draft" | "active" | "paused" | "completed" | "cancelled"
      crew_availability_status:
        | "available"
        | "busy"
        | "unavailable"
        | "tentative"
      discount_type: "percentage" | "fixed_amount" | "free_trial"
      equipment_status: "available" | "checked_out" | "maintenance" | "retired"
      production_role: "producer" | "coordinator" | "crew" | "viewer"
      project_status: "active" | "draft" | "completed" | "archived"
      target_audience:
        | "all_users"
        | "new_users"
        | "existing_users"
        | "specific_plans"
        | "custom_segment"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      approval_status: [
        "pending",
        "approved",
        "rejected",
        "revision_requested",
      ],
      campaign_status: ["draft", "active", "paused", "completed", "cancelled"],
      crew_availability_status: [
        "available",
        "busy",
        "unavailable",
        "tentative",
      ],
      discount_type: ["percentage", "fixed_amount", "free_trial"],
      equipment_status: ["available", "checked_out", "maintenance", "retired"],
      production_role: ["producer", "coordinator", "crew", "viewer"],
      project_status: ["active", "draft", "completed", "archived"],
      target_audience: [
        "all_users",
        "new_users",
        "existing_users",
        "specific_plans",
        "custom_segment",
      ],
    },
  },
} as const
