@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 222.2 84% 4.9%;
  --radius: 0.5rem;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 212.7 26.8% 83.9%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Simplified Prose Styles for Blog Content */
.prose {
  color: hsl(var(--foreground));
  max-width: none;
}

.prose h1 {
  font-family: "Playfair Display", Georgia, serif;
  font-size: 2.75rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 0;
  margin-bottom: 2rem;
  color: hsl(var(--foreground));
}

.prose h2 {
  font-family: "Playfair Display", Georgia, serif;
  font-size: 2.25rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 3rem;
  margin-bottom: 1.5rem;
  color: hsl(var(--foreground));
}

.prose h3 {
  font-family: "Playfair Display", Georgia, serif;
  font-size: 1.75rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  color: hsl(var(--foreground));
}

.prose h4 {
  font-family: "Inter", sans-serif;
  font-size: 1.375rem;
  font-weight: 600;
  line-height: 1.5;
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  color: hsl(var(--foreground));
}

.prose p {
  font-family: "Inter", sans-serif;
  font-size: 1.125rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  color: hsl(var(--foreground));
}

.prose strong {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.prose em {
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 2px;
}

.prose a:hover {
  color: hsl(var(--primary) / 0.8);
}

.prose ul, .prose ol {
  font-family: "Inter", sans-serif;
  font-size: 1.125rem;
  line-height: 1.8;
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--primary));
  padding: 1.5rem;
  margin: 2rem 0;
  font-style: italic;
  font-size: 1.2rem;
  background: hsl(var(--muted) / 0.3);
  border-radius: 0.5rem;
}

.prose code {
  background: hsl(var(--muted));
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
  font-family: 'Monaco', 'Consolas', monospace;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .prose h1 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .prose h2 {
    font-size: 1.75rem;
    margin-top: 2rem;
  }
  
  .prose h3 {
    font-size: 1.5rem;
    margin-top: 1.5rem;
  }
  
  .prose p, .prose ul, .prose ol {
    font-size: 1rem;
    line-height: 1.7;
  }
}

/* ... keep existing code (utility classes and animations) */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.font-playfair {
  font-family: "Playfair Display", serif;
}

.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cinema-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.gold-gradient {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e  50%, #ffd700 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(-10px) rotate(-1deg); }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-scale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-slide-up.delay-200 {
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

.animate-slide-up.delay-300 {
  animation-delay: 0.3s;
  animation-fill-mode: both;
}

.animate-fade-scale {
  animation: fade-scale 0.6s ease-out;
  animation-fill-mode: both;
}

.text-balance {
  text-wrap: balance;
}

@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .animate-slide-up,
  .animate-fade-scale {
    animation: none;
  }
  
  .cinema-card {
    transition: none;
  }
}

.cinema-card,
.animate-float,
.animate-slide-up,
.animate-fade-scale {
  will-change: transform;
  transform: translateZ(0);
}
