import { logger } from './logger';

export type StorageType = 'memory' | 'localStorage' | 'sessionStorage' | 'indexedDB';

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  storage?: StorageType;
  maxSize?: number; // Maximum number of items
  version?: string; // Cache version for invalidation
}

export interface CacheEntry<T> {
  key: string;
  value: T;
  timestamp: number;
  expires?: number;
}

export class Cache {
  private static instance: Cache;
  private memoryCache: Map<string, CacheEntry<any>> = new Map();
  private options: CacheOptions;
  private storage: Storage | null = null;
  private db: IDBDatabase | null = null;

  private constructor(options: CacheOptions = {}) {
    this.options = {
      ttl: 5 * 60 * 1000, // 5 minutes default TTL
      storage: 'memory',
      maxSize: 1000,
      version: '1.0',
      ...options
    };

    this.initializeStorage();
  }

  static getInstance(options?: CacheOptions): Cache {
    if (!Cache.instance) {
      Cache.instance = new Cache(options);
    }
    return Cache.instance;
  }

  // Initialize storage based on type
  private async initializeStorage(): Promise<void> {
    switch (this.options.storage) {
      case 'localStorage':
        this.storage = window.localStorage;
        break;
      case 'sessionStorage':
        this.storage = window.sessionStorage;
        break;
      case 'indexedDB':
        await this.initializeIndexedDB();
        break;
      default:
        this.storage = null;
    }
  }

  // Initialize IndexedDB
  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('cache', 1);

      request.onerror = () => {
        logger.error('Failed to open IndexedDB');
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('cache')) {
          db.createObjectStore('cache', { keyPath: 'key' });
        }
      };
    });
  }

  // Set a value in cache
  async set<T>(key: string, value: T, options?: Partial<CacheOptions>): Promise<void> {
    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: Date.now(),
      expires: options?.ttl ? Date.now() + options.ttl : undefined
    };

    try {
      switch (this.options.storage) {
        case 'memory':
          this.setInMemory(key, entry);
          break;
        case 'localStorage':
        case 'sessionStorage':
          await this.setInStorage(key, entry);
          break;
        case 'indexedDB':
          await this.setInIndexedDB(key, entry);
          break;
      }

      logger.debug('Cache set', { key, storage: this.options.storage });
    } catch (error) {
      logger.error('Failed to set cache', { key, error });
      throw error;
    }
  }

  // Get a value from cache
  async get<T>(key: string): Promise<T | null> {
    try {
      let entry: CacheEntry<T> | null = null;

      switch (this.options.storage) {
        case 'memory':
          entry = this.getFromMemory(key);
          break;
        case 'localStorage':
        case 'sessionStorage':
          entry = await this.getFromStorage(key);
          break;
        case 'indexedDB':
          entry = await this.getFromIndexedDB(key);
          break;
      }

      if (!entry) return null;

      // Check if entry has expired
      if (entry.expires && entry.expires < Date.now()) {
        await this.delete(key);
        return null;
      }

      return entry.value;
    } catch (error) {
      logger.error('Failed to get from cache', { key, error });
      return null;
    }
  }

  // Delete a value from cache
  async delete(key: string): Promise<void> {
    try {
      switch (this.options.storage) {
        case 'memory':
          this.memoryCache.delete(key);
          break;
        case 'localStorage':
        case 'sessionStorage':
          this.storage?.removeItem(key);
          break;
        case 'indexedDB':
          await this.deleteFromIndexedDB(key);
          break;
      }

      logger.debug('Cache deleted', { key });
    } catch (error) {
      logger.error('Failed to delete from cache', { key, error });
      throw error;
    }
  }

  // Clear all cache entries
  async clear(): Promise<void> {
    try {
      switch (this.options.storage) {
        case 'memory':
          this.memoryCache.clear();
          break;
        case 'localStorage':
        case 'sessionStorage':
          this.storage?.clear();
          break;
        case 'indexedDB':
          await this.clearIndexedDB();
          break;
      }

      logger.info('Cache cleared');
    } catch (error) {
      logger.error('Failed to clear cache', { error });
      throw error;
    }
  }

  // Set in memory cache
  private setInMemory<T>(key: string, entry: CacheEntry<T>): void {
    if (this.memoryCache.size >= (this.options.maxSize || 1000)) {
      const oldestKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(oldestKey);
    }
    this.memoryCache.set(key, entry);
  }

  // Get from memory cache
  private getFromMemory<T>(key: string): CacheEntry<T> | null {
    return this.memoryCache.get(key) || null;
  }

  // Set in storage
  private async setInStorage<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    if (!this.storage) return;
    this.storage.setItem(key, JSON.stringify(entry));
  }

  // Get from storage
  private async getFromStorage<T>(key: string): Promise<CacheEntry<T> | null> {
    if (!this.storage) return null;
    const data = this.storage.getItem(key);
    return data ? JSON.parse(data) : null;
  }

  // Set in IndexedDB
  private async setInIndexedDB<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    if (!this.db) return;
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      const request = store.put(entry);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Get from IndexedDB
  private async getFromIndexedDB<T>(key: string): Promise<CacheEntry<T> | null> {
    if (!this.db) return null;
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const request = store.get(key);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  // Delete from IndexedDB
  private async deleteFromIndexedDB(key: string): Promise<void> {
    if (!this.db) return;
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      const request = store.delete(key);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Clear IndexedDB
  private async clearIndexedDB(): Promise<void> {
    if (!this.db) return;
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  // Get cache statistics
  async getStats(): Promise<{
    size: number;
    storage: StorageType;
    version: string;
  }> {
    let size = 0;

    switch (this.options.storage) {
      case 'memory':
        size = this.memoryCache.size;
        break;
      case 'localStorage':
      case 'sessionStorage':
        size = this.storage?.length || 0;
        break;
      case 'indexedDB':
        if (this.db) {
          const transaction = this.db.transaction(['cache'], 'readonly');
          const store = transaction.objectStore('cache');
          size = await new Promise((resolve) => {
            const request = store.count();
            request.onsuccess = () => resolve(request.result);
          });
        }
        break;
    }

    return {
      size,
      storage: this.options.storage || 'memory',
      version: this.options.version || '1.0'
    };
  }
}

// Example usage:
/*
const cache = Cache.getInstance({
  storage: 'localStorage',
  ttl: 3600000, // 1 hour
  maxSize: 100
});

// Set value
await cache.set('user', { id: 1, name: 'John' });

// Get value
const user = await cache.get('user');

// Delete value
await cache.delete('user');

// Clear cache
await cache.clear();

// Get stats
const stats = await cache.getStats();
console.log(stats);
*/ 