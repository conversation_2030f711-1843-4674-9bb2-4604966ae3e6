
/**
 * PWA utility functions for service worker registration and app installation
 */

export interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

class PWAManager {
  private deferredPrompt: BeforeInstallPromptEvent | null = null;
  private isInstallable = false;
  private installCallbacks: Array<(canInstall: boolean) => void> = [];

  constructor() {
    this.initialize();
  }

  private initialize() {
    // Listen for the beforeinstallprompt event
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA: Install prompt available');
      e.preventDefault();
      this.deferredPrompt = e as BeforeInstallPromptEvent;
      this.isInstallable = true;
      this.notifyInstallCallbacks(true);
    });

    // Listen for the app being installed
    window.addEventListener('appinstalled', () => {
      console.log('PWA: App was installed');
      this.deferredPrompt = null;
      this.isInstallable = false;
      this.notifyInstallCallbacks(false);
    });
  }

  /**
   * Register the service worker
   */
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if (!('serviceWorker' in navigator)) {
      console.warn('PWA: Service workers not supported');
      return null;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/'
      });

      console.log('PWA: Service worker registered successfully');

      // Handle updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('PWA: New content available, reload to update');
              // You could show a toast notification here
            }
          });
        }
      });

      return registration;
    } catch (error) {
      console.error('PWA: Service worker registration failed:', error);
      return null;
    }
  }

  /**
   * Check if the app can be installed
   */
  canInstall(): boolean {
    return this.isInstallable;
  }

  /**
   * Check if the app is already installed
   */
  isInstalled(): boolean {
    return window.matchMedia('(display-mode: standalone)').matches ||
           (window.navigator as any).standalone === true ||
           document.referrer.includes('android-app://');
  }

  /**
   * Prompt the user to install the app
   */
  async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.warn('PWA: No install prompt available');
      return false;
    }

    try {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;
      
      console.log(`PWA: User ${outcome} the install prompt`);
      
      this.deferredPrompt = null;
      this.isInstallable = false;
      this.notifyInstallCallbacks(false);

      return outcome === 'accepted';
    } catch (error) {
      console.error('PWA: Install prompt failed:', error);
      return false;
    }
  }

  /**
   * Subscribe to install availability changes
   */
  onInstallAvailable(callback: (canInstall: boolean) => void): () => void {
    this.installCallbacks.push(callback);
    // Call immediately with current state
    callback(this.isInstallable);

    // Return unsubscribe function
    return () => {
      const index = this.installCallbacks.indexOf(callback);
      if (index > -1) {
        this.installCallbacks.splice(index, 1);
      }
    };
  }

  private notifyInstallCallbacks(canInstall: boolean) {
    this.installCallbacks.forEach(callback => callback(canInstall));
  }

  /**
   * Get app installation info
   */
  getInstallInfo() {
    return {
      canInstall: this.canInstall(),
      isInstalled: this.isInstalled(),
      isStandalone: window.matchMedia('(display-mode: standalone)').matches
    };
  }
}

// Create and export singleton instance
export const pwaManager = new PWAManager();

// Export utility functions
export const registerServiceWorker = () => pwaManager.registerServiceWorker();
export const canInstallApp = () => pwaManager.canInstall();
export const isAppInstalled = () => pwaManager.isInstalled();
export const promptAppInstall = () => pwaManager.promptInstall();
export const onInstallAvailable = (callback: (canInstall: boolean) => void) => 
  pwaManager.onInstallAvailable(callback);
