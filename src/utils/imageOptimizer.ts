import { logger } from './logger';

export interface ImageOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  blur?: number;
  fit?: 'cover' | 'contain' | 'fill';
}

export class ImageOptimizer {
  private static instance: ImageOptimizer;
  private imageCache: Map<string, string> = new Map();
  private loadingImages: Map<string, Promise<string>> = new Map();

  private constructor() {}

  static getInstance(): ImageOptimizer {
    if (!ImageOptimizer.instance) {
      ImageOptimizer.instance = new ImageOptimizer();
    }
    return ImageOptimizer.instance;
  }

  // Generate optimized image URL
  async getOptimizedImageUrl(
    src: string,
    options: ImageOptions = {}
  ): Promise<string> {
    const cacheKey = this.generateCacheKey(src, options);
    
    // Return cached image if available
    if (this.imageCache.has(cacheKey)) {
      return this.imageCache.get(cacheKey)!;
    }

    // Return existing loading promise if image is being processed
    if (this.loadingImages.has(cacheKey)) {
      return this.loadingImages.get(cacheKey)!;
    }

    // Process new image
    const loadingPromise = this.processImage(src, options).then(url => {
      this.imageCache.set(cacheKey, url);
      this.loadingImages.delete(cacheKey);
      return url;
    });

    this.loadingImages.set(cacheKey, loadingPromise);
    return loadingPromise;
  }

  // Process image with given options
  private async processImage(
    src: string,
    options: ImageOptions
  ): Promise<string> {
    try {
      // Create image element
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      // Load image
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = src;
      });

      // Create canvas
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Failed to get canvas context');

      // Set dimensions
      const { width, height } = this.calculateDimensions(img, options);
      canvas.width = width;
      canvas.height = height;

      // Apply blur if specified
      if (options.blur) {
        ctx.filter = `blur(${options.blur}px)`;
      }

      // Draw image
      ctx.drawImage(img, 0, 0, width, height);

      // Convert to desired format
      const format = options.format || 'webp';
      const quality = options.quality || 0.8;
      
      return canvas.toDataURL(`image/${format}`, quality);
    } catch (error) {
      logger.error('Image processing failed:', error);
      return src; // Return original source on error
    }
  }

  // Calculate image dimensions
  private calculateDimensions(
    img: HTMLImageElement,
    options: ImageOptions
  ): { width: number; height: number } {
    const { width: targetWidth, height: targetHeight, fit = 'cover' } = options;
    const aspectRatio = img.width / img.height;

    if (!targetWidth && !targetHeight) {
      return { width: img.width, height: img.height };
    }

    if (fit === 'contain') {
      if (targetWidth && targetHeight) {
        const scale = Math.min(
          targetWidth / img.width,
          targetHeight / img.height
        );
        return {
          width: img.width * scale,
          height: img.height * scale
        };
      }
      if (targetWidth) {
        return {
          width: targetWidth,
          height: targetWidth / aspectRatio
        };
      }
      return {
        width: targetHeight! * aspectRatio,
        height: targetHeight!
      };
    }

    if (fit === 'fill') {
      return {
        width: targetWidth || img.width,
        height: targetHeight || img.height
      };
    }

    // Default to 'cover'
    if (targetWidth && targetHeight) {
      const scale = Math.max(
        targetWidth / img.width,
        targetHeight / img.height
      );
      return {
        width: img.width * scale,
        height: img.height * scale
      };
    }
    if (targetWidth) {
      return {
        width: targetWidth,
        height: targetWidth / aspectRatio
      };
    }
    return {
      width: targetHeight! * aspectRatio,
      height: targetHeight!
    };
  }

  // Generate cache key
  private generateCacheKey(src: string, options: ImageOptions): string {
    return `${src}-${JSON.stringify(options)}`;
  }

  // Clear image cache
  clearCache(): void {
    this.imageCache.clear();
    this.loadingImages.clear();
  }

  // Preload images
  async preloadImages(urls: string[], options: ImageOptions = {}): Promise<void> {
    await Promise.all(
      urls.map(url => this.getOptimizedImageUrl(url, options))
    );
  }
}

// Example usage:
/*
const imageOptimizer = ImageOptimizer.getInstance();

// Optimize single image
const optimizedUrl = await imageOptimizer.getOptimizedImageUrl('image.jpg', {
  width: 800,
  height: 600,
  format: 'webp',
  quality: 0.8,
  blur: 0.5
});

// Preload multiple images
await imageOptimizer.preloadImages([
  'image1.jpg',
  'image2.jpg',
  'image3.jpg'
], {
  width: 400,
  height: 300,
  format: 'webp'
});

// Clear cache
imageOptimizer.clearCache();
*/ 