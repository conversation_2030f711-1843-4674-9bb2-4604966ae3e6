
import { BundleAnalyzer, BundleAnalysis } from './bundle/bundleAnalyzer';
import { ChunkPreloader } from './bundle/chunkPreloader';
import { IntelligentPreloader } from './bundle/intelligentPreloader';
import { PerformanceTracker } from './bundle/performanceTracker';

class BundleOptimizer {
  private static instance: BundleOptimizer;
  private bundleAnalyzer: BundleAnalyzer;
  private chunkPreloader: ChunkPreloader;
  private intelligentPreloader: IntelligentPreloader;
  private performanceTracker: PerformanceTracker;

  private constructor() {
    this.bundleAnalyzer = new BundleAnalyzer();
    this.chunkPreloader = new ChunkPreloader();
    this.intelligentPreloader = new IntelligentPreloader(this.chunkPreloader);
    this.performanceTracker = new PerformanceTracker();
  }

  static getInstance(): BundleOptimizer {
    if (!BundleOptimizer.instance) {
      BundleOptimizer.instance = new BundleOptimizer();
    }
    return BundleOptimizer.instance;
  }

  // Analyze current bundle usage
  analyzeBundleUsage(): BundleAnalysis {
    return this.bundleAnalyzer.analyzeBundleUsage();
  }

  // Preload critical chunks based on user behavior
  async preloadCriticalChunks(userRole?: string, subscriptionTier?: string) {
    return this.chunkPreloader.preloadCriticalChunks(userRole, subscriptionTier);
  }

  // Intelligent chunk preloading on user interaction
  setupIntelligentPreloading() {
    this.intelligentPreloader.setupIntelligentPreloading();
  }

  // Monitor chunk loading performance
  trackChunkPerformance(chunkName: string, loadTime: number) {
    this.performanceTracker.trackChunkPerformance(chunkName, loadTime);
  }

  // Get performance insights
  getPerformanceInsights(): string[] {
    return this.performanceTracker.getPerformanceInsights();
  }
}

export const bundleOptimizer = BundleOptimizer.getInstance();
