
import { lazy } from 'react';

/**
 * Centralized lazy component loading with optimized chunk names
 * This helps Vite create better bundle splitting by route and feature
 */

// Main application chunks - core app functionality
export const Dashboard = lazy(() => 
  import('@/components/Dashboard')
    .then(module => ({ default: module.default }))
);

export const LandingPage = lazy(() => 
  import('@/components/LandingPage')
    .then(module => ({ default: module.default }))
);

// Standalone page chunks - separate from dashboard features
export const ProductionToolsPage = lazy(() => 
  import('@/pages/ProductionTools')
    .then(module => ({ default: module.default }))
);

export const MarketplaceInfoPage = lazy(() => 
  import('@/pages/MarketplaceInfo')
    .then(module => ({ default: module.default }))
);

// Feature chunks - each major feature gets its own chunk (dashboard-specific)
export const ScreenplayMarketplaceFeature = lazy(() => 
  import('@/features/marketplace/ScreenplayMarketplace')
    .then(module => ({ default: module.default }))
);

export const ProductionToolsFeature = lazy(() => 
  import('@/features/production/ProductionTools')
    .then(module => ({ default: module.default }))
);

export const CoverageGeneratorFeature = lazy(() => 
  import('@/features/coverage/CoverageGenerator')
    .then(module => ({ default: module.default }))
);

// Editor chunks - heavy editing components with ProseMirror lazy loading
export const LazyProseMirrorEditor = lazy(() => 
  import('@/components/lazy/LazyProseMirrorEditor')
    .then(module => ({ default: module.default }))
);

export const StoryboardStudio = lazy(() => 
  import('@/components/StoryboardStudio')
    .then(module => ({ default: module.default }))
);

export const BeatBoard = lazy(() => 
  import('@/components/BeatBoard')
    .then(module => ({ default: module.default }))
);

// Admin chunks - separate administrative functionality
export const AdminDashboard = lazy(() => 
  import('@/pages/AdminDashboard')
    .then(module => ({ default: module.default }))
);

export const PromptManagement = lazy(() => 
  import('@/pages/PromptManagement')
    .then(module => ({ default: module.default }))
);

// Team management - collaborative features
export const TeamManagement = lazy(() => 
  import('@/components/TeamManagement')
    .then(module => ({ default: module.default }))
);
