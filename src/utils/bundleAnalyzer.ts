import { logger } from './logger';

export interface BundleStats {
  totalSize: number;
  chunkCount: number;
  moduleCount: number;
  duplicateModules: number;
  chunks: {
    name: string;
    size: number;
    modules: {
      name: string;
      size: number;
    }[];
  }[];
}

export class BundleAnalyzer {
  private static instance: BundleAnalyzer;
  private stats: BundleStats | null = null;

  private constructor() {}

  static getInstance(): BundleAnalyzer {
    if (!BundleAnalyzer.instance) {
      BundleAnalyzer.instance = new BundleAnalyzer();
    }
    return BundleAnalyzer.instance;
  }

  // Load bundle stats from the stats file
  async loadStats(): Promise<BundleStats> {
    try {
      const response = await fetch('/dist/stats.html');
      const stats = await response.json();
      this.stats = stats;
      return stats;
    } catch (error) {
      logger.error('Failed to load bundle stats:', error);
      throw error;
    }
  }

  // Get largest chunks
  getLargestChunks(limit = 5): { name: string; size: number }[] {
    if (!this.stats) {
      throw new Error('Bundle stats not loaded');
    }

    return this.stats.chunks
      .sort((a, b) => b.size - a.size)
      .slice(0, limit)
      .map(chunk => ({
        name: chunk.name,
        size: chunk.size
      }));
  }

  // Get duplicate modules
  getDuplicateModules(): { name: string; count: number }[] {
    if (!this.stats) {
      throw new Error('Bundle stats not loaded');
    }

    const moduleCount = new Map<string, number>();
    this.stats.chunks.forEach(chunk => {
      chunk.modules.forEach(module => {
        moduleCount.set(
          module.name,
          (moduleCount.get(module.name) || 0) + 1
        );
      });
    });

    return Array.from(moduleCount.entries())
      .filter(([_, count]) => count > 1)
      .map(([name, count]) => ({
        name,
        count
      }))
      .sort((a, b) => b.count - a.count);
  }

  // Get optimization suggestions
  getOptimizationSuggestions(): string[] {
    if (!this.stats) {
      throw new Error('Bundle stats not loaded');
    }

    const suggestions: string[] = [];

    // Check for large chunks
    const largeChunks = this.getLargestChunks();
    if (largeChunks.some(chunk => chunk.size > 500 * 1024)) {
      suggestions.push(
        'Consider code splitting large chunks: ' +
        largeChunks
          .filter(chunk => chunk.size > 500 * 1024)
          .map(chunk => chunk.name)
          .join(', ')
      );
    }

    // Check for duplicate modules
    const duplicates = this.getDuplicateModules();
    if (duplicates.length > 0) {
      suggestions.push(
        'Consider deduplicating modules: ' +
        duplicates
          .slice(0, 5)
          .map(dup => `${dup.name} (${dup.count} instances)`)
          .join(', ')
      );
    }

    // Check total bundle size
    if (this.stats.totalSize > 2 * 1024 * 1024) {
      suggestions.push(
        `Total bundle size (${(this.stats.totalSize / 1024 / 1024).toFixed(2)}MB) is too large. ` +
        'Consider implementing more aggressive code splitting.'
      );
    }

    // Check chunk count
    if (this.stats.chunkCount > 20) {
      suggestions.push(
        `High number of chunks (${this.stats.chunkCount}). ` +
        'Consider consolidating related chunks.'
      );
    }

    return suggestions;
  }

  // Generate optimization report
  async generateReport(): Promise<string> {
    if (!this.stats) {
      await this.loadStats();
    }

    const report = [
      'Bundle Analysis Report',
      '===================',
      '',
      `Total Size: ${(this.stats!.totalSize / 1024 / 1024).toFixed(2)}MB`,
      `Chunk Count: ${this.stats!.chunkCount}`,
      `Module Count: ${this.stats!.moduleCount}`,
      `Duplicate Modules: ${this.stats!.duplicateModules}`,
      '',
      'Largest Chunks:',
      ...this.getLargestChunks().map(
        chunk => `- ${chunk.name}: ${(chunk.size / 1024).toFixed(2)}KB`
      ),
      '',
      'Duplicate Modules:',
      ...this.getDuplicateModules().map(
        dup => `- ${dup.name}: ${dup.count} instances`
      ),
      '',
      'Optimization Suggestions:',
      ...this.getOptimizationSuggestions().map(suggestion => `- ${suggestion}`)
    ].join('\n');

    return report;
  }
}

// Example usage:
/*
const analyzer = BundleAnalyzer.getInstance();

// Load and analyze bundle stats
await analyzer.loadStats();

// Get optimization suggestions
const suggestions = analyzer.getOptimizationSuggestions();
console.log('Optimization suggestions:', suggestions);

// Generate full report
const report = await analyzer.generateReport();
console.log(report);
*/ 