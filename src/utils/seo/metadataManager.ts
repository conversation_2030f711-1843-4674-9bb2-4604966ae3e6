
import { SEOMetadata } from './types';

export class MetadataManager {
  setMetadata(metadata: SEOMetadata) {
    try {
      // Only update if we're in the browser
      if (typeof document === 'undefined') return;

      // Update title
      if (metadata.title) {
        document.title = metadata.title;
        this.updateMetaTag('og:title', metadata.title);
        this.updateMetaTag('twitter:title', metadata.title);
      }

      // Update description
      if (metadata.description) {
        this.updateMetaTag('description', metadata.description);
        this.updateMetaTag('og:description', metadata.description);
        this.updateMetaTag('twitter:description', metadata.description);
      }

      // Update keywords (mobile-optimized)
      if (metadata.keywords && metadata.keywords.length > 0) {
        const keywordsString = metadata.keywords.slice(0, 10).join(', '); // Limit for mobile
        this.updateMetaTag('keywords', keywordsString);
      }

      // Update author
      if (metadata.author) {
        this.updateMetaTag('author', metadata.author);
      }

      // Update type
      if (metadata.type) {
        this.updateMetaTag('og:type', metadata.type);
      }

      // Update canonical URL
      if (metadata.canonical) {
        this.updateLinkTag('canonical', `${window.location.origin}${metadata.canonical}`);
      }

      // Update robots
      if (metadata.robots) {
        this.updateMetaTag('robots', metadata.robots);
      }
    } catch (error) {
      console.warn('SEO metadata update failed:', error);
    }
  }

  private updateMetaTag(name: string, content: string) {
    if (typeof document === 'undefined') return;

    try {
      let element = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`) as HTMLMetaElement;
      
      if (!element) {
        element = document.createElement('meta');
        if (name.startsWith('og:') || name.startsWith('twitter:')) {
          element.setAttribute('property', name);
        } else {
          element.setAttribute('name', name);
        }
        document.head.appendChild(element);
      }
      
      element.setAttribute('content', content);
    } catch (error) {
      console.warn(`Failed to update meta tag ${name}:`, error);
    }
  }

  private updateLinkTag(rel: string, href: string) {
    if (typeof document === 'undefined') return;

    try {
      let element = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
      
      if (!element) {
        element = document.createElement('link');
        element.setAttribute('rel', rel);
        document.head.appendChild(element);
      }
      
      element.setAttribute('href', href);
    } catch (error) {
      console.warn(`Failed to update link tag ${rel}:`, error);
    }
  }
}
