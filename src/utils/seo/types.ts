
export interface SEOMetadata {
  title?: string;
  description?: string;
  keywords?: string[];
  author?: string;
  type?: string;
  canonical?: string;
  robots?: string;
}

export interface WebsiteData {
  "@context": string;
  "@type": string;
  name: string;
  url: string;
  description: string;
  potentialAction?: {
    "@type": string;
    target: string;
    "query-input": string;
  };
  sameAs?: string[];
}

export interface OrganizationData {
  "@context": string;
  "@type": string;
  name: string;
  url: string;
  logo: string;
  description: string;
  foundingDate?: string;
  industry?: string;
  numberOfEmployees?: string;
  contactPoint?: {
    "@type": string;
    contactType: string;
    email: string;
    availableLanguage: string;
  };
  sameAs: string[];
}

export interface ArticleData {
  "@context": string;
  "@type": string;
  headline: string;
  description: string;
  image: {
    "@type": string;
    url: string;
    width: number;
    height: number;
  };
  datePublished: string;
  dateModified: string;
  author: {
    "@type": string;
    name: string;
    url?: string;
  };
  publisher: {
    "@type": string;
    name: string;
    logo: {
      "@type": string;
      url: string;
      width: number;
      height: number;
    };
  };
  mainEntityOfPage?: {
    "@type": string;
    "@id": string;
  };
}
