
import { WebsiteData, OrganizationData, ArticleData } from './types';

export class DataGenerators {
  static generateWebsiteData(): WebsiteData {
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://scriptgenius.com';
    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      name: "ScriptGenius",
      url: origin,
      description: "AI-powered screenwriting platform for modern storytellers. Create professional screenplays with collaborative tools and intelligent assistance.",
      potentialAction: {
        "@type": "SearchAction",
        target: `${origin}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      },
      sameAs: [
        "https://twitter.com/scriptgenius",
        "https://linkedin.com/company/scriptgenius",
        "https://facebook.com/scriptgenius"
      ]
    };
  }

  static generateOrganizationData(): OrganizationData {
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://scriptgenius.com';
    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      name: "ScriptGenius",
      url: origin,
      logo: `${origin}/placeholder.svg`,
      description: "AI-powered screenwriting platform for modern storytellers. Professional tools for collaborative screenplay development.",
      foundingDate: "2024",
      industry: "Software Development",
      numberOfEmployees: "1-10",
      contactPoint: {
        "@type": "ContactPoint",
        contactType: "customer service",
        email: "<EMAIL>",
        availableLanguage: "English"
      },
      sameAs: [
        "https://twitter.com/scriptgenius",
        "https://linkedin.com/company/scriptgenius",
        "https://facebook.com/scriptgenius"
      ]
    };
  }

  static generateArticleData(data: {
    headline: string;
    description: string;
    image: string;
    datePublished: string;
    dateModified: string;
    author: { name: string; url?: string };
    publisher: { name: string; logo: { url: string; width: number; height: number } };
  }): ArticleData {
    return {
      "@context": "https://schema.org",
      "@type": "Article",
      headline: data.headline,
      description: data.description,
      image: {
        "@type": "ImageObject",
        url: data.image,
        width: 1200,
        height: 630
      },
      datePublished: data.datePublished,
      dateModified: data.dateModified,
      author: {
        "@type": "Person",
        name: data.author.name,
        url: data.author.url
      },
      publisher: {
        "@type": "Organization",
        name: data.publisher.name,
        logo: {
          "@type": "ImageObject",
          url: data.publisher.logo.url,
          width: data.publisher.logo.width,
          height: data.publisher.logo.height
        }
      },
      mainEntityOfPage: {
        "@type": "WebPage",
        "@id": typeof window !== 'undefined' ? window.location.href : ''
      }
    };
  }

  static generateSoftwareApplicationData() {
    const origin = typeof window !== 'undefined' ? window.location.origin : 'https://scriptgenius.com';
    return {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      name: "ScriptGenius",
      description: "Professional screenwriting tools with AI-powered assistance for modern storytellers",
      url: origin,
      applicationCategory: "ProductivityApplication",
      operatingSystem: "Web Browser",
      offers: {
        "@type": "Offer",
        price: "0",
        priceCurrency: "USD",
        availability: "https://schema.org/InStock"
      },
      aggregateRating: {
        "@type": "AggregateRating",
        ratingValue: "4.8",
        reviewCount: "150",
        bestRating: "5",
        worstRating: "1"
      },
      creator: {
        "@type": "Organization",
        name: "ScriptGenius",
        url: origin
      }
    };
  }
}
