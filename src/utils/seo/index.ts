
import { SEOMetadata, WebsiteData, OrganizationData, ArticleData } from './types';
import { MetadataManager } from './metadataManager';
import { StructuredDataManager } from './structuredDataManager';
import { DataGenerators } from './dataGenerators';

class SEOManager {
  private metadataManager = new MetadataManager();
  private structuredDataManager = new StructuredDataManager();

  setMetadata(metadata: SEOMetadata) {
    try {
      this.metadataManager.setMetadata(metadata);
    } catch (error) {
      console.warn('Failed to set SEO metadata:', error);
    }
  }

  generateWebsiteData(): WebsiteData {
    try {
      return DataGenerators.generateWebsiteData();
    } catch (error) {
      console.warn('Failed to generate website data:', error);
      return {
        "@context": "https://schema.org",
        "@type": "WebSite",
        name: "ScriptGenius",
        url: typeof window !== 'undefined' ? window.location.origin : '',
        description: "AI-powered screenwriting platform for modern storytellers"
      };
    }
  }

  generateOrganizationData(): OrganizationData {
    try {
      return DataGenerators.generateOrganizationData();
    } catch (error) {
      console.warn('Failed to generate organization data:', error);
      return {
        "@context": "https://schema.org",
        "@type": "Organization",
        name: "ScriptGenius",
        url: typeof window !== 'undefined' ? window.location.origin : '',
        logo: typeof window !== 'undefined' ? `${window.location.origin}/logo.png` : '/logo.png',
        description: "AI-powered screenwriting platform for modern storytellers",
        sameAs: []
      };
    }
  }

  generateArticleData(data: {
    headline: string;
    description: string;
    image: string;
    datePublished: string;
    dateModified: string;
    author: { name: string; url?: string };
    publisher: { name: string; logo: { url: string; width: number; height: number } };
  }): ArticleData {
    try {
      return DataGenerators.generateArticleData(data);
    } catch (error) {
      console.warn('Failed to generate article data:', error);
      return {
        "@context": "https://schema.org",
        "@type": "Article",
        headline: data.headline,
        description: data.description,
        image: {
          "@type": "ImageObject",
          url: data.image,
          width: 1200,
          height: 630
        },
        datePublished: data.datePublished,
        dateModified: data.dateModified,
        author: {
          "@type": "Person",
          name: data.author.name,
          url: data.author.url
        },
        publisher: {
          "@type": "Organization",
          name: data.publisher.name,
          logo: {
            "@type": "ImageObject",
            url: data.publisher.logo.url,
            width: data.publisher.logo.width,
            height: data.publisher.logo.height
          }
        }
      };
    }
  }

  addStructuredData(data: WebsiteData | ArticleData | OrganizationData) {
    try {
      this.structuredDataManager.addStructuredData(data);
    } catch (error) {
      console.warn('Failed to add structured data:', error);
    }
  }

  clearStructuredData() {
    try {
      this.structuredDataManager.clearStructuredData();
    } catch (error) {
      console.warn('Failed to clear structured data:', error);
    }
  }
}

// Create a fallback SEO manager that implements the same interface
class FallbackSEOManager {
  setMetadata() {}
  
  generateWebsiteData(): WebsiteData {
    return {
      "@context": "https://schema.org",
      "@type": "WebSite",
      name: "ScriptGenius",
      url: "",
      description: "AI-powered screenwriting platform for modern storytellers"
    };
  }
  
  generateOrganizationData(): OrganizationData {
    return {
      "@context": "https://schema.org",
      "@type": "Organization",
      name: "ScriptGenius",
      url: "",
      logo: "",
      description: "AI-powered screenwriting platform for modern storytellers",
      sameAs: []
    };
  }
  
  generateArticleData(data: any): ArticleData {
    return {
      "@context": "https://schema.org",
      "@type": "Article",
      headline: data.headline,
      description: data.description,
      image: {
        "@type": "ImageObject",
        url: data.image,
        width: 1200,
        height: 630
      },
      datePublished: data.datePublished,
      dateModified: data.dateModified,
      author: {
        "@type": "Person",
        name: data.author.name,
        url: data.author.url
      },
      publisher: {
        "@type": "Organization",
        name: data.publisher.name,
        logo: {
          "@type": "ImageObject",
          url: data.publisher.logo.url,
          width: data.publisher.logo.width,
          height: data.publisher.logo.height
        }
      }
    };
  }
  
  addStructuredData() {}
  clearStructuredData() {}
}

// Create singleton instance with error handling
let seoInstance: SEOManager | FallbackSEOManager;

try {
  seoInstance = new SEOManager();
} catch (error) {
  console.warn('Failed to initialize SEO manager:', error);
  seoInstance = new FallbackSEOManager();
}

export const seo = seoInstance;
export * from './types';
export { MetadataManager } from './metadataManager';
export { StructuredDataManager } from './structuredDataManager';
export { DataGenerators } from './dataGenerators';
