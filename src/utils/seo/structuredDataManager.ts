
import { WebsiteData, OrganizationData, ArticleData } from './types';

export class StructuredDataManager {
  private structuredDataElements: HTMLElement[] = [];

  addStructuredData(data: WebsiteData | ArticleData | OrganizationData) {
    if (typeof document === 'undefined') return;

    try {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(data);
      document.head.appendChild(script);
      this.structuredDataElements.push(script);
    } catch (error) {
      console.warn('Failed to add structured data:', error);
    }
  }

  clearStructuredData() {
    if (typeof document === 'undefined') return;

    try {
      this.structuredDataElements.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });
      this.structuredDataElements = [];
    } catch (error) {
      console.warn('Failed to clear structured data:', error);
    }
  }
}
