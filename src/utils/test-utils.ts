
import { logger } from './logger';
import { render, RenderOptions } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ReactElement } from 'react';

export interface TestUtils {
  render: typeof render;
  userEvent: typeof userEvent;
  mockApi: MockApi;
  mockStorage: MockStorage;
  mockRouter: MockRouter;
  mockStore: MockStore;
  generateMockData: GenerateMockData;
  waitForCondition: WaitForCondition;
  createTestWrapper: CreateTestWrapper;
}

export interface MockApi {
  get: any;
  post: any;
  put: any;
  delete: any;
  reset: () => void;
}

export interface MockStorage {
  getItem: any;
  setItem: any;
  removeItem: any;
  clear: any;
  reset: () => void;
}

export interface MockRouter {
  push: any;
  replace: any;
  back: any;
  forward: any;
  pathname: string;
  query: Record<string, string>;
  reset: () => void;
}

export interface MockStore {
  dispatch: any;
  getState: any;
  subscribe: any;
  reset: () => void;
}

export type GenerateMockData = <T>(
  template: Partial<T>,
  count?: number
) => T[];

export type WaitForCondition = (
  condition: () => boolean | Promise<boolean>,
  options?: {
    timeout?: number;
    interval?: number;
  }
) => Promise<void>;

export type CreateTestWrapper = (
  options?: RenderOptions
) => (ui: ReactElement) => ReactElement;

// Create mock API
const createMockApi = (): MockApi => {
  const mockApi = {
    get: () => Promise.resolve({ data: null }),
    post: () => Promise.resolve({ data: null }),
    put: () => Promise.resolve({ data: null }),
    delete: () => Promise.resolve({ data: null }),
    reset: () => {
      // Reset implementation
    }
  };

  return mockApi;
};

// Create mock storage
const createMockStorage = (): MockStorage => {
  const storage: Record<string, string> = {};
  const mockStorage = {
    getItem: (key: string) => storage[key] || null,
    setItem: (key: string, value: string) => {
      storage[key] = value;
    },
    removeItem: (key: string) => {
      delete storage[key];
    },
    clear: () => {
      Object.keys(storage).forEach(key => {
        delete storage[key];
      });
    },
    reset: () => {
      Object.keys(storage).forEach(key => {
        delete storage[key];
      });
    }
  };

  return mockStorage;
};

// Create mock router
const createMockRouter = (): MockRouter => {
  const mockRouter = {
    push: () => Promise.resolve(),
    replace: () => Promise.resolve(),
    back: () => {},
    forward: () => {},
    pathname: '/',
    query: {},
    reset: () => {
      mockRouter.pathname = '/';
      mockRouter.query = {};
    }
  };

  return mockRouter;
};

// Create mock store
const createMockStore = (): MockStore => {
  const mockStore = {
    dispatch: () => {},
    getState: () => ({}),
    subscribe: () => () => {},
    reset: () => {
      // Reset implementation
    }
  };

  return mockStore;
};

// Generate mock data
const generateMockData = <T>(
  template: Partial<T>,
  count: number = 1
): T[] => {
  const generateItem = (index: number): T => {
    const item: any = { ...template };
    Object.keys(item).forEach(key => {
      if (typeof item[key] === 'function') {
        item[key] = item[key](index);
      }
    });
    return item as T;
  };

  return Array.from({ length: count }, (_, index) => generateItem(index));
};

// Wait for condition
const waitForCondition = async (
  condition: () => boolean | Promise<boolean>,
  options: {
    timeout?: number;
    interval?: number;
  } = {}
): Promise<void> => {
  const {
    timeout = 5000,
    interval = 100
  } = options;

  const startTime = Date.now();

  while (Date.now() - startTime < timeout) {
    const result = await condition();
    if (result) return;

    await new Promise(resolve => setTimeout(resolve, interval));
  }

  throw new Error(`Condition not met after ${timeout}ms`);
};

// Create test wrapper
const createTestWrapper = (options: RenderOptions = {}) => {
  return (ui: ReactElement): ReactElement => {
    return ui;
  };
};

// Create test utilities
export const createTestUtils = (): TestUtils => {
  const mockApi = createMockApi();
  const mockStorage = createMockStorage();
  const mockRouter = createMockRouter();
  const mockStore = createMockStore();

  return {
    render,
    userEvent,
    mockApi,
    mockStorage,
    mockRouter,
    mockStore,
    generateMockData,
    waitForCondition,
    createTestWrapper
  };
};
