
interface UploadResult {
  success: boolean;
  error?: string;
  url?: string;
}

interface UploadOptions {
  maxSize?: number;
  allowedTypes?: string[];
  fileNamePattern?: RegExp;
}

interface ProgressListener {
  (progress: number): void;
}

export class UploadManager {
  private progressListeners: Set<ProgressListener> = new Set();

  validateFileSize(file: File, maxSizeMB: number): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }

  validateFileType(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.includes(file.type);
  }

  validateFileName(file: File, pattern: RegExp): boolean {
    return pattern.test(file.name);
  }

  async upload(file: File, options: UploadOptions = {}): Promise<UploadResult> {
    try {
      // Validate file size
      if (options.maxSize && !this.validateFileSize(file, options.maxSize)) {
        return { success: false, error: 'File size too large' };
      }

      // Validate file type
      if (options.allowedTypes && !this.validateFileType(file, options.allowedTypes)) {
        return { success: false, error: 'File type not allowed' };
      }

      // Validate file name
      if (options.fileNamePattern && !this.validateFileName(file, options.fileNamePattern)) {
        return { success: false, error: 'Invalid file name' };
      }

      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 10));
        this.notifyProgress(i);
      }

      return { success: true, url: `uploads/${file.name}` };
    } catch (error) {
      return { success: false, error: (error as Error).message };
    }
  }

  onProgress(listener: ProgressListener): () => void {
    this.progressListeners.add(listener);
    return () => this.progressListeners.delete(listener);
  }

  private notifyProgress(progress: number): void {
    this.progressListeners.forEach(listener => listener(progress));
  }

  reset(): void {
    this.progressListeners.clear();
  }
}

export const uploadManager = new UploadManager();
