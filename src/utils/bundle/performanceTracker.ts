
export class PerformanceTracker {
  private chunkLoadTimes = new Map<string, number[]>();
  private performanceThresholds = {
    fast: 100,
    moderate: 500,
    slow: 1000
  };

  trackChunkPerformance(chunkName: string, loadTime: number) {
    if (!this.chunkLoadTimes.has(chunkName)) {
      this.chunkLoadTimes.set(chunkName, []);
    }
    
    this.chunkLoadTimes.get(chunkName)!.push(loadTime);
    
    // Log slow chunks
    if (loadTime > this.performanceThresholds.slow) {
      console.warn(`Slow chunk load detected: ${chunkName} took ${loadTime}ms`);
    }
  }

  getPerformanceInsights(): string[] {
    const insights: string[] = [];
    
    this.chunkLoadTimes.forEach((times, chunkName) => {
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      
      if (avgTime > this.performanceThresholds.slow) {
        insights.push(`${chunkName} chunk is loading slowly (avg: ${avgTime.toFixed(0)}ms)`);
      }
      
      const slowLoads = times.filter(time => time > this.performanceThresholds.slow).length;
      const loadAttempts = times.length;
      
      if (slowLoads / loadAttempts > 0.3) {
        insights.push(`${chunkName} has frequent slow loads (${slowLoads}/${loadAttempts})`);
      }
    });
    
    return insights;
  }

  generatePerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      chunkPerformance: {} as Record<string, any>,
      overallMetrics: this.calculateOverallMetrics(),
      recommendations: this.getPerformanceInsights()
    };
    
    this.chunkLoadTimes.forEach((times, chunkName) => {
      report.chunkPerformance[chunkName] = {
        averageLoadTime: times.reduce((a, b) => a + b, 0) / times.length,
        minLoadTime: Math.min(...times),
        maxLoadTime: Math.max(...times),
        loadAttempts: times.length,
        performanceRating: this.getPerformanceRating(times)
      };
    });
    
    return report;
  }

  private calculateOverallMetrics() {
    const allTimes = Array.from(this.chunkLoadTimes.values()).flat();
    
    if (allTimes.length === 0) {
      return { avgLoadTime: 0, totalChunks: 0, performanceScore: 100 };
    }
    
    const avgLoadTime = allTimes.reduce((a, b) => a + b, 0) / allTimes.length;
    const performanceScore = this.calculatePerformanceScore(avgLoadTime);
    
    return {
      avgLoadTime,
      totalChunks: this.chunkLoadTimes.size,
      performanceScore
    };
  }

  private getPerformanceRating(times: number[]): 'excellent' | 'good' | 'fair' | 'poor' {
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
    
    if (avgTime < this.performanceThresholds.fast) return 'excellent';
    if (avgTime < this.performanceThresholds.moderate) return 'good';
    if (avgTime < this.performanceThresholds.slow) return 'fair';
    return 'poor';
  }

  private calculatePerformanceScore(avgLoadTime: number): number {
    // Score from 0-100, where 100 is best
    if (avgLoadTime < this.performanceThresholds.fast) return 100;
    if (avgLoadTime < this.performanceThresholds.moderate) return 80;
    if (avgLoadTime < this.performanceThresholds.slow) return 60;
    return Math.max(0, 40 - (avgLoadTime - this.performanceThresholds.slow) / 100);
  }
}
