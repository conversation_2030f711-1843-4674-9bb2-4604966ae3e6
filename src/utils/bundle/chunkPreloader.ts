
export class ChunkPreloader {
  private preloadedChunks = new Set<string>();
  private priorityMap: Record<string, number> = {
    'vendor': 10,
    'ui': 8,
    'data': 7,
    'forms': 6,
    'charts': 5,
    'editor': 4,
    'utils': 3
  };

  async preloadCriticalChunks(userRole?: string, subscriptionTier?: string) {
    const criticalChunks = this.getCriticalChunks(userRole, subscriptionTier);
    
    const preloadPromises = criticalChunks.map(chunk => this.preloadChunk(chunk));
    
    try {
      await Promise.allSettled(preloadPromises);
      console.log('✅ Critical chunks preloaded successfully');
    } catch (error) {
      console.warn('Some chunks failed to preload:', error);
    }
  }

  private getCriticalChunks(userRole?: string, subscriptionTier?: string): string[] {
    const baseCritical = ['vendor', 'ui', 'data'];
    
    // Add role-specific chunks
    if (userRole === 'admin' || userRole === 'super_admin') {
      baseCritical.push('utils', 'forms');
    }
    
    // Add subscription-tier specific chunks
    if (subscriptionTier === 'studio' || subscriptionTier === 'enterprise') {
      baseCritical.push('charts', 'editor');
    }
    
    return baseCritical;
  }

  private async preloadChunk(chunkName: string): Promise<void> {
    if (this.preloadedChunks.has(chunkName)) {
      return;
    }

    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'modulepreload';
      link.href = `/chunks/${chunkName}.js`;
      
      link.onload = () => {
        this.preloadedChunks.add(chunkName);
        resolve();
      };
      
      link.onerror = () => {
        reject(new Error(`Failed to preload chunk: ${chunkName}`));
      };
      
      document.head.appendChild(link);
    });
  }

  preloadOnInteraction(element: HTMLElement, chunkName: string) {
    const events = ['mouseenter', 'touchstart', 'focus'];
    
    const preload = () => {
      this.preloadChunk(chunkName);
      // Remove listeners after first trigger
      events.forEach(event => {
        element.removeEventListener(event, preload);
      });
    };
    
    events.forEach(event => {
      element.addEventListener(event, preload, { once: true, passive: true });
    });
  }
}
