
import { ChunkPreloader } from './chunkPreloader';

export class IntelligentPreloader {
  private chunkPreloader: ChunkPreloader;
  private interactionMap = new Map<string, string[]>();

  constructor(chunkPreloader: ChunkPreloader) {
    this.chunkPreloader = chunkPreloader;
    this.setupRouteChunkMapping();
  }

  setupIntelligentPreloading() {
    this.setupRouteBasedPreloading();
    this.setupUserBehaviorPreloading();
    this.setupNetworkAwarePreloading();
  }

  private setupRouteChunkMapping() {
    this.interactionMap.set('/database-monitoring', ['data', 'charts', 'forms']);
    this.interactionMap.set('/production', ['editor', 'forms', 'charts']);
    this.interactionMap.set('/marketplace', ['charts', 'data']);
    this.interactionMap.set('/settings', ['forms', 'ui']);
  }

  private setupRouteBasedPreloading() {
    // Preload chunks for likely next routes
    const currentPath = window.location.pathname;
    const likelyRoutes = this.getLikelyNextRoutes(currentPath);
    
    likelyRoutes.forEach(route => {
      const chunks = this.interactionMap.get(route) || [];
      chunks.forEach(chunk => {
        // Delay preloading to not block initial load
        setTimeout(() => {
          this.chunkPreloader.preloadCriticalChunks();
        }, 2000);
      });
    });
  }

  private setupUserBehaviorPreloading() {
    // Track user interactions and preload accordingly
    document.addEventListener('mouseover', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a[href]') as HTMLAnchorElement;
      
      if (link && link.href) {
        const route = new URL(link.href).pathname;
        const chunks = this.interactionMap.get(route) || [];
        
        chunks.forEach(chunk => {
          this.chunkPreloader.preloadCriticalChunks();
        });
      }
    }, { passive: true });
  }

  private setupNetworkAwarePreloading() {
    // Only aggressive preload on fast connections
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      
      if (connection && connection.effectiveType === '4g') {
        // More aggressive preloading on fast connections
        this.preloadAllChunks();
      }
    }
  }

  private getLikelyNextRoutes(currentPath: string): string[] {
    const routeTransitions: Record<string, string[]> = {
      '/': ['/database-monitoring', '/production'],
      '/database-monitoring': ['/production', '/settings'],
      '/production': ['/database-monitoring', '/marketplace'],
      '/marketplace': ['/production', '/settings'],
      '/settings': ['/database-monitoring', '/production']
    };
    
    return routeTransitions[currentPath] || [];
  }

  private async preloadAllChunks() {
    const allChunks = Array.from(this.interactionMap.values()).flat();
    const uniqueChunks = [...new Set(allChunks)];
    
    for (const chunk of uniqueChunks) {
      await this.chunkPreloader.preloadCriticalChunks();
    }
  }
}
