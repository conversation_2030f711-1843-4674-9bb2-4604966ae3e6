import { logger } from './logger';

export type EventHandler<T = any> = (data: T) => void | Promise<void>;
export type EventFilter<T = any> = (data: T) => boolean;

export interface EventSubscription {
  id: string;
  event: string;
  handler: EventHandler;
  filter?: EventFilter;
  once: boolean;
}

export class EventBus {
  private static instance: EventBus;
  private subscriptions: Map<string, EventSubscription[]> = new Map();
  private eventHistory: Map<string, any[]> = new Map();
  private maxHistorySize: number = 100;

  private constructor() {}

  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  // Subscribe to an event
  subscribe<T = any>(
    event: string,
    handler: EventHandler<T>,
    options: {
      filter?: EventFilter<T>;
      once?: boolean;
    } = {}
  ): string {
    const subscription: EventSubscription = {
      id: this.generateId(),
      event,
      handler,
      filter: options.filter,
      once: options.once || false
    };

    if (!this.subscriptions.has(event)) {
      this.subscriptions.set(event, []);
    }

    this.subscriptions.get(event)!.push(subscription);
    logger.debug('Event subscription added', { event, subscriptionId: subscription.id });

    return subscription.id;
  }

  // Subscribe to an event once
  once<T = any>(
    event: string,
    handler: EventHandler<T>,
    filter?: EventFilter<T>
  ): string {
    return this.subscribe(event, handler, { once: true, filter });
  }

  // Unsubscribe from an event
  unsubscribe(subscriptionId: string): void {
    for (const [event, subscriptions] of this.subscriptions.entries()) {
      const index = subscriptions.findIndex(sub => sub.id === subscriptionId);
      if (index !== -1) {
        subscriptions.splice(index, 1);
        logger.debug('Event subscription removed', { event, subscriptionId });
        break;
      }
    }
  }

  // Emit an event
  async emit<T = any>(event: string, data: T): Promise<void> {
    // Store in history
    this.addToHistory(event, data);

    const subscriptions = this.subscriptions.get(event) || [];
    const promises: Promise<void>[] = [];

    for (const subscription of subscriptions) {
      // Check filter if exists
      if (subscription.filter && !subscription.filter(data)) {
        continue;
      }

      // Execute handler
      try {
        const result = subscription.handler(data);
        if (result instanceof Promise) {
          promises.push(result);
        }
      } catch (error) {
        logger.error('Event handler error', { event, error });
      }

      // Remove subscription if once
      if (subscription.once) {
        this.unsubscribe(subscription.id);
      }
    }

    // Wait for all async handlers
    await Promise.all(promises);
  }

  // Get event history
  getHistory(event: string): any[] {
    return this.eventHistory.get(event) || [];
  }

  // Clear event history
  clearHistory(event?: string): void {
    if (event) {
      this.eventHistory.delete(event);
    } else {
      this.eventHistory.clear();
    }
    logger.debug('Event history cleared', { event });
  }

  // Set maximum history size
  setMaxHistorySize(size: number): void {
    this.maxHistorySize = size;
    this.trimHistory();
  }

  // Add event to history
  private addToHistory(event: string, data: any): void {
    if (!this.eventHistory.has(event)) {
      this.eventHistory.set(event, []);
    }

    const history = this.eventHistory.get(event)!;
    history.push({
      data,
      timestamp: Date.now()
    });

    this.trimHistory();
  }

  // Trim history to max size
  private trimHistory(): void {
    for (const [event, history] of this.eventHistory.entries()) {
      if (history.length > this.maxHistorySize) {
        this.eventHistory.set(
          event,
          history.slice(-this.maxHistorySize)
        );
      }
    }
  }

  // Generate unique subscription ID
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Get all subscriptions
  getSubscriptions(): Map<string, EventSubscription[]> {
    return new Map(this.subscriptions);
  }

  // Clear all subscriptions
  clear(): void {
    this.subscriptions.clear();
    this.eventHistory.clear();
    logger.info('Event bus cleared');
  }
}

// Example usage:
/*
const eventBus = EventBus.getInstance();

// Subscribe to an event
const subscriptionId = eventBus.subscribe('user:login', (user) => {
  console.log('User logged in:', user);
});

// Subscribe with filter
eventBus.subscribe('user:update', (user) => {
  console.log('User updated:', user);
}, {
  filter: (user) => user.role === 'admin'
});

// Subscribe once
eventBus.once('user:logout', (user) => {
  console.log('User logged out:', user);
});

// Emit an event
await eventBus.emit('user:login', { id: 1, name: 'John' });

// Unsubscribe
eventBus.unsubscribe(subscriptionId);

// Get event history
const history = eventBus.getHistory('user:login');
console.log(history);

// Clear history
eventBus.clearHistory('user:login');

// Set max history size
eventBus.setMaxHistorySize(50);

// Clear all
eventBus.clear();
*/ 