/**
 * Dynamic imports for heavy libraries to reduce initial bundle size
 * These libraries are loaded only when needed
 */

// Cache for loaded modules
const moduleCache = new Map<string, any>();

// Recharts - heavy charting library
export const loadRecharts = () => {
  const cacheKey = 'recharts';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return import('recharts').then(module => {
    moduleCache.set(cacheKey, module);
    return module;
  });
};

// Date-fns - date manipulation library
export const loadDateFns = () => {
  const cacheKey = 'date-fns';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return import('date-fns').then(module => {
    moduleCache.set(cacheKey, module);
    return module;
  });
};

// React Hook Form - form handling
export const loadReactHookForm = () => {
  const cacheKey = 'react-hook-form';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return import('react-hook-form').then(module => {
    moduleCache.set(cacheKey, module);
    return module;
  });
};

// Zod - validation library
export const loadZod = () => {
  const cacheKey = 'zod';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return import('zod').then(module => {
    moduleCache.set(cacheKey, module);
    return module;
  });
};

// ProseMirror core modules - heavy editor dependencies
export const loadProseMirrorCore = () => {
  const cacheKey = 'prosemirror-core';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return Promise.all([
    import('prosemirror-state'),
    import('prosemirror-view'),
    import('prosemirror-commands'),
    import('prosemirror-keymap'),
    import('prosemirror-history')
  ]).then(([state, view, commands, keymap, history]) => {
    const module = { state, view, commands, keymap, history };
    moduleCache.set(cacheKey, module);
    return module;
  });
};

// Lucide React icons - split by category
export const loadLucideIcons = () => {
  const cacheKey = 'lucide-react';
  if (moduleCache.has(cacheKey)) {
    return Promise.resolve(moduleCache.get(cacheKey));
  }
  return import('lucide-react').then(module => {
    moduleCache.set(cacheKey, module);
    return module;
  });
};

/**
 * Utility to preload heavy libraries on user interaction
 */
export const preloadLibraries = {
  charts: () => loadRecharts(),
  editor: () => loadProseMirrorCore(),
  forms: () => Promise.all([loadReactHookForm(), loadZod()]),
  dates: () => loadDateFns()
};

/**
 * Preload libraries on hover/focus for better UX
 */
export const preloadOnHover = (libraryKey: keyof typeof preloadLibraries) => {
  let timeoutId: number | null = null;
  
  return {
    onMouseEnter: () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => preloadLibraries[libraryKey](), 100);
    },
    onFocus: () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => preloadLibraries[libraryKey](), 100);
    }
  };
};
