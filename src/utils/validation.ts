// Re-export everything from the main validation library for backward compatibility
export {
  type ValidationRule,
  type ValidationResult,
  type FieldValidation,
  type FormValidation,
  ValidationError,
  Validator,
  AsyncValidator,
  createValidationRule,
  required,
  minLength,
  maxLength,
  pattern,
  email,
  number,
  min,
  max,
  createFormValidator
} from '../lib/validation';

// Helper function to create validation rules
// export const createValidationRule = <T = any>(
//   validate: (value: T) => string | boolean | Promise<string | boolean>
// ): ValidationRule<T> => ({
//   validate
// });

// // Common validation rules
// export const required = createValidationRule((value: any) => {
//   if (value === undefined || value === null || value === '') {
//     return 'This field is required';
//   }
//   return true;
// });

// export const minLength = (min: number) => createValidationRule((value: string) => {
//   if (value.length < min) {
//     return `Minimum length is ${min} characters`;
//   }
//   return true;
// });

// export const maxLength = (max: number) => createValidationRule((value: string) => {
//   if (value.length > max) {
//     return `Maximum length is ${max} characters`;
//   }
//   return true;
// });

// export const pattern = (regex: RegExp, message: string) => createValidationRule((value: string) => {
//   if (!regex.test(value)) {
//     return message;
//   }
//   return true;
// });

// export const email = createValidationRule((value: string) => {
//   const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
//   if (!emailRegex.test(value)) {
//     return 'Invalid email format';
//   }
//   return true;
// });

// export const number = createValidationRule((value: any) => {
//   if (isNaN(Number(value))) {
//     return 'Must be a number';
//   }
//   return true;
// });

// export const min = (min: number) => createValidationRule((value: number) => {
//   if (value < min) {
//     return `Minimum value is ${min}`;
//   }
//   return true;
// });

// export const max = (max: number) => createValidationRule((value: number) => {
//   if (value > max) {
//     return `Maximum value is ${max}`;
//   }
//   return true;
// });

// export interface FieldValidation<T = any> {
//   required?: boolean;
//   rules?: ValidationRule<T>[];
//   message?: string;
// }

// export interface FormValidation {
//   [fieldName: string]: FieldValidation;
// }

// export class ValidationError extends Error {
//   constructor(
//     message: string,
//     public field?: string,
//     public code?: string
//   ) {
//     super(message);
//     this.name = 'ValidationError';
//   }
// }

// // Form validation helper
// export function createFormValidator(schema: FormValidation): Validator {
//   const validator = new Validator();

//   Object.entries(schema).forEach(([field, config]) => {
//     if (config.required) {
//       validator.addRule(field, required);
//     }

//     if (config.rules) {
//       config.rules.forEach(rule => {
//         validator.addRule(field, rule);
//       });
//     }
//   });

//   return validator;
// }

// // Async validation support
// export class AsyncValidator extends Validator {
//   private asyncRules: Map<string, ((value: any) => Promise<string | null>)[]> = new Map();

//   addAsyncRule(
//     field: string, 
//     rule: (value: any) => Promise<string | null>, 
//     message?: string
//   ): this {
//     if (!this.asyncRules.has(field)) {
//       this.asyncRules.set(field, []);
//     }
    
//     this.asyncRules.get(field)!.push(rule);
    
//     if (message) {
//       this.messages.push(message);
//     }
    
//     return this;
//   }

//   async validateFieldAsync(field: string, value: any): Promise<ValidationResult> {
//     // First run synchronous validation
//     const syncResult = await this.validate(value, [field]);
//     if (!syncResult.isValid) {
//       return syncResult;
//     }

//     // Then run async validation
//     const asyncRules = this.asyncRules.get(field) || [];
//     const errors: string[] = [];

//     for (let i = 0; i < asyncRules.length; i++) {
//       const rule = asyncRules[i];
//       try {
//         const error = await rule(value);
//         if (error) {
//           errors.push(error);
//         }
//       } catch (err) {
//         errors.push(`Validation error: ${(err as Error).message}`);
//       }
//     }

//     return {
//       isValid: errors.length === 0,
//       errors
//     };
//   }

//   async validateAsync(data: Record<string, any>): Promise<ValidationResult> {
//     const errors: string[] = [];

//     // Run synchronous validation first
//     const syncResult = await this.validate(data, Object.keys(data));
//     if (!syncResult.isValid) {
//       errors.push(...syncResult.errors);
//     }

//     // Run async validation for fields that have async rules
//     const asyncPromises = Object.entries(data)
//       .filter(([field]) => this.asyncRules.has(field))
//       .map(async ([field, value]) => {
//         const result = await this.validateFieldAsync(field, value);
//         if (!result.isValid) {
//           return result.errors.map(err => `${field}: ${err}`);
//         }
//         return [];
//       });

//     const asyncResults = await Promise.all(asyncPromises);
//     const asyncErrors = asyncResults.flat();

//     return {
//       isValid: errors.length === 0 && asyncErrors.length === 0,
//       errors: [...errors, ...asyncErrors]
//     };
//   }
// }
