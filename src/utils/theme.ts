
interface ThemeChangeListener {
  (theme: string): void;
}

export class ThemeManager {
  private currentTheme: string = 'light';
  private listeners: Set<ThemeChangeListener> = new Set();

  getCurrentTheme(): string {
    return this.currentTheme;
  }

  setTheme(theme: string): void {
    if (theme === 'light' || theme === 'dark') {
      this.currentTheme = theme;
      localStorage.setItem('theme', theme);
      this.notifyListeners();
    }
  }

  subscribe(listener: ThemeChangeListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentTheme));
  }

  reset(): void {
    this.currentTheme = 'light';
    this.listeners.clear();
    localStorage.removeItem('theme');
  }
}

export const themeManager = new ThemeManager();
