import { logger } from './logger';

export type NotificationType = 'info' | 'success' | 'warning' | 'error';

export interface NotificationOptions {
  type?: NotificationType;
  title?: string;
  message: string;
  duration?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showProgress?: boolean;
  closable?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface Notification extends NotificationOptions {
  id: string;
  timestamp: number;
  progress: number;
}

export class NotificationManager {
  private static instance: NotificationManager;
  private notifications: Map<string, Notification> = new Map();
  private listeners: Set<(notifications: Notification[]) => void> = new Set();
  private progressIntervals: Map<string, number> = new Map();
  private container: HTMLDivElement | null = null;

  private constructor() {
    this.createContainer();
  }

  static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  // Create notification container
  private createContainer(): void {
    this.container = document.createElement('div');
    this.container.className = 'notification-container';
    this.container.style.cssText = `
      position: fixed;
      z-index: 9999;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      padding: 1rem;
      pointer-events: none;
    `;
    document.body.appendChild(this.container);
  }

  // Show notification
  show(options: NotificationOptions): string {
    const id = this.generateId();
    const notification: Notification = {
      id,
      type: options.type || 'info',
      title: options.title,
      message: options.message,
      duration: options.duration || 5000,
      position: options.position || 'top-right',
      showProgress: options.showProgress ?? true,
      closable: options.closable ?? true,
      action: options.action,
      timestamp: Date.now(),
      progress: 100
    };

    this.notifications.set(id, notification);
    this.renderNotification(notification);
    this.startProgress(id, notification.duration);
    this.notifyListeners();

    return id;
  }

  // Render notification
  private renderNotification(notification: Notification): void {
    if (!this.container) return;

    const element = document.createElement('div');
    element.className = `notification notification-${notification.type}`;
    element.style.cssText = `
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      padding: 1rem;
      min-width: 300px;
      max-width: 400px;
      pointer-events: auto;
      position: relative;
      overflow: hidden;
      animation: slideIn 0.3s ease-out;
    `;

    // Add title
    if (notification.title) {
      const title = document.createElement('div');
      title.className = 'notification-title';
      title.style.cssText = `
        font-weight: 600;
        margin-bottom: 0.5rem;
      `;
      title.textContent = notification.title;
      element.appendChild(title);
    }

    // Add message
    const message = document.createElement('div');
    message.className = 'notification-message';
    message.style.cssText = `
      color: #666;
      font-size: 0.875rem;
    `;
    message.textContent = notification.message;
    element.appendChild(message);

    // Add progress bar
    if (notification.showProgress) {
      const progress = document.createElement('div');
      progress.className = 'notification-progress';
      progress.style.cssText = `
        position: absolute;
        bottom: 0;
        left: 0;
        height: 3px;
        background: #007AFF;
        width: ${notification.progress}%;
        transition: width 0.1s linear;
      `;
      element.appendChild(progress);
    }

    // Add close button
    if (notification.closable) {
      const close = document.createElement('button');
      close.className = 'notification-close';
      close.style.cssText = `
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background: none;
        border: none;
        padding: 0.25rem;
        cursor: pointer;
        opacity: 0.5;
        transition: opacity 0.2s;
      `;
      close.innerHTML = '×';
      close.onclick = () => this.close(notification.id);
      element.appendChild(close);
    }

    // Add action button
    if (notification.action) {
      const action = document.createElement('button');
      action.className = 'notification-action';
      action.style.cssText = `
        margin-top: 0.5rem;
        padding: 0.5rem 1rem;
        background: #007AFF;
        color: white;
        border: none;
        border-radius: 0.25rem;
        cursor: pointer;
        font-size: 0.875rem;
        transition: background 0.2s;
      `;
      action.textContent = notification.action.label;
      action.onclick = () => {
        notification.action?.onClick();
        this.close(notification.id);
      };
      element.appendChild(action);
    }

    // Add to container
    this.container.appendChild(element);

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideIn {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      @keyframes slideOut {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(style);
  }

  // Start progress
  private startProgress(id: string, duration: number): void {
    const startTime = Date.now();
    const interval = window.setInterval(() => {
      const notification = this.notifications.get(id);
      if (!notification) {
        clearInterval(interval);
        return;
      }

      const elapsed = Date.now() - startTime;
      const progress = Math.max(0, 100 - (elapsed / duration) * 100);

      notification.progress = progress;
      this.updateProgress(id, progress);

      if (progress <= 0) {
        this.close(id);
      }
    }, 10);

    this.progressIntervals.set(id, interval);
  }

  // Update progress
  private updateProgress(id: string, progress: number): void {
    const element = document.querySelector(`[data-notification-id="${id}"] .notification-progress`);
    if (element instanceof HTMLElement) {
      element.style.width = `${progress}%`;
    }
  }

  // Close notification
  close(id: string): void {
    const notification = this.notifications.get(id);
    if (!notification) return;

    const element = document.querySelector(`[data-notification-id="${id}"]`);
    if (element instanceof HTMLElement) {
      element.style.animation = 'slideOut 0.3s ease-out forwards';
      setTimeout(() => element.remove(), 300);
    }

    clearInterval(this.progressIntervals.get(id));
    this.progressIntervals.delete(id);
    this.notifications.delete(id);
    this.notifyListeners();
  }

  // Close all notifications
  closeAll(): void {
    this.notifications.forEach((_, id) => this.close(id));
  }

  // Add notification change listener
  onNotificationChange(listener: (notifications: Notification[]) => void): void {
    this.listeners.add(listener);
  }

  // Remove notification change listener
  offNotificationChange(listener: (notifications: Notification[]) => void): void {
    this.listeners.delete(listener);
  }

  // Notify listeners
  private notifyListeners(): void {
    const notifications = Array.from(this.notifications.values());
    this.listeners.forEach(listener => listener(notifications));
  }

  // Generate unique ID
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }

  // Get all notifications
  getAllNotifications(): Notification[] {
    return Array.from(this.notifications.values());
  }

  // Get notification by ID
  getNotification(id: string): Notification | undefined {
    return this.notifications.get(id);
  }
}

// Example usage:
/*
const notificationManager = NotificationManager.getInstance();

// Show info notification
notificationManager.show({
  type: 'info',
  title: 'Information',
  message: 'This is an info notification',
  duration: 5000
});

// Show success notification with action
notificationManager.show({
  type: 'success',
  title: 'Success',
  message: 'Operation completed successfully',
  action: {
    label: 'View Details',
    onClick: () => console.log('Action clicked')
  }
});

// Show warning notification
notificationManager.show({
  type: 'warning',
  title: 'Warning',
  message: 'Please be careful',
  showProgress: false
});

// Show error notification
notificationManager.show({
  type: 'error',
  title: 'Error',
  message: 'Something went wrong',
  closable: false
});

// Add notification change listener
notificationManager.onNotificationChange((notifications) => {
  console.log('Current notifications:', notifications);
});

// Close all notifications
notificationManager.closeAll();
*/ 