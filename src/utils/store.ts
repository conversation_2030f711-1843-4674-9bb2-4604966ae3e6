
import { logger } from './logger';
import { Action } from './types';

export type Reducer<S, A extends Action<any>> = (state: S, action: A) => S;
export type Middleware<S, A extends Action<any>> = (store: Store<S, A>) => (next: (action: A) => void) => (action: A) => void;
export type Listener<S> = (state: S) => void;

export interface StoreOptions<S = any> {
  initialState: S;
  middleware?: Middleware<S, any>[];
  persist?: {
    key: string;
    storage?: Storage;
    whitelist?: (keyof S)[];
    blacklist?: (keyof S)[];
  };
  devTools?: boolean;
}

export class Store<S, A extends Action<any>> {
  private state: S;
  private reducer: Reducer<S, A>;
  private listeners: Set<Listener<S>> = new Set();
  private middleware: Middleware<S, A>[] = [];
  private history: { state: S; action: A }[] = [];
  private historyIndex: number = -1;
  private options: StoreOptions<S>;

  constructor(
    reducer: Reducer<S, A>,
    options: StoreOptions<S>
  ) {
    this.reducer = reducer;
    this.options = options;
    this.state = this.loadPersistedState() || options.initialState;
    this.history.push({ state: this.state, action: {} as A });
    this.historyIndex = 0;

    if (options.devTools) {
      this.initializeDevTools();
    }
  }

  static getInstance<S, A extends Action<any>>(
    reducer: Reducer<S, A>,
    options: StoreOptions<S>
  ): Store<S, A> {
    return new Store(reducer, options);
  }

  // Initialize Redux DevTools
  private initializeDevTools(): void {
    if (typeof window !== 'undefined' && (window as any).__REDUX_DEVTOOLS_EXTENSION__) {
      const devTools = (window as any).__REDUX_DEVTOOLS_EXTENSION__.connect({
        name: 'Store'
      });

      devTools.subscribe((message: any) => {
        if (message.type === 'DISPATCH') {
          switch (message.payload.type) {
            case 'JUMP_TO_STATE':
            case 'JUMP_TO_ACTION':
              this.timeTravel(parseInt(message.payload.actionId));
              break;
            case 'TOGGLE_ACTION':
              this.toggleAction(parseInt(message.payload.actionId));
              break;
          }
        }
      });

      this.middleware.push((store) => (next) => (action) => {
        const result = next(action);
        devTools.send(action, store.getState());
        return result;
      });
    }
  }

  // Load persisted state
  private loadPersistedState(): S | null {
    if (!this.options.persist) return null;

    try {
      const storage = this.options.persist.storage || localStorage;
      const persistedState = storage.getItem(this.options.persist.key);
      if (!persistedState) return null;

      const state = JSON.parse(persistedState);
      return this.filterPersistedState(state);
    } catch (error) {
      logger.error('Failed to load persisted state', error);
      return null;
    }
  }

  // Filter persisted state
  private filterPersistedState(state: S): S {
    if (!this.options.persist) return state;

    const { whitelist, blacklist } = this.options.persist;
    const filteredState = { ...state };

    if (whitelist) {
      Object.keys(filteredState).forEach(key => {
        if (!whitelist.includes(key as keyof S)) {
          delete filteredState[key as keyof S];
        }
      });
    }

    if (blacklist) {
      blacklist.forEach(key => {
        delete filteredState[key];
      });
    }

    return filteredState;
  }

  // Persist state
  private persistState(): void {
    if (!this.options.persist) return;

    try {
      const storage = this.options.persist.storage || localStorage;
      const stateToPersist = this.filterPersistedState(this.state);
      storage.setItem(
        this.options.persist.key,
        JSON.stringify(stateToPersist)
      );
    } catch (error) {
      logger.error('Failed to persist state', error);
    }
  }

  // Dispatch action
  dispatch(action: A): void {
    const next = (action: A) => {
      const newState = this.reducer(this.state, action);
      this.history = this.history.slice(0, this.historyIndex + 1);
      this.history.push({ state: newState, action });
      this.historyIndex++;
      this.state = newState;
      this.persistState();
      this.notify();
    };

    // Create the middleware chain
    const chain = this.middleware.reduceRight<((action: A) => void)>(
      (next, middleware) => middleware(this)(next),
      next
    );

    chain(action);
  }

  // Get current state
  getState(): S {
    return this.state;
  }

  // Subscribe to state changes
  subscribe(listener: Listener<S>): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Notify listeners of state change
  private notify(): void {
    this.listeners.forEach(listener => listener(this.state));
  }

  // Time travel to specific state
  timeTravel(index: number): void {
    if (index < 0 || index >= this.history.length) {
      logger.warn('Invalid time travel index', { index });
      return;
    }

    this.state = this.history[index].state;
    this.historyIndex = index;
    this.notify();
    logger.info('Time traveled to state', { index });
  }

  // Toggle specific action
  toggleAction(index: number): void {
    if (index < 0 || index >= this.history.length) {
      logger.warn('Invalid action index', { index });
      return;
    }

    this.state = this.history[index].state;
    this.historyIndex = index;
    this.notify();
    logger.info('Action toggled', { index });
  }

  // Apply middleware
  applyMiddleware(...middleware: Middleware<S, A>[]): void {
    this.middleware = middleware;
  }

  // Undo action
  undo(): void {
    if (this.historyIndex > 0) {
      this.historyIndex--;
      this.state = this.history[this.historyIndex].state;
      this.notify();
    }
  }

  // Redo action
  redo(): void {
    if (this.historyIndex < this.history.length - 1) {
      this.historyIndex++;
      this.state = this.history[this.historyIndex].state;
      this.notify();
    }
  }

  // Replace reducer
  replaceReducer(reducer: Reducer<S, A>): void {
    this.reducer = reducer;
    logger.info('Reducer replaced');
  }

  getHistory(): { state: S; action: A }[] {
    return this.history;
  }

  clearHistory(): void {
    this.history = [];
    this.historyIndex = -1;
  }
}
