import { logger } from './logger';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

export interface PerformanceOptions {
  sampleRate?: number;
  maxMetrics?: number;
  reportInterval?: number;
  reportUrl?: string;
  autoReport?: boolean;
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private options: PerformanceOptions;
  private metrics: Map<string, number> = new Map();
  private observers: Set<PerformanceObserver> = new Set();
  private reportInterval: number | null = null;

  private constructor(options: PerformanceOptions = {}) {
    this.options = {
      sampleRate: 1.0,
      maxMetrics: 1000,
      reportInterval: 60000, // 1 minute
      autoReport: true,
      ...options
    };

    if (this.options.autoReport) {
      this.startReporting();
    }

    this.initializeObservers();
  }

  static getInstance(options?: PerformanceOptions): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor(options);
    }
    return PerformanceMonitor.instance;
  }

  private initializeObservers(): void {
    // Observe resource timing
    if ('PerformanceObserver' in window) {
      this.observeResourceTiming();
      this.observeLongTasks();
    }
  }

  trackMetric(name: string, value: number): void {
    this.metrics.set(name, value);
  }

  getMetric(name: string): number | undefined {
    return this.metrics.get(name);
  }

  getMetrics(): Map<string, number> {
    return new Map(this.metrics);
  }

  clearMetrics(): void {
    this.metrics.clear();
  }

  getReport(): Record<string, number> {
    return Object.fromEntries(this.metrics);
  }

  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.metrics.clear();
  }

  // Start monitoring
  startMonitoring(): void {
    this.observeNavigationTiming();
    this.observeResourceTiming();
    this.observeLongTasks();
    this.observeLayoutShifts();
    this.observeFirstInput();
    this.observeLargestContentfulPaint();
  }

  // Stop monitoring
  stopMonitoring(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    if (this.reportInterval) {
      clearInterval(this.reportInterval);
      this.reportInterval = null;
    }
  }

  // Observe navigation timing
  private observeNavigationTiming(): void {
    if (!('PerformanceNavigationTiming' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        this.addMetric({
          name: 'navigation',
          value: entry.duration,
          timestamp: entry.startTime,
          tags: {
            type: entry.entryType,
            name: entry.name
          }
        });
      });
    });

    observer.observe({ entryTypes: ['navigation'] });
    this.observers.add(observer);
  }

  // Observe resource timing
  private observeResourceTiming(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        const resourceEntry = entry as any;
        if (resourceEntry.initiatorType) {
          this.trackMetric(`${resourceEntry.initiatorType}_load_time`, resourceEntry.duration);
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
    this.observers.add(observer);
  }

  // Observe long tasks
  private observeLongTasks(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        this.trackMetric('long_task_duration', entry.duration);
      });
    });

    observer.observe({ entryTypes: ['longtask'] });
    this.observers.add(observer);
  }

  // Observe layout shifts
  private observeLayoutShifts(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        const layoutShiftEntry = entry as any;
        this.addMetric({
          name: 'layout-shift',
          value: layoutShiftEntry.value || 0,
          timestamp: entry.startTime,
          tags: {
            name: entry.name
          }
        });
      });
    });

    observer.observe({ entryTypes: ['layout-shift'] });
    this.observers.add(observer);
  }

  // Observe first input
  private observeFirstInput(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        this.addMetric({
          name: 'first-input',
          value: entry.duration,
          timestamp: entry.startTime,
          tags: {
            name: entry.name
          }
        });
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
    this.observers.add(observer);
  }

  // Observe largest contentful paint
  private observeLargestContentfulPaint(): void {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach(entry => {
        this.addMetric({
          name: 'largest-contentful-paint',
          value: entry.startTime,
          timestamp: entry.startTime,
          tags: {
            name: entry.name
          }
        });
      });
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.add(observer);
  }

  // Add custom metric
  addMetric(metric: PerformanceMetric): void {
    if (Math.random() > (this.options.sampleRate || 1.0)) return;

    this.metrics.set(metric.name, metric.value);
    if (this.metrics.size > (this.options.maxMetrics || 1000)) {
      const oldestMetric = this.metrics.entries().next().value;
      this.metrics.delete(oldestMetric[0]);
    }
  }

  // Start reporting
  private startReporting(): void {
    if (this.reportInterval) return;

    this.reportInterval = window.setInterval(
      () => this.reportMetrics(),
      this.options.reportInterval || 60000
    );
  }

  // Report metrics
  async reportMetrics(): Promise<void> {
    if (!this.options.reportUrl || this.metrics.size === 0) return;

    try {
      const response = await fetch(this.options.reportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          metrics: this.getReport(),
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      });

      if (response.ok) {
        this.clearMetrics();
      } else {
        logger.error('Failed to report metrics', { error: await response.text() });
      }
    } catch (error) {
      logger.error('Error reporting metrics', error);
    }
  }

  // Get metrics
  getMetricsByName(name: string): PerformanceMetric[] {
    const metrics: PerformanceMetric[] = [];
    this.metrics.forEach((value, key) => {
      if (key === name) {
        metrics.push({
          name: key,
          value,
          timestamp: Date.now(),
          tags: {}
        });
      }
    });
    return metrics;
  }

  // Get metrics by time range
  getMetricsByTimeRange(start: number, end: number): PerformanceMetric[] {
    const metrics: PerformanceMetric[] = [];
    this.metrics.forEach((value, key) => {
      if (key === 'navigation' || key === 'layout-shift') {
        const timestamp = key === 'navigation' ? start : end;
        metrics.push({
          name: key,
          value,
          timestamp,
          tags: {}
        });
      }
    });
    return metrics;
  }

  // Get metrics by tags
  getMetricsByTags(tags: Record<string, string>): PerformanceMetric[] {
    const metrics: PerformanceMetric[] = [];
    this.metrics.forEach((value, key) => {
      if (key === 'navigation' || key === 'layout-shift') {
        const metric: PerformanceMetric = {
          name: key,
          value,
          timestamp: Date.now(),
          tags: {}
        };
        if (tags.type && key === 'navigation') {
          metric.tags = { type: tags.type };
        }
        if (tags.name && key === 'layout-shift') {
          metric.tags = { name: tags.name };
        }
        metrics.push(metric);
      }
    });
    return metrics;
  }

  // Calculate average
  calculateAverage(metrics: PerformanceMetric[]): number {
    if (metrics.length === 0) return 0;
    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / metrics.length;
  }

  // Calculate percentile
  calculatePercentile(metrics: PerformanceMetric[], percentile: number): number {
    if (metrics.length === 0) return 0;
    const sorted = [...metrics].sort((a, b) => a.value - b.value);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index].value;
  }

  // Generate report
  generateReport(): {
    summary: Record<string, {
      count: number;
      average: number;
      p95: number;
      p99: number;
    }>;
    metrics: PerformanceMetric[];
  } {
    const summary: Record<string, {
      count: number;
      average: number;
      p95: number;
      p99: number;
    }> = {};

    const metrics: PerformanceMetric[] = [];
    this.metrics.forEach((value, key) => {
      if (key === 'navigation' || key === 'layout-shift') {
        const metric: PerformanceMetric = {
          name: key,
          value,
          timestamp: Date.now(),
          tags: {}
        };
        if (key === 'navigation') {
          metric.tags = { type: 'navigation' };
        } else if (key === 'layout-shift') {
          metric.tags = { name: 'layout-shift' };
        }
        metrics.push(metric);
      }
    });

    // Group metrics by name
    const groupedMetrics = metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric);
      return acc;
    }, {} as Record<string, PerformanceMetric[]>);

    // Calculate statistics for each group
    Object.entries(groupedMetrics).forEach(([name, metrics]) => {
      summary[name] = {
        count: metrics.length,
        average: this.calculateAverage(metrics),
        p95: this.calculatePercentile(metrics, 95),
        p99: this.calculatePercentile(metrics, 99)
      };
    });

    return {
      summary,
      metrics
    };
  }
}

// Example usage:
/*
const performance = PerformanceMonitor.getInstance({
  sampleRate: 1.0,
  maxMetrics: 1000,
  reportInterval: 60000,
  reportUrl: '/api/metrics',
  autoReport: true
});

// Start monitoring
performance.startMonitoring();

// Add custom metric
performance.addMetric({
  name: 'custom',
  value: 100,
  timestamp: Date.now(),
  tags: {
    type: 'test'
  }
});

// Get metrics
const metrics = performance.getMetrics();
console.log('All metrics:', metrics);

// Get metrics by name
const navigationMetrics = performance.getMetricsByName('navigation');
console.log('Navigation metrics:', navigationMetrics);

// Get metrics by time range
const recentMetrics = performance.getMetricsByTimeRange(
  Date.now() - 3600000,
  Date.now()
);
console.log('Recent metrics:', recentMetrics);

// Get metrics by tags
const testMetrics = performance.getMetricsByTags({ type: 'test' });
console.log('Test metrics:', testMetrics);

// Calculate statistics
const average = performance.calculateAverage(navigationMetrics);
console.log('Average navigation time:', average);

const p95 = performance.calculatePercentile(navigationMetrics, 95);
console.log('95th percentile:', p95);

// Generate report
const report = performance.generateReport();
console.log('Performance report:', report);

// Stop monitoring
performance.stopMonitoring();
*/
