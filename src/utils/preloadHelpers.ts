/**
 * Preload utilities for better user experience
 * These help reduce perceived loading times by preloading resources
 */

import { preloadLibraries } from './dynamicImports';

// Track preloaded resources to avoid duplicate loading
const preloadedResources = new Set<string>();

/**
 * Preload critical routes based on user behavior
 */
export const preloadRoutes = {
  // Preload marketplace when user hovers over marketplace button
  marketplace: () => {
    if (preloadedResources.has('marketplace')) return;
    preloadedResources.add('marketplace');
    return import('@/features/marketplace/ScreenplayMarketplace');
  },
  
  // Preload production tools when user hovers over production button
  production: () => {
    if (preloadedResources.has('production')) return;
    preloadedResources.add('production');
    return import('@/features/production/ProductionTools');
  },
  
  // Preload editor when user shows intent to write
  editor: () => {
    if (preloadedResources.has('editor')) return;
    preloadedResources.add('editor');
    return import('@/components/lazy/LazyProseMirrorEditor');
  },
  
  // Preload admin pages for admin users
  admin: () => {
    if (preloadedResources.has('admin')) return;
    preloadedResources.add('admin');
    return Promise.all([
      import('@/pages/AdminDashboard'),
      import('@/pages/PromptManagement')
    ]);
  }
};

/**
 * Smart preloading based on user role and subscription
 */
export const smartPreload = {
  // Preload based on subscription tier
  subscriber: () => {
    // Use Promise.all for parallel loading
    return Promise.all([
      preloadRoutes.marketplace(),
      preloadRoutes.production(),
      preloadLibraries.charts()
    ]);
  },
  
  // Preload for free users showing upgrade intent
  freeUser: () => {
    return preloadRoutes.marketplace();
  },
  
  // Preload for admin users
  admin: () => {
    return Promise.all([
      preloadRoutes.admin(),
      preloadLibraries.forms()
    ]);
  }
};

/**
 * Progressive enhancement - preload on interaction
 */
export const createPreloadTriggers = (target: keyof typeof preloadRoutes) => {
  let timeoutId: number | null = null;
  
  return {
    onMouseEnter: () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => preloadRoutes[target](), 100);
    },
    onFocus: () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => preloadRoutes[target](), 100);
    },
    onTouchStart: () => {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => preloadRoutes[target](), 100);
    }
  };
};

/**
 * Preload on idle for better perceived performance
 */
export const preloadOnIdle = () => {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      // Preload only essential features
      preloadLibraries.editor();
    }, { timeout: 3000 });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      preloadLibraries.editor();
    }, 3000);
  }
};

/**
 * Initialize smart preloading on app start
 */
export const initializePreloading = () => {
  // Check network conditions
  const isFastConnection = 'connection' in navigator && 
    (navigator as any).connection?.effectiveType === '4g' && 
    !(navigator as any).connection?.saveData;

  // Preload on idle with increased timeout
  preloadOnIdle();
  
  // Only preload additional resources on fast connections
  if (isFastConnection) {
    // Use requestIdleCallback for non-critical preloading
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        smartPreload.subscriber();
      }, { timeout: 5000 });
    } else {
      setTimeout(() => {
        smartPreload.subscriber();
      }, 5000);
    }
  }
};
