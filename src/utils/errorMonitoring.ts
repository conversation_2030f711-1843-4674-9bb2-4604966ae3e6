
export interface ErrorContext {
  userId?: string;
  url?: string;
  userAgent?: string;
  timestamp: string;
  sessionId?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorReport {
  message: string;
  stack?: string;
  level: 'error' | 'warning' | 'info';
  context: ErrorContext;
}

export class ErrorMonitor {
  private static instance: ErrorMonitor;
  private errorQueue: ErrorReport[] = [];
  private isOnline = true;
  private maxQueueSize = 100;

  static getInstance(): ErrorMonitor {
    if (!ErrorMonitor.instance) {
      ErrorMonitor.instance = new ErrorMonitor();
    }
    return ErrorMonitor.instance;
  }

  constructor() {
    this.setupErrorHandlers();
    this.setupNetworkHandlers();
  }

  private setupErrorHandlers() {
    // Global error handler
    window.addEventListener('error', (event) => {
      this.captureError(event.error || new Error(event.message), {
        level: 'error',
        additionalData: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError(
        event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
        {
          level: 'error',
          additionalData: {
            type: 'unhandledrejection'
          }
        }
      );
    });

    // React error boundary integration
    window.addEventListener('react-error', ((event: CustomEvent) => {
      this.captureError(event.detail.error, {
        level: 'error',
        additionalData: {
          componentStack: event.detail.componentStack,
          type: 'react-error'
        }
      });
    }) as EventListener);
  }

  private setupNetworkHandlers() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.flushErrorQueue();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  captureError(error: Error, options: {
    level?: 'error' | 'warning' | 'info';
    additionalData?: Record<string, any>;
    userId?: string;
  } = {}) {
    const context: ErrorContext = {
      userId: options.userId,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      additionalData: options.additionalData
    };

    const errorReport: ErrorReport = {
      message: error.message,
      stack: error.stack,
      level: options.level || 'error',
      context
    };

    this.queueError(errorReport);

    // Also log to console in development
    if (import.meta.env.NODE_ENV === 'development') {
      console.error('Error captured:', errorReport);
    }
  }

  captureMessage(message: string, level: 'error' | 'warning' | 'info' = 'info', additionalData?: Record<string, any>) {
    this.captureError(new Error(message), { level, additionalData });
  }

  private queueError(errorReport: ErrorReport) {
    // Add to queue
    this.errorQueue.push(errorReport);

    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }

    // Try to send immediately if online
    if (this.isOnline) {
      this.flushErrorQueue();
    }
  }

  private async flushErrorQueue() {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // In production, you would send to your error monitoring service
      // For now, we'll send to a custom endpoint or log service
      await this.sendErrors(errors);
    } catch (error) {
      console.error('Failed to send errors:', error);
      // Re-queue errors if sending fails
      this.errorQueue.unshift(...errors);
    }
  }

  private async sendErrors(errors: ErrorReport[]) {
    // In a real application, replace this with your error monitoring service
    // Examples: Sentry, LogRocket, Bugsnag, etc.
    
    const endpoint = import.meta.env.VITE_ERROR_ENDPOINT;
    
    if (!endpoint) {
      // Development fallback - just log to console
      console.group('Error Reports');
      errors.forEach(error => console.error(error));
      console.groupEnd();
      return;
    }

    try {
      await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          errors,
          metadata: {
            environment: import.meta.env.NODE_ENV,
            version: import.meta.env.VITE_APP_VERSION,
            timestamp: new Date().toISOString()
          }
        })
      });
    } catch (fetchError) {
      console.error('Failed to send error reports:', fetchError);
      throw fetchError;
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('error-session-id');
    if (!sessionId) {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('error-session-id', sessionId);
    }
    return sessionId;
  }

  // Performance monitoring
  measurePerformance(name: string, fn: () => void | Promise<void>) {
    const start = performance.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - start;
          this.reportPerformance(name, duration);
        });
      } else {
        const duration = performance.now() - start;
        this.reportPerformance(name, duration);
        return result;
      }
    } catch (error) {
      const duration = performance.now() - start;
      this.captureError(error as Error, {
        additionalData: {
          performanceOperation: name,
          duration
        }
      });
      throw error;
    }
  }

  private reportPerformance(operation: string, duration: number) {
    // Log slow operations
    if (duration > 1000) { // More than 1 second
      this.captureMessage(`Slow operation: ${operation} took ${duration.toFixed(2)}ms`, 'warning', {
        operation,
        duration,
        type: 'performance'
      });
    }
  }
}
