/**
 * Utility for setting up the initial Super_Admin user
 * 
 * This should only be used once during initial setup when no Super_Admin exists yet.
 * After the first Super_Admin is created, use the admin interface for role management.
 */

import { supabase } from '@/integrations/supabase/client';

export interface SetupResult {
  success: boolean;
  message: string;
  error?: string;
}

/**
 * Set up the initial Super_Admin user
 * This function can only be used when no Super_Admin exists yet
 */
export async function setupInitialSuperAdmin(adminEmail: string): Promise<SetupResult> {
  try {
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(adminEmail)) {
      return {
        success: false,
        message: 'Invalid email format',
        error: 'Please provide a valid email address',
      };
    }

    // Call the database function to set up initial super admin
    const { data, error } = await supabase.rpc('setup_initial_super_admin', {
      admin_email: adminEmail,
    });

    if (error) {
      console.error('Setup failed:', error);
      
      // Handle specific error cases
      if (error.message.includes('Super_Admin already exists')) {
        return {
          success: false,
          message: 'Super_Admin already exists',
          error: 'A Super_Admin user has already been set up. Use the admin interface to manage roles.',
        };
      }
      
      if (error.message.includes('not found')) {
        return {
          success: false,
          message: 'User not found',
          error: 'No user found with that email address. Please ensure the user has signed up first.',
        };
      }
      
      return {
        success: false,
        message: 'Setup failed',
        error: error.message,
      };
    }

    return {
      success: true,
      message: `Successfully promoted ${adminEmail} to Super_Admin`,
    };
  } catch (error) {
    console.error('Unexpected error during setup:', error);
    return {
      success: false,
      message: 'Unexpected error',
      error: error instanceof Error ? error.message : 'An unknown error occurred',
    };
  }
}

/**
 * Check if any Super_Admin users exist
 */
export async function checkSuperAdminExists(): Promise<{
  exists: boolean;
  count: number;
  error?: string;
}> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('id', { count: 'exact' })
      .eq('role', 'super_admin');

    if (error) {
      console.error('Failed to check Super_Admin existence:', error);
      return {
        exists: false,
        count: 0,
        error: error.message,
      };
    }

    const count = data?.length || 0;
    return {
      exists: count > 0,
      count,
    };
  } catch (error) {
    console.error('Unexpected error checking Super_Admin:', error);
    return {
      exists: false,
      count: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get current user's role
 */
export async function getCurrentUserRole(): Promise<{
  role: string | null;
  isSuperAdmin: boolean;
  error?: string;
}> {
  try {
    const { data: user } = await supabase.auth.getUser();
    
    if (!user.user) {
      return {
        role: null,
        isSuperAdmin: false,
        error: 'Not authenticated',
      };
    }

    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.user.id)
      .single();

    if (error) {
      console.error('Failed to get user role:', error);
      return {
        role: null,
        isSuperAdmin: false,
        error: error.message,
      };
    }

    const role = profile?.role || 'writer';
    return {
      role,
      isSuperAdmin: role === 'super_admin',
    };
  } catch (error) {
    console.error('Unexpected error getting user role:', error);
    return {
      role: null,
      isSuperAdmin: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Console utility for development - DO NOT USE IN PRODUCTION
 * This is for development/testing purposes only
 */
export const devUtils = {
  async promoteCurrentUserToSuperAdmin(): Promise<SetupResult> {
    if (process.env.NODE_ENV === 'production') {
      return {
        success: false,
        message: 'Not allowed in production',
        error: 'This utility is only available in development mode',
      };
    }

    try {
      const { data: user } = await supabase.auth.getUser();
      
      if (!user.user?.email) {
        return {
          success: false,
          message: 'Not authenticated',
          error: 'Please sign in first',
        };
      }

      return await setupInitialSuperAdmin(user.user.email);
    } catch (error) {
      return {
        success: false,
        message: 'Failed to promote current user',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  },

  async logCurrentUserInfo() {
    if (process.env.NODE_ENV === 'production') {
      console.warn('Dev utils not available in production');
      return;
    }

    try {
      const { data: user } = await supabase.auth.getUser();
      const roleInfo = await getCurrentUserRole();
      const superAdminCheck = await checkSuperAdminExists();

      console.group('🔍 Current User Info');
      console.log('User ID:', user.user?.id);
      console.log('Email:', user.user?.email);
      console.log('Role:', roleInfo.role);
      console.log('Is Super Admin:', roleInfo.isSuperAdmin);
      console.log('Super Admins exist:', superAdminCheck.exists);
      console.log('Super Admin count:', superAdminCheck.count);
      console.groupEnd();
    } catch (error) {
      console.error('Failed to log user info:', error);
    }
  },
};

// Make dev utils available globally in development
if (process.env.NODE_ENV === 'development') {
  (window as any).superAdminUtils = devUtils;
  console.log('🛠️ Super Admin dev utils available at window.superAdminUtils');
}
