
interface MessageListener {
  (message: string): void;
}

export class WebSocketManager {
  private ws: WebSocket | null = null;
  private messageListeners: Set<MessageListener> = new Set();

  connect(url: string): void {
    try {
      this.ws = new WebSocket(url);
      
      this.ws.addEventListener('message', (event) => {
        this.notifyMessageListeners(event.data);
      });
    } catch (error) {
      throw new Error(`Connection failed: ${(error as Error).message}`);
    }
  }

  send(message: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(message);
    }
  }

  onMessage(listener: MessageListener): () => void {
    this.messageListeners.add(listener);
    return () => this.messageListeners.delete(listener);
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  private notifyMessageListeners(message: string): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  reset(): void {
    this.disconnect();
    this.messageListeners.clear();
  }
}

export const websocketManager = new WebSocketManager();
