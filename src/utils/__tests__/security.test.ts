import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SecurityMiddleware, SecurityOptions } from '../security';

describe('SecurityMiddleware', () => {
  let security: SecurityMiddleware;
  const options: SecurityOptions = {
    csrfSecret: 'test-secret',
    rateLimit: {
      windowMs: 1000,
      maxRequests: 2
    },
    trustedOrigins: ['https://test.com']
  };

  beforeEach(() => {
    security = SecurityMiddleware.getInstance(options);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('CSRF Protection', () => {
    it('should generate and validate CSRF tokens', () => {
      const sessionId = 'test-session';
      const token = security.generateCSRFToken(sessionId);
      
      expect(token).toBeDefined();
      expect(token.length).toBeGreaterThan(0);
      expect(security.validateCSRFToken(sessionId, token)).toBe(true);
      expect(security.validateCSRFToken(sessionId, 'invalid-token')).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', async () => {
      const key = 'test-ip';
      
      expect(await security.checkRateLimit(key)).toBe(true);
      expect(await security.checkRateLimit(key)).toBe(true);
      expect(await security.checkRateLimit(key)).toBe(false);
    });

    it('should reset rate limit after window expires', async () => {
      const key = 'test-ip';
      
      expect(await security.checkRateLimit(key)).toBe(true);
      expect(await security.checkRateLimit(key)).toBe(true);
      
      // Wait for rate limit window to expire
      await new Promise(resolve => setTimeout(resolve, options.rateLimit.windowMs + 100));
      
      expect(await security.checkRateLimit(key)).toBe(true);
    });
  });

  describe('Input Sanitization', () => {
    it('should sanitize string input', () => {
      const input = '<script>alert("xss")</script>';
      const sanitized = security.sanitizeInput(input);
      
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('alert');
    });

    it('should sanitize object input', () => {
      const input = {
        name: '<script>alert("xss")</script>',
        nested: {
          value: 'javascript:alert("xss")'
        }
      };
      
      const sanitized = security.sanitizeObject(input);
      
      expect(sanitized.name).not.toContain('<script>');
      expect(sanitized.nested.value).not.toContain('javascript:');
    });
  });

  describe('Security Headers', () => {
    it('should return security headers', () => {
      const headers = security.getSecurityHeaders();
      
      expect(headers['Content-Security-Policy']).toBeDefined();
      expect(headers['X-Frame-Options']).toBe('DENY');
      expect(headers['X-Content-Type-Options']).toBe('nosniff');
      expect(headers['X-XSS-Protection']).toBe('1; mode=block');
    });
  });

  describe('Origin Validation', () => {
    it('should validate trusted origins', () => {
      expect(security.validateOrigin('https://test.com')).toBe(true);
      expect(security.validateOrigin('https://malicious.com')).toBe(false);
    });
  });

  describe('Request Validation', () => {
    it('should validate requests with valid origin and CSRF token', () => {
      const sessionId = 'test-session';
      const token = security.generateCSRFToken(sessionId);
      
      const request = new Request('https://test.com', {
        headers: {
          'origin': 'https://test.com',
          'x-csrf-token': token,
          'x-session-id': sessionId
        }
      });
      
      expect(security.validateRequest(request)).toBe(true);
    });

    it('should reject requests with invalid origin', () => {
      const request = new Request('https://test.com', {
        headers: {
          'origin': 'https://malicious.com'
        }
      });
      
      expect(security.validateRequest(request)).toBe(false);
    });

    it('should reject requests with invalid CSRF token', () => {
      const request = new Request('https://test.com', {
        headers: {
          'origin': 'https://test.com',
          'x-csrf-token': 'invalid-token',
          'x-session-id': 'test-session'
        }
      });
      
      expect(security.validateRequest(request)).toBe(false);
    });
  });

  describe('Security Monitoring', () => {
    it('should log security events', () => {
      const consoleSpy = vi.spyOn(console, 'warn');
      
      security.logSecurityEvent('test_event', { test: 'data' });
      
      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toContain('Security Event');
    });

    it('should send security alerts', async () => {
      const consoleSpy = vi.spyOn(console, 'warn');
      
      await security.sendSecurityAlert('test_alert', { test: 'data' });
      
      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleSpy.mock.calls[0][0]).toContain('Security Event');
    });
  });
}); 