import { logger } from './logger';

export interface Translation {
  [key: string]: string | Translation;
}

export interface I18nConfig {
  defaultLocale: string;
  fallbackLocale: string;
  messages: Record<string, Translation>;
}

export class I18n {
  private static instance: I18n;
  private config: I18nConfig;
  private currentLocale: string;

  private constructor(config: I18nConfig) {
    this.config = config;
    this.currentLocale = config.defaultLocale;
  }

  static getInstance(config?: I18nConfig): I18n {
    if (!I18n.instance && config) {
      I18n.instance = new I18n(config);
    }
    return I18n.instance;
  }

  setLocale(locale: string): void {
    if (this.config.messages[locale]) {
      this.currentLocale = locale;
    } else {
      console.warn(`Locale ${locale} not found, falling back to ${this.config.fallbackLocale}`);
      this.currentLocale = this.config.fallbackLocale;
    }
  }

  getLocale(): string {
    return this.currentLocale;
  }

  translate(key: string, params?: Record<string, string>): string {
    const message = this.getMessage(key);
    if (typeof message !== 'string') {
      console.warn(`Translation key ${key} not found`);
      return key;
    }

    if (!params) return message;

    return message.replace(/\{(\w+)\}/g, (_, key) => params[key] || `{${key}}`);
  }

  private getMessage(key: string): string | Translation {
    const keys = key.split('.');
    let current: string | Translation = this.config.messages[this.currentLocale];

    for (const k of keys) {
      if (typeof current === 'string') {
        return key;
      }
      current = current[k];
      if (!current) {
        return key;
      }
    }

    return current;
  }

  addMessages(locale: string, messages: Translation): void {
    this.config.messages[locale] = {
      ...this.config.messages[locale],
      ...messages
    };
  }

  getMessages(locale?: string): Translation {
    return this.config.messages[locale || this.currentLocale];
  }
}

// Helper function to create a type-safe translation
export const createTranslation = <T extends Translation>(translation: T): T => translation;

// Example usage:
const messages = {
  en: createTranslation({
    common: {
      welcome: 'Welcome',
      hello: 'Hello, {name}!'
    }
  }),
  es: createTranslation({
    common: {
      welcome: 'Bienvenido',
      hello: '¡Hola, {name}!'
    }
  })
};

export const i18n = I18n.getInstance({
  defaultLocale: 'en',
  fallbackLocale: 'en',
  messages
});

// Example usage:
/*
const i18n = I18n.getInstance({
  defaultLocale: 'en',
  fallbackLocale: 'en',
  loadLocale: async (locale) => {
    const response = await fetch(`/locales/${locale}.json`);
    return response.json();
  }
});

// Set locale
await i18n.setLocale('fr');

// Translate
const message = i18n.t('welcome.message', { name: 'John' });
console.log(message);

// Format number
const number = i18n.formatNumber(1234.56, {
  style: 'currency',
  currency: 'EUR'
});
console.log(number);

// Format date
const date = i18n.formatDate(new Date(), {
  dateStyle: 'full',
  timeStyle: 'long'
});
console.log(date);

// Format relative time
const relativeTime = i18n.formatRelativeTime(-1, 'day');
console.log(relativeTime);

// Format plural
const plural = i18n.formatPlural(5, {
  one: '1 item',
  other: '{count} items'
});
console.log(plural);

// Add locale change listener
i18n.onLocaleChange((locale) => {
  console.log('Locale changed to:', locale);
});
*/ 