
/**
 * Performance optimization utilities for data fetching and component rendering
 */

import { useCallback, useRef, useEffect } from 'react';

/**
 * Debounce hook for reducing API call frequency
 */
export const useDebounce = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  const debouncedCallback = useCallback((...args: Parameters<T>) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]) as T;

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
};

/**
 * Throttle hook for rate limiting API calls
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  limit: number
): T => {
  const inThrottle = useRef(false);

  const throttledCallback = useCallback((...args: Parameters<T>) => {
    if (!inThrottle.current) {
      callback(...args);
      inThrottle.current = true;
      setTimeout(() => {
        inThrottle.current = false;
      }, limit);
    }
  }, [callback, limit]) as T;

  return throttledCallback;
};

/**
 * Request batching utility for combining multiple API calls
 */
export class RequestBatcher {
  private batches = new Map<string, {
    requests: Array<{ resolve: Function; reject: Function; params: any }>;
    timeout: NodeJS.Timeout;
  }>();
  
  private batchDelay: number;

  constructor(batchDelay = 50) {
    this.batchDelay = batchDelay;
  }

  batch<T>(key: string, params: any, executor: (batchedParams: any[]) => Promise<T[]>): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(key)) {
        this.batches.set(key, {
          requests: [],
          timeout: setTimeout(() => this.executeBatch(key, executor), this.batchDelay)
        });
      }

      const batch = this.batches.get(key)!;
      batch.requests.push({ resolve, reject, params });
    });
  }

  private async executeBatch<T>(key: string, executor: (batchedParams: any[]) => Promise<T[]>) {
    const batch = this.batches.get(key);
    if (!batch) return;

    this.batches.delete(key);
    clearTimeout(batch.timeout);

    try {
      const batchedParams = batch.requests.map(req => req.params);
      const results = await executor(batchedParams);
      
      batch.requests.forEach((req, index) => {
        req.resolve(results[index]);
      });
    } catch (error) {
      batch.requests.forEach(req => {
        req.reject(error);
      });
    }
  }
}

/**
 * Global request batcher instance
 */
export const requestBatcher = new RequestBatcher();

/**
 * Memory optimization utilities
 */
export const memoryOptimization = {
  // Clear unused query cache
  clearStaleCache: (queryClient: any, maxAge = 10 * 60 * 1000) => {
    const now = Date.now();
    queryClient.getQueryCache().getAll().forEach((query: any) => {
      if (now - query.state.dataUpdatedAt > maxAge) {
        queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });
  },

  // Optimize image loading
  preloadCriticalImages: (imageUrls: string[]) => {
    imageUrls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  },

  // Cleanup event listeners
  cleanupEventListeners: (element: Element) => {
    const clone = element.cloneNode(true);
    element.parentNode?.replaceChild(clone, element);
    return clone;
  }
};

/**
 * Component performance monitoring
 */
export const performanceMonitor = {
  measureRender: (componentName: string, renderFn: () => void) => {
    const start = performance.now();
    renderFn();
    const end = performance.now();
    
    console.log(`${componentName} render time: ${(end - start).toFixed(2)}ms`);
  },

  measureAsync: async <T>(operationName: string, asyncFn: () => Promise<T>): Promise<T> => {
    const start = performance.now();
    const result = await asyncFn();
    const end = performance.now();
    
    console.log(`${operationName} execution time: ${(end - start).toFixed(2)}ms`);
    return result;
  }
};
