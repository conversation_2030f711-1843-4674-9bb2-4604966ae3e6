import { SecurityMiddleware } from './security';

const security = SecurityMiddleware.getInstance({
  csrfSecret: process.env.CSRF_SECRET || 'default-secret',
  rateLimit: {
    windowMs: 15 * 60 * 1000,
    maxRequests: 100
  },
  trustedOrigins: [process.env.APP_ORIGIN || 'http://localhost:3000']
});

export function secureApiHandler(handler: (req: any, res: any) => Promise<any> | any) {
  return async (req: any, res: any) => {
    // Set security headers
    const headers = security.getSecurityHeaders();
    Object.entries(headers).forEach(([key, value]) => res.setHeader(key, value));

    // Validate origin
    const origin = req.headers['origin'] || req.headers['referer'];
    if (origin && !security.validateOrigin(origin)) {
      res.status(403).json({ error: 'Invalid origin' });
      return;
    }

    // Rate limiting
    const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const allowed = await security.checkRateLimit(ip);
    if (!allowed) {
      res.status(429).json({ error: 'Too many requests' });
      return;
    }

    // CSRF protection (for unsafe methods)
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
      const csrfToken = req.headers['x-csrf-token'];
      const sessionId = req.headers['x-session-id'];
      if (!csrfToken || !sessionId || !security.validateCSRFToken(sessionId, csrfToken)) {
        res.status(403).json({ error: 'Invalid CSRF token' });
        return;
      }
    }

    // Input sanitization (for body)
    if (req.body) {
      req.body = security.sanitizeObject(req.body);
    }

    // Call the actual handler
    return handler(req, res);
  };
}

// Example usage (Express):
// import express from 'express';
// import { secureApiHandler } from './utils/secureApiHandler';
//
// const app = express();
// app.use(express.json());
// app.post('/api/data', secureApiHandler(async (req, res) => {
//   res.json({ ok: true });
// })); 