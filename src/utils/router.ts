import { logger } from './logger';

export interface Route {
  path: string;
  component: React.ComponentType;
  children?: Route[];
  guards?: RouteGuard[];
  middleware?: RouteMiddleware[];
  meta?: Record<string, any>;
}

export type RouteGuard = (context: RouteContext) => boolean | Promise<boolean>;
export type RouteMiddleware = (context: RouteContext) => void | Promise<void>;

export interface RouteContext {
  path: string;
  params: Record<string, string>;
  query: Record<string, string>;
  meta: Record<string, any>;
  navigate: (path: string) => void;
}

export interface RouterOptions {
  basePath?: string;
  historyMode?: 'hash' | 'history';
  onRouteChange?: (context: RouteContext) => void;
}

export class Router {
  private static instance: Router;
  private routes: Route[] = [];
  private options: RouterOptions;
  private currentRoute: RouteContext | null = null;
  private listeners: Set<(context: RouteContext) => void> = new Set();

  private constructor(options: RouterOptions = {}) {
    this.options = {
      historyMode: 'history',
      ...options
    };

    this.initializeHistory();
  }

  static getInstance(options?: RouterOptions): Router {
    if (!Router.instance) {
      Router.instance = new Router(options);
    }
    return Router.instance;
  }

  // Initialize history
  private initializeHistory(): void {
    if (this.options.historyMode === 'hash') {
      window.addEventListener('hashchange', () => this.handleRouteChange());
    } else {
      window.addEventListener('popstate', () => this.handleRouteChange());
    }
  }

  // Add routes
  addRoutes(routes: Route[]): void {
    this.routes = [...this.routes, ...routes];
    logger.debug('Routes added', { count: routes.length });
  }

  // Handle route change
  private async handleRouteChange(): Promise<void> {
    const path = this.getCurrentPath();
    const route = this.findRoute(path);

    if (!route) {
      logger.warn('Route not found', { path });
      return;
    }

    const context = this.createRouteContext(path, route);

    // Run middleware
    if (route.middleware) {
      for (const middleware of route.middleware) {
        try {
          await middleware(context);
        } catch (error) {
          logger.error('Middleware error', { error });
          return;
        }
      }
    }

    // Run guards
    if (route.guards) {
      for (const guard of route.guards) {
        try {
          const canActivate = await guard(context);
          if (!canActivate) {
            logger.warn('Route guard blocked navigation', { path });
            return;
          }
        } catch (error) {
          logger.error('Route guard error', { error });
          return;
        }
      }
    }

    this.currentRoute = context;
    this.notifyListeners();
    this.options.onRouteChange?.(context);

    logger.info('Route changed', { path });
  }

  // Find matching route
  private findRoute(path: string): Route | null {
    const findRouteRecursive = (routes: Route[], path: string): Route | null => {
      for (const route of routes) {
        if (this.matchPath(route.path, path)) {
          return route;
        }
        if (route.children) {
          const childRoute = findRouteRecursive(route.children, path);
          if (childRoute) return childRoute;
        }
      }
      return null;
    };

    return findRouteRecursive(this.routes, path);
  }

  // Match path against route pattern
  private matchPath(pattern: string, path: string): boolean {
    const patternParts = pattern.split('/');
    const pathParts = path.split('/');

    if (patternParts.length !== pathParts.length) {
      return false;
    }

    return patternParts.every((part, index) => {
      if (part.startsWith(':')) {
        return true;
      }
      return part === pathParts[index];
    });
  }

  // Create route context
  private createRouteContext(path: string, route: Route): RouteContext {
    const params = this.extractParams(route.path, path);
    const query = this.extractQuery(path);

    return {
      path,
      params,
      query,
      meta: route.meta || {},
      navigate: (newPath: string) => this.navigate(newPath)
    };
  }

  // Extract route parameters
  private extractParams(pattern: string, path: string): Record<string, string> {
    const params: Record<string, string> = {};
    const patternParts = pattern.split('/');
    const pathParts = path.split('/');

    patternParts.forEach((part, index) => {
      if (part.startsWith(':')) {
        const paramName = part.slice(1);
        params[paramName] = pathParts[index];
      }
    });

    return params;
  }

  // Extract query parameters
  private extractQuery(path: string): Record<string, string> {
    const query: Record<string, string> = {};
    const queryString = path.split('?')[1];

    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=');
        query[key] = decodeURIComponent(value);
      });
    }

    return query;
  }

  // Get current path
  private getCurrentPath(): string {
    if (this.options.historyMode === 'hash') {
      return window.location.hash.slice(1) || '/';
    }
    return window.location.pathname;
  }

  // Navigate to path
  navigate(path: string): void {
    const fullPath = this.options.basePath
      ? `${this.options.basePath}${path}`
      : path;

    if (this.options.historyMode === 'hash') {
      window.location.hash = fullPath;
    } else {
      window.history.pushState(null, '', fullPath);
      this.handleRouteChange();
    }
  }

  // Add route change listener
  addRouteChangeListener(listener: (context: RouteContext) => void): void {
    this.listeners.add(listener);
  }

  // Remove route change listener
  removeRouteChangeListener(listener: (context: RouteContext) => void): void {
    this.listeners.delete(listener);
  }

  // Notify listeners of route change
  private notifyListeners(): void {
    if (!this.currentRoute) return;
    this.listeners.forEach(listener => listener(this.currentRoute!));
  }

  // Get current route context
  getCurrentRoute(): RouteContext | null {
    return this.currentRoute;
  }

  // Get all routes
  getRoutes(): Route[] {
    return this.routes;
  }
}

// Example usage:
/*
const router = Router.getInstance({
  historyMode: 'history',
  basePath: '/app'
});

// Define routes
const routes: Route[] = [
  {
    path: '/',
    component: HomePage,
    meta: { title: 'Home' }
  },
  {
    path: '/dashboard',
    component: DashboardPage,
    guards: [
      (context) => isAuthenticated(),
      (context) => hasPermission('view_dashboard')
    ],
    middleware: [
      (context) => loadUserData()
    ],
    meta: { title: 'Dashboard' }
  },
  {
    path: '/users',
    component: UsersPage,
    children: [
      {
        path: '/users/:id',
        component: UserDetailPage,
        guards: [
          (context) => hasPermission('view_user_details')
        ]
      }
    ]
  }
];

// Add routes
router.addRoutes(routes);

// Listen for route changes
router.addRouteChangeListener((context) => {
  console.log('Route changed:', context.path);
  document.title = context.meta.title || 'My App';
});

// Navigate programmatically
router.navigate('/dashboard');

// Get current route
const currentRoute = router.getCurrentRoute();
console.log(currentRoute);
*/ 