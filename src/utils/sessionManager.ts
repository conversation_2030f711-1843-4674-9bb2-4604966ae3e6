
import { supabase } from '@/integrations/supabase/client';

export interface SessionConfig {
  refreshThreshold: number; // Minutes before expiry to refresh
  maxRetries: number;
  retryDelay: number; // Milliseconds
}

export class SessionManager {
  private static instance: SessionManager;
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;
  
  private config: SessionConfig = {
    refreshThreshold: 10, // Refresh 10 minutes before expiry
    maxRetries: 3,
    retryDelay: 1000
  };

  static getInstance(config?: Partial<SessionConfig>): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager(config);
    }
    return SessionManager.instance;
  }

  constructor(config?: Partial<SessionConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
    this.initializeSessionManagement();
  }

  private async initializeSessionManagement() {
    // Listen for auth state changes
    supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);
      
      if (event === 'SIGNED_IN' && session) {
        this.scheduleTokenRefresh(session.expires_at);
      } else if (event === 'SIGNED_OUT') {
        this.clearRefreshTimer();
      } else if (event === 'TOKEN_REFRESHED' && session) {
        this.scheduleTokenRefresh(session.expires_at);
      }
    });

    // Check current session
    const { data: { session } } = await supabase.auth.getSession();
    if (session) {
      this.scheduleTokenRefresh(session.expires_at);
    }
  }

  private scheduleTokenRefresh(expiresAt?: number) {
    this.clearRefreshTimer();
    
    if (!expiresAt) return;

    const now = Math.floor(Date.now() / 1000);
    const refreshTime = expiresAt - (this.config.refreshThreshold * 60);
    const delay = Math.max(0, (refreshTime - now) * 1000);

    console.log(`Scheduling token refresh in ${delay / 1000} seconds`);

    this.refreshTimer = setTimeout(async () => {
      await this.refreshSession();
    }, delay);
  }

  private clearRefreshTimer() {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  async refreshSession(retryCount = 0): Promise<boolean> {
    if (this.isRefreshing) {
      console.log('Session refresh already in progress');
      return false;
    }

    this.isRefreshing = true;

    try {
      console.log(`Attempting session refresh (attempt ${retryCount + 1})`);
      
      const { data, error } = await supabase.auth.refreshSession();
      
      if (error) {
        throw error;
      }

      if (data.session) {
        console.log('Session refreshed successfully');
        this.scheduleTokenRefresh(data.session.expires_at);
        return true;
      }

      throw new Error('No session returned from refresh');
    } catch (error) {
      console.error('Session refresh failed:', error);
      
      if (retryCount < this.config.maxRetries - 1) {
        console.log(`Retrying session refresh in ${this.config.retryDelay}ms`);
        setTimeout(() => {
          this.refreshSession(retryCount + 1);
        }, this.config.retryDelay);
        return false;
      }

      // Max retries exceeded - sign out user
      console.error('Max refresh retries exceeded, signing out user');
      await supabase.auth.signOut();
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  async validateSession(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error || !session) {
        return false;
      }

      const now = Math.floor(Date.now() / 1000);
      const expiresAt = session.expires_at || 0;

      // Check if token is expired or expires soon
      if (expiresAt <= now + (this.config.refreshThreshold * 60)) {
        console.log('Token expires soon, attempting refresh');
        return await this.refreshSession();
      }

      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      return false;
    }
  }

  destroy() {
    this.clearRefreshTimer();
    this.isRefreshing = false;
  }
}
