import { logger } from './logger';

export interface User {
  id: string;
  email: string;
  roles: string[];
  permissions: string[];
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthOptions {
  tokenKey?: string;
  refreshTokenKey?: string;
  userKey?: string;
  tokenExpiry?: number;
  refreshTokenExpiry?: number;
  autoRefresh?: boolean;
  refreshThreshold?: number;
}

export class Auth {
  private static instance: Auth;
  private options: AuthOptions = {
    tokenKey: 'auth_token',
    refreshTokenKey: 'refresh_token',
    userKey: 'auth_user',
    tokenExpiry: 3600, // 1 hour
    refreshTokenExpiry: 604800, // 7 days
    autoRefresh: true,
    refreshThreshold: 300 // 5 minutes
  };
  private refreshTimeout: number | null = null;
  private user: User | null = null;

  private constructor(options?: AuthOptions) {
    this.options = { ...this.options, ...options };
    this.initialize();
  }

  static getInstance(options?: AuthOptions): Auth {
    if (!Auth.instance) {
      Auth.instance = new Auth(options);
    }
    return Auth.instance;
  }

  // Initialize auth state
  private initialize(): void {
    this.user = this.getStoredUser();
    if (this.options.autoRefresh) {
      this.scheduleTokenRefresh();
    }
  }

  // Login
  async login(email: string, password: string): Promise<User> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email, password })
      });

      if (!response.ok) {
        throw new Error('Login failed');
      }

      const { user, tokens } = await response.json();
      this.setTokens(tokens);
      this.setUser(user);
      this.scheduleTokenRefresh();

      return user;
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const refreshToken = this.getRefreshToken();
      if (refreshToken) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ refreshToken })
        });
      }
    } catch (error) {
      logger.error('Logout error:', error);
    } finally {
      this.clearAuth();
    }
  }

  // Refresh token
  async refreshToken(): Promise<AuthTokens> {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken })
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const tokens = await response.json();
      this.setTokens(tokens);
      this.scheduleTokenRefresh();

      return tokens;
    } catch (error) {
      logger.error('Token refresh error:', error);
      this.clearAuth();
      throw error;
    }
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.user;
  }

  // Check if user has role
  hasRole(role: string): boolean {
    return this.user?.roles.includes(role) || false;
  }

  // Check if user has permission
  hasPermission(permission: string): boolean {
    return this.user?.permissions.includes(permission) || false;
  }

  // Get access token
  getAccessToken(): string | null {
    return localStorage.getItem(this.options.tokenKey || 'auth_token');
  }

  // Get refresh token
  private getRefreshToken(): string | null {
    return localStorage.getItem(this.options.refreshTokenKey || 'refresh_token');
  }

  // Set tokens
  private setTokens(tokens: AuthTokens): void {
    localStorage.setItem(this.options.tokenKey || 'auth_token', tokens.accessToken);
    localStorage.setItem(this.options.refreshTokenKey || 'refresh_token', tokens.refreshToken);
  }

  // Set user
  private setUser(user: User): void {
    this.user = user;
    localStorage.setItem(this.options.userKey || 'auth_user', JSON.stringify(user));
  }

  // Get stored user
  private getStoredUser(): User | null {
    const userStr = localStorage.getItem(this.options.userKey || 'auth_user');
    return userStr ? JSON.parse(userStr) : null;
  }

  // Clear auth data
  private clearAuth(): void {
    localStorage.removeItem(this.options.tokenKey || 'auth_token');
    localStorage.removeItem(this.options.refreshTokenKey || 'refresh_token');
    localStorage.removeItem(this.options.userKey || 'auth_user');
    this.user = null;
    this.clearRefreshTimeout();
  }

  // Schedule token refresh
  private scheduleTokenRefresh(): void {
    this.clearRefreshTimeout();

    const token = this.getAccessToken();
    if (!token) return;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const expiry = payload.exp * 1000;
      const now = Date.now();
      const timeUntilRefresh = expiry - now - (this.options.refreshThreshold || 300) * 1000;

      if (timeUntilRefresh > 0) {
        this.refreshTimeout = window.setTimeout(
          () => this.refreshToken(),
          timeUntilRefresh
        );
      } else {
        this.refreshToken();
      }
    } catch (error) {
      logger.error('Error scheduling token refresh:', error);
    }
  }

  // Clear refresh timeout
  private clearRefreshTimeout(): void {
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }
  }

  // Create auth header
  getAuthHeader(): Record<string, string> {
    const token = this.getAccessToken();
    return token ? { Authorization: `Bearer ${token}` } : {};
  }
}

// Example usage:
/*
const auth = Auth.getInstance({
  tokenExpiry: 3600,
  refreshTokenExpiry: 604800,
  autoRefresh: true
});

// Login
try {
  const user = await auth.login('<EMAIL>', 'password');
  console.log('Logged in as:', user);
} catch (error) {
  console.error('Login failed:', error);
}

// Check authentication
if (auth.isAuthenticated()) {
  const user = auth.getCurrentUser();
  console.log('Current user:', user);
}

// Check roles and permissions
if (auth.hasRole('admin')) {
  console.log('User is an admin');
}

if (auth.hasPermission('create:post')) {
  console.log('User can create posts');
}

// Logout
await auth.logout();

// Get auth header for API requests
const headers = auth.getAuthHeader();
fetch('/api/protected', { headers });
*/ 