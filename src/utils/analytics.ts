import { logger } from './logger';

export interface AnalyticsEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  metadata?: Record<string, any>;
  timestamp: number;
}

export interface AnalyticsUser {
  id: string;
  properties?: Record<string, any>;
}

export class Analytics {
  private static instance: Analytics;
  private events: AnalyticsEvent[] = [];
  private user?: AnalyticsUser;
  private queue: AnalyticsEvent[] = [];
  private isProcessing = false;
  private endpoint: string;
  private batchSize: number;
  private flushInterval: number;

  private constructor(
    endpoint: string = '/api/analytics',
    batchSize: number = 10,
    flushInterval: number = 30000
  ) {
    this.endpoint = endpoint;
    this.batchSize = batchSize;
    this.flushInterval = flushInterval;
    this.startAutoFlush();
  }

  static getInstance(
    endpoint?: string,
    batchSize?: number,
    flushInterval?: number
  ): Analytics {
    if (!Analytics.instance) {
      Analytics.instance = new Analytics(endpoint, batchSize, flushInterval);
    }
    return Analytics.instance;
  }

  // Set user information
  setUser(user: AnalyticsUser): void {
    this.user = user;
    logger.info('Analytics user set', { userId: user.id });
  }

  // Track an event
  trackEvent(event: Omit<AnalyticsEvent, 'timestamp'>): void {
    const fullEvent: AnalyticsEvent = {
      ...event,
      timestamp: Date.now()
    };

    this.queue.push(fullEvent);
    logger.debug('Event queued', { event: fullEvent });

    if (this.queue.length >= this.batchSize) {
      this.flush();
    }
  }

  // Track page view
  trackPageView(path: string, metadata?: Record<string, any>): void {
    this.trackEvent({
      category: 'page',
      action: 'view',
      label: path,
      metadata
    });
  }

  // Track user interaction
  trackInteraction(
    element: string,
    action: string,
    metadata?: Record<string, any>
  ): void {
    this.trackEvent({
      category: 'interaction',
      action,
      label: element,
      metadata
    });
  }

  // Track error
  trackError(error: Error, metadata?: Record<string, any>): void {
    this.trackEvent({
      category: 'error',
      action: error.name,
      label: error.message,
      metadata: {
        ...metadata,
        stack: error.stack
      }
    });
  }

  // Track performance metric
  trackPerformance(
    metric: string,
    value: number,
    metadata?: Record<string, any>
  ): void {
    this.trackEvent({
      category: 'performance',
      action: metric,
      value,
      metadata
    });
  }

  // Start automatic flushing
  private startAutoFlush(): void {
    setInterval(() => {
      if (this.queue.length > 0) {
        this.flush();
      }
    }, this.flushInterval);
  }

  // Flush queued events
  async flush(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) return;

    this.isProcessing = true;
    const events = this.queue.splice(0, this.batchSize);

    try {
      await this.sendEvents(events);
      logger.info('Events flushed successfully', { count: events.length });
    } catch (error) {
      // Put events back in queue
      this.queue.unshift(...events);
      logger.error('Failed to flush events', { error });
    } finally {
      this.isProcessing = false;
    }
  }

  // Send events to analytics endpoint
  private async sendEvents(events: AnalyticsEvent[]): Promise<void> {
    const payload = {
      user: this.user,
      events
    };

    const response = await fetch(this.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Failed to send events: ${response.statusText}`);
    }
  }

  // Get queued events
  getQueuedEvents(): AnalyticsEvent[] {
    return [...this.queue];
  }

  // Clear queued events
  clearQueue(): void {
    this.queue = [];
    logger.info('Analytics queue cleared');
  }
}

// Example usage:
/*
const analytics = Analytics.getInstance();

// Set user
analytics.setUser({
  id: 'user123',
  properties: {
    plan: 'premium',
    signupDate: new Date()
  }
});

// Track page view
analytics.trackPageView('/dashboard', {
  referrer: document.referrer
});

// Track interaction
analytics.trackInteraction('button', 'click', {
  buttonId: 'submit-form'
});

// Track error
analytics.trackError(new Error('API request failed'), {
  endpoint: '/api/data'
});

// Track performance
analytics.trackPerformance('load-time', 1500, {
  page: '/dashboard'
});

// Force flush
await analytics.flush();
*/ 