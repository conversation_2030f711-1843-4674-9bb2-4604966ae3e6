
import crypto from 'crypto';

export interface SecurityOptions {
  csrfSecret: string;
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  trustedOrigins: string[];
}

export class SecurityMiddleware {
  private static instance: SecurityMiddleware;
  private options: SecurityOptions;
  private rateLimitStore = new Map<string, { count: number; resetTime: number }>();
  private csrfTokens = new Map<string, string>();

  private constructor(options: SecurityOptions) {
    this.options = options;
  }

  static getInstance(options?: SecurityOptions): SecurityMiddleware {
    if (!SecurityMiddleware.instance && options) {
      SecurityMiddleware.instance = new SecurityMiddleware(options);
    }
    return SecurityMiddleware.instance;
  }

  generateCSRFToken(sessionId: string): string {
    const token = crypto.randomBytes(32).toString('hex');
    this.csrfTokens.set(sessionId, token);
    return token;
  }

  validateCSRFToken(sessionId: string, token: string): boolean {
    const storedToken = this.csrfTokens.get(sessionId);
    return storedToken === token;
  }

  async checkRateLimit(key: string): Promise<boolean> {
    const now = Date.now();
    const record = this.rateLimitStore.get(key);

    if (!record || now > record.resetTime) {
      this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now + this.options.rateLimit.windowMs
      });
      return true;
    }

    if (record.count >= this.options.rateLimit.maxRequests) {
      return false;
    }

    record.count++;
    return true;
  }

  sanitizeInput(input: string): string {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  sanitizeObject(obj: any): any {
    if (typeof obj === 'string') {
      return this.sanitizeInput(obj);
    }
    if (typeof obj === 'object' && obj !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.sanitizeObject(value);
      }
      return sanitized;
    }
    return obj;
  }

  getSecurityHeaders(): Record<string, string> {
    return {
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block'
    };
  }

  validateOrigin(origin: string): boolean {
    return this.options.trustedOrigins.includes(origin);
  }

  validateRequest(request: Request): boolean {
    const origin = request.headers.get('origin');
    if (origin && !this.validateOrigin(origin)) {
      return false;
    }

    const csrfToken = request.headers.get('x-csrf-token');
    const sessionId = request.headers.get('x-session-id');
    
    if (csrfToken && sessionId) {
      return this.validateCSRFToken(sessionId, csrfToken);
    }

    return true;
  }

  logSecurityEvent(event: string, data: any): void {
    console.warn('Security Event:', event, data);
  }

  async sendSecurityAlert(alert: string, data: any): Promise<void> {
    console.warn('Security Event:', alert, data);
  }
}
