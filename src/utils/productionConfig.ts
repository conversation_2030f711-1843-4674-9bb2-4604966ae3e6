
export interface ProductionConfig {
  environment: 'development' | 'staging' | 'production';
  apiUrl: string;
  supabaseUrl: string;
  supabaseAnonKey: string;
  version: string;
  features: {
    errorReporting: boolean;
    analytics: boolean;
    debugging: boolean;
    performanceMonitoring: boolean;
  };
  security: {
    enforceHttps: boolean;
    csrfProtection: boolean;
    rateLimiting: boolean;
  };
  monitoring: {
    healthCheckInterval: number;
    errorReportingEndpoint?: string;
    metricsEndpoint?: string;
  };
}

class ProductionConfigManager {
  private static instance: ProductionConfigManager;
  private config: ProductionConfig;

  static getInstance(): ProductionConfigManager {
    if (!ProductionConfigManager.instance) {
      ProductionConfigManager.instance = new ProductionConfigManager();
    }
    return ProductionConfigManager.instance;
  }

  constructor() {
    this.config = this.loadConfig();
    this.validateConfig();
    this.setupEnvironment();
  }

  private loadConfig(): ProductionConfig {
    const environment = (import.meta.env.NODE_ENV || 'development') as ProductionConfig['environment'];
    
    // Use the hardcoded Supabase values from the integration
    const supabaseUrl = "https://dygcfpcndgivnuimdgqv.supabase.co";
    const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rTipcyif1-ERYZDbq5RC3fJl_KUiJJMqPtQvDdFDlFQ";
    
    return {
      environment,
      apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:8080',
      supabaseUrl,
      supabaseAnonKey,
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      features: {
        errorReporting: environment === 'production' || environment === 'staging',
        analytics: environment === 'production',
        debugging: environment === 'development',
        performanceMonitoring: environment !== 'development'
      },
      security: {
        enforceHttps: environment === 'production',
        csrfProtection: environment !== 'development',
        rateLimiting: environment !== 'development'
      },
      monitoring: {
        healthCheckInterval: environment === 'production' ? 30000 : 60000, // ms
        errorReportingEndpoint: import.meta.env.VITE_ERROR_ENDPOINT,
        metricsEndpoint: import.meta.env.VITE_METRICS_ENDPOINT
      }
    };
  }

  private validateConfig() {
    // Only validate that we have the hardcoded values
    if (!this.config.supabaseUrl || !this.config.supabaseAnonKey) {
      throw new Error('Supabase configuration is missing');
    }

    // Validate URLs if they exist
    try {
      new URL(this.config.supabaseUrl);
      new URL(this.config.apiUrl);
    } catch (error) {
      console.warn('Invalid URL configuration:', error);
    }

    // Environment-specific validations
    if (this.config.environment === 'production') {
      if (!this.config.supabaseUrl.includes('supabase.co')) {
        console.warn('Production environment should use official Supabase URLs');
      }
      
      if (this.config.apiUrl.includes('localhost')) {
        console.warn('Production environment using localhost URLs');
      }
    }
  }

  private setupEnvironment() {
    // Set global error handling based on environment
    if (this.config.features.debugging) {
      // Enable verbose logging in development
      console.info('🔧 Debug mode enabled');
    } else {
      // Disable console logs in production
      if (this.config.environment === 'production') {
        console.log = () => {};
        console.info = () => {};
        console.warn = () => {};
      }
    }

    // HTTPS enforcement
    if (this.config.security.enforceHttps && location.protocol !== 'https:') {
      console.warn('HTTPS enforcement enabled but page loaded over HTTP');
      if (this.config.environment === 'production') {
        location.replace(location.href.replace('http:', 'https:'));
      }
    }

    // Set up CSP headers (if supported)
    if (this.config.security.csrfProtection) {
      this.setupCSP();
    }
  }

  private setupCSP() {
    // Basic Content Security Policy setup
    // In a real app, this would be handled by your web server
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
      "font-src 'self' data:",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
    
    document.head.appendChild(meta);
  }

  getConfig(): ProductionConfig {
    return { ...this.config };
  }

  isFeatureEnabled(feature: keyof ProductionConfig['features']): boolean {
    return this.config.features[feature];
  }

  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  updateConfig(updates: Partial<ProductionConfig>) {
    this.config = { ...this.config, ...updates };
    this.validateConfig();
  }
}

export const productionConfig = ProductionConfigManager.getInstance();
