
interface LogContext {
  [key: string]: any;
}

interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: string;
  context?: LogContext;
}

class Logger {
  private logs: LogEntry[] = [];
  private logLevel: 'debug' | 'info' | 'warn' | 'error' = 'info';
  private maxLogs = 1000;

  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
    console.log(`[INFO] ${message}`, context || '');
  }

  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
    console.warn(`[WARN] ${message}`, context || '');
  }

  error(message: string, error?: Error | LogContext, context?: LogContext): void {
    if (error instanceof Error) {
      this.log('error', message, { ...context, error: error.message, stack: error.stack });
      console.error(`[ERROR] ${message}`, error, context || '');
    } else {
      this.log('error', message, error);
      console.error(`[ERROR] ${message}`, error || '');
    }
  }

  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
    console.debug(`[DEBUG] ${message}`, context || '');
  }

  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string, context?: LogContext): void {
    if (!this.shouldLog(level)) return;

    const entry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context
    };

    this.logs.push(entry);

    // Rotate logs if needed
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  private shouldLog(level: 'debug' | 'info' | 'warn' | 'error'): boolean {
    const levels = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.logLevel);
  }

  setLogLevel(level: 'debug' | 'info' | 'warn' | 'error'): void {
    this.logLevel = level;
  }

  getLogs(level?: 'debug' | 'info' | 'warn' | 'error'): LogEntry[] {
    if (level) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }

  clear(): void {
    this.logs = [];
  }

  clearLogs(): void {
    this.logs = [];
  }

  async exportLogs(): Promise<string> {
    return JSON.stringify(this.logs, null, 2);
  }
}

export const logger = new Logger();
