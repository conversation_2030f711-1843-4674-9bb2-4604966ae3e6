
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  environment: string;
  services: {
    database: 'up' | 'down';
    auth: 'up' | 'down';
    storage: 'up' | 'down';
  };
  metrics: {
    uptime: number;
    memoryUsage?: number;
    responseTime: number;
  };
}

export class HealthChecker {
  private static instance: HealthChecker;
  private startTime: number = Date.now();

  static getInstance(): HealthChecker {
    if (!HealthChecker.instance) {
      HealthChecker.instance = new HealthChecker();
    }
    return HealthChecker.instance;
  }

  async checkHealth(): Promise<HealthStatus> {
    const startCheck = Date.now();
    
    try {
      const [dbStatus, authStatus, storageStatus] = await Promise.allSettled([
        this.checkDatabase(),
        this.checkAuth(),
        this.checkStorage()
      ]);

      const services = {
        database: dbStatus.status === 'fulfilled' && dbStatus.value ? 'up' : 'down',
        auth: authStatus.status === 'fulfilled' && authStatus.value ? 'up' : 'down',
        storage: storageStatus.status === 'fulfilled' && storageStatus.value ? 'up' : 'down'
      } as const;

      const allUp = Object.values(services).every(status => status === 'up');
      const anyDown = Object.values(services).some(status => status === 'down');

      return {
        status: allUp ? 'healthy' : anyDown ? 'unhealthy' : 'degraded',
        timestamp: new Date().toISOString(),
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        environment: import.meta.env.NODE_ENV || 'development',
        services,
        metrics: {
          uptime: Date.now() - this.startTime,
          responseTime: Date.now() - startCheck,
          memoryUsage: this.getMemoryUsage()
        }
      };
    } catch (error) {
      console.error('Health check failed:', error);
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        environment: import.meta.env.NODE_ENV || 'development',
        services: {
          database: 'down',
          auth: 'down',
          storage: 'down'
        },
        metrics: {
          uptime: Date.now() - this.startTime,
          responseTime: Date.now() - startCheck
        }
      };
    }
  }

  private async checkDatabase(): Promise<boolean> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { error } = await supabase.from('profiles').select('id').limit(1);
      return !error;
    } catch {
      return false;
    }
  }

  private async checkAuth(): Promise<boolean> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { error } = await supabase.auth.getSession();
      return !error;
    } catch {
      return false;
    }
  }

  private async checkStorage(): Promise<boolean> {
    try {
      const { supabase } = await import('@/integrations/supabase/client');
      const { data, error } = await supabase.storage.listBuckets();
      return !error && Array.isArray(data);
    } catch {
      return false;
    }
  }

  private getMemoryUsage(): number | undefined {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      return (window.performance as any).memory.usedJSHeapSize;
    }
    return undefined;
  }
}
