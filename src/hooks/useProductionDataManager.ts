
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useProductionApiRetry } from './useProductionApiRetry';
import { toast } from '@/hooks/use-toast';
import { validateFormData, formatValidationErrors } from '@/features/production/validation/schemas';
import type { ApiResponse } from '@/lib/api/types';
import type { ZodSchema } from 'zod';

interface UseProductionDataManagerConfig<T, CreateInput> {
  entityName: string;
  apiService: {
    getItems: (orgId: string) => Promise<ApiResponse<T[]>>;
    createItem: (data: any) => Promise<ApiResponse<T>>;
  };
  validationSchema: ZodSchema<CreateInput>;
  getDisplayName: (item: T) => string;
}

export const useProductionDataManager = <T, CreateInput>(
  config: UseProductionDataManagerConfig<T, CreateInput>
) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const apiRetry = useProductionApiRetry();
  
  const [items, setItems] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const clearErrors = () => {
    setErrors({});
  };

  const fetchItems = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    clearErrors();

    const result = await apiRetry.execute(async () => {
      const apiResult = await config.apiService.getItems(currentOrganization.id);

      if (!apiResult.success) {
        throw new Error(`Failed to fetch ${config.entityName}`);
      }

      return apiResult.data || [];
    });

    if (result) {
      setItems(result);
    }
    setLoading(false);
  };

  const createItem = async (itemData: CreateInput) => {
    if (!currentOrganization || !user) return null;

    const validation = validateFormData(config.validationSchema, itemData);
    if (!validation.success) {
      const validationErrors = formatValidationErrors(validation.error);
      setErrors(validationErrors);
      toast({
        title: "Validation Error",
        description: "Please check your input and try again",
        variant: "destructive"
      });
      return null;
    }

    clearErrors();
    setCreating(true);

    const result = await apiRetry.execute(async () => {
      const apiResult = await config.apiService.createItem({
        ...validation.data,
        org_id: currentOrganization.id,
        user_id: user.id
      });

      if (!apiResult.success) {
        throw new Error(apiResult.error || `Failed to create ${config.entityName}`);
      }

      return apiResult.data;
    });

    setCreating(false);

    if (result) {
      setItems(prev => [result, ...prev]);
      
      toast({
        title: `${config.entityName} created`,
        description: `"${config.getDisplayName(result)}" has been created successfully`,
      });
    }

    return result;
  };

  return {
    items,
    loading,
    creating,
    errors,
    createItem,
    fetchItems,
    clearErrors
  };
};
