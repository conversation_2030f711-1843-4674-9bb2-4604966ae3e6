
import { useState, useEffect, useMemo } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { productionApi } from '@/lib/api';
import type { ProductionBudget, BudgetLineItem } from '@/lib/api/production';

interface BudgetTracking {
  totalEstimated: number;
  totalActual: number;
  remainingBudget: number;
  budgetVariance: number;
  budgetUtilization: number;
  isOverBudget: boolean;
  categoryBreakdown: Array<{
    category: string;
    estimated: number;
    actual: number;
    variance: number;
    items: BudgetLineItem[];
  }>;
  alerts: Array<{
    type: 'over_budget' | 'approaching_limit' | 'variance_warning';
    message: string;
    severity: 'info' | 'warning' | 'error';
    amount?: number;
  }>;
}

export const useBudgetTracking = (budgetId?: string) => {
  const { currentOrganization } = useOrganization();
  const [budget, setBudget] = useState<ProductionBudget | null>(null);
  const [lineItems, setLineItems] = useState<BudgetLineItem[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchBudgetData = async () => {
    if (!currentOrganization || !budgetId) return;

    setLoading(true);
    try {
      const [budgetsResult, lineItemsResult] = await Promise.all([
        productionApi.getBudgets(currentOrganization.id),
        productionApi.getBudgetLineItems(budgetId)
      ]);

      if (budgetsResult.success && budgetsResult.data) {
        const foundBudget = budgetsResult.data.find(b => b.id === budgetId);
        setBudget(foundBudget || null);
      }

      if (lineItemsResult.success && lineItemsResult.data) {
        setLineItems(lineItemsResult.data);
      }
    } catch (error) {
      console.error('Error fetching budget data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchBudgetData();
  }, [budgetId, currentOrganization]);

  const tracking: BudgetTracking = useMemo(() => {
    if (!budget || !lineItems.length) {
      return {
        totalEstimated: 0,
        totalActual: 0,
        remainingBudget: budget?.total_budget || 0,
        budgetVariance: 0,
        budgetUtilization: 0,
        isOverBudget: false,
        categoryBreakdown: [],
        alerts: []
      };
    }

    const totalEstimated = lineItems.reduce((sum, item) => sum + item.estimated_cost, 0);
    const totalActual = lineItems.reduce((sum, item) => sum + (item.actual_cost || 0), 0);
    const remainingBudget = budget.total_budget - totalActual;
    const budgetVariance = totalActual - totalEstimated;
    const budgetUtilization = (totalActual / budget.total_budget) * 100;
    const isOverBudget = totalActual > budget.total_budget;

    // Category breakdown
    const categoryMap = new Map<string, BudgetLineItem[]>();
    lineItems.forEach(item => {
      const items = categoryMap.get(item.category) || [];
      items.push(item);
      categoryMap.set(item.category, items);
    });

    const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, items]) => ({
      category,
      estimated: items.reduce((sum, item) => sum + item.estimated_cost, 0),
      actual: items.reduce((sum, item) => sum + (item.actual_cost || 0), 0),
      variance: items.reduce((sum, item) => sum + ((item.actual_cost || 0) - item.estimated_cost), 0),
      items
    }));

    // Generate alerts
    const alerts: BudgetTracking['alerts'] = [];

    if (isOverBudget) {
      alerts.push({
        type: 'over_budget',
        message: `Budget exceeded by ${Math.abs(remainingBudget).toLocaleString('en-US', { style: 'currency', currency: budget.currency })}`,
        severity: 'error',
        amount: Math.abs(remainingBudget)
      });
    } else if (budgetUtilization > 90) {
      alerts.push({
        type: 'approaching_limit',
        message: `Budget utilization at ${budgetUtilization.toFixed(1)}% - approaching limit`,
        severity: 'warning'
      });
    } else if (budgetUtilization > 75) {
      alerts.push({
        type: 'approaching_limit',
        message: `Budget utilization at ${budgetUtilization.toFixed(1)}%`,
        severity: 'info'
      });
    }

    if (Math.abs(budgetVariance) > budget.total_budget * 0.1) {
      alerts.push({
        type: 'variance_warning',
        message: `Significant variance detected: ${budgetVariance > 0 ? 'over' : 'under'} by ${Math.abs(budgetVariance).toLocaleString('en-US', { style: 'currency', currency: budget.currency })}`,
        severity: budgetVariance > 0 ? 'warning' : 'info',
        amount: Math.abs(budgetVariance)
      });
    }

    return {
      totalEstimated,
      totalActual,
      remainingBudget,
      budgetVariance,
      budgetUtilization,
      isOverBudget,
      categoryBreakdown,
      alerts
    };
  }, [budget, lineItems]);

  return {
    tracking,
    budget,
    lineItems,
    loading,
    refetch: fetchBudgetData
  };
};
