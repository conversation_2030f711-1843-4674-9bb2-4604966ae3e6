import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { paymentsApi, type SellerAccount, type OnboardingStatusResponse } from '@/lib/api/payments';
import { toast } from 'sonner';

export const useStripeConnect = () => {
  const [sellerAccount, setSellerAccount] = useState<SellerAccount | null>(null);
  const [needsOnboarding, setNeedsOnboarding] = useState<boolean>(false);
  const [onboardingLink, setOnboardingLink] = useState<string | null>(null);
  const [accountRequirements, setAccountRequirements] = useState<{
    requirements: any;
    accountStatus: string;
    missingRequirements: string[];
  } | null>(null);

  const { execute: executeGetAccount, loading: loadingAccount } = useAsyncOperation<SellerAccount>({
    errorMessage: 'Failed to fetch seller account'
  });

  const { execute: executeCreateAccount, loading: creatingAccount } = useAsyncOperation<{
    accountLink: string;
    accountId: string;
  }>({
    errorMessage: 'Failed to create seller account'
  });

  const { execute: executeCheckOnboarding, loading: checkingOnboarding } = useAsyncOperation<OnboardingStatusResponse>({
    errorMessage: 'Failed to check onboarding status'
  });

  const { execute: executeGetRequirements, loading: loadingRequirements } = useAsyncOperation<{
    requirements: any;
    accountStatus: string;
    missingRequirements: string[];
  }>({
    errorMessage: 'Failed to fetch account requirements'
  });

  const fetchSellerAccount = async () => {
    const { data, success } = await executeGetAccount(async () => {
      const result = await paymentsApi.getSellerAccount();
      if (result.success && result.data) {
        return result.data;
      }
      // Return null if no account exists (this is expected for new sellers)
      if (result.error && typeof result.error === 'string' && result.error.includes('No rows')) {
        return null;
      }
      throw new Error(result.error || 'Failed to fetch seller account');
    });

    if (success) {
      setSellerAccount(data);
    }
  };

  const checkOnboardingStatus = async () => {
    const { data, success } = await executeCheckOnboarding(async () => {
      const result = await paymentsApi.checkOnboardingStatus();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to check onboarding status');
    });

    if (success && data) {
      setNeedsOnboarding(data.needsOnboarding);
      setOnboardingLink(data.accountLink || null);
    }
  };

  const createSellerAccount = async () => {
    const { data, success } = await executeCreateAccount(async () => {
      const result = await paymentsApi.createSellerAccount();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to create seller account');
    });

    if (success && data) {
      toast.success('Seller account created! Redirecting to Stripe onboarding...');
      // Open Stripe onboarding in new tab
      window.open(data.accountLink, '_blank');
      
      // Refresh account data
      await fetchSellerAccount();
      await checkOnboardingStatus();
      
      return data;
    }
  };

  const getAccountRequirements = async () => {
    const { data, success } = await executeGetRequirements(async () => {
      const result = await paymentsApi.getAccountRequirements();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch account requirements');
    });

    if (success && data) {
      setAccountRequirements(data);
    }
  };

  const continueOnboarding = () => {
    if (onboardingLink) {
      window.open(onboardingLink, '_blank');
    } else {
      toast.error('No active onboarding session found. Please create a new seller account.');
    }
  };

  const refreshAccountData = async () => {
    await Promise.all([
      fetchSellerAccount(),
      checkOnboardingStatus(),
      getAccountRequirements()
    ]);
  };

  useEffect(() => {
    fetchSellerAccount();
    checkOnboardingStatus();
  }, []);

  useEffect(() => {
    if (sellerAccount) {
      getAccountRequirements();
    }
  }, [sellerAccount]);

  const isAccountReady = sellerAccount?.onboarding_completed && 
                        sellerAccount?.charges_enabled && 
                        sellerAccount?.payouts_enabled;

  const accountStatusText = sellerAccount ? {
    'pending': 'Account setup in progress',
    'active': 'Account active and ready for payments',
    'restricted': 'Account restricted - additional information required',
    'rejected': 'Account application rejected'
  }[sellerAccount.account_status] || 'Unknown status' : 'No account';

  return {
    sellerAccount,
    needsOnboarding,
    onboardingLink,
    accountRequirements,
    isAccountReady,
    accountStatusText,
    loadingAccount,
    creatingAccount,
    checkingOnboarding,
    loadingRequirements,
    createSellerAccount,
    continueOnboarding,
    refreshAccountData,
    fetchSellerAccount,
    checkOnboardingStatus,
    getAccountRequirements
  };
};
