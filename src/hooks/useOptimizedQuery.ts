import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';

/**
 * Optimized query hook with built-in caching, error handling, and performance optimizations
 */
export const useOptimizedQuery = <TData = unknown, TError = Error>(
  queryKey: string[],
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> & {
    cacheTime?: number;
    staleTime?: number;
    refetchOnWindowFocus?: boolean;
    refetchOnMount?: boolean;
    retry?: number | boolean;
  }
) => {
  const defaultOptions = useMemo(() => ({
    cacheTime: 10 * 60 * 1000, // 10 minutes
    staleTime: 5 * 60 * 1000,  // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true,
    retry: 2,
    ...options
  }), [options]);

  return useQuery({
    queryKey,
    queryFn,
    ...defaultOptions
  });
};

/**
 * Optimized mutation hook with automatic cache invalidation
 */
export const useOptimizedMutation = <TData = unknown, TError = Error, TVariables = void>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: UseMutationOptions<TData, TError, TVariables> & {
    invalidateQueries?: string[][];
    updateQueries?: Array<{
      queryKey: string[];
      updater: (oldData: any, newData: TData) => any;
    }>;
  }
) => {
  const queryClient = useQueryClient();

  const defaultOptions = useMemo(() => ({
    onSuccess: (data: TData, variables: TVariables, context: unknown) => {
      // Invalidate specified queries
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach(queryKey => {
          queryClient.invalidateQueries({ queryKey });
        });
      }

      // Update specified queries
      if (options?.updateQueries) {
        options.updateQueries.forEach(({ queryKey, updater }) => {
          queryClient.setQueryData(queryKey, (oldData: any) => 
            updater(oldData, data)
          );
        });
      }

      // Call original onSuccess if provided
      options?.onSuccess?.(data, variables, context);
    },
    onError: (error: TError, variables: TVariables, context: unknown) => {
      console.error('Mutation error:', error);
      options?.onError?.(error, variables, context);
    },
    ...options
  }), [options, queryClient]);

  return useMutation({
    mutationFn,
    ...defaultOptions
  });
};

/**
 * Paginated query hook with optimized caching
 */
export const usePaginatedQuery = <TData = unknown, TError = Error>(
  baseQueryKey: string[],
  queryFn: (page: number, limit: number) => Promise<{ data: TData[]; total: number }>,
  options?: {
    page?: number;
    limit?: number;
    cacheTime?: number;
    staleTime?: number;
    keepPreviousData?: boolean;
  }
) => {
  const page = options?.page || 1;
  const limit = options?.limit || 20;

  const queryKey = useMemo(() => [...baseQueryKey, 'paginated', page, limit], [baseQueryKey, page, limit]);

  return useOptimizedQuery(
    queryKey,
    () => queryFn(page, limit),
    {
      cacheTime: options?.cacheTime || 15 * 60 * 1000, // 15 minutes for paginated data
      staleTime: options?.staleTime || 10 * 60 * 1000,  // 10 minutes
      keepPreviousData: options?.keepPreviousData ?? true,
    }
  );
};

/**
 * Infinite query hook for large datasets
 */
export const useInfiniteOptimizedQuery = <TData = unknown, TError = Error>(
  queryKey: string[],
  queryFn: ({ pageParam }: { pageParam: number }) => Promise<{ data: TData[]; nextPage?: number; hasMore: boolean }>,
  options?: {
    cacheTime?: number;
    staleTime?: number;
    getNextPageParam?: (lastPage: any, pages: any[]) => number | undefined;
  }
) => {
  const { useInfiniteQuery } = require('@tanstack/react-query');

  return useInfiniteQuery({
    queryKey,
    queryFn,
    getNextPageParam: options?.getNextPageParam || ((lastPage) => 
      lastPage.hasMore ? lastPage.nextPage : undefined
    ),
    cacheTime: options?.cacheTime || 20 * 60 * 1000, // 20 minutes
    staleTime: options?.staleTime || 15 * 60 * 1000,  // 15 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Background sync hook for non-critical data
 */
export const useBackgroundSync = <TData = unknown>(
  queryKey: string[],
  queryFn: () => Promise<TData>,
  options?: {
    syncInterval?: number;
    enabled?: boolean;
  }
) => {
  const syncInterval = options?.syncInterval || 30 * 1000; // 30 seconds
  const enabled = options?.enabled ?? true;

  return useOptimizedQuery(
    queryKey,
    queryFn,
    {
      refetchInterval: enabled ? syncInterval : false,
      refetchIntervalInBackground: true,
      cacheTime: 60 * 60 * 1000, // 1 hour
      staleTime: 30 * 60 * 1000,  // 30 minutes
    }
  );
};

/**
 * Optimized query client utilities
 */
export const useQueryUtils = () => {
  const queryClient = useQueryClient();

  const prefetchQuery = useCallback(async <TData>(
    queryKey: string[],
    queryFn: () => Promise<TData>,
    options?: { staleTime?: number }
  ) => {
    await queryClient.prefetchQuery({
      queryKey,
      queryFn,
      staleTime: options?.staleTime || 5 * 60 * 1000,
    });
  }, [queryClient]);

  const invalidateQueries = useCallback((queryKey: string[]) => {
    queryClient.invalidateQueries({ queryKey });
  }, [queryClient]);

  const setQueryData = useCallback(<TData>(
    queryKey: string[],
    data: TData | ((oldData: TData | undefined) => TData)
  ) => {
    queryClient.setQueryData(queryKey, data);
  }, [queryClient]);

  const removeQueries = useCallback((queryKey: string[]) => {
    queryClient.removeQueries({ queryKey });
  }, [queryClient]);

  const getQueryData = useCallback(<TData>(queryKey: string[]): TData | undefined => {
    return queryClient.getQueryData(queryKey);
  }, [queryClient]);

  const clearCache = useCallback(() => {
    queryClient.clear();
  }, [queryClient]);

  return {
    prefetchQuery,
    invalidateQueries,
    setQueryData,
    removeQueries,
    getQueryData,
    clearCache,
  };
};

/**
 * Request deduplication utility
 */
const requestCache = new Map<string, Promise<any>>();

export const useRequestDeduplication = () => {
  const deduplicateRequest = useCallback(async <TData>(
    key: string,
    requestFn: () => Promise<TData>,
    ttl: number = 1000 // 1 second TTL for deduplication
  ): Promise<TData> => {
    if (requestCache.has(key)) {
      return requestCache.get(key);
    }

    const promise = requestFn();
    requestCache.set(key, promise);

    // Clean up after TTL
    setTimeout(() => {
      requestCache.delete(key);
    }, ttl);

    try {
      const result = await promise;
      return result;
    } catch (error) {
      requestCache.delete(key); // Remove failed requests immediately
      throw error;
    }
  }, []);

  const clearRequestCache = useCallback(() => {
    requestCache.clear();
  }, []);

  return {
    deduplicateRequest,
    clearRequestCache,
  };
};

/**
 * Optimized data fetching patterns
 */
export const useOptimizedDataFetching = () => {
  const queryUtils = useQueryUtils();
  const { deduplicateRequest } = useRequestDeduplication();

  // Parallel data fetching
  const fetchParallel = useCallback(async <T extends Record<string, any>>(
    requests: Record<keyof T, () => Promise<any>>
  ): Promise<T> => {
    const keys = Object.keys(requests) as (keyof T)[];
    const promises = keys.map(key => 
      deduplicateRequest(String(key), requests[key])
    );

    const results = await Promise.all(promises);
    
    return keys.reduce((acc, key, index) => {
      acc[key] = results[index];
      return acc;
    }, {} as T);
  }, [deduplicateRequest]);

  // Sequential data fetching with dependency
  const fetchSequential = useCallback(async <T1, T2>(
    firstRequest: () => Promise<T1>,
    secondRequest: (firstResult: T1) => Promise<T2>
  ): Promise<[T1, T2]> => {
    const firstResult = await firstRequest();
    const secondResult = await secondRequest(firstResult);
    return [firstResult, secondResult];
  }, []);

  return {
    fetchParallel,
    fetchSequential,
    ...queryUtils,
  };
};

export default {
  useOptimizedQuery,
  useOptimizedMutation,
  usePaginatedQuery,
  useInfiniteOptimizedQuery,
  useBackgroundSync,
  useQueryUtils,
  useRequestDeduplication,
  useOptimizedDataFetching,
};
