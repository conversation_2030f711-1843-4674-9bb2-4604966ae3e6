
import { useState, useCallback } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { productionApi } from '@/lib/api';
import type { ProductionScheduleItem, ProductionResource } from '@/lib/api/production';

interface ConflictDetection {
  hasConflicts: boolean;
  conflicts: Array<{
    type: 'time_overlap' | 'resource_double_booking' | 'location_conflict';
    message: string;
    severity: 'warning' | 'error';
    conflictingItems?: ProductionScheduleItem[];
    conflictingResources?: ProductionResource[];
  }>;
}

export const useScheduleConflictDetection = () => {
  const { currentOrganization } = useOrganization();
  const [loading, setLoading] = useState(false);

  const checkConflicts = useCallback(async (
    scheduleItem: Partial<ProductionScheduleItem>,
    excludeItemId?: string
  ): Promise<ConflictDetection> => {
    if (!currentOrganization || !scheduleItem.schedule_id || !scheduleItem.scheduled_date) {
      return { hasConflicts: false, conflicts: [] };
    }

    setLoading(true);
    
    try {
      // Get all schedule items for the same schedule
      const itemsResult = await productionApi.getScheduleItems(scheduleItem.schedule_id);
      
      if (!itemsResult.success) {
        throw new Error('Failed to fetch schedule items');
      }

      const allItems = itemsResult.data || [];
      const conflicts: ConflictDetection['conflicts'] = [];

      // Filter out the current item being checked
      const otherItems = allItems.filter(item => item.id !== excludeItemId);

      // Check for time overlaps on the same date
      const sameDate = otherItems.filter(item => 
        item.scheduled_date === scheduleItem.scheduled_date
      );

      for (const item of sameDate) {
        if (scheduleItem.start_time && scheduleItem.end_time && 
            item.start_time && item.end_time) {
          
          const newStart = new Date(`1970-01-01T${scheduleItem.start_time}`);
          const newEnd = new Date(`1970-01-01T${scheduleItem.end_time}`);
          const existingStart = new Date(`1970-01-01T${item.start_time}`);
          const existingEnd = new Date(`1970-01-01T${item.end_time}`);

          // Check for overlap
          if (newStart < existingEnd && newEnd > existingStart) {
            conflicts.push({
              type: 'time_overlap',
              message: `Time overlap with "${item.title}" (${item.start_time} - ${item.end_time})`,
              severity: 'error',
              conflictingItems: [item]
            });
          }
        }

        // Check for location conflicts
        if (scheduleItem.location_id && item.location_id === scheduleItem.location_id) {
          conflicts.push({
            type: 'location_conflict',
            message: `Location already booked for "${item.title}"`,
            severity: 'warning',
            conflictingItems: [item]
          });
        }
      }

      return {
        hasConflicts: conflicts.length > 0,
        conflicts
      };

    } catch (error) {
      console.error('Error checking schedule conflicts:', error);
      return { hasConflicts: false, conflicts: [] };
    } finally {
      setLoading(false);
    }
  }, [currentOrganization]);

  return {
    checkConflicts,
    loading
  };
};
