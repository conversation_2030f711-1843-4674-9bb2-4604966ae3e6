
import { useSubscriptionAccess } from './useSubscriptionAccess';

export interface ProductionAccess {
  canAccess: boolean;
  tier: string | null;
  loading: boolean;
  subscribed: boolean;
}

export const useProductionAccess = (): ProductionAccess => {
  const { subscribed, tier, loading } = useSubscriptionAccess();

  const getProductionAccess = (): ProductionAccess => {
    if (loading) {
      return {
        canAccess: false,
        tier,
        loading: true,
        subscribed: false
      };
    }

    // Only allow access if user has an active subscription
    if (!subscribed || !tier) {
      return {
        canAccess: false,
        tier,
        loading: false,
        subscribed: false
      };
    }

    // Only Studio and Enterprise tiers have access to Production Tools
    switch (tier) {
      case 'studio':
      case 'enterprise':
        return {
          canAccess: true,
          tier,
          loading: false,
          subscribed: true
        };
      
      default:
        return {
          canAccess: false,
          tier,
          loading: false,
          subscribed: false
        };
    }
  };

  return getProductionAccess();
};
