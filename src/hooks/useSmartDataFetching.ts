
import { useSmartQuery } from './useSmartQuery';
import { useSmartInfiniteQuery } from './useSmartInfiniteQuery';
import { useOptimizedOrgQuery } from './useOptimizedOrgQuery';

/**
 * Smart data fetching hook with optimized query management and index utilization
 */
export const useSmartDataFetching = () => {
  return {
    useSmartQuery,
    useSmartInfiniteQuery,
    useOptimizedOrgQuery,
  };
};
