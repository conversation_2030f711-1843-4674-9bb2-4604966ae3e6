
import { 
  useSubscriptionData,
  useFeatureAccess,
  useUsageTracking,
  useCheckoutOperations,
  usePlanUtilities,
  type SubscriptionPlan,
  type UserSubscription,
  type FeatureAccess
} from './subscription';

export type { SubscriptionPlan, UserSubscription, FeatureAccess };

export const useEnhancedSubscription = () => {
  const {
    subscription,
    plans,
    loading,
    error,
    fetchSubscription
  } = useSubscriptionData();

  const {
    hasFeature,
    getUsageLimit,
    getCurrentUsage,
    isUsageLimitExceeded,
    getRemainingUsage
  } = useFeatureAccess(subscription);

  const { trackFeatureUsage } = useUsageTracking();
  const { createCheckoutSession, openCustomerPortal } = useCheckoutOperations(plans);
  const { getPlanById, getPlanDisplayInfo, comparePlans } = usePlanUtilities(plans);

  return {
    // Data
    subscription,
    plans,
    loading,
    error,
    
    // Actions
    fetchSubscription,
    trackFeatureUsage,
    createCheckoutSession,
    openCustomerPortal,
    
    // Utilities
    getPlanById,
    getPlanDisplayInfo,
    comparePlans,
    hasFeature,
    getUsageLimit,
    getCurrentUsage,
    isUsageLimitExceeded,
    getRemainingUsage,
  };
};
