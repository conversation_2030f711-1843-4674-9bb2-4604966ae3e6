
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';

export interface CoverageUsage {
  id: string;
  generation_count: number;
  daily_limit: number;
  remaining_generations: number;
  loading: boolean;
  error: string | null;
}

export const useCoverageUsage = () => {
  const { currentOrganization } = useOrganization();
  const [usage, setUsage] = useState<CoverageUsage>({
    id: '',
    generation_count: 0,
    daily_limit: 0,
    remaining_generations: 0,
    loading: true,
    error: null
  });

  const refreshUsage = async () => {
    if (!currentOrganization) {
      setUsage(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      setUsage(prev => ({ ...prev, loading: true, error: null }));

      const { data, error } = await supabase
        .rpc('get_or_create_daily_usage', {
          target_org_id: currentOrganization.id
        });

      if (error) {
        console.error('Failed to fetch coverage usage:', error);
        setUsage(prev => ({ 
          ...prev, 
          loading: false, 
          error: error.message 
        }));
        return;
      }

      if (data && data.length > 0) {
        const result = data[0];
        setUsage({
          id: result.id,
          generation_count: result.generation_count,
          daily_limit: result.daily_limit,
          remaining_generations: result.remaining_generations,
          loading: false,
          error: null
        });
      } else {
        setUsage(prev => ({ 
          ...prev, 
          loading: false, 
          error: 'No usage data available' 
        }));
      }
    } catch (error) {
      console.error('Error fetching coverage usage:', error);
      setUsage(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }));
    }
  };

  useEffect(() => {
    refreshUsage();
  }, [currentOrganization]);

  return {
    usage,
    refreshUsage
  };
};
