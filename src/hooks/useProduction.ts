
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { productionApi } from '@/lib/api';
import { useErrorHandler } from './useErrorHandler';
import type { 
  ProductionSchedule, 
  ProductionBudget, 
  ProductionResource, 
  ProductionReport 
} from '@/lib/api/production';

// Define input types that exclude org_id and user_id since the hook adds them
type ScheduleInput = Omit<ProductionSchedule, 'id' | 'created_at' | 'updated_at' | 'org_id' | 'user_id'>;
type BudgetInput = Omit<ProductionBudget, 'id' | 'created_at' | 'updated_at' | 'org_id' | 'user_id'>;
type ResourceInput = Omit<ProductionResource, 'id' | 'created_at' | 'updated_at' | 'org_id' | 'user_id'>;
type ReportInput = Omit<ProductionReport, 'id' | 'created_at' | 'updated_at' | 'org_id' | 'user_id'>;

export const useProduction = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { handleError } = useErrorHandler();

  const [schedules, setSchedules] = useState<ProductionSchedule[]>([]);
  const [budgets, setBudgets] = useState<ProductionBudget[]>([]);
  const [resources, setResources] = useState<ProductionResource[]>([]);
  const [reports, setReports] = useState<ProductionReport[]>([]);
  const [loading, setLoading] = useState(false);

  // Fetch all production data
  const fetchProductionData = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const [schedulesResult, budgetsResult, resourcesResult, reportsResult] = await Promise.all([
        productionApi.getSchedules(currentOrganization.id),
        productionApi.getBudgets(currentOrganization.id),
        productionApi.getResources(currentOrganization.id),
        productionApi.getReports(currentOrganization.id)
      ]);

      if (schedulesResult.success && schedulesResult.data) {
        setSchedules(schedulesResult.data);
      }
      if (budgetsResult.success && budgetsResult.data) {
        setBudgets(budgetsResult.data);
      }
      if (resourcesResult.success && resourcesResult.data) {
        setResources(resourcesResult.data);
      }
      if (reportsResult.success && reportsResult.data) {
        setReports(reportsResult.data);
      }
    } catch (error) {
      handleError(error, 'Failed to fetch production data');
    } finally {
      setLoading(false);
    }
  };

  // Create new schedule
  const createSchedule = async (scheduleData: ScheduleInput) => {
    if (!currentOrganization || !user) return;

    try {
      const result = await productionApi.createSchedule({
        ...scheduleData,
        org_id: currentOrganization.id,
        user_id: user.id
      });

      if (result.success && result.data) {
        setSchedules(prev => [result.data!, ...prev]);
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create schedule');
      }
    } catch (error) {
      handleError(error, 'Failed to create schedule');
    }
  };

  // Create new budget
  const createBudget = async (budgetData: BudgetInput) => {
    if (!currentOrganization || !user) return;

    try {
      const result = await productionApi.createBudget({
        ...budgetData,
        org_id: currentOrganization.id,
        user_id: user.id
      });

      if (result.success && result.data) {
        setBudgets(prev => [result.data!, ...prev]);
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create budget');
      }
    } catch (error) {
      handleError(error, 'Failed to create budget');
    }
  };

  // Create new resource
  const createResource = async (resourceData: ResourceInput) => {
    if (!currentOrganization || !user) return;

    try {
      const result = await productionApi.createResource({
        ...resourceData,
        org_id: currentOrganization.id,
        user_id: user.id
      });

      if (result.success && result.data) {
        setResources(prev => [result.data!, ...prev]);
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create resource');
      }
    } catch (error) {
      handleError(error, 'Failed to create resource');
    }
  };

  // Create new report
  const createReport = async (reportData: ReportInput) => {
    if (!currentOrganization || !user) return;

    try {
      const result = await productionApi.createReport({
        ...reportData,
        org_id: currentOrganization.id,
        user_id: user.id
      });

      if (result.success && result.data) {
        setReports(prev => [result.data!, ...prev]);
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create report');
      }
    } catch (error) {
      handleError(error, 'Failed to create report');
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      fetchProductionData();
    }
  }, [currentOrganization]);

  return {
    schedules,
    budgets,
    resources,
    reports,
    loading,
    createSchedule,
    createBudget,
    createResource,
    createReport,
    refetch: fetchProductionData
  };
};
