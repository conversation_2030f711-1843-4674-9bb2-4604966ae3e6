
import { useCallback } from 'react';
import { ErrorHandler, type AppError } from '@/lib/error-handling/ErrorHandler';

/**
 * Custom hook for consistent error handling across the application
 * Uses the standardized ErrorHandler system without Sentry integration
 */
export const useErrorHandler = () => {
  const handleError = useCallback((error: unknown, customUserMessage?: string) => {
    if (typeof error === 'string') {
      return ErrorHandler.handleError(error);
    }
    
    if (error instanceof Error) {
      return ErrorHandler.handleError(error);
    }
    
    // Handle AppError objects
    if (error && typeof error === 'object' && 'code' in error) {
      return ErrorHandler.handleError(error as AppError);
    }
    
    // Fallback for unknown error types
    const unknownError = ErrorHandler.createError(
      'UNKNOWN_ERROR', 
      { originalError: error },
      customUserMessage
    );
    
    return ErrorHandler.handleError(unknownError);
  }, []);

  const handleAsyncError = useCallback(
    <T>(asyncFn: () => Promise<T>, errorCode?: string, customUserMessage?: string) => {
      return async (): Promise<T | undefined> => {
        try {
          return await asyncFn();
        } catch (error) {
          const appError = errorCode 
            ? ErrorHandler.createError(errorCode, { originalError: error }, customUserMessage)
            : handleError(error, customUserMessage);
          
          return undefined;
        }
      };
    },
    [handleError]
  );

  const createError = useCallback((code: string, details?: Record<string, any>, customUserMessage?: string) => {
    return ErrorHandler.createError(code, details, customUserMessage);
  }, []);

  return {
    handleError,
    handleAsyncError,
    createError,
    formatValidationErrors: ErrorHandler.formatValidationErrors
  };
};
