import { useCallback, useMemo } from 'react';
import { storyboardsApi, type Storyboard, type StoryboardPanel } from '@/lib/api/storyboards';
import { useOptimizedQuery, useOptimizedMutation, usePaginatedQuery, useQueryUtils } from './useOptimizedQuery';
import { useToast } from './use-toast';

/**
 * Optimized storyboard data management with React Query
 */
export const useOptimizedStoryboards = (orgId?: string) => {
  const { toast } = useToast();
  const { invalidateQueries, setQueryData } = useQueryUtils();

  // Query keys
  const storyboardsQueryKey = useMemo(() => ['storyboards', orgId], [orgId]);
  const usageQueryKey = useMemo(() => ['storyboard-usage', orgId], [orgId]);

  // Fetch storyboards with caching
  const {
    data: storyboards = [],
    isLoading: isLoadingStoryboards,
    error: storyboardsError,
    refetch: refetchStoryboards
  } = useOptimizedQuery(
    storyboardsQueryKey,
    () => storyboardsApi.getStoryboards(),
    {
      enabled: Boolean(orgId),
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      select: (data) => data.success ? data.data || [] : [],
    }
  );

  // Fetch usage quota with background sync
  const {
    data: usage,
    isLoading: isLoadingUsage,
    error: usageError
  } = useOptimizedQuery(
    usageQueryKey,
    async () => {
      // Mock usage data - replace with actual API call
      return {
        dailyLimit: 50,
        remaining: 35,
        used: 15,
        resetTime: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };
    },
    {
      enabled: Boolean(orgId),
      staleTime: 2 * 60 * 1000, // 2 minutes
      refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
    }
  );

  // Create storyboard mutation
  const createStoryboardMutation = useOptimizedMutation(
    (storyboardData: any) => storyboardsApi.createStoryboard(storyboardData),
    {
      invalidateQueries: [storyboardsQueryKey, usageQueryKey],
      onSuccess: (result) => {
        if (result.success) {
          toast({
            title: "Success",
            description: "Storyboard created successfully",
          });
        } else {
          throw new Error(result.error || 'Failed to create storyboard');
        }
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to create storyboard",
          variant: "destructive",
        });
      }
    }
  );

  // Update storyboard mutation
  const updateStoryboardMutation = useOptimizedMutation(
    ({ id, data }: { id: string; data: Partial<Storyboard> }) => 
      storyboardsApi.updateStoryboard(id, data),
    {
      updateQueries: [{
        queryKey: storyboardsQueryKey,
        updater: (oldData: Storyboard[], newData: any) => {
          if (!newData.success || !oldData) return oldData;
          return oldData.map(storyboard => 
            storyboard.id === newData.data.id ? { ...storyboard, ...newData.data } : storyboard
          );
        }
      }],
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Storyboard updated successfully",
        });
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to update storyboard",
          variant: "destructive",
        });
      }
    }
  );

  // Delete storyboard mutation
  const deleteStoryboardMutation = useOptimizedMutation(
    (id: string) => storyboardsApi.deleteStoryboard(id),
    {
      updateQueries: [{
        queryKey: storyboardsQueryKey,
        updater: (oldData: Storyboard[], deletedId: string) => {
          if (!oldData) return oldData;
          return oldData.filter(storyboard => storyboard.id !== deletedId);
        }
      }],
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Storyboard deleted successfully",
        });
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to delete storyboard",
          variant: "destructive",
        });
      }
    }
  );

  // Search storyboards with debouncing
  const searchStoryboards = useCallback(async (query: string) => {
    if (!orgId || !query.trim()) return [];
    
    try {
      const result = await storyboardsApi.searchStoryboards(orgId, query);
      return result.success ? result.data || [] : [];
    } catch (error) {
      console.error('Search failed:', error);
      return [];
    }
  }, [orgId]);

  // Optimized panel management
  const useStoryboardPanels = (storyboardId?: string) => {
    const panelsQueryKey = useMemo(() => 
      ['storyboard-panels', storyboardId], [storyboardId]
    );

    const {
      data: panels = [],
      isLoading: isLoadingPanels,
      error: panelsError
    } = useOptimizedQuery(
      panelsQueryKey,
      () => storyboardsApi.getStoryboardPanels(storyboardId!),
      {
        enabled: Boolean(storyboardId),
        staleTime: 3 * 60 * 1000, // 3 minutes
        select: (data) => data.success ? data.data || [] : [],
      }
    );

    // Create panel mutation
    const createPanelMutation = useOptimizedMutation(
      (panelData: any) => storyboardsApi.createStoryboardPanel(panelData),
      {
        updateQueries: [{
          queryKey: panelsQueryKey,
          updater: (oldData: StoryboardPanel[], newData: any) => {
            if (!newData.success || !oldData) return oldData;
            return [...oldData, newData.data].sort((a, b) => a.order_index - b.order_index);
          }
        }],
        invalidateQueries: [usageQueryKey],
      }
    );

    // Update panel mutation
    const updatePanelMutation = useOptimizedMutation(
      ({ id, data }: { id: string; data: Partial<StoryboardPanel> }) =>
        storyboardsApi.updateStoryboardPanel(id, data),
      {
        updateQueries: [{
          queryKey: panelsQueryKey,
          updater: (oldData: StoryboardPanel[], newData: any) => {
            if (!newData.success || !oldData) return oldData;
            return oldData.map(panel => 
              panel.id === newData.data.id ? { ...panel, ...newData.data } : panel
            );
          }
        }],
      }
    );

    // Delete panel mutation
    const deletePanelMutation = useOptimizedMutation(
      (id: string) => storyboardsApi.deleteStoryboardPanel(id),
      {
        updateQueries: [{
          queryKey: panelsQueryKey,
          updater: (oldData: StoryboardPanel[], deletedId: string) => {
            if (!oldData) return oldData;
            return oldData.filter(panel => panel.id !== deletedId);
          }
        }],
        invalidateQueries: [usageQueryKey],
      }
    );

    return {
      panels,
      isLoadingPanels,
      panelsError,
      createPanel: createPanelMutation.mutate,
      updatePanel: updatePanelMutation.mutate,
      deletePanel: deletePanelMutation.mutate,
      isCreatingPanel: createPanelMutation.isPending,
      isUpdatingPanel: updatePanelMutation.isPending,
      isDeletingPanel: deletePanelMutation.isPending,
    };
  };

  // Memoized actions
  const actions = useMemo(() => ({
    createStoryboard: createStoryboardMutation.mutate,
    updateStoryboard: updateStoryboardMutation.mutate,
    deleteStoryboard: deleteStoryboardMutation.mutate,
    searchStoryboards,
    refetchStoryboards,
  }), [
    createStoryboardMutation.mutate,
    updateStoryboardMutation.mutate,
    deleteStoryboardMutation.mutate,
    searchStoryboards,
    refetchStoryboards,
  ]);

  // Memoized loading states
  const loadingStates = useMemo(() => ({
    isLoadingStoryboards,
    isLoadingUsage,
    isCreating: createStoryboardMutation.isPending,
    isUpdating: updateStoryboardMutation.isPending,
    isDeleting: deleteStoryboardMutation.isPending,
  }), [
    isLoadingStoryboards,
    isLoadingUsage,
    createStoryboardMutation.isPending,
    updateStoryboardMutation.isPending,
    deleteStoryboardMutation.isPending,
  ]);

  // Memoized error states
  const errorStates = useMemo(() => ({
    storyboardsError,
    usageError,
    createError: createStoryboardMutation.error,
    updateError: updateStoryboardMutation.error,
    deleteError: deleteStoryboardMutation.error,
  }), [
    storyboardsError,
    usageError,
    createStoryboardMutation.error,
    updateStoryboardMutation.error,
    deleteStoryboardMutation.error,
  ]);

  return {
    // Data
    storyboards,
    usage,
    
    // Loading states
    ...loadingStates,
    
    // Error states
    ...errorStates,
    
    // Actions
    ...actions,
    
    // Panel management
    useStoryboardPanels,
  };
};

export default useOptimizedStoryboards;
