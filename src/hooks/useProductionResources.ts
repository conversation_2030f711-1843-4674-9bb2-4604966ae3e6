
import { resourcesApi } from '@/lib/api/production';
import { useProductionDataManager } from './useProductionDataManager';
import { createResourceSchema, type CreateResourceInput } from '@/features/production/validation/schemas';
import type { ProductionResource } from '@/lib/api/production';

export const useProductionResources = () => {
  const baseHook = useProductionDataManager<ProductionResource, CreateResourceInput>({
    entityName: 'resource',
    apiService: {
      getItems: resourcesApi.getResources,
      createItem: resourcesApi.createResource
    },
    validationSchema: createResourceSchema,
    getDisplayName: (resource) => resource.name
  });

  return {
    resources: baseHook.items,
    loading: baseHook.loading,
    creating: baseHook.creating,
    errors: baseHook.errors,
    createResource: baseHook.createItem,
    fetchResources: baseHook.fetchItems,
    clearErrors: baseHook.clearErrors
  };
};
