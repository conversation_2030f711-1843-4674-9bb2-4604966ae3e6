import { useCallback, useState, useEffect } from 'react';
import { checkUserRateLimit, rateLimitConfigs, RateLimitError } from '@/lib/security/rateLimiter';
import { useToast } from './use-toast';

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  burstSize?: number;
  keyGenerator?: (identifier: string) => string;
}

interface RateLimitStatus {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * React hook for rate limiting API calls and user actions
 */
export function useRateLimit(
  configKey: keyof typeof rateLimitConfigs | RateLimitConfig,
  options?: {
    showToast?: boolean;
    autoRetry?: boolean;
    retryDelay?: number;
  }
) {
  const { toast } = useToast();
  const [status, setStatus] = useState<RateLimitStatus | null>(null);
  const [isLimited, setIsLimited] = useState(false);

  const config = typeof configKey === 'string' ? rateLimitConfigs[configKey] : configKey;
  const showToast = options?.showToast ?? true;
  const autoRetry = options?.autoRetry ?? false;
  const retryDelay = options?.retryDelay ?? 1000;

  /**
   * Check if an action is allowed under rate limiting
   */
  const checkLimit = useCallback(async (): Promise<RateLimitStatus> => {
    try {
      const result = await checkUserRateLimit(config);
      setStatus(result);
      setIsLimited(!result.allowed);

      if (!result.allowed && showToast) {
        const retryTime = result.retryAfter 
          ? new Date(Date.now() + result.retryAfter).toLocaleTimeString()
          : new Date(result.resetTime).toLocaleTimeString();

        toast({
          title: "Rate Limit Exceeded",
          description: `Too many requests. Please try again at ${retryTime}.`,
          variant: "destructive",
        });
      }

      return result;
    } catch (error) {
      console.error('Rate limit check failed:', error);
      // Allow the action if rate limit check fails
      const fallbackResult: RateLimitStatus = {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: Date.now() + config.windowMs,
      };
      setStatus(fallbackResult);
      setIsLimited(false);
      return fallbackResult;
    }
  }, [config, showToast, toast]);

  /**
   * Execute a function with rate limiting
   */
  const executeWithLimit = useCallback(async <T>(
    fn: () => Promise<T>,
    options?: { skipCheck?: boolean }
  ): Promise<T> => {
    if (!options?.skipCheck) {
      const limitStatus = await checkLimit();
      if (!limitStatus.allowed) {
        throw new RateLimitError(
          limitStatus.remaining,
          limitStatus.resetTime,
          'Rate limit exceeded'
        );
      }
    }

    try {
      const result = await fn();
      return result;
    } catch (error) {
      // If it's a rate limit error from the server, update our local status
      if (error instanceof Error && error.message.includes('rate limit')) {
        setIsLimited(true);
        if (showToast) {
          toast({
            title: "Rate Limit Exceeded",
            description: "Please slow down and try again in a moment.",
            variant: "destructive",
          });
        }
      }
      throw error;
    }
  }, [checkLimit, showToast, toast]);

  /**
   * Execute with automatic retry on rate limit
   */
  const executeWithRetry = useCallback(async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3
  ): Promise<T> => {
    let retries = 0;

    while (retries <= maxRetries) {
      try {
        return await executeWithLimit(fn);
      } catch (error) {
        if (error instanceof RateLimitError && retries < maxRetries) {
          retries++;
          const delay = error.resetTime - Date.now();
          
          if (showToast) {
            toast({
              title: "Rate Limited - Retrying",
              description: `Attempt ${retries}/${maxRetries}. Waiting ${Math.ceil(delay / 1000)}s...`,
            });
          }

          await new Promise(resolve => setTimeout(resolve, Math.max(delay, retryDelay)));
          continue;
        }
        throw error;
      }
    }

    throw new Error('Max retries exceeded');
  }, [executeWithLimit, showToast, toast, retryDelay]);

  /**
   * Get current rate limit status without making a request
   */
  const getStatus = useCallback(() => status, [status]);

  /**
   * Reset rate limit status (useful for testing or manual reset)
   */
  const resetStatus = useCallback(() => {
    setStatus(null);
    setIsLimited(false);
  }, []);

  // Auto-retry functionality
  useEffect(() => {
    if (autoRetry && isLimited && status?.retryAfter) {
      const timer = setTimeout(() => {
        setIsLimited(false);
        setStatus(null);
      }, status.retryAfter);

      return () => clearTimeout(timer);
    }
  }, [autoRetry, isLimited, status?.retryAfter]);

  return {
    // Status
    isLimited,
    status,
    remaining: status?.remaining ?? config.maxRequests,
    resetTime: status?.resetTime,
    retryAfter: status?.retryAfter,

    // Actions
    checkLimit,
    executeWithLimit,
    executeWithRetry,
    getStatus,
    resetStatus,

    // Utilities
    canExecute: !isLimited,
    timeUntilReset: status?.resetTime ? Math.max(0, status.resetTime - Date.now()) : 0,
  };
}

/**
 * Hook for rate limiting specific API endpoints
 */
export function useAPIRateLimit(endpoint: keyof typeof rateLimitConfigs) {
  return useRateLimit(endpoint, { showToast: true });
}

/**
 * Hook for rate limiting user actions (like button clicks)
 */
export function useActionRateLimit(
  config: RateLimitConfig,
  options?: { showToast?: boolean }
) {
  return useRateLimit(config, { 
    showToast: options?.showToast ?? false,
    autoRetry: false 
  });
}

/**
 * Hook for rate limiting with automatic retry
 */
export function useRateLimitWithRetry(
  configKey: keyof typeof rateLimitConfigs,
  maxRetries: number = 3
) {
  const rateLimit = useRateLimit(configKey, { 
    showToast: true, 
    autoRetry: true 
  });

  const executeWithRetry = useCallback(async <T>(
    fn: () => Promise<T>
  ): Promise<T> => {
    return rateLimit.executeWithRetry(fn, maxRetries);
  }, [rateLimit, maxRetries]);

  return {
    ...rateLimit,
    executeWithRetry,
  };
}

export default useRateLimit;
