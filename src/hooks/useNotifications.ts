
import { useState, useEffect, useCallback } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { notificationsApi, type Notification } from '@/lib/api/notifications';
import { supabase } from '@/integrations/supabase/client';

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  
  const { execute: executeGetNotifications, loading } = useAsyncOperation<Notification[]>({
    errorMessage: 'Failed to fetch notifications'
  });

  const { execute: executeMarkAsRead } = useAsyncOperation<Notification>({
    errorMessage: 'Failed to mark notification as read'
  });

  const { execute: executeMarkAllAsRead } = useAsyncOperation<void>({
    errorMessage: 'Failed to mark all notifications as read'
  });

  const { execute: executeGetUnreadCount } = useAsyncOperation<number>({
    errorMessage: 'Failed to get unread count'
  });

  const fetchNotifications = useCallback(async () => {
    const { data, success } = await executeGetNotifications(async () => {
      const result = await notificationsApi.getMyNotifications();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch notifications');
    });

    if (success && data) {
      setNotifications(data);
      setUnreadCount(data.filter(n => !n.read).length);
    }
  }, [executeGetNotifications]);

  const markAsRead = async (notificationId: string) => {
    const { success } = await executeMarkAsRead(async () => {
      const result = await notificationsApi.markAsRead(notificationId);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to mark notification as read');
    });

    if (success) {
      setNotifications(prev => 
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    }

    return success;
  };

  const markAllAsRead = async () => {
    const { success } = await executeMarkAllAsRead(async () => {
      const result = await notificationsApi.markAllAsRead();
      if (result.success) {
        return undefined;
      }
      throw new Error(result.error || 'Failed to mark all notifications as read');
    });

    if (success) {
      setNotifications(prev => prev.map(n => ({ ...n, read: true })));
      setUnreadCount(0);
    }

    return success;
  };

  // Set up real-time subscription for new notifications
  useEffect(() => {
    const setupRealtime = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const channel = supabase
        .channel('notifications')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            const newNotification = payload.new as Notification;
            setNotifications(prev => [newNotification, ...prev]);
            setUnreadCount(prev => prev + 1);
          }
        )
        .subscribe();

      return () => {
        supabase.removeChannel(channel);
      };
    };

    setupRealtime();
  }, []);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    markAsRead,
    markAllAsRead,
    refreshNotifications: fetchNotifications
  };
};
