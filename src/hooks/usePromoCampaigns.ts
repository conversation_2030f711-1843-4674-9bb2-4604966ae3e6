
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { promoCampaignsApi, type PromoCampaign, type PromoCampaignInsert, type PromoCampaignUpdate } from '@/lib/api/promo-campaigns';

export const usePromoCampaigns = () => {
  const [campaigns, setCampaigns] = useState<PromoCampaign[]>([]);
  const [loading, setLoading] = useState(true);

  const { execute: executeGetCampaigns } = useAsyncOperation<PromoCampaign[]>({
    errorMessage: 'Failed to fetch campaigns'
  });

  const { execute: executeCreateCampaign, loading: creating } = useAsyncOperation<PromoCampaign>({
    successMessage: 'Campaign created successfully',
    errorMessage: 'Failed to create campaign'
  });

  const { execute: executeUpdateCampaign, loading: updating } = useAsyncOperation<PromoCampaign>({
    successMessage: 'Campaign updated successfully',
    errorMessage: 'Failed to update campaign'
  });

  const { execute: executeDeleteCampaign, loading: deleting } = useAsyncOperation<void>({
    successMessage: 'Campaign deleted successfully',
    errorMessage: 'Failed to delete campaign'
  });

  const fetchCampaigns = async () => {
    setLoading(true);
    const { data, success } = await executeGetCampaigns(async () => {
      const result = await promoCampaignsApi.getCampaigns();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch campaigns');
    });

    if (success && data) {
      setCampaigns(data);
    }
    setLoading(false);
  };

  const createCampaign = async (campaign: PromoCampaignInsert) => {
    const { data, success } = await executeCreateCampaign(async () => {
      const result = await promoCampaignsApi.createCampaign(campaign);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to create campaign');
    });

    if (success && data) {
      setCampaigns(prev => [data, ...prev]);
      return data;
    }
    return null;
  };

  const updateCampaign = async (id: string, updates: PromoCampaignUpdate) => {
    const { data, success } = await executeUpdateCampaign(async () => {
      const result = await promoCampaignsApi.updateCampaign(id, updates);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to update campaign');
    });

    if (success && data) {
      setCampaigns(prev => prev.map(c => c.id === id ? data : c));
      return data;
    }
    return null;
  };

  const deleteCampaign = async (id: string) => {
    const { success } = await executeDeleteCampaign(async () => {
      const result = await promoCampaignsApi.deleteCampaign(id);
      if (result.success) {
        return;
      }
      throw new Error(result.error || 'Failed to delete campaign');
    });

    if (success) {
      setCampaigns(prev => prev.filter(c => c.id !== id));
    }
  };

  useEffect(() => {
    fetchCampaigns();
  }, []);

  return {
    campaigns,
    loading,
    creating,
    updating,
    deleting,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    refetch: fetchCampaigns
  };
};

export const usePromoCampaignAnalytics = () => {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  const { execute: executeGetAnalytics } = useAsyncOperation({
    errorMessage: 'Failed to fetch analytics'
  });

  const fetchAnalytics = async () => {
    setLoading(true);
    const { data, success } = await executeGetAnalytics(async () => {
      const result = await promoCampaignsApi.getAnalyticsSummary();
      if (result.success && result.data) {
        setAnalytics(result.data);
        return;
      }
      throw new Error(result.error || 'Failed to fetch analytics');
    });

    setLoading(false);
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  return {
    analytics,
    loading,
    refetch: fetchAnalytics
  };
};
