
import { useState, useEffect } from 'react';
import { scenesApi } from '@/lib/api';
import { useOrganization } from '@/contexts/OrganizationContext';

interface Scene {
  id: string;
  title: string;
  description?: string;
  content?: string;
  act?: number;
  order_index: number;
  duration_minutes?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  locations?: { name: string };
  scene_characters?: Array<{
    id: string;
    is_main_character: boolean;
    characters: { name: string };
  }>;
}

export const useScenes = () => {
  const { currentOrganization } = useOrganization();
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchScenes = async () => {
    if (!currentOrganization) return;
    
    setLoading(true);
    try {
      const result = await scenesApi.getScenes(currentOrganization.id);
      if (result.success && result.data) {
        setScenes(result.data);
      }
    } catch (error) {
      console.error('Error fetching scenes:', error);
    } finally {
      setLoading(false);
    }
  };

  const createScene = async (data: { 
    title: string;
    description?: string;
    content?: string;
    act?: number;
    location_id?: string;
  }) => {
    if (!currentOrganization) return;

    try {
      // Get the current max order_index for the act
      const actScenes = scenes.filter(s => s.act === data.act);
      const maxOrder = Math.max(...actScenes.map(s => s.order_index), -1);

      const result = await scenesApi.createScene({
        ...data,
        org_id: currentOrganization.id,
        order_index: maxOrder + 1
      });
      
      if (result.success) {
        await fetchScenes();
        return result;
      }
    } catch (error) {
      console.error('Error creating scene:', error);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      fetchScenes();
    }
  }, [currentOrganization]);

  return {
    scenes,
    loading,
    fetchScenes,
    createScene
  };
};
