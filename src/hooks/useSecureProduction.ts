
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { secureSchedulesApi } from '@/lib/api/production/secureSchedules';
import { SecurityMiddleware } from '@/lib/security/securityMiddleware';
import { RateLimitError } from '@/lib/security/rateLimiter';
import { useErrorHandler } from './useErrorHandler';
import { toast } from '@/hooks/use-toast';
import type { ProductionSchedule } from '@/lib/api/production';

export const useSecureProduction = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { handleError } = useErrorHandler();

  const [schedules, setSchedules] = useState<ProductionSchedule[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);

  // Fetch schedules with security middleware
  const fetchSchedules = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const result = await secureSchedulesApi.getSchedules(currentOrganization.id);
      
      if (result.success && result.data) {
        setSchedules(result.data);
      } else if (result.error) {
        handleError(result.error, 'Failed to fetch schedules');
      }
    } catch (error) {
      if (error instanceof RateLimitError) {
        toast({
          title: "Rate Limit Exceeded",
          description: error.message,
          variant: "destructive"
        });
      } else {
        handleError(error, 'Failed to fetch schedules');
      }
    } finally {
      setLoading(false);
    }
  };

  // Create schedule with enhanced security
  const createSchedule = async (scheduleData: any) => {
    if (!currentOrganization || !user) return null;

    setCreating(true);
    try {
      const dataWithIds = {
        ...scheduleData,
        org_id: currentOrganization.id,
        user_id: user.id
      };

      const result = await secureSchedulesApi.createSchedule(dataWithIds);
      
      if (result.success && result.data) {
        setSchedules(prev => [result.data!, ...prev]);
        toast({
          title: "Schedule Created",
          description: `"${result.data.title}" has been created successfully`,
        });
        return result.data;
      } else if (result.error) {
        toast({
          title: "Creation Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      if (error instanceof RateLimitError) {
        toast({
          title: "Rate Limit Exceeded",
          description: error.message,
          variant: "destructive"
        });
      } else {
        handleError(error, 'Failed to create schedule');
      }
    } finally {
      setCreating(false);
    }

    return null;
  };

  // Update schedule with validation
  const updateSchedule = async (id: string, updates: any) => {
    try {
      const result = await secureSchedulesApi.updateSchedule(id, updates);
      
      if (result.success && result.data) {
        setSchedules(prev => prev.map(schedule => 
          schedule.id === id ? result.data! : schedule
        ));
        toast({
          title: "Schedule Updated",
          description: "Schedule has been updated successfully",
        });
        return result.data;
      } else if (result.error) {
        toast({
          title: "Update Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      if (error instanceof RateLimitError) {
        toast({
          title: "Rate Limit Exceeded",
          description: error.message,
          variant: "destructive"
        });
      } else {
        handleError(error, 'Failed to update schedule');
      }
    }

    return null;
  };

  // Delete schedule
  const deleteSchedule = async (id: string) => {
    try {
      const result = await secureSchedulesApi.deleteSchedule(id);
      
      if (result.success) {
        setSchedules(prev => prev.filter(schedule => schedule.id !== id));
        toast({
          title: "Schedule Deleted",
          description: "Schedule has been deleted successfully",
        });
        return true;
      } else if (result.error) {
        toast({
          title: "Deletion Failed",
          description: result.error,
          variant: "destructive"
        });
      }
    } catch (error) {
      if (error instanceof RateLimitError) {
        toast({
          title: "Rate Limit Exceeded",
          description: error.message,
          variant: "destructive"
        });
      } else {
        handleError(error, 'Failed to delete schedule');
      }
    }

    return false;
  };

  useEffect(() => {
    if (currentOrganization) {
      fetchSchedules();
    }
  }, [currentOrganization]);

  return {
    schedules,
    loading,
    creating,
    createSchedule,
    updateSchedule,
    deleteSchedule,
    refetch: fetchSchedules
  };
};
