
import { useSubscriptionAccess } from './useSubscriptionAccess';

export interface CoverageAccess {
  canAccess: boolean;
  tier: string | null;
  loading: boolean;
  subscribed: boolean;
  dailyLimit: number;
  fidelityAccess: {
    basic: boolean;
    standard: boolean;
    premium: boolean;
  };
}

export const useCoverageAccess = (): CoverageAccess => {
  const { subscribed, tier, loading } = useSubscriptionAccess();

  const getCoverageAccess = (): CoverageAccess => {
    if (loading) {
      return {
        canAccess: false,
        tier,
        loading: true,
        subscribed: false,
        dailyLimit: 0,
        fidelityAccess: { basic: false, standard: false, premium: false }
      };
    }

    // Only allow access if user has an active subscription
    if (!subscribed || !tier) {
      return {
        canAccess: false,
        tier,
        loading: false,
        subscribed: false,
        dailyLimit: 0,
        fidelityAccess: { basic: false, standard: false, premium: false }
      };
    }

    // Define tier-specific access and limits
    switch (tier) {
      case 'pro-solo':
        return {
          canAccess: true,
          tier,
          loading: false,
          subscribed: true,
          dailyLimit: 5, // 5 generations per day
          fidelityAccess: { basic: true, standard: true, premium: false }
        };
      
      case 'pro-team':
        return {
          canAccess: true,
          tier,
          loading: false,
          subscribed: true,
          dailyLimit: 15, // 15 generations per day for teams
          fidelityAccess: { basic: true, standard: true, premium: true }
        };
      
      case 'studio':
        return {
          canAccess: true,
          tier,
          loading: false,
          subscribed: true,
          dailyLimit: 50, // 50 generations per day for studios
          fidelityAccess: { basic: true, standard: true, premium: true }
        };
        
      case 'enterprise':
        return {
          canAccess: true,
          tier,
          loading: false,
          subscribed: true,
          dailyLimit: 200, // 200 generations per day for enterprise
          fidelityAccess: { basic: true, standard: true, premium: true }
        };
      
      default:
        return {
          canAccess: false,
          tier,
          loading: false,
          subscribed: false,
          dailyLimit: 0,
          fidelityAccess: { basic: false, standard: false, premium: false }
        };
    }
  };

  return getCoverageAccess();
};
