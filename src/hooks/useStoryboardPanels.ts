
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { useError<PERSON><PERSON><PERSON> } from "@/hooks/useErrorHandler";
import { storyboardsApi, type Storyboard, type StoryboardPanel } from "@/lib/api/storyboards";

export function useStoryboardPanels(currentOrganization: any) {
  const { handleError } = useErrorHandler();
  const { toast } = useToast();

  const [storyboards, setStoryboards] = useState<Storyboard[]>([]);
  const [selectedStoryboard, setSelectedStoryboard] = useState<Storyboard | null>(null);
  const [panels, setPanels] = useState<StoryboardPanel[]>([]);
  const [loading, setLoading] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newStoryboard, setNewStoryboard] = useState({
    title: "",
    description: "",
    fidelity: "Sketch",
  });

  const loadStoryboards = async () => {
    if (!currentOrganization) return;
    setLoading(true);
    try {
      const result = await storyboardsApi.getStoryboards();
      if (result.success && result.data) {
        setStoryboards(result.data);
      } else if (result.error) {
        handleError(result.error, "Failed to load storyboards");
      }
    } catch (error) {
      handleError(error, "Failed to load storyboards");
    } finally {
      setLoading(false);
    }
  };

  const loadPanels = async (storyboardId: string) => {
    try {
      const result = await storyboardsApi.getStoryboardPanels(storyboardId);
      if (result.success && result.data) {
        setPanels(result.data);
      } else if (result.error) {
        handleError(result.error, "Failed to load storyboard panels");
      }
    } catch (error) {
      handleError(error, "Failed to load storyboard panels");
    }
  };

  const handleStoryboardChange = (field: string, value: string) => {
    setNewStoryboard({ ...newStoryboard, [field]: value });
  };

  const createStoryboard = async () => {
    if (!currentOrganization || !newStoryboard.title.trim()) return;
    setLoading(true);
    try {
      const result = await storyboardsApi.createStoryboard({
        org_id: currentOrganization.id,
        title: newStoryboard.title,
        description: newStoryboard.description,
        fidelity: newStoryboard.fidelity,
      });
      if (result.success && result.data) {
        setStoryboards([result.data, ...storyboards]);
        setNewStoryboard({ title: "", description: "", fidelity: "Sketch" });
        setShowCreateForm(false);
        toast({
          title: "Storyboard Created",
          description: `"${result.data.title}" has been created successfully.`,
        });
      } else if (result.error) {
        handleError(result.error, "Failed to create storyboard");
      }
    } catch (error) {
      handleError(error, "Failed to create storyboard");
    } finally {
      setLoading(false);
    }
  };

  const selectStoryboard = (storyboard: Storyboard) => {
    setSelectedStoryboard(storyboard);
    loadPanels(storyboard.id);
  };

  const addPanel = async () => {
    if (!selectedStoryboard || !currentOrganization) return;
    try {
      const result = await storyboardsApi.createStoryboardPanel({
        storyboard_id: selectedStoryboard.id,
        org_id: currentOrganization.id,
        order_index: panels.length,
        dialogue: "New panel dialogue...",
      });
      if (result.success && result.data) {
        setPanels([...panels, result.data]);
        toast({
          title: "Panel Added",
          description: "New storyboard panel has been added.",
        });
      } else if (result.error) {
        handleError(result.error, "Failed to add panel");
      }
    } catch (error) {
      handleError(error, "Failed to add panel");
    }
  };

  const updatePanelDialogue = async (panelId: string, dialogue: string) => {
    try {
      const result = await storyboardsApi.updateStoryboardPanel(panelId, { dialogue });
      if (result.success && result.data) {
        setPanels(panels.map((panel) => (panel.id === panelId ? result.data! : panel)));
      }
    } catch (error) {
      handleError(error, "Failed to update panel");
    }
  };

  const deletePanel = async (panelId: string) => {
    try {
      const result = await storyboardsApi.deleteStoryboardPanel(panelId);
      if (result.success) {
        setPanels(panels.filter((panel) => panel.id !== panelId));
        toast({
          title: "Panel Deleted",
          description: "Storyboard panel has been removed.",
        });
      } else if (result.error) {
        handleError(result.error, "Failed to delete panel");
      }
    } catch (error) {
      handleError(error, "Failed to delete panel");
    }
  };

  const movePanelUp = async (index: number) => {
    if (index === 0) return;
    const newPanels = [...panels];
    [newPanels[index], newPanels[index - 1]] = [newPanels[index - 1], newPanels[index]];
    setPanels(newPanels);
    try {
      await Promise.all([
        storyboardsApi.updateStoryboardPanel(newPanels[index].id, { order_index: index }),
        storyboardsApi.updateStoryboardPanel(newPanels[index - 1].id, { order_index: index - 1 }),
      ]);
    } catch (error) {
      handleError(error, "Failed to update panel order");
    }
  };

  const movePanelDown = async (index: number) => {
    if (index === panels.length - 1) return;
    const newPanels = [...panels];
    [newPanels[index], newPanels[index + 1]] = [newPanels[index + 1], newPanels[index]];
    setPanels(newPanels);
    try {
      await Promise.all([
        storyboardsApi.updateStoryboardPanel(newPanels[index].id, { order_index: index }),
        storyboardsApi.updateStoryboardPanel(newPanels[index + 1].id, { order_index: index + 1 }),
      ]);
    } catch (error) {
      handleError(error, "Failed to update panel order");
    }
  };

  const exportStoryboard = async () => {
    if (!selectedStoryboard) return;
    toast({
      title: "Export Started",
      description: "Preparing your storyboard for export...",
    });
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: "Your storyboard has been exported successfully.",
      });
    }, 2000);
  };

  return {
    storyboards,
    setStoryboards,
    selectedStoryboard,
    setSelectedStoryboard,
    panels,
    setPanels,
    loading,
    showCreateForm,
    setShowCreateForm,
    newStoryboard,
    setNewStoryboard,
    handleStoryboardChange,
    createStoryboard,
    selectStoryboard,
    addPanel,
    updatePanelDialogue,
    deletePanel,
    movePanelUp,
    movePanelDown,
    exportStoryboard,
    loadStoryboards,
  };
}
