
import { useQuery, Query<PERSON><PERSON>, UseQueryOptions } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { getErrorMessage, getPriorityConfig } from './smartQueryHelpers';

interface SmartQueryConfig {
  priority?: 'high' | 'medium' | 'low';
  enableBackground?: boolean;
  staleTime?: number;
  useOptimizedIndexes?: boolean;
}

/**
 * Smart query hook with optimized cache management and retry logic
 */
export const useSmartQuery = <T = unknown, TError = Error>(
  queryKey: QueryKey,
  queryFn: () => Promise<T>,
  config: SmartQueryConfig & Omit<UseQueryOptions<T, TError>, 'queryKey' | 'queryFn'> = {}
) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const {
    priority = 'medium',
    enableBackground = true,
    staleTime = 5 * 60 * 1000,
    useOptimizedIndexes = true,
    ...queryOptions
  } = config;

  const priorityConfig = getPriorityConfig(priority, useOptimizedIndexes);

  return useQuery({
    queryKey,
    queryFn,
    staleTime: priorityConfig.staleTime,
    gcTime: priorityConfig.gcTime,
    refetchInterval: priorityConfig.refetchInterval,
    refetchOnWindowFocus: enableBackground && priority === 'high',
    refetchOnReconnect: enableBackground,
    enabled: !!user && !!currentOrganization,
    // Add retry configuration for better reliability
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      const errorMessage = getErrorMessage(error);
      if (errorMessage.includes('auth') || errorMessage.includes('permission')) {
        return false;
      }
      // Retry up to 3 times for other errors
      return failureCount < 3;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...queryOptions,
  });
};
