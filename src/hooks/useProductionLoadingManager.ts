
import { useMemo } from 'react';

interface LoadingStates {
  schedule: boolean;
  budget: boolean;
  resource: boolean;
  report: boolean;
}

interface LoadingManager {
  loading: boolean;
  creating: LoadingStates;
  isAnyCreating: boolean;
  isAnyLoading: boolean;
  getEntityLoadingState: (entity: keyof LoadingStates) => boolean;
}

interface LoadingManagerInput {
  scheduleLoading: boolean;
  budgetLoading: boolean;
  resourceLoading: boolean;
  reportLoading: boolean;
  scheduleCreating: boolean;
  budgetCreating: boolean;
  resourceCreating: boolean;
  reportCreating: boolean;
}

/**
 * Hook for managing loading states across production entities
 */
export const useProductionLoadingManager = (input: LoadingManagerInput): LoadingManager => {
  const {
    scheduleLoading,
    budgetLoading,
    resourceLoading,
    reportLoading,
    scheduleCreating,
    budgetCreating,
    resourceCreating,
    reportCreating
  } = input;

  const loading = useMemo(() => (
    scheduleLoading || budgetLoading || resourceLoading || reportLoading
  ), [scheduleLoading, budgetLoading, resourceLoading, reportLoading]);

  const creating = useMemo(() => ({
    schedule: scheduleCreating,
    budget: budgetCreating,
    resource: resourceCreating,
    report: reportCreating
  }), [scheduleCreating, budgetCreating, resourceCreating, reportCreating]);

  const isAnyCreating = useMemo(() => (
    scheduleCreating || budgetCreating || resourceCreating || reportCreating
  ), [scheduleCreating, budgetCreating, resourceCreating, reportCreating]);

  const isAnyLoading = useMemo(() => (
    loading || isAnyCreating
  ), [loading, isAnyCreating]);

  const getEntityLoadingState = (entity: keyof LoadingStates) => creating[entity];

  return {
    loading,
    creating,
    isAnyCreating,
    isAnyLoading,
    getEntityLoadingState
  };
};
