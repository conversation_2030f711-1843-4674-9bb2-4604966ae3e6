
import { useState, useCallback } from 'react';
import { useErrorHand<PERSON> } from './useErrorHandler';
import { toast } from '@/hooks/use-toast';

interface RetryConfig {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

interface UseProductionApiRetryResult<T> {
  execute: (operation: () => Promise<T>) => Promise<T | null>;
  loading: boolean;
  retryCount: number;
  isRetrying: boolean;
}

/**
 * Custom hook for handling API calls with exponential backoff retry logic
 * Specifically designed for production tools API operations
 */
export const useProductionApiRetry = <T = any>(
  config: RetryConfig = {}
): UseProductionApiRetryResult<T> => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2
  } = config;

  const [loading, setLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { handleError } = useErrorHandler();

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const calculateDelay = (attempt: number): number => {
    const delay = baseDelay * Math.pow(backoffFactor, attempt);
    return Math.min(delay, maxDelay);
  };

  const execute = useCallback(async (operation: () => Promise<T>): Promise<T | null> => {
    setLoading(true);
    setRetryCount(0);
    setIsRetrying(false);

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Production API attempt ${attempt + 1}/${maxRetries + 1}`);
        
        if (attempt > 0) {
          setIsRetrying(true);
          const delay = calculateDelay(attempt - 1);
          console.log(`Retrying in ${delay}ms...`);
          
          toast({
            title: "Retrying...",
            description: `Attempt ${attempt + 1} of ${maxRetries + 1}`,
          });
          
          await sleep(delay);
        }

        const result = await operation();
        
        if (attempt > 0) {
          toast({
            title: "Success!",
            description: "Operation completed successfully after retry",
          });
        }
        
        setLoading(false);
        setIsRetrying(false);
        return result;
        
      } catch (error) {
        console.error(`Production API attempt ${attempt + 1} failed:`, error);
        setRetryCount(attempt + 1);
        
        // If this is the last attempt, handle the error
        if (attempt === maxRetries) {
          handleError(error, `Operation failed after ${maxRetries + 1} attempts`);
          setLoading(false);
          setIsRetrying(false);
          return null;
        }
      }
    }

    setLoading(false);
    setIsRetrying(false);
    return null;
  }, [maxRetries, baseDelay, maxDelay, backoffFactor, handleError]);

  return {
    execute,
    loading,
    retryCount,
    isRetrying
  };
};
