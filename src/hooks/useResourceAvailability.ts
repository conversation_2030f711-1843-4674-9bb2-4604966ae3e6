
import { useState, useCallback } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { productionApi } from '@/lib/api';
import type { ProductionResource } from '@/lib/api/production';

interface AvailabilityCheck {
  isAvailable: boolean;
  conflicts: Array<{
    date: string;
    conflictingBooking: {
      id: string;
      schedule_item_id: string;
      start_date: string;
      end_date: string;
      notes?: string;
    };
  }>;
  nextAvailable?: string;
}

interface ResourceAvailability {
  [resourceId: string]: AvailabilityCheck;
}

export const useResourceAvailability = () => {
  const { currentOrganization } = useOrganization();
  const [loading, setLoading] = useState(false);
  const [availability, setAvailability] = useState<ResourceAvailability>({});

  const checkAvailability = useCallback(async (
    resourceIds: string[],
    startDate: string,
    endDate: string
  ): Promise<ResourceAvailability> => {
    if (!currentOrganization || !resourceIds.length) {
      return {};
    }

    setLoading(true);
    
    try {
      const resourcesResult = await productionApi.getResources(currentOrganization.id);
      
      if (!resourcesResult.success) {
        throw new Error('Failed to fetch resources');
      }

      const allResources = resourcesResult.data || [];
      const result: ResourceAvailability = {};

      for (const resourceId of resourceIds) {
        const resource = allResources.find(r => r.id === resourceId);
        
        if (!resource) {
          result[resourceId] = {
            isAvailable: false,
            conflicts: []
          };
          continue;
        }

        // Check basic availability status
        if (resource.availability_status !== 'available') {
          result[resourceId] = {
            isAvailable: false,
            conflicts: [],
            nextAvailable: resource.availability_status === 'maintenance' ? 'TBD' : undefined
          };
          continue;
        }

        // For now, assume no booking conflicts since we don't have booking data
        // In a real implementation, you would query resource_bookings table
        result[resourceId] = {
          isAvailable: true,
          conflicts: []
        };
      }

      setAvailability(result);
      return result;

    } catch (error) {
      console.error('Error checking resource availability:', error);
      return {};
    } finally {
      setLoading(false);
    }
  }, [currentOrganization]);

  const getResourceStatus = useCallback((resourceId: string): AvailabilityCheck | null => {
    return availability[resourceId] || null;
  }, [availability]);

  return {
    checkAvailability,
    getResourceStatus,
    availability,
    loading
  };
};
