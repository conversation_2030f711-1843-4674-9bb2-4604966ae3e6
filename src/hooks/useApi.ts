
import { useState, useCallback } from 'react';
import type { ApiResponse } from '@/lib/api';

/**
 * Custom hook for managing API call states (loading, error, data)
 * 
 * @template T
 * @returns Object containing state and utilities for API calls
 * 
 * @example
 * ```tsx
 * const { execute, loading, error, data } = useApi();
 * 
 * const handleFetchData = useCallback(async () => {
 *   const result = await execute(() => organizationApi.getOrganizations());
 *   if (result.success) {
 *     console.log('Data:', result.data);
 *   }
 * }, [execute]);
 * ```
 */
export const useApi = <T>() => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = useCallback(async (
    apiCall: () => Promise<ApiResponse<T>>
  ): Promise<ApiResponse<T>> => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await apiCall();
      
      if (result.success) {
        setData(result.data);
        setError(null);
      } else {
        setError(result.error);
        setData(null);
      }
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setData(null);
      
      return {
        data: null,
        error: errorMessage,
        success: false
      };
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
  }, []);

  return {
    loading,
    error,
    data,
    execute,
    reset
  };
};
