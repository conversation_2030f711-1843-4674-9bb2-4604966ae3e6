
// Helper function to safely extract error message
export const getErrorMessage = (error: unknown): string => {
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  return 'An unknown error occurred';
};

// Priority configuration for different query types
export const getPriorityConfig = (priority: 'high' | 'medium' | 'low', useOptimizedIndexes: boolean) => {
  const configs = {
    high: { 
      staleTime: 30 * 1000, // 30 seconds for high priority
      gcTime: 5 * 60 * 1000, // 5 minutes cache
      refetchInterval: useOptimizedIndexes ? 60 * 1000 : undefined // 1 minute if using indexes
    },
    medium: { 
      staleTime: 2 * 60 * 1000, // 2 minutes for medium priority  
      gcTime: 10 * 60 * 1000, // 10 minutes cache
      refetchInterval: useOptimizedIndexes ? 5 * 60 * 1000 : undefined // 5 minutes if using indexes
    },
    low: { 
      staleTime: 15 * 60 * 1000, // 15 minutes for low priority
      gcTime: 30 * 60 * 1000, // 30 minutes cache
      refetchInterval: undefined // No automatic refetch for low priority
    }
  };

  return configs[priority];
};
