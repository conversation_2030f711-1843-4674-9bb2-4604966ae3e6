
import { useOrganization } from "@/contexts/OrganizationContext";
import { useStoryboardPanels } from "./useStoryboardPanels";
import { useStoryboardUsageQuota } from "./useStoryboardUsageQuota";
import { useStoryboardData } from "./storyboard/useStoryboardData";
import { useStoryboardOperations } from "./storyboard/useStoryboardOperations";
import { useStoryboardForm } from "./storyboard/useStoryboardForm";

// Unified hook that composes smaller, focused hooks
export function useStoryboardStudio() {
  const { currentOrganization } = useOrganization();
  const orgId = currentOrganization?.id;

  // Data management
  const dataState = useStoryboardData(orgId);
  
  // Form management
  const formState = useStoryboardForm();
  
  // Operations
  const operations = useStoryboardOperations(orgId, () => {
    dataState.refetch();
    formState.resetForm();
  });

  // Panel management (existing)
  const panelState = useStoryboardPanels(currentOrganization);

  // Usage quota (existing)
  const { usage, usageLoading, fetchUsageQuota } = useStoryboardUsageQuota({
    enabled: Boolean(currentOrganization),
  });

  return {
    currentOrganization,
    // Data state
    ...dataState,
    // Form state
    ...formState,
    // Operations
    ...operations,
    // Panel state
    ...panelState,
    // Usage
    usage,
    usageLoading,
    fetchUsageQuota,
  };
}
