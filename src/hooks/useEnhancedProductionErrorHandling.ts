
import { useCallback, useMemo } from 'react';
import { useProductionErrorManager } from './useProductionErrorManager';

interface UseEnhancedProductionErrorHandlingProps {
  dataError: Error | null;
}

export const useEnhancedProductionErrorHandling = ({ dataError }: UseEnhancedProductionErrorHandlingProps) => {
  const errorManager = useProductionErrorManager();

  // Enhanced error handling that captures errors from all sources
  const combinedError = useMemo(() => {
    if (dataError && !errorManager.hasErrors) {
      // Set error in error manager if we have a data error but no managed errors
      errorManager.setError('general', dataError.message || 'An error occurred');
    }
    return dataError;
  }, [dataError, errorManager]);

  return {
    combinedError,
    errors: errorManager.errors,
    hasErrors: errorManager.hasErrors,
    clearErrors: errorManager.clearAllErrors,
    clearError: errorManager.clearError,
    setError: errorManager.setError,
    errorManager
  };
};
