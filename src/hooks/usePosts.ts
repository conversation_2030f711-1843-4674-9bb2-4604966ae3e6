
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useOrganization } from '@/contexts/OrganizationContext';

interface Post {
  id: string;
  title: string;
  content: string;
  created_at: string;
  org_id: string;
  profiles: {
    username: string | null;
    full_name: string | null;
  } | null;
}

export const usePosts = () => {
  const { currentOrganization } = useOrganization();
  const [posts, setPosts] = useState<Post[]>([]);

  const fetchPosts = async () => {
    if (!currentOrganization) return;

    try {
      const { data, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles (
            username,
            full_name
          )
        `)
        .eq('org_id', currentOrganization.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform the data to match our Post interface
      const transformedData = data?.map(post => ({
        ...post,
        profiles: post.profiles ? {
          username: post.profiles.username,
          full_name: post.profiles.full_name
        } : null
      })) || [];

      setPosts(transformedData);
    } catch (error) {
      console.error('Error fetching posts:', error);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      fetchPosts();
    }
  }, [currentOrganization]);

  return {
    posts,
    fetchPosts
  };
};
