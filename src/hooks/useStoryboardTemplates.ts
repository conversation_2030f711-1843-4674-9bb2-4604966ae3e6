
import { useState, useEffect } from 'react';
import { useError<PERSON><PERSON><PERSON> } from '@/hooks/useErrorHandler';
import { storyboardTemplatesApi, type StoryboardTemplate, type CreateTemplateData } from '@/lib/api/storyboard-templates';
import { useToast } from '@/hooks/use-toast';

export const useStoryboardTemplates = (orgId?: string) => {
  const [templates, setTemplates] = useState<StoryboardTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const { handleError } = useErrorHandler();
  const { toast } = useToast();

  const fetchTemplates = async () => {
    setLoading(true);
    try {
      const result = await storyboardTemplatesApi.getTemplates(orgId);
      if (result.success && result.data) {
        setTemplates(result.data);
      } else if (result.error) {
        handleError(result.error, 'Failed to fetch templates');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch templates');
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (templateData: CreateTemplateData) => {
    setCreating(true);
    try {
      const result = await storyboardTemplatesApi.createTemplate(templateData);
      if (result.success && result.data) {
        setTemplates(prev => [result.data!, ...prev]);
        toast({
          title: "Template Created",
          description: "Storyboard template has been created successfully.",
        });
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create template');
        return null;
      }
    } catch (error) {
      handleError(error, 'Failed to create template');
      return null;
    } finally {
      setCreating(false);
    }
  };

  const updateTemplate = async (id: string, updates: Partial<CreateTemplateData>) => {
    try {
      const result = await storyboardTemplatesApi.updateTemplate(id, updates);
      if (result.success && result.data) {
        setTemplates(prev => prev.map(template => 
          template.id === id ? result.data! : template
        ));
        toast({
          title: "Template Updated",
          description: "Template has been updated successfully.",
        });
      } else if (result.error) {
        handleError(result.error, 'Failed to update template');
      }
    } catch (error) {
      handleError(error, 'Failed to update template');
    }
  };

  const deleteTemplate = async (id: string) => {
    try {
      const result = await storyboardTemplatesApi.deleteTemplate(id);
      if (result.success) {
        setTemplates(prev => prev.filter(template => template.id !== id));
        toast({
          title: "Template Deleted",
          description: "Template has been deleted successfully.",
        });
      } else if (result.error) {
        handleError(result.error, 'Failed to delete template');
      }
    } catch (error) {
      handleError(error, 'Failed to delete template');
    }
  };

  const useTemplate = async (template: StoryboardTemplate) => {
    try {
      await storyboardTemplatesApi.incrementUsage(template.id);
      return template.template_data;
    } catch (error) {
      handleError(error, 'Failed to use template');
      return null;
    }
  };

  useEffect(() => {
    fetchTemplates();
  }, [orgId]);

  return {
    templates,
    loading,
    creating,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    useTemplate,
    refetch: fetchTemplates
  };
};
