
import { useOrganization } from '@/contexts/OrganizationContext';
import { useSmartQuery } from './useSmartQuery';

interface SmartQueryConfig {
  priority?: 'high' | 'medium' | 'low';
  enableBackground?: boolean;
  staleTime?: number;
  useOptimizedIndexes?: boolean;
}

/**
 * Optimized query hook for organization-specific data with index utilization
 */
export const useOptimizedOrgQuery = <T = unknown>(
  tableName: string,
  config: SmartQueryConfig & {
    select?: string;
    filters?: Record<string, any>;
    orderBy?: { column: string; ascending?: boolean };
    search?: { column: string; query: string };
  } = {}
) => {
  const { currentOrganization } = useOrganization();
  const { select = '*', filters = {}, orderBy, search, ...restConfig } = config;

  return useSmartQuery(
    ['optimized-org-query', tableName, currentOrganization?.id, select, filters, orderBy, search],
    async () => {
      if (!currentOrganization?.id) return [];

      const { supabase } = await import('@/integrations/supabase/client');
      
      // Use any to bypass strict typing for dynamic table access
      let query = (supabase as any)
        .from(tableName)
        .select(select);

      // Always filter by org_id first (uses index)
      query = query.eq('org_id', currentOrganization.id);

      // Apply additional filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      // Apply search if provided (uses full-text search indexes)
      if (search?.query && search?.column) {
        query = query.textSearch(search.column, search.query);
      }

      // Apply ordering (uses indexes when possible)
      if (orderBy) {
        query = query.order(orderBy.column, { ascending: orderBy.ascending ?? false });
      }

      const { data, error } = await query;
      if (error) throw error;
      
      return data as T[];
    },
    {
      priority: 'medium',
      useOptimizedIndexes: true,
      ...restConfig,
    }
  );
};
