
import { useState } from 'react';
import { storyboardsApi } from '@/lib/api';
import { useErrorHandler } from '@/hooks/useErrorHandler';

export function useStoryboardOperations(orgId?: string, onSuccess?: () => void) {
  const [loading, setLoading] = useState(false);
  const { handleError } = useErrorHandler();

  const createStoryboard = async (storyboardData: any) => {
    if (!orgId) return null;
    
    setLoading(true);
    try {
      const result = await storyboardsApi.createStoryboard({
        ...storyboardData,
        org_id: orgId
      });
      
      if (result.success) {
        onSuccess?.();
        return result.data;
      } else {
        handleError(new Error(result.error || 'Failed to create storyboard'));
        return null;
      }
    } catch (error) {
      handleError(error);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const exportStoryboard = () => {
    // Implementation for export functionality
    console.log('Exporting storyboard...');
  };

  return {
    createStoryboard,
    exportStoryboard,
    loading
  };
}
