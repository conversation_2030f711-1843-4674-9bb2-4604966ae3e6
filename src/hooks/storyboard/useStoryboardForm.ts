
import { useState } from 'react';

interface NewStoryboard {
  title: string;
  description: string;
}

export function useStoryboardForm() {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newStoryboard, setNewStoryboard] = useState<NewStoryboard>({
    title: '',
    description: ''
  });

  const handleStoryboardChange = (field: keyof NewStoryboard, value: string) => {
    setNewStoryboard(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setNewStoryboard({
      title: '',
      description: ''
    });
    setShowCreateForm(false);
  };

  return {
    showCreateForm,
    setShowCreateForm,
    newStoryboard,
    handleStoryboardChange,
    resetForm
  };
}
