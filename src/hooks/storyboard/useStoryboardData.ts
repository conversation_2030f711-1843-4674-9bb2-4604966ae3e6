
import { useState, useEffect } from 'react';
import { storyboardsApi } from '@/lib/api';
import { useErrorHandler } from '@/hooks/useErrorHandler';

export function useStoryboardData(orgId?: string) {
  const [storyboards, setStoryboards] = useState([]);
  const [selectedStoryboard, setSelectedStoryboard] = useState(null);
  const [loading, setLoading] = useState(false);
  const { handleError } = useErrorHandler();

  const fetchStoryboards = async () => {
    if (!orgId) return;
    
    setLoading(true);
    try {
      const result = await storyboardsApi.getStoryboards();
      if (result.success) {
        setStoryboards(result.data || []);
      } else {
        handleError(new Error(result.error || 'Failed to fetch storyboards'));
      }
    } catch (error) {
      handleError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStoryboards();
  }, [orgId]);

  const selectStoryboard = (storyboard: any) => {
    setSelectedStoryboard(storyboard);
  };

  return {
    storyboards,
    selectedStoryboard,
    loading,
    selectStoryboard,
    refetch: fetchStoryboards
  };
}
