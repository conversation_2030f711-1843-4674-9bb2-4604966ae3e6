
import { useState, useCallback } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export interface SearchResult {
  entity_type: string;
  entity_id: string;
  title: string;
  description: string;
  status: string;
  created_at: string;
  relevance_rank: number;
}

export const useProductionSearch = () => {
  const { currentOrganization } = useOrganization();
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const search = useCallback(async (
    query: string = '',
    entityType: string = 'all',
    page: number = 1,
    pageSize: number = 20
  ) => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('search_production_data', {
        org_id_param: currentOrganization.id,
        search_query: query,
        entity_type_filter: entityType,
        page_num: page,
        page_size: pageSize
      });

      if (error) throw error;

      setResults(data || []);
      setTotalCount(data?.length || 0);
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Error",
        description: "Failed to search production data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [currentOrganization]);

  return {
    results,
    loading,
    totalCount,
    search
  };
};
