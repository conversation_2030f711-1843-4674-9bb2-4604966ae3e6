
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { coverageApi, type CoverageReport } from '@/lib/api/coverage';

export const useCoverage = () => {
  const { currentOrganization } = useOrganization();
  const [coverageReports, setCoverageReports] = useState<CoverageReport[]>([]);
  
  const { execute: executeRefresh, loading } = useAsyncOperation<CoverageReport[]>({
    errorMessage: 'Failed to fetch coverage reports'
  });

  const refreshCoverageReports = async () => {
    if (!currentOrganization) return;

    const { data, success } = await executeRefresh(async () => {
      const result = await coverageApi.getCoverageReports();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch coverage reports');
    });

    if (success && data) {
      setCoverageReports(data);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      refreshCoverageReports();
    } else {
      setCoverageReports([]);
    }
  }, [currentOrganization]);

  return {
    coverageReports,
    loading,
    refreshCoverageReports
  };
};
