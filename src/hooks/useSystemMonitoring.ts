
import { useState, useEffect, useCallback } from 'react';
import { healthMonitor, type SystemHealthStatus } from '@/lib/monitoring/core/healthMonitor';
import { alertManager, type Alert } from '@/lib/monitoring/core/alertManager';
import { errorTracker } from '@/lib/monitoring/core/errorTracker';

export function useSystemMonitoring() {
  const [healthStatus, setHealthStatus] = useState<SystemHealthStatus | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshData = useCallback(async () => {
    try {
      setError(null);
      const [health, alertsData] = await Promise.all([
        healthMonitor.runHealthChecks(),
        alertManager.getRecentAlerts(50)
      ]);
      
      setHealthStatus(health);
      setAlerts(alertsData);
      
      // Evaluate alert rules based on current health status
      await alertManager.evaluateRules({
        databaseStatus: health.checks.database?.status,
        authStatus: health.checks.auth?.status,
        storageStatus: health.checks.storage?.status,
        memoryUsage: health.checks.memory?.details?.usagePercent || 0,
        avgResponseTime: Object.values(health.checks).reduce((sum, check) => sum + check.responseTime, 0) / Object.keys(health.checks).length
      });
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load monitoring data';
      setError(errorMessage);
      
      errorTracker.captureError({
        level: 'error',
        message: errorMessage,
        stack: err instanceof Error ? err.stack : undefined,
        context: {
          component: 'useSystemMonitoring'
        }
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Start health monitoring if not already running
    if (!healthMonitor.isCurrentlyMonitoring()) {
      healthMonitor.startMonitoring(60000); // Check every minute
    }
    
    // Initial data load
    refreshData();
    
    // Set up periodic refresh
    const interval = setInterval(refreshData, 60000);
    
    return () => {
      clearInterval(interval);
    };
  }, [refreshData]);

  const acknowledgeAlert = useCallback(async (alertId: string) => {
    try {
      await alertManager.acknowledgeAlert(alertId);
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      ));
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to acknowledge alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'useSystemMonitoring',
          additionalData: { alertId }
        }
      });
    }
  }, []);

  const resolveAlert = useCallback(async (alertId: string) => {
    try {
      await alertManager.resolveAlert(alertId);
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, resolved: true } : alert
      ));
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to resolve alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'useSystemMonitoring',
          additionalData: { alertId }
        }
      });
    }
  }, []);

  const triggerAlert = useCallback(async (alert: Omit<Alert, 'id' | 'createdAt'>) => {
    try {
      const alertId = await alertManager.triggerAlert(alert);
      if (alertId) {
        setAlerts(prev => [{
          id: alertId,
          ...alert,
          createdAt: new Date().toISOString()
        }, ...prev]);
      }
      return alertId;
    } catch (error) {
      errorTracker.captureError({
        level: 'error',
        message: 'Failed to trigger alert',
        stack: error instanceof Error ? error.stack : undefined,
        context: {
          component: 'useSystemMonitoring',
          additionalData: { alert }
        }
      });
      return null;
    }
  }, []);

  return {
    // State
    healthStatus,
    alerts,
    isLoading,
    error,
    
    // Actions
    refreshData,
    acknowledgeAlert,
    resolveAlert,
    triggerAlert,
    
    // Utilities
    getActiveAlerts: () => alerts.filter(alert => !alert.resolved),
    getCriticalAlerts: () => alerts.filter(alert => alert.severity === 'critical' && !alert.resolved),
    getSystemStatus: () => healthStatus?.overallStatus || 'unknown',
    isSystemHealthy: () => healthStatus?.overallStatus === 'healthy',
    
    // Performance tracking
    measurePerformance: errorTracker.measurePerformance.bind(errorTracker)
  };
}
