
import { useQuery } from '@tanstack/react-query';
import { teamAccessApi } from '@/lib/api/team';
import { useOrganization } from '@/contexts/OrganizationContext';

export const useTeamAccess = () => {
  const { currentOrganization } = useOrganization();
  const orgId = currentOrganization?.id || '';

  const teamAccessQuery = useQuery({
    queryKey: ['team', 'access', orgId],
    queryFn: () => teamAccessApi.getTeamAccess(orgId),
    enabled: !!orgId,
  });

  return {
    teamAccess: teamAccessQuery.data?.data,
    isLoading: teamAccessQuery.isLoading,
    error: teamAccessQuery.error,
    refetch: teamAccessQuery.refetch,
  };
};
