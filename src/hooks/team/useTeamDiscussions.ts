
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teamDiscussionsApi } from '@/lib/api/team';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useToast } from '@/hooks/use-toast';

export const useTeamDiscussions = () => {
  const { currentOrganization } = useOrganization();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const orgId = currentOrganization?.id || '';

  // Team discussions query
  const discussionsQuery = useQuery({
    queryKey: ['team', 'discussions', orgId],
    queryFn: () => teamDiscussionsApi.getDiscussions(orgId),
    enabled: !!orgId,
  });

  // Create discussion mutation
  const createDiscussionMutation = useMutation({
    mutationFn: ({
      title,
      content,
      discussionType,
      isAnnouncement
    }: {
      title: string;
      content: string;
      discussionType?: string;
      isAnnouncement?: boolean;
    }) => teamDiscussionsApi.createDiscussion(orgId, title, content, discussionType, isAnnouncement),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['team', 'discussions', orgId] });
      toast({
        title: "Success",
        description: "Discussion created successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create discussion",
        variant: "destructive",
      });
    },
  });

  // Add reply mutation
  const addReplyMutation = useMutation({
    mutationFn: ({ discussionId, content }: { discussionId: string; content: string }) =>
      teamDiscussionsApi.addReply(discussionId, content),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['team', 'discussion-replies', variables.discussionId] });
      toast({
        title: "Success",
        description: "Reply added successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add reply",
        variant: "destructive",
      });
    },
  });

  return {
    discussions: discussionsQuery.data?.data || [],
    isDiscussionsLoading: discussionsQuery.isLoading,
    createDiscussion: createDiscussionMutation.mutate,
    addReply: addReplyMutation.mutate,
    isCreatingDiscussion: createDiscussionMutation.isPending,
    isAddingReply: addReplyMutation.isPending,
    refetchDiscussions: discussionsQuery.refetch,
  };
};

// Hook for discussion replies
export const useDiscussionReplies = (discussionId: string) => {
  const repliesQuery = useQuery({
    queryKey: ['team', 'discussion-replies', discussionId],
    queryFn: () => teamDiscussionsApi.getDiscussionReplies(discussionId),
    enabled: !!discussionId,
  });

  return {
    replies: repliesQuery.data?.data || [],
    isLoading: repliesQuery.isLoading,
    refetch: repliesQuery.refetch,
  };
};
