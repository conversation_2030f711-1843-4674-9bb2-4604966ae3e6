
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teamMembershipsApi } from '@/lib/api/team/memberships';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';

export function useProjectTeam(projectId: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const {
    data: membersResponse,
    isLoading,
    error
  } = useQuery({
    queryKey: ['project-team', projectId],
    queryFn: () => teamMembershipsApi.getProjectTeamMembers(projectId),
    enabled: !!projectId
  });

  const addMemberMutation = useMutation({
    mutationFn: (memberData: {
      userId: string;
      role?: string;
      permissions?: any;
    }) => teamMembershipsApi.addProjectMember({
      projectId,
      userId: memberData.userId,
      role: memberData.role || 'member',
      permissions: memberData.permissions || {},
      invitedBy: user?.id || ''
    }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-team', projectId] });
      toast({
        title: "Success",
        description: "Team member added successfully"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add team member",
        variant: "destructive"
      });
    }
  });

  const updateMemberMutation = useMutation({
    mutationFn: ({ membershipId, role, permissions }: {
      membershipId: string;
      role: string;
      permissions?: any;
    }) => teamMembershipsApi.updateMemberRole(membershipId, role, permissions),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-team', projectId] });
      toast({
        title: "Success",
        description: "Member role updated successfully"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update member role",
        variant: "destructive"
      });
    }
  });

  const removeMemberMutation = useMutation({
    mutationFn: (membershipId: string) => teamMembershipsApi.removeProjectMember(membershipId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['project-team', projectId] });
      toast({
        title: "Success",
        description: "Team member removed successfully"
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to remove team member",
        variant: "destructive"
      });
    }
  });

  return {
    members: membersResponse?.data || [],
    isLoading,
    error,
    addMember: addMemberMutation.mutate,
    updateMember: updateMemberMutation.mutate,
    removeMember: removeMemberMutation.mutate,
    isAddingMember: addMemberMutation.isPending,
    isUpdatingMember: updateMemberMutation.isPending,
    isRemovingMember: removeMemberMutation.isPending
  };
}
