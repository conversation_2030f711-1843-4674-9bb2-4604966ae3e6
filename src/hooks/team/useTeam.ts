
import { useTeamAccess } from './useTeamAccess';
import { useTeamActivities } from './useTeamActivities';
import { useTeamDiscussions } from './useTeamDiscussions';

// Backward compatibility hook that combines all team functionality
export const useTeam = () => {
  const { teamAccess, isLoading: isAccessLoading } = useTeamAccess();
  const { 
    activities, 
    isActivitiesLoading, 
    trackActivity, 
    isTrackingActivity, 
    refetchActivities 
  } = useTeamActivities();
  const { 
    discussions, 
    isDiscussionsLoading, 
    createDiscussion, 
    addReply, 
    isCreatingDiscussion, 
    isAddingReply, 
    refetchDiscussions 
  } = useTeamDiscussions();

  return {
    // Data
    teamAccess,
    activities,
    discussions,

    // Loading states
    isLoading: isAccessLoading || isActivitiesLoading || isDiscussionsLoading,
    isActivitiesLoading,
    isDiscussionsLoading,

    // Mutations
    trackActivity,
    createDiscussion,
    addReply,

    // Mutation states
    isTrackingActivity,
    isCreatingDiscussion,
    isAddingReply,

    // Refetch functions
    refetchActivities,
    refetchDiscussions,
  };
};
