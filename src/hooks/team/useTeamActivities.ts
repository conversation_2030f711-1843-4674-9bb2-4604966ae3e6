
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { teamActivitiesApi } from '@/lib/api/team';
import { useOrganization } from '@/contexts/OrganizationContext';

export const useTeamActivities = () => {
  const { currentOrganization } = useOrganization();
  const queryClient = useQueryClient();
  const orgId = currentOrganization?.id || '';

  // Team activities query
  const activitiesQuery = useQuery({
    queryKey: ['team', 'activities', orgId],
    queryFn: () => teamActivitiesApi.getTeamActivities(orgId),
    enabled: !!orgId,
  });

  // Track activity mutation
  const trackActivityMutation = useMutation({
    mutationFn: ({
      activityType,
      description,
      entityType,
      entityId,
      metadata
    }: {
      activityType: string;
      description: string;
      entityType?: string;
      entityId?: string;
      metadata?: any;
    }) => teamActivitiesApi.trackActivity(orgId, activityType, description, entityType, entityId, metadata),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['team', 'activities', orgId] });
    },
  });

  return {
    activities: activitiesQuery.data?.data || [],
    isActivitiesLoading: activitiesQuery.isLoading,
    trackActivity: trackActivityMutation.mutate,
    isTrackingActivity: trackActivityMutation.isPending,
    refetchActivities: activitiesQuery.refetch,
  };
};
