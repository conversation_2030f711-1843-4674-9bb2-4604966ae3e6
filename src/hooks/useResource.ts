
import { useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { useResourcesQuery, useCreateResourceMutation } from '@/lib/api/production/queries';
import { validateFormData, formatValidationErrors, createResourceSchema } from '@/features/production/validation/schemas';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import type { CreateResourceInput } from '@/features/production/validation/schemas';
import type { ProductionResource } from '@/lib/api/production';

/**
 * Reusable hook for resource operations with React Query caching
 */
export const useResource = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const resourcesQuery = useResourcesQuery();
  const createMutation = useCreateResourceMutation();

  const resources = useMemo(() => resourcesQuery.data || [], [resourcesQuery.data]);

  const createResource = async (resourceData: CreateResourceInput) => {
    if (!currentOrganization || !user) {
      toast({
        title: "Error",
        description: "Organization and user context required",
        variant: "destructive"
      });
      return { success: false, error: "Missing context" };
    }

    const validation = validateFormData(createResourceSchema, resourceData);
    
    if (!validation.success) {
      const errors = formatValidationErrors(validation.error);
      toast({
        title: "Validation Error",
        description: "Please check your input and try again",
        variant: "destructive"
      });
      return { success: false, errors };
    }

    try {
      const result = await createMutation.mutateAsync({
        ...validation.data,
        org_id: currentOrganization.id,
        user_id: user.id,
        name: validation.data.name || '',
        type: validation.data.type || 'equipment',
        availability_status: validation.data.availability_status || 'available'
      });
      
      toast({
        title: "Resource created",
        description: `"${result?.name}" has been created successfully`,
      });
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create resource';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return { success: false, error: errorMessage };
    }
  };

  const resourcesByType = useMemo(() => {
    return resources.reduce((acc, resource) => {
      const type = resource.type;
      if (!acc[type]) acc[type] = [];
      acc[type].push(resource);
      return acc;
    }, {} as Record<ProductionResource['type'], ProductionResource[]>);
  }, [resources]);

  const availableResources = useMemo(() => {
    return resources.filter(resource => resource.availability_status === 'available');
  }, [resources]);

  const resourcesByStatus = useMemo(() => {
    return resources.reduce((acc, resource) => {
      const status = resource.availability_status;
      if (!acc[status]) acc[status] = [];
      acc[status].push(resource);
      return acc;
    }, {} as Record<string, typeof resources>);
  }, [resources]);

  return {
    resources,
    resourcesByType,
    availableResources,
    resourcesByStatus,
    loading: resourcesQuery.isLoading,
    error: resourcesQuery.error,
    isCreating: createMutation.isPending,
    createResource,
    refetch: resourcesQuery.refetch,
    // Query status helpers
    isStale: resourcesQuery.isStale,
    isFetching: resourcesQuery.isFetching,
  };
};
