
import { useEffect, useRef, useCallback } from 'react';

interface PerformanceOptions {
  enableResourceHints?: boolean;
  enableLazyLoading?: boolean;
  enablePreloading?: boolean;
  enableServiceWorker?: boolean;
}

export const usePerformanceOptimizations = (options: PerformanceOptions = {}) => {
  const {
    enableResourceHints = true,
    enableLazyLoading = true,
    enablePreloading = true,
    enableServiceWorker = true
  } = options;

  const observerRef = useRef<IntersectionObserver | null>(null);

  // Preload critical resources
  const preloadResource = useCallback((href: string, as: string, type?: string) => {
    if (!enablePreloading) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    
    // Avoid duplicates
    const existing = document.querySelector(`link[href="${href}"]`);
    if (!existing) {
      document.head.appendChild(link);
    }
  }, [enablePreloading]);

  // DNS prefetch for external domains
  const prefetchDNS = useCallback((domain: string) => {
    if (!enableResourceHints) return;

    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;
    
    const existing = document.querySelector(`link[href="${domain}"]`);
    if (!existing) {
      document.head.appendChild(link);
    }
  }, [enableResourceHints]);

  // Lazy load images
  const lazyLoadImage = useCallback((img: HTMLImageElement) => {
    if (!enableLazyLoading || !observerRef.current) return;

    observerRef.current.observe(img);
  }, [enableLazyLoading]);

  // Initialize intersection observer for lazy loading
  useEffect(() => {
    if (!enableLazyLoading) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              observerRef.current?.unobserve(img);
            }
          }
        });
      },
      { rootMargin: '50px' }
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, [enableLazyLoading]);

  // Register service worker for caching
  useEffect(() => {
    if (!enableServiceWorker || !('serviceWorker' in navigator)) return;

    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered:', registration);
      })
      .catch((error) => {
        console.log('SW registration failed:', error);
      });
  }, [enableServiceWorker]);

  // Performance monitoring
  const measurePerformance = useCallback((name: string, fn: () => void | Promise<void>) => {
    const start = performance.now();
    
    const measure = () => {
      const end = performance.now();
      console.log(`${name}: ${(end - start).toFixed(2)}ms`);
    };

    const result = fn();
    
    if (result instanceof Promise) {
      return result.then(measure);
    } else {
      measure();
      return result;
    }
  }, []);

  return {
    preloadResource,
    prefetchDNS,
    lazyLoadImage,
    measurePerformance
  };
};
