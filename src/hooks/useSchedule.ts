
import { useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { useSchedulesQuery, useCreateScheduleMutation, useScheduleItemsQuery } from '@/lib/api/production/queries';
import { validateFormData, formatValidationErrors, createScheduleSchema } from '@/features/production/validation/schemas';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import type { CreateScheduleInput } from '@/features/production/validation/schemas';

/**
 * Reusable hook for schedule operations with React Query caching
 */
export const useSchedule = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const schedulesQuery = useSchedulesQuery();
  const createMutation = useCreateScheduleMutation();

  const schedules = useMemo(() => schedulesQuery.data || [], [schedulesQuery.data]);

  const createSchedule = async (scheduleData: CreateScheduleInput) => {
    if (!currentOrganization || !user) {
      toast({
        title: "Error",
        description: "Organization and user context required",
        variant: "destructive"
      });
      return { success: false, error: "Missing context" };
    }

    const validation = validateFormData(createScheduleSchema, scheduleData);
    
    if (!validation.success) {
      const errors = formatValidationErrors(validation.error);
      toast({
        title: "Validation Error",
        description: "Please check your input and try again",
        variant: "destructive"
      });
      return { success: false, errors };
    }

    try {
      const result = await createMutation.mutateAsync({
        ...validation.data,
        org_id: currentOrganization.id,
        user_id: user.id,
        status: validation.data.status || 'draft',
        title: validation.data.title || '',
        start_date: validation.data.start_date || '',
        end_date: validation.data.end_date || ''
      });
      
      toast({
        title: "Schedule created",
        description: `"${result?.title}" has been created successfully`,
      });
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create schedule';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return { success: false, error: errorMessage };
    }
  };

  return {
    schedules,
    loading: schedulesQuery.isLoading,
    error: schedulesQuery.error,
    isCreating: createMutation.isPending,
    createSchedule,
    refetch: schedulesQuery.refetch,
    // Query status helpers
    isStale: schedulesQuery.isStale,
    isFetching: schedulesQuery.isFetching,
  };
};

/**
 * Hook for managing schedule items for a specific schedule
 */
export const useScheduleItems = (scheduleId: string | undefined) => {
  const itemsQuery = useScheduleItemsQuery(scheduleId);

  const items = useMemo(() => itemsQuery.data || [], [itemsQuery.data]);

  return {
    items,
    loading: itemsQuery.isLoading,
    error: itemsQuery.error,
    refetch: itemsQuery.refetch,
    isStale: itemsQuery.isStale,
    isFetching: itemsQuery.isFetching,
  };
};
