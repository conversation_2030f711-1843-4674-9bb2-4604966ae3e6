
import { useState, useEffect } from 'react';
import { useLocalStorage } from '@/hooks/useLocalStorage';

export function useOnboarding() {
  const [hasSeenOnboarding, setHasSeenOnboarding] = useLocalStorage('scriptgenius-onboarding-completed', false);
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    // Show onboarding for new users after a short delay
    if (!hasSeenOnboarding) {
      const timer = setTimeout(() => {
        setShowOnboarding(true);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [hasSeenOnboarding]);

  const completeOnboarding = () => {
    setHasSeenOnboarding(true);
    setShowOnboarding(false);
  };

  const skipOnboarding = () => {
    setHasSeenOnboarding(true);
    setShowOnboarding(false);
  };

  const resetOnboarding = () => {
    setHasSeenOnboarding(false);
    setShowOnboarding(true);
  };

  return {
    showOnboarding,
    hasSeenOnboarding,
    completeOnboarding,
    skipOnboarding,
    resetOnboarding,
  };
}
