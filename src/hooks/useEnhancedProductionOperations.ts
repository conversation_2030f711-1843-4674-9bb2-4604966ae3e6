
import { useCallback } from 'react';
import { useProductionErrorManager } from './useProductionErrorManager';
import { useErrorHandler } from './useErrorHandler';

interface CreateOperation<T> {
  (data: any): Promise<{ success: boolean; data?: T; error?: string; errors?: Record<string, string> } | null>;
}

interface UseEnhancedProductionOperationsProps {
  createSchedule: CreateOperation<any>;
  createBudget: CreateOperation<any>;
  createResource: CreateOperation<any>;
  createReport: CreateOperation<any>;
  refetch: () => Promise<void>;
}

export const useEnhancedProductionOperations = ({
  createSchedule,
  createBudget,
  createResource,
  createReport,
  refetch
}: UseEnhancedProductionOperationsProps) => {
  const errorManager = useProductionErrorManager();
  const { createError, handleError } = useErrorHandler();

  // Enhanced create functions with standardized error handling
  const createScheduleWithErrorHandling = useCallback(async (scheduleData: any) => {
    try {
      errorManager.clearError('schedule');
      const result = await createSchedule(scheduleData);
      
      if (result && !result.success) {
        if (result.errors) {
          errorManager.setErrors(result.errors);
        } else {
          const appError = createError('SCHEDULE_CREATE_FAILED', { result });
          handleError(appError);
          errorManager.setError('schedule', appError.userMessage);
        }
        return result;
      }
      
      if (result && result.success) {
        errorManager.clearAllErrors();
      }
      
      return result;
    } catch (error) {
      const appError = createError('SCHEDULE_CREATE_FAILED', { originalError: error });
      handleError(appError);
      errorManager.setError('schedule', appError.userMessage);
      return { success: false, error: appError.userMessage };
    }
  }, [createSchedule, errorManager, createError, handleError]);

  const createBudgetWithErrorHandling = useCallback(async (budgetData: any) => {
    try {
      errorManager.clearError('budget');
      const result = await createBudget(budgetData);
      
      if (result && !result.success) {
        if (result.errors) {
          errorManager.setErrors(result.errors);
        } else {
          const appError = createError('BUDGET_CREATE_FAILED', { result });
          handleError(appError);
          errorManager.setError('budget', appError.userMessage);
        }
        return result;
      }
      
      if (result && result.success) {
        errorManager.clearAllErrors();
      }
      
      return result;
    } catch (error) {
      const appError = createError('BUDGET_CREATE_FAILED', { originalError: error });
      handleError(appError);
      errorManager.setError('budget', appError.userMessage);
      return { success: false, error: appError.userMessage };
    }
  }, [createBudget, errorManager, createError, handleError]);

  const createResourceWithErrorHandling = useCallback(async (resourceData: any) => {
    try {
      errorManager.clearError('resource');
      const result = await createResource(resourceData);
      
      if (result && !result.success) {
        if (result.errors) {
          errorManager.setErrors(result.errors);
        } else {
          const appError = createError('RESOURCE_CREATE_FAILED', { result });
          handleError(appError);
          errorManager.setError('resource', appError.userMessage);
        }
        return result;
      }
      
      if (result && result.success) {
        errorManager.clearAllErrors();
      }
      
      return result;
    } catch (error) {
      const appError = createError('RESOURCE_CREATE_FAILED', { originalError: error });
      handleError(appError);
      errorManager.setError('resource', appError.userMessage);
      return { success: false, error: appError.userMessage };
    }
  }, [createResource, errorManager, createError, handleError]);

  const createReportWithErrorHandling = useCallback(async (reportData: any) => {
    try {
      errorManager.clearError('report');
      const result = await createReport(reportData);
      
      if (result && !result.success) {
        if (result.errors) {
          errorManager.setErrors(result.errors);
        } else {
          const appError = createError('REPORT_CREATE_FAILED', { result });
          handleError(appError);
          errorManager.setError('report', appError.userMessage);
        }
        return result;
      }
      
      if (result && result.success) {
        errorManager.clearAllErrors();
      }
      
      return result;
    } catch (error) {
      const appError = createError('REPORT_CREATE_FAILED', { originalError: error });
      handleError(appError);
      errorManager.setError('report', appError.userMessage);
      return { success: false, error: appError.userMessage };
    }
  }, [createReport, errorManager, createError, handleError]);

  // Enhanced refetch with error handling
  const refetchWithErrorHandling = useCallback(async () => {
    try {
      errorManager.clearError('refetch');
      await refetch();
    } catch (error) {
      const appError = createError('API_ERROR', { originalError: error }, 'Failed to refresh data');
      handleError(appError);
      errorManager.setError('refetch', appError.userMessage);
    }
  }, [refetch, errorManager, createError, handleError]);

  return {
    createSchedule: createScheduleWithErrorHandling,
    createBudget: createBudgetWithErrorHandling,
    createResource: createResourceWithErrorHandling,
    createReport: createReportWithErrorHandling,
    refetch: refetchWithErrorHandling,
    errorManager
  };
};
