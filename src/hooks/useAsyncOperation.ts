
import { useState, useCallback } from 'react';
import { useError<PERSON>andler } from './useErrorHandler';

interface UseAsyncOperationOptions {
  onSuccess?: () => void;
  onError?: (error: unknown) => void;
  successMessage?: string;
  errorMessage?: string;
}

/**
 * Generic hook for handling async operations with loading states and error handling
 * 
 * @template T - The return type of the async operation
 * @param options - Configuration options for success/error handling
 * @returns Object containing execute function, loading state, and error state
 */
export const useAsyncOperation = <T = void>(options: UseAsyncOperationOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { handleError } = useErrorHandler();

  const execute = useCallback(async (
    operation: () => Promise<T>
  ): Promise<{ data: T | null; success: boolean }> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation();
      
      if (options.onSuccess) {
        options.onSuccess();
      }

      return { data: result, success: true };
    } catch (err) {
      const errorMessage = options.errorMessage || 'Operation failed';
      handleError(err, errorMessage);
      setError(errorMessage);
      
      if (options.onError) {
        options.onError(err);
      }

      return { data: null, success: false };
    } finally {
      setLoading(false);
    }
  }, [handleError, options]);

  const reset = useCallback(() => {
    setLoading(false);
    setError(null);
  }, []);

  return {
    execute,
    loading,
    error,
    reset
  };
};
