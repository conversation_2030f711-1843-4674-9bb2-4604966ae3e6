import { useAuth } from '@/contexts/AuthContext';
import { useProfile } from '@/hooks/useProfile';

export interface BlogPermissions {
  canManageBlog: boolean;
  canCreatePosts: boolean;
  canEditPosts: boolean;
  canDeletePosts: boolean;
  canUploadImages: boolean;
  canManageCategories: boolean;
  loading: boolean;
}

export const useBlogPermissions = (): BlogPermissions => {
  const { user } = useAuth();
  const { profile, loading } = useProfile();

  const isSuperAdmin = profile?.role === 'super_admin';

  return {
    canManageBlog: isSuperAdmin,
    canCreatePosts: isSuperAdmin,
    canEditPosts: isSuperAdmin,
    canDeletePosts: isSuperAdmin,
    canUploadImages: isSuperAdmin,
    canManageCategories: isSuperAdmin,
    loading: loading || !user
  };
};
