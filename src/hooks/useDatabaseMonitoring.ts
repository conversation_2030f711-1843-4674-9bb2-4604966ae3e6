
import { useState, useEffect, useCallback } from 'react';
import { 
  databaseMonitoring, 
  type DatabaseBackup, 
  type MigrationRecord, 
  type DatabasePerformanceMetrics,
  type ConnectionPoolMetrics 
} from '@/lib/monitoring/databaseMonitoring';

export function useDatabaseMonitoring() {
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [migrations, setMigrations] = useState<MigrationRecord[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<DatabasePerformanceMetrics[]>([]);
  const [connectionMetrics, setConnectionMetrics] = useState<ConnectionPoolMetrics[]>([]);
  const [healthReport, setHealthReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshData = useCallback(async () => {
    try {
      setError(null);
      const [
        backupsData,
        migrationsData,
        performanceData,
        connectionData,
        healthData
      ] = await Promise.all([
        databaseMonitoring.getBackups(20),
        databaseMonitoring.getMigrationHistory(20),
        databaseMonitoring.getPerformanceMetrics(50),
        databaseMonitoring.getConnectionPoolMetrics(50),
        databaseMonitoring.generateHealthReport()
      ]);

      setBackups(backupsData);
      setMigrations(migrationsData);
      setPerformanceMetrics(performanceData);
      setConnectionMetrics(connectionData);
      setHealthReport(healthData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load database monitoring data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Start monitoring
    if (!databaseMonitoring.isCurrentlyMonitoring()) {
      databaseMonitoring.startMonitoring(30000);
    }

    // Initial data load
    refreshData();

    // Set up periodic refresh
    const interval = setInterval(refreshData, 60000);

    return () => {
      clearInterval(interval);
    };
  }, [refreshData]);

  const initiateBackup = useCallback(async (
    backupType: 'full' | 'incremental' | 'point_in_time' = 'full',
    retentionDays: number = 30
  ) => {
    try {
      const backupId = await databaseMonitoring.initiateBackup(backupType, retentionDays);
      await refreshData(); // Refresh to show new backup
      return backupId;
    } catch (error) {
      throw new Error(`Failed to initiate backup: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [refreshData]);

  const recordMigration = useCallback(async (
    migrationName: string,
    migrationVersion: string,
    rollbackSql?: string,
    migrationChecksum?: string
  ) => {
    try {
      const migrationId = await databaseMonitoring.recordMigration(
        migrationName,
        migrationVersion,
        rollbackSql,
        migrationChecksum
      );
      await refreshData(); // Refresh to show new migration
      return migrationId;
    } catch (error) {
      throw new Error(`Failed to record migration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [refreshData]);

  const collectMetrics = useCallback(async () => {
    try {
      await databaseMonitoring.collectPerformanceMetrics();
      await refreshData();
    } catch (error) {
      throw new Error(`Failed to collect metrics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [refreshData]);

  const startMonitoring = useCallback((intervalMs: number = 60000) => {
    databaseMonitoring.startMonitoring(intervalMs);
  }, []);

  const stopMonitoring = useCallback(() => {
    databaseMonitoring.stopMonitoring();
  }, []);

  return {
    // State
    backups,
    migrations,
    performanceMetrics,
    connectionMetrics,
    healthReport,
    isLoading,
    error,

    // Actions
    refreshData,
    initiateBackup,
    recordMigration,
    collectMetrics,
    startMonitoring,
    stopMonitoring,

    // Utilities
    isMonitoring: databaseMonitoring.isCurrentlyMonitoring(),
    getLatestMetrics: () => performanceMetrics[0],
    getRecentBackups: (hours: number = 24) => {
      const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
      return backups.filter(b => new Date(b.startedAt) > cutoff);
    },
    getFailedBackups: () => backups.filter(b => b.backupStatus === 'failed'),
    getRollbackAvailableMigrations: () => migrations.filter(m => m.rollbackAvailable),
    getSystemHealth: () => ({
      isHealthy: healthReport?.backupStatus === 'healthy' && 
                 healthReport?.performanceStatus === 'healthy' && 
                 healthReport?.connectionStatus === 'healthy',
      criticalIssues: healthReport?.recommendations?.length || 0
    })
  };
}
