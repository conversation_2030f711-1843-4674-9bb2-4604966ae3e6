import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';

interface UseLoadingStateOptions<TData, TError> extends Omit<UseQueryOptions<TData, TError>, 'queryKey' | 'queryFn'> {
  loadingText?: string;
  errorText?: string;
}

export function useLoadingState<TData, TError>(
  queryKey: string[],
  queryFn: () => Promise<TData>,
  options: UseLoadingStateOptions<TData, TError> = {}
): UseQueryResult<TData, TError> & {
  isLoading: boolean;
  isError: boolean;
  error: TError | null;
  loadingText: string;
  errorText: string;
} {
  const {
    loadingText = 'Loading...',
    errorText = 'An error occurred while fetching data',
    ...queryOptions
  } = options;

  const query = useQuery<TData, TError>({
    queryKey,
    queryFn,
    ...queryOptions,
  });

  return {
    ...query,
    loadingText,
    errorText,
  };
} 