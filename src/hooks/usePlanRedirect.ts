
import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { usePlanSelection } from './usePlanSelection';
import { toast } from 'sonner';

export const usePlanRedirect = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { handlePlanSelection } = usePlanSelection();

  useEffect(() => {
    // Check for stored plan selection after authentication
    if (user) {
      const storedPlan = localStorage.getItem('selectedPlan');
      if (storedPlan) {
        try {
          const planData = JSON.parse(storedPlan);
          localStorage.removeItem('selectedPlan');
          
          toast.info('Continuing with your plan selection...', {
            description: `Proceeding with ${planData.planId} plan`
          });
          
          // Small delay to allow the toast to show
          setTimeout(() => {
            handlePlanSelection(
              planData.planId, 
              planData.billingCycle || 'monthly',
              planData.promoCode
            );
          }, 1500);
        } catch (error) {
          console.error('Error processing stored plan selection:', error);
          localStorage.removeItem('selectedPlan');
        }
      }
    }
  }, [user, handlePlanSelection]);

  const storePlanSelection = (planId: string, billingCycle: 'monthly' | 'yearly' = 'monthly', promoCode?: string) => {
    localStorage.setItem('selectedPlan', JSON.stringify({
      planId,
      billingCycle,
      promoCode,
      timestamp: Date.now()
    }));
  };

  const clearStoredPlan = () => {
    localStorage.removeItem('selectedPlan');
  };

  return {
    storePlanSelection,
    clearStoredPlan
  };
};
