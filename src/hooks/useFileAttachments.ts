
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface FileAttachment {
  id: string;
  entity_type: string;
  entity_id: string;
  file_name: string;
  file_type: string;
  file_size: number;
  storage_path: string;
  description?: string;
  uploaded_at: string;
  user_id: string;
}

export const useFileAttachments = (entityType?: string, entityId?: string) => {
  const { currentOrganization } = useOrganization();
  const [attachments, setAttachments] = useState<FileAttachment[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchAttachments = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      let query = supabase
        .from('production_file_attachments')
        .select('*')
        .eq('org_id', currentOrganization.id)
        .order('uploaded_at', { ascending: false });

      if (entityType && entityId) {
        query = query.eq('entity_type', entityType).eq('entity_id', entityId);
      }

      const { data, error } = await query;

      if (error) throw error;
      setAttachments(data || []);
    } catch (error) {
      console.error('Error fetching attachments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch file attachments",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteAttachment = async (attachmentId: string) => {
    try {
      const { error } = await supabase
        .from('production_file_attachments')
        .delete()
        .eq('id', attachmentId);

      if (error) throw error;

      setAttachments(prev => prev.filter(attachment => attachment.id !== attachmentId));
      
      toast({
        title: "Attachment deleted",
        description: "File attachment has been removed"
      });
    } catch (error) {
      console.error('Error deleting attachment:', error);
      toast({
        title: "Error",
        description: "Failed to delete attachment",
        variant: "destructive"
      });
    }
  };

  const getAttachmentsByEntity = (type: string, id: string) => {
    return attachments.filter(attachment => 
      attachment.entity_type === type && attachment.entity_id === id
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  useEffect(() => {
    fetchAttachments();
  }, [currentOrganization, entityType, entityId]);

  return {
    attachments,
    loading,
    deleteAttachment,
    getAttachmentsByEntity,
    formatFileSize,
    refetch: fetchAttachments
  };
};
