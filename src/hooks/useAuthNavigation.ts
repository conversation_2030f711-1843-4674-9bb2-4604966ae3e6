
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCallback, useMemo } from 'react';

// Types for the declarative navigation DSL
export interface NavigationRule {
  condition?: (user: any, profile: any) => boolean;
  path: string;
  replace?: boolean;
  state?: any;
}

export interface AuthNavigationConfig {
  onSignIn?: NavigationRule[];
  onSignOut?: NavigationRule[];
  onSignUp?: NavigationRule[];
  fallbacks?: {
    signIn?: string;
    signOut?: string;
    signUp?: string;
  };
}

export interface AuthNavigationHistory {
  previousPath?: string;
  returnPath?: string;
  timestamp: number;
}

// Default navigation configuration
const DEFAULT_CONFIG: AuthNavigationConfig = {
  onSignIn: [
    {
      condition: (user, profile) => profile?.role === 'admin',
      path: '/admin',
      replace: true
    },
    {
      condition: (user, profile) => profile?.role === 'super_admin',
      path: '/admin',
      replace: true
    },
    {
      path: '/dashboard',
      replace: true
    }
  ],
  onSignOut: [
    {
      path: '/',
      replace: true
    }
  ],
  onSignUp: [
    {
      path: '/auth',
      state: { message: 'Please check your email for verification' }
    }
  ],
  fallbacks: {
    signIn: '/dashboard',
    signOut: '/',
    signUp: '/auth'
  }
};

export const useAuthNavigation = (config?: Partial<AuthNavigationConfig>) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, signOut, signUp, user, profile } = useAuth();

  // Merge provided config with defaults
  const navigationConfig = useMemo(() => ({
    ...DEFAULT_CONFIG,
    ...config,
    onSignIn: config?.onSignIn || DEFAULT_CONFIG.onSignIn,
    onSignOut: config?.onSignOut || DEFAULT_CONFIG.onSignOut,
    onSignUp: config?.onSignUp || DEFAULT_CONFIG.onSignUp,
    fallbacks: { ...DEFAULT_CONFIG.fallbacks, ...config?.fallbacks }
  }), [config]);

  // Navigation history management
  const getNavigationHistory = useCallback((): AuthNavigationHistory => {
    const stored = sessionStorage.getItem('auth_navigation_history');
    return stored ? JSON.parse(stored) : { timestamp: Date.now() };
  }, []);

  const updateNavigationHistory = useCallback((updates: Partial<AuthNavigationHistory>) => {
    const current = getNavigationHistory();
    const updated = { ...current, ...updates, timestamp: Date.now() };
    sessionStorage.setItem('auth_navigation_history', JSON.stringify(updated));
  }, [getNavigationHistory]);

  // Core navigation resolver
  const resolveNavigation = useCallback((rules: NavigationRule[], fallback: string) => {
    for (const rule of rules) {
      if (!rule.condition || rule.condition(user, profile)) {
        return {
          path: rule.path,
          options: {
            replace: rule.replace,
            state: rule.state
          }
        };
      }
    }
    return { path: fallback, options: { replace: true } };
  }, [user, profile]);

  // Enhanced navigation functions
  const navigateAfterAuth = useCallback((
    rules: NavigationRule[], 
    fallback: string,
    historyUpdate?: Partial<AuthNavigationHistory>
  ) => {
    const { path, options } = resolveNavigation(rules, fallback);
    
    if (historyUpdate) {
      updateNavigationHistory(historyUpdate);
    }
    
    navigate(path, options);
  }, [resolveNavigation, updateNavigationHistory, navigate]);

  // Enhanced sign in with navigation
  const signInWithNavigation = useCallback(async (
    email: string, 
    password: string,
    customConfig?: { 
      successPath?: string;
      preserveReturnPath?: boolean;
      replace?: boolean;
    }
  ) => {
    try {
      // Store current location as return path if needed
      if (customConfig?.preserveReturnPath) {
        updateNavigationHistory({ 
          previousPath: location.pathname,
          returnPath: location.state?.from?.pathname || location.pathname 
        });
      }

      await signIn(email, password);
      
      // Use custom path if provided, otherwise use config rules
      if (customConfig?.successPath) {
        navigate(customConfig.successPath, { replace: customConfig.replace });
      } else {
        // Check for return path first
        const history = getNavigationHistory();
        if (customConfig?.preserveReturnPath && history.returnPath) {
          navigate(history.returnPath, { replace: true });
          updateNavigationHistory({ returnPath: undefined });
        } else {
          navigateAfterAuth(
            navigationConfig.onSignIn!,
            navigationConfig.fallbacks!.signIn!,
            { previousPath: location.pathname }
          );
        }
      }
    } catch (error) {
      console.error('Sign in navigation error:', error);
      throw error;
    }
  }, [signIn, navigate, location, navigationConfig, navigateAfterAuth, updateNavigationHistory, getNavigationHistory]);

  // Enhanced sign out with navigation
  const signOutWithNavigation = useCallback(async (customPath?: string) => {
    try {
      updateNavigationHistory({ previousPath: location.pathname });
      
      await signOut();
      
      if (customPath) {
        navigate(customPath, { replace: true });
      } else {
        navigateAfterAuth(
          navigationConfig.onSignOut!,
          navigationConfig.fallbacks!.signOut!
        );
      }
    } catch (error) {
      console.error('Sign out navigation error:', error);
      throw error;
    }
  }, [signOut, navigate, location, navigationConfig, navigateAfterAuth, updateNavigationHistory]);

  // Enhanced sign up with navigation
  const signUpWithNavigation = useCallback(async (
    email: string, 
    password: string, 
    fullName?: string,
    customPath?: string
  ) => {
    try {
      updateNavigationHistory({ previousPath: location.pathname });
      
      await signUp(email, password, fullName);
      
      if (customPath) {
        navigate(customPath);
      } else {
        navigateAfterAuth(
          navigationConfig.onSignUp!,
          navigationConfig.fallbacks!.signUp!
        );
      }
    } catch (error) {
      console.error('Sign up navigation error:', error);
      throw error;
    }
  }, [signUp, navigate, location, navigationConfig, navigateAfterAuth, updateNavigationHistory]);

  // Utility functions
  const goToPreviousPath = useCallback(() => {
    const history = getNavigationHistory();
    if (history.previousPath) {
      navigate(history.previousPath);
    } else {
      navigate(-1);
    }
  }, [navigate, getNavigationHistory]);

  const clearNavigationHistory = useCallback(() => {
    sessionStorage.removeItem('auth_navigation_history');
  }, []);

  // Protected route navigation
  const redirectToAuth = useCallback((returnPath?: string) => {
    const targetPath = returnPath || location.pathname;
    navigate('/auth', { 
      state: { from: { pathname: targetPath } },
      replace: true 
    });
  }, [navigate, location]);

  // Role-based navigation helpers
  const navigateByRole = useCallback((roleRoutes: Record<string, string>, defaultRoute: string = '/dashboard') => {
    const userRole = profile?.role;
    const targetPath = userRole && roleRoutes[userRole] ? roleRoutes[userRole] : defaultRoute;
    navigate(targetPath, { replace: true });
  }, [navigate, profile]);

  return {
    // Enhanced auth navigation functions
    signInWithNavigation,
    signOutWithNavigation,
    signUpWithNavigation,
    
    // Navigation utilities
    goToPreviousPath,
    clearNavigationHistory,
    redirectToAuth,
    navigateByRole,
    
    // History management
    getNavigationHistory,
    updateNavigationHistory,
    
    // Configuration
    navigationConfig,
    
    // Direct access to router
    navigate,
    location
  };
};

// Convenience hook for common auth flows
export const useSimpleAuthNavigation = () => {
  const { signInWithNavigation, signOutWithNavigation, signUpWithNavigation } = useAuthNavigation();
  
  return {
    signIn: signInWithNavigation,
    signOut: signOutWithNavigation,
    signUp: signUpWithNavigation
  };
};

// Hook for role-based navigation configuration
export const useRoleBasedNavigation = (roleConfig: Record<string, string>) => {
  return useAuthNavigation({
    onSignIn: [
      ...Object.entries(roleConfig).map(([role, path]) => ({
        condition: (user: any, profile: any) => profile?.role === role,
        path,
        replace: true
      })),
      {
        path: '/dashboard', // fallback
        replace: true
      }
    ]
  });
};
