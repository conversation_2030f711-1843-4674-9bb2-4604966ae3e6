
import { useEffect, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';

interface DataFetchingConfig {
  enableBackgroundRefetch?: boolean;
  staleTime?: number;
  cacheTime?: number;
  maxConcurrentQueries?: number;
}

/**
 * Hook for managing optimized data fetching with controlled concurrency
 */
export const useOptimizedDataFetching = (config: DataFetchingConfig = {}) => {
  const {
    enableBackgroundRefetch = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    maxConcurrentQueries = 3
  } = config;

  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const activeQueriesRef = useRef(0);
  const queuedQueriesRef = useRef<(() => Promise<any>)[]>([]);

  // Enhanced query queue manager with concurrency control
  const executeQueuedQuery = useCallback(async () => {
    if (activeQueriesRef.current >= maxConcurrentQueries || queuedQueriesRef.current.length === 0) {
      return;
    }

    const query = queuedQueriesRef.current.shift();
    if (!query) return;

    activeQueriesRef.current++;
    
    try {
      await query();
    } catch (error) {
      console.error('Queued query failed:', error);
    } finally {
      activeQueriesRef.current--;
      // Process next query in queue
      setTimeout(executeQueuedQuery, 100);
    }
  }, [maxConcurrentQueries]);

  // Queue a query for execution with concurrency control
  const queueQuery = useCallback((queryFn: () => Promise<any>) => {
    queuedQueriesRef.current.push(queryFn);
    executeQueuedQuery();
  }, [executeQueuedQuery]);

  // Prefetch critical data on app initialization
  const prefetchCriticalData = useCallback(() => {
    if (!user || !currentOrganization) return;

    const criticalQueries = [
      // User profile - highest priority
      () => queryClient.prefetchQuery({
        queryKey: ['profile', user.id],
        queryFn: async () => {
          console.log('Prefetching user profile...');
          // This would be replaced with actual API call
          return { id: user.id, name: user.email };
        },
        staleTime,
        gcTime: cacheTime,
      }),
      
      // Organization data - second priority
      () => queryClient.prefetchQuery({
        queryKey: ['organization', currentOrganization.id],
        queryFn: async () => {
          console.log('Prefetching organization data...');
          return currentOrganization;
        },
        staleTime,
        gcTime: cacheTime,
      }),
    ];

    // Queue critical queries with controlled execution
    criticalQueries.forEach(query => queueQuery(query));
  }, [user, currentOrganization, queryClient, staleTime, cacheTime, queueQuery]);

  // Background refetch for stale data
  const enableBackgroundRefetching = useCallback(() => {
    if (!enableBackgroundRefetch) return;

    const interval = setInterval(() => {
      // Only refetch if user is active and online
      if (document.visibilityState === 'visible' && navigator.onLine) {
        queryClient.invalidateQueries({
          predicate: (query) => {
            const now = Date.now();
            const lastFetched = query.state.dataUpdatedAt;
            return now - lastFetched > staleTime;
          },
        });
      }
    }, staleTime);

    return () => clearInterval(interval);
  }, [enableBackgroundRefetch, queryClient, staleTime]);

  // Initialize optimizations
  useEffect(() => {
    prefetchCriticalData();
    const cleanup = enableBackgroundRefetching();
    
    return cleanup;
  }, [prefetchCriticalData, enableBackgroundRefetching]);

  // Utility to get current queue status
  const getQueueStatus = useCallback(() => ({
    activeQueries: activeQueriesRef.current,
    queuedQueries: queuedQueriesRef.current.length,
    maxConcurrent: maxConcurrentQueries
  }), [maxConcurrentQueries]);

  return {
    queueQuery,
    getQueueStatus,
    prefetchCriticalData
  };
};
