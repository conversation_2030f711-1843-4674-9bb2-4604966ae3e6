
import { useInfiniteQuery, Query<PERSON><PERSON>, UseInfiniteQueryOptions } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { getErrorMessage, getPriorityConfig } from './smartQueryHelpers';

interface SmartQueryConfig {
  priority?: 'high' | 'medium' | 'low';
  enableBackground?: boolean;
  staleTime?: number;
  useOptimizedIndexes?: boolean;
}

/**
 * Smart infinite query hook with optimized pagination and cache management
 */
export const useSmartInfiniteQuery = <T = unknown, TError = Error>(
  queryKey: QueryKey,
  queryFn: ({ pageParam }: { pageParam: unknown }) => Promise<T>,
  config: SmartQueryConfig & Omit<UseInfiniteQueryOptions<T, TError>, 'queryKey' | 'queryFn'> & {
    getNextPageParam?: (lastPage: T) => unknown;
    initialPageParam?: unknown;
  } = {
    getNextPageParam: (lastPage: any) => lastPage?.nextCursor,
    initialPageParam: 0,
  }
) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const {
    priority = 'medium',
    enableBackground = true,
    staleTime = 5 * 60 * 1000,
    useOptimizedIndexes = true,
    getNextPageParam = (lastPage: any) => lastPage?.nextCursor,
    initialPageParam = 0,
    ...queryOptions
  } = config;

  const priorityConfig = getPriorityConfig(priority, useOptimizedIndexes);

  return useInfiniteQuery({
    queryKey,
    queryFn,
    staleTime: priorityConfig.staleTime,
    gcTime: priorityConfig.gcTime,
    refetchInterval: priorityConfig.refetchInterval,
    refetchOnWindowFocus: enableBackground && priority === 'high',
    refetchOnReconnect: enableBackground,
    enabled: !!user && !!currentOrganization,
    initialPageParam,
    getNextPageParam,
    retry: (failureCount, error) => {
      const errorMessage = getErrorMessage(error);
      if (errorMessage.includes('auth') || errorMessage.includes('permission')) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...queryOptions,
  });
};
