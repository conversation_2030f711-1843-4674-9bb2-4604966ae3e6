
import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

export const useCustomerPortal = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const openCustomerPortal = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to manage your subscription",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    
    try {
      toast({
        title: "Opening subscription management...",
        description: "Redirecting to Stripe Customer Portal",
      });

      const { data, error } = await supabase.functions.invoke('customer-portal');

      if (error) {
        throw error;
      }

      if (data?.url) {
        // Small delay to let the user see the toast, then open portal
        setTimeout(() => {
          window.open(data.url, '_blank');
        }, 500);
      } else {
        throw new Error('No portal URL received');
      }
    } catch (error) {
      console.error('Error opening customer portal:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to open subscription management. Please try again.",
        variant: "destructive"
      });
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  return {
    openCustomerPortal,
    loading
  };
};
