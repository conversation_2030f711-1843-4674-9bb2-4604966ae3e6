
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';

export const useDashboardState = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [activeTab, setActiveTab] = useState('overview');

  // Get tab from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');
    const tool = urlParams.get('tool');
    
    if (tab) {
      setActiveTab(tab);
    } else if (tool) {
      // Map tools to tabs
      const toolToTabMap: Record<string, string> = {
        'script_editor': 'editor',
        'coverage_generator': 'coverage',
        'storyboard_studio': 'storyboard',
        'marketplace': 'marketplace',
        'production_tools': 'production'
      };
      
      if (toolToTabMap[tool]) {
        setActiveTab(toolToTabMap[tool]);
      }
    }
  }, []);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update URL without page reload
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    window.history.pushState({}, '', url.toString());
  };

  return {
    user,
    currentOrganization,
    activeTab,
    handleTabChange
  };
};
