
import { useState, useCallback } from 'react';
import { useError<PERSON><PERSON><PERSON> } from './useErrorHandler';

interface ErrorRecoveryState {
  isRetrying: boolean;
  retryCount: number;
  lastError?: Error;
}

export const useErrorRecovery = (maxRetries: number = 3) => {
  const [state, setState] = useState<ErrorRecoveryState>({
    isRetrying: false,
    retryCount: 0
  });
  const { handleError } = useErrorHandler();

  const retry = useCallback(async (operation: () => Promise<any>) => {
    if (state.retryCount >= maxRetries) {
      handleError('Maximum retry attempts reached');
      return null;
    }

    setState(prev => ({ ...prev, isRetrying: true }));
    
    try {
      const result = await operation();
      setState({ isRetrying: false, retryCount: 0 });
      return result;
    } catch (error) {
      setState(prev => ({
        isRetrying: false,
        retryCount: prev.retryCount + 1,
        lastError: error as Error
      }));
      
      // Exponential backoff
      const delay = Math.pow(2, state.retryCount) * 1000;
      setTimeout(() => {
        if (state.retryCount < maxRetries) {
          retry(operation);
        }
      }, delay);
      
      return null;
    }
  }, [state.retryCount, maxRetries, handleError]);

  const reset = useCallback(() => {
    setState({ isRetrying: false, retryCount: 0 });
  }, []);

  return {
    retry,
    reset,
    isRetrying: state.isRetrying,
    retryCount: state.retryCount,
    canRetry: state.retryCount < maxRetries,
    lastError: state.lastError
  };
};
