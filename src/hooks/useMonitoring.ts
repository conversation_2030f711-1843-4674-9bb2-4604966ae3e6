
import { useState, useEffect, useCallback } from 'react';
import { healthCheckManager, type SystemHealth } from '@/lib/monitoring/healthChecks';
import { alertingSystem, type Alert } from '@/lib/monitoring/alertingSystem';
import { systemMonitor, type MonitoringMetrics } from '@/lib/monitoring/systemMonitor';
import { disasterRecovery } from '@/lib/monitoring/disasterRecovery';

export function useMonitoring() {
  const [healthStatus, setHealthStatus] = useState<SystemHealth | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [metrics, setMetrics] = useState<MonitoringMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const refreshData = useCallback(async () => {
    try {
      setError(null);
      const [health, alertData, metricsData] = await Promise.all([
        healthCheckManager.runAllChecks(),
        Promise.resolve(alertingSystem.getAlerts()),
        Promise.resolve(systemMonitor.getMetrics(10))
      ]);
      
      setHealthStatus(health);
      setAlerts(alertData);
      setMetrics(metricsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load monitoring data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Start monitoring
    systemMonitor.startMonitoring(30000);
    
    // Initial load
    refreshData();
    
    // Set up periodic refresh
    const interval = setInterval(refreshData, 30000);
    
    return () => {
      clearInterval(interval);
      systemMonitor.stopMonitoring();
    };
  }, [refreshData]);

  const acknowledgeAlert = useCallback((alertId: string) => {
    alertingSystem.acknowledgeAlert(alertId);
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, acknowledged: true } : alert
    ));
  }, []);

  const resolveAlert = useCallback((alertId: string) => {
    alertingSystem.resolveAlert(alertId);
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolvedAt: new Date().toISOString() } : alert
    ));
  }, []);

  const triggerAlert = useCallback(async (alertData: Omit<Alert, 'id' | 'timestamp'>) => {
    const alert = await alertingSystem.triggerAlert(alertData);
    setAlerts(prev => [alert, ...prev]);
    return alert;
  }, []);

  const testRecoveryPlan = useCallback(async (planId: string, dryRun: boolean = true) => {
    try {
      const result = await disasterRecovery.testRecoveryPlan(planId, dryRun);
      await triggerAlert({
        level: 'info',
        title: 'Recovery Plan Test',
        message: `Recovery plan test ${result.success ? 'passed' : 'failed'} in ${result.duration}ms`,
        source: 'disaster-recovery-test',
        metadata: { planId, result }
      });
      return result;
    } catch (error) {
      await triggerAlert({
        level: 'error',
        title: 'Recovery Plan Test Failed',
        message: `Failed to test recovery plan: ${error instanceof Error ? error.message : 'Unknown error'}`,
        source: 'disaster-recovery-test',
        metadata: { planId, error: error instanceof Error ? error.message : 'Unknown error' }
      });
      throw error;
    }
  }, [triggerAlert]);

  const runHealthCheck = useCallback(async (checkName?: string) => {
    try {
      if (checkName) {
        return await healthCheckManager.runCheck(checkName);
      } else {
        const health = await healthCheckManager.runAllChecks();
        setHealthStatus(health);
        return health;
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Health check failed');
      throw error;
    }
  }, []);

  return {
    // State
    healthStatus,
    alerts,
    metrics,
    isLoading,
    error,
    
    // Actions
    refreshData,
    acknowledgeAlert,
    resolveAlert,
    triggerAlert,
    testRecoveryPlan,
    runHealthCheck,
    
    // Utilities
    getActiveAlerts: () => alerts.filter(alert => !alert.resolvedAt),
    getCriticalAlerts: () => alerts.filter(alert => alert.level === 'critical' && !alert.resolvedAt),
    getSystemStatus: () => healthStatus?.status || 'unknown',
    isSystemHealthy: () => healthStatus?.status === 'healthy'
  };
}
