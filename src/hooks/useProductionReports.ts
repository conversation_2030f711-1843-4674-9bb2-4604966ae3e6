
import { reportsApi } from '@/lib/api/production';
import { useProductionDataManager } from './useProductionDataManager';
import { createReportSchema, type CreateReportInput } from '@/features/production/validation/schemas';
import type { ProductionReport } from '@/lib/api/production';

export const useProductionReports = () => {
  const baseHook = useProductionDataManager<ProductionReport, CreateReportInput>({
    entityName: 'report',
    apiService: {
      getItems: reportsApi.getReports,
      createItem: reportsApi.createReport
    },
    validationSchema: createReportSchema,
    getDisplayName: (report) => report.title
  });

  return {
    reports: baseHook.items,
    loading: baseHook.loading,
    creating: baseHook.creating,
    errors: baseHook.errors,
    createReport: baseHook.createItem,
    fetchReports: baseHook.fetchItems,
    clearErrors: baseHook.clearErrors
  };
};
