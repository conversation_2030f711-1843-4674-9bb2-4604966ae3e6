
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface BetaMetrics {
  activeUsers: number;
  acceptanceRate: number;
  feedbackCount: number;
  bugReports: number;
  retentionRate: number;
  npsScore: number;
}

export function useBetaMetrics() {
  const [metrics, setMetrics] = useState<BetaMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchMetrics = async () => {
    try {
      // Get latest metrics summary
      const { data: metricsData, error: metricsError } = await supabase
        .from('beta_metrics_summary')
        .select('*')
        .order('date', { ascending: false })
        .limit(1)
        .single();

      if (metricsError && metricsError.code !== 'PGRST116') {
        throw metricsError;
      }

      // Get current counts if no metrics summary exists
      const [invitationsResult, feedbackResult, cohortsResult] = await Promise.all([
        supabase
          .from('beta_invitations')
          .select('status')
          .eq('status', 'accepted'),
        supabase
          .from('beta_feedback')
          .select('feedback_type'),
        supabase
          .from('beta_cohorts')
          .select('is_active')
          .eq('is_active', true)
      ]);

      const acceptedInvitations = invitationsResult.data?.length || 0;
      const totalFeedback = feedbackResult.data?.length || 0;
      const bugReports = feedbackResult.data?.filter(f => f.feedback_type === 'bug_report').length || 0;
      const activeUsers = cohortsResult.data?.length || 0;

      setMetrics({
        activeUsers: metricsData?.active_users || activeUsers,
        acceptanceRate: metricsData?.conversion_rate || 0,
        feedbackCount: metricsData?.feedback_count || totalFeedback,
        bugReports: metricsData?.bug_reports || bugReports,
        retentionRate: metricsData?.retention_rate || 0,
        npsScore: metricsData?.nps_score || 0,
      });
    } catch (error) {
      console.error('Error fetching beta metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, []);

  return {
    metrics,
    loading,
    refetch: fetchMetrics,
  };
}
