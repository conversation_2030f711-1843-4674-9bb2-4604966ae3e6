import { useEffect, useRef, useCallback, useMemo } from 'react';

/**
 * Performance optimization hook for heavy components
 * Provides debouncing, throttling, and intersection observer utilities
 */
export const usePerformanceOptimization = () => {
  // Debounce function for search inputs and form fields
  const useDebounce = <T>(value: T, delay: number): T => {
    const [debouncedValue, setDebouncedValue] = useState<T>(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  // Throttle function for scroll events and resize handlers
  const useThrottle = <T extends (...args: any[]) => any>(
    callback: T,
    delay: number
  ): T => {
    const lastRun = useRef(Date.now());

    return useCallback(
      ((...args) => {
        if (Date.now() - lastRun.current >= delay) {
          callback(...args);
          lastRun.current = Date.now();
        }
      }) as T,
      [callback, delay]
    );
  };

  // Intersection Observer for lazy loading
  const useIntersectionObserver = (
    options: IntersectionObserverInit = {}
  ) => {
    const [isIntersecting, setIsIntersecting] = useState(false);
    const targetRef = useRef<HTMLElement>(null);

    useEffect(() => {
      const observer = new IntersectionObserver(([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      }, options);

      const currentTarget = targetRef.current;
      if (currentTarget) {
        observer.observe(currentTarget);
      }

      return () => {
        if (currentTarget) {
          observer.unobserve(currentTarget);
        }
      };
    }, [options]);

    return { targetRef, isIntersecting };
  };

  // Memoized array comparison for preventing unnecessary re-renders
  const useDeepCompareMemo = <T>(factory: () => T, deps: any[]): T => {
    const ref = useRef<{ deps: any[]; value: T }>();

    if (!ref.current || !areEqual(deps, ref.current.deps)) {
      ref.current = { deps, value: factory() };
    }

    return ref.current.value;
  };

  // Simple deep equality check for arrays and objects
  const areEqual = (a: any[], b: any[]): boolean => {
    if (a.length !== b.length) return false;
    return a.every((val, index) => {
      if (typeof val === 'object' && val !== null) {
        return JSON.stringify(val) === JSON.stringify(b[index]);
      }
      return val === b[index];
    });
  };

  // Virtual scrolling for large lists
  const useVirtualScrolling = (
    items: any[],
    itemHeight: number,
    containerHeight: number
  ) => {
    const [scrollTop, setScrollTop] = useState(0);

    const visibleItems = useMemo(() => {
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / itemHeight) + 1,
        items.length
      );

      return {
        startIndex,
        endIndex,
        items: items.slice(startIndex, endIndex),
        totalHeight: items.length * itemHeight,
        offsetY: startIndex * itemHeight
      };
    }, [items, itemHeight, containerHeight, scrollTop]);

    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      setScrollTop(e.currentTarget.scrollTop);
    }, []);

    return { visibleItems, handleScroll };
  };

  return {
    useDebounce,
    useThrottle,
    useIntersectionObserver,
    useDeepCompareMemo,
    useVirtualScrolling
  };
};

// Import useState at the top
import { useState } from 'react';

/**
 * Lazy loading component wrapper
 */
export const LazyWrapper: React.FC<{
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
}> = ({ children, fallback = <div>Loading...</div>, threshold = 0.1 }) => {
  const { targetRef, isIntersecting } = usePerformanceOptimization().useIntersectionObserver({
    threshold
  });

  return (
    <div ref={targetRef}>
      {isIntersecting ? children : fallback}
    </div>
  );
};

/**
 * Performance monitoring hook
 */
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const startTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const renderTime = Date.now() - startTime.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} rendered ${renderCount.current} times in ${renderTime}ms`);
    }
    
    startTime.current = Date.now();
  });

  return { renderCount: renderCount.current };
};

/**
 * Optimized list component for large datasets
 */
export const OptimizedList: React.FC<{
  items: any[];
  renderItem: (item: any, index: number) => React.ReactNode;
  itemHeight?: number;
  containerHeight?: number;
  keyExtractor: (item: any, index: number) => string;
}> = ({ 
  items, 
  renderItem, 
  itemHeight = 50, 
  containerHeight = 400,
  keyExtractor 
}) => {
  const { useVirtualScrolling } = usePerformanceOptimization();
  const { visibleItems, handleScroll } = useVirtualScrolling(items, itemHeight, containerHeight);

  return (
    <div 
      style={{ height: containerHeight, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: visibleItems.totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${visibleItems.offsetY}px)` }}>
          {visibleItems.items.map((item, index) => (
            <div key={keyExtractor(item, visibleItems.startIndex + index)}>
              {renderItem(item, visibleItems.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

/**
 * Image lazy loading component
 */
export const LazyImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
}> = ({ src, alt, className, placeholder = '/placeholder.jpg' }) => {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [isLoaded, setIsLoaded] = useState(false);
  const { targetRef, isIntersecting } = usePerformanceOptimization().useIntersectionObserver();

  useEffect(() => {
    if (isIntersecting && !isLoaded) {
      const img = new Image();
      img.onload = () => {
        setImageSrc(src);
        setIsLoaded(true);
      };
      img.src = src;
    }
  }, [isIntersecting, isLoaded, src]);

  return (
    <img
      ref={targetRef}
      src={imageSrc}
      alt={alt}
      className={className}
      loading="lazy"
    />
  );
};
