
import { useState } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

export interface ExportData {
  data_type: string;
  export_data: any;
  record_count: number;
}

export const useProductionExport = () => {
  const { currentOrganization } = useOrganization();
  const [exporting, setExporting] = useState(false);

  const exportData = async () => {
    if (!currentOrganization) return null;

    setExporting(true);
    try {
      const { data, error } = await supabase.rpc('export_production_data', {
        org_id_param: currentOrganization.id
      });

      if (error) throw error;

      // Create downloadable JSON file
      const exportObj = {
        organization: currentOrganization.name,
        exported_at: new Date().toISOString(),
        data: data
      };

      const blob = new Blob([JSON.stringify(exportObj, null, 2)], {
        type: 'application/json'
      });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `production_data_${currentOrganization.name}_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: "Production data has been exported successfully",
      });

      return data;
    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: "Export Error",
        description: "Failed to export production data",
        variant: "destructive"
      });
      return null;
    } finally {
      setExporting(false);
    }
  };

  return {
    exporting,
    exportData
  };
};
