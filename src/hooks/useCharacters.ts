
import { useState, useEffect } from 'react';
import { charactersApi } from '@/lib/api';
import { useOrganization } from '@/contexts/OrganizationContext';

interface Character {
  id: string;
  name: string;
  description?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export const useCharacters = () => {
  const { currentOrganization } = useOrganization();
  const [characters, setCharacters] = useState<Character[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchCharacters = async () => {
    if (!currentOrganization) return;
    
    setLoading(true);
    try {
      const result = await charactersApi.getCharacters(currentOrganization.id);
      if (result.success && result.data) {
        setCharacters(result.data);
      }
    } catch (error) {
      console.error('Error fetching characters:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCharacter = async (data: { 
    name: string; 
    description?: string; 
    notes?: string; 
  }) => {
    if (!currentOrganization) return;

    try {
      const result = await charactersApi.createCharacter({
        ...data,
        org_id: currentOrganization.id
      });
      if (result.success) {
        await fetchCharacters();
        return result;
      }
    } catch (error) {
      console.error('Error creating character:', error);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      fetchCharacters();
    }
  }, [currentOrganization]);

  return {
    characters,
    loading,
    fetchCharacters,
    createCharacter
  };
};
