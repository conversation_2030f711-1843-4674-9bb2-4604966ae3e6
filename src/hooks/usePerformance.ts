import { useEffect, useCallback } from 'react';
import { performanceMonitoring } from '../lib/performance';

interface PerformanceOptions {
  componentName: string;
  trackRenderTime?: boolean;
  trackInteractionTime?: boolean;
}

export function usePerformance({ componentName, trackRenderTime = true, trackInteractionTime = true }: PerformanceOptions) {
  useEffect(() => {
    if (trackRenderTime) {
      const startTime = performance.now();
      
      return () => {
        const renderTime = performance.now() - startTime;
        performanceMonitoring.trackCustomMetric(`${componentName}_render_time`, renderTime);
      };
    }
  }, [componentName, trackRenderTime]);

  const trackInteraction = useCallback((interactionName: string) => {
    if (trackInteractionTime) {
      const startTime = performance.now();
      
      return () => {
        const interactionTime = performance.now() - startTime;
        performanceMonitoring.trackCustomMetric(
          `${componentName}_${interactionName}_time`,
          interactionTime
        );
      };
    }
    return () => {};
  }, [componentName, trackInteractionTime]);

  return {
    trackInteraction,
  };
}

// Example usage:
/*
function MyComponent() {
  const { trackInteraction } = usePerformance({
    componentName: 'MyComponent',
    trackRenderTime: true,
    trackInteractionTime: true,
  });

  const handleClick = () => {
    const endTracking = trackInteraction('button_click');
    // Do something
    endTracking();
  };

  return <button onClick={handleClick}>Click me</button>;
}
*/ 