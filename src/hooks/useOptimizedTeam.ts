import { useCallback, useMemo } from 'react';
import { teamApi, type TeamAccess, type TeamActivity, type TeamDiscussion, type TeamMembership } from '@/lib/api/team';
import { useOptimizedQuery, useOptimizedMutation, usePaginatedQuery, useBackgroundSync, useQueryUtils } from './useOptimizedQuery';
import { useToast } from './use-toast';

/**
 * Optimized team data management with React Query and caching
 */
export const useOptimizedTeam = (orgId?: string) => {
  const { toast } = useToast();
  const { invalidateQueries, setQueryData, prefetchQuery } = useQueryUtils();

  // Query keys
  const teamAccessQueryKey = useMemo(() => ['team-access', orgId], [orgId]);
  const teamActivitiesQueryKey = useMemo(() => ['team-activities', orgId], [orgId]);
  const teamDiscussionsQueryKey = useMemo(() => ['team-discussions', orgId], [orgId]);
  const teamMembersQueryKey = useMemo(() => ['team-members', orgId], [orgId]);

  // Team access with long cache time (rarely changes)
  const {
    data: teamAccess,
    isLoading: isLoadingAccess,
    error: accessError
  } = useOptimizedQuery(
    teamAccessQueryKey,
    () => teamApi.getTeamAccess(orgId!),
    {
      enabled: Boolean(orgId),
      staleTime: 30 * 60 * 1000, // 30 minutes
      cacheTime: 60 * 60 * 1000, // 1 hour
      select: (data) => data.success ? data.data : null,
    }
  );

  // Team activities with background sync
  const {
    data: activities = [],
    isLoading: isLoadingActivities,
    error: activitiesError,
    refetch: refetchActivities
  } = useBackgroundSync(
    teamActivitiesQueryKey,
    async () => {
      const result = await teamApi.getTeamActivities(orgId!, 50);
      return result.success ? result.data || [] : [];
    },
    {
      enabled: Boolean(orgId),
      syncInterval: 30 * 1000, // 30 seconds
    }
  );

  // Team discussions with pagination
  const useTeamDiscussions = (page: number = 1, limit: number = 20) => {
    const {
      data: discussionsData,
      isLoading: isLoadingDiscussions,
      error: discussionsError
    } = usePaginatedQuery(
      teamDiscussionsQueryKey,
      async (page, limit) => {
        const result = await teamApi.getTeamDiscussions(orgId!, limit, (page - 1) * limit);
        if (result.success && result.data) {
          return {
            data: result.data,
            total: result.data.length // This should come from API
          };
        }
        return { data: [], total: 0 };
      },
      {
        page,
        limit,
        staleTime: 2 * 60 * 1000, // 2 minutes
      }
    );

    return {
      discussions: discussionsData?.data || [],
      totalDiscussions: discussionsData?.total || 0,
      isLoadingDiscussions,
      discussionsError,
    };
  };

  // Create discussion mutation
  const createDiscussionMutation = useOptimizedMutation(
    (discussionData: { title: string; content: string; type: string; isAnnouncement?: boolean }) =>
      teamApi.createDiscussion(orgId!, discussionData.title, discussionData.content, discussionData.type, discussionData.isAnnouncement),
    {
      invalidateQueries: [teamDiscussionsQueryKey, teamActivitiesQueryKey],
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Discussion created successfully",
        });
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to create discussion",
          variant: "destructive",
        });
      }
    }
  );

  // Add reply mutation
  const addReplyMutation = useOptimizedMutation(
    ({ discussionId, content }: { discussionId: string; content: string }) =>
      teamApi.addReply(discussionId, content),
    {
      invalidateQueries: [teamDiscussionsQueryKey, teamActivitiesQueryKey],
      onSuccess: () => {
        toast({
          title: "Success",
          description: "Reply added successfully",
        });
      },
      onError: (error: any) => {
        toast({
          title: "Error",
          description: error.message || "Failed to add reply",
          variant: "destructive",
        });
      }
    }
  );

  // Track activity mutation
  const trackActivityMutation = useOptimizedMutation(
    (activityData: {
      activityType: string;
      description: string;
      entityType?: string;
      entityId?: string;
      metadata?: any;
    }) => teamApi.trackActivity(
      orgId!,
      activityData.activityType,
      activityData.description,
      activityData.entityType,
      activityData.entityId,
      activityData.metadata
    ),
    {
      updateQueries: [{
        queryKey: teamActivitiesQueryKey,
        updater: (oldData: TeamActivity[], newData: any) => {
          if (!newData.success || !oldData) return oldData;
          return [newData.data, ...oldData].slice(0, 50); // Keep only latest 50
        }
      }],
      onError: (error: any) => {
        console.error('Failed to track activity:', error);
      }
    }
  );

  // Project team management
  const useProjectTeam = (projectId?: string) => {
    const projectTeamQueryKey = useMemo(() => 
      ['project-team', projectId], [projectId]
    );

    const {
      data: projectMembers = [],
      isLoading: isLoadingProjectMembers,
      error: projectMembersError
    } = useOptimizedQuery(
      projectTeamQueryKey,
      () => teamApi.getProjectTeamMembers(projectId!),
      {
        enabled: Boolean(projectId),
        staleTime: 5 * 60 * 1000, // 5 minutes
        select: (data) => data.success ? data.data || [] : [],
      }
    );

    // Add project member mutation
    const addProjectMemberMutation = useOptimizedMutation(
      (memberData: {
        userId: string;
        role?: string;
        permissions?: any;
        invitedBy: string;
      }) => teamApi.addProjectMember({
        projectId: projectId!,
        ...memberData
      }),
      {
        invalidateQueries: [projectTeamQueryKey, teamActivitiesQueryKey],
        onSuccess: () => {
          toast({
            title: "Success",
            description: "Team member added successfully",
          });
        },
        onError: (error: any) => {
          toast({
            title: "Error",
            description: error.message || "Failed to add team member",
            variant: "destructive",
          });
        }
      }
    );

    return {
      projectMembers,
      isLoadingProjectMembers,
      projectMembersError,
      addProjectMember: addProjectMemberMutation.mutate,
      isAddingMember: addProjectMemberMutation.isPending,
    };
  };

  // Prefetch related data
  const prefetchTeamData = useCallback(async () => {
    if (!orgId) return;

    const prefetchPromises = [
      prefetchQuery(teamActivitiesQueryKey, () => teamApi.getTeamActivities(orgId, 20)),
      prefetchQuery(teamDiscussionsQueryKey, () => teamApi.getTeamDiscussions(orgId, 10)),
    ];

    await Promise.allSettled(prefetchPromises);
  }, [orgId, prefetchQuery, teamActivitiesQueryKey, teamDiscussionsQueryKey]);

  // Memoized actions
  const actions = useMemo(() => ({
    createDiscussion: createDiscussionMutation.mutate,
    addReply: addReplyMutation.mutate,
    trackActivity: trackActivityMutation.mutate,
    refetchActivities,
    prefetchTeamData,
  }), [
    createDiscussionMutation.mutate,
    addReplyMutation.mutate,
    trackActivityMutation.mutate,
    refetchActivities,
    prefetchTeamData,
  ]);

  // Memoized loading states
  const loadingStates = useMemo(() => ({
    isLoadingAccess,
    isLoadingActivities,
    isCreatingDiscussion: createDiscussionMutation.isPending,
    isAddingReply: addReplyMutation.isPending,
    isTrackingActivity: trackActivityMutation.isPending,
  }), [
    isLoadingAccess,
    isLoadingActivities,
    createDiscussionMutation.isPending,
    addReplyMutation.isPending,
    trackActivityMutation.isPending,
  ]);

  // Memoized error states
  const errorStates = useMemo(() => ({
    accessError,
    activitiesError,
    createDiscussionError: createDiscussionMutation.error,
    addReplyError: addReplyMutation.error,
    trackActivityError: trackActivityMutation.error,
  }), [
    accessError,
    activitiesError,
    createDiscussionMutation.error,
    addReplyMutation.error,
    trackActivityMutation.error,
  ]);

  // Auto-prefetch when team access is available
  useMemo(() => {
    if (teamAccess?.can_manage_teams) {
      prefetchTeamData();
    }
  }, [teamAccess?.can_manage_teams, prefetchTeamData]);

  return {
    // Data
    teamAccess,
    activities,
    
    // Loading states
    ...loadingStates,
    
    // Error states
    ...errorStates,
    
    // Actions
    ...actions,
    
    // Hooks for specific features
    useTeamDiscussions,
    useProjectTeam,
  };
};

export default useOptimizedTeam;
