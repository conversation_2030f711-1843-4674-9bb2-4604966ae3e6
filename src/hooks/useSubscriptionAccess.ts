import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';

export type SubscriptionTier = 'starter' | 'pro-solo' | 'pro-team' | 'studio' | 'enterprise';

export interface SubscriptionStatus {
  subscribed: boolean;
  tier: SubscriptionTier | null;
  subscriptionEnd: string | null;
  loading: boolean;
  error: string | null;
  lastChecked?: number;
}

interface SubscriberRecord {
  id: string;
  user_id: string;
  email: string;
  stripe_customer_id: string | null;
  subscribed: boolean;
  subscription_tier: string | null;
  subscription_end: string | null;
  updated_at: string;
  created_at: string;
}

// Enhanced cache with metadata
interface CacheEntry {
  data: SubscriptionStatus;
  timestamp: number;
  freshUntil: number;
  staleUntil: number;
}

// Global cache with longer durations and stale-while-revalidate pattern
const subscriptionCache = new Map<string, CacheEntry>();
const FRESH_DURATION = 2 * 60 * 1000; // 2 minutes - data is fresh
const STALE_DURATION = 10 * 60 * 1000; // 10 minutes - serve stale data while revalidating
const MAX_AGE = 30 * 60 * 1000; // 30 minutes - maximum cache age

// Background refresh queue
const refreshQueue = new Set<string>();
let backgroundRefreshInProgress = false;

export const useSubscriptionAccess = () => {
  const { user } = useAuth();
  const [status, setStatus] = useState<SubscriptionStatus>({
    subscribed: false,
    tier: null,
    subscriptionEnd: null,
    loading: true,
    error: null
  });

  const refreshTimeoutRef = useRef<NodeJS.Timeout>();
  const isMountedRef = useRef(true);

  // Background refresh function
  const performBackgroundRefresh = useCallback(async (userId: string) => {
    if (backgroundRefreshInProgress || refreshQueue.has(userId)) {
      return;
    }

    refreshQueue.add(userId);
    backgroundRefreshInProgress = true;

    try {
      console.log('[Subscription] Starting background refresh for user:', userId);

      // First check local database for cached subscription data
      const { data: subscribers, error: dbError } = await supabase
        .from('subscribers')
        .select('*')
        .eq('user_id', userId)
        .single();

      let shouldCheckStripe = true;
      let result: SubscriptionStatus;

      if (!dbError && subscribers) {
        const subscriberData = subscribers as unknown as SubscriberRecord;
        const lastUpdate = new Date(subscriberData.updated_at);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        // If database data is recent, use it
        if (lastUpdate > fiveMinutesAgo) {
          result = {
            subscribed: subscriberData.subscribed,
            tier: subscriberData.subscription_tier as SubscriptionTier,
            subscriptionEnd: subscriberData.subscription_end,
            loading: false,
            error: null,
            lastChecked: Date.now()
          };
          shouldCheckStripe = false;
          console.log('[Subscription] Using cached database data');
        }
      }

      // Only check Stripe if database data is stale
      if (shouldCheckStripe) {
        console.log('[Subscription] Checking with Stripe API');
        const { data, error } = await supabase.functions.invoke('check-subscription');

        if (error) {
          throw error;
        }

        result = {
          subscribed: data.subscribed || false,
          tier: data.subscription_tier as SubscriptionTier,
          subscriptionEnd: data.subscription_end,
          loading: false,
          error: null,
          lastChecked: Date.now()
        };
      }

      // Update cache with new data
      const now = Date.now();
      const cacheEntry: CacheEntry = {
        data: result!,
        timestamp: now,
        freshUntil: now + FRESH_DURATION,
        staleUntil: now + STALE_DURATION
      };
      subscriptionCache.set(userId, cacheEntry);

      // Update state if component is still mounted
      if (isMountedRef.current) {
        setStatus(result!);
      }

      console.log('[Subscription] Background refresh completed successfully');
    } catch (error) {
      console.error('[Subscription] Background refresh failed:', error);
      
      // On error, update cache entry to mark the error but keep existing data if available
      const existingCache = subscriptionCache.get(userId);
      if (existingCache && isMountedRef.current) {
        setStatus(prev => ({ 
          ...prev, 
          error: error instanceof Error ? error.message : 'Background refresh failed',
          lastChecked: Date.now()
        }));
      }
    } finally {
      refreshQueue.delete(userId);
      backgroundRefreshInProgress = false;
    }
  }, []);

  const checkSubscription = useCallback(async () => {
    if (!user) {
      setStatus({
        subscribed: false,
        tier: null,
        subscriptionEnd: null,
        loading: false,
        error: null
      });
      return;
    }

    const userId = user.id;
    const now = Date.now();
    const cached = subscriptionCache.get(userId);

    // If we have fresh data, use it immediately
    if (cached && now < cached.freshUntil) {
      console.log('[Subscription] Using fresh cached data');
      setStatus(cached.data);
      return;
    }

    // If we have stale data, use it optimistically and refresh in background
    if (cached && now < cached.staleUntil) {
      console.log('[Subscription] Using stale data optimistically, refreshing in background');
      setStatus({ ...cached.data, loading: false });
      
      // Trigger background refresh
      setTimeout(() => performBackgroundRefresh(userId), 0);
      return;
    }

    // If no cache or very stale data, show loading and fetch
    setStatus(prev => ({ ...prev, loading: true, error: null }));

    try {
      console.log('[Subscription] No valid cache, fetching fresh data');

      // Check local database first
      const { data: subscribers, error: dbError } = await supabase
        .from('subscribers')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (!dbError && subscribers) {
        const subscriberData = subscribers as unknown as SubscriberRecord;
        const lastUpdate = new Date(subscriberData.updated_at);
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
        
        if (lastUpdate > tenMinutesAgo) {
          const result = {
            subscribed: subscriberData.subscribed,
            tier: subscriberData.subscription_tier as SubscriptionTier,
            subscriptionEnd: subscriberData.subscription_end,
            loading: false,
            error: null,
            lastChecked: Date.now()
          };
          
          // Cache the result
          const cacheEntry: CacheEntry = {
            data: result,
            timestamp: now,
            freshUntil: now + FRESH_DURATION,
            staleUntil: now + STALE_DURATION
          };
          subscriptionCache.set(userId, cacheEntry);
          setStatus(result);
          return;
        }
      }

      // Fallback to Stripe check
      const { data, error } = await supabase.functions.invoke('check-subscription');

      if (error) {
        throw error;
      }

      const result = {
        subscribed: data.subscribed || false,
        tier: data.subscription_tier as SubscriptionTier,
        subscriptionEnd: data.subscription_end,
        loading: false,
        error: null,
        lastChecked: Date.now()
      };

      // Cache the result
      const cacheEntry: CacheEntry = {
        data: result,
        timestamp: now,
        freshUntil: now + FRESH_DURATION,
        staleUntil: now + STALE_DURATION
      };
      subscriptionCache.set(userId, cacheEntry);
      setStatus(result);
    } catch (error) {
      console.error('[Subscription] Error checking subscription:', error);
      const errorResult = {
        subscribed: false,
        tier: null,
        subscriptionEnd: null,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to check subscription',
        lastChecked: Date.now()
      };
      setStatus(errorResult);
    }
  }, [user, performBackgroundRefresh]);

  const refreshSubscription = useCallback(() => {
    // Clear cache for this user to force fresh fetch
    if (user) {
      subscriptionCache.delete(user.id);
      console.log('[Subscription] Cache cleared, forcing refresh');
    }
    return checkSubscription();
  }, [user, checkSubscription]);

  // Set up periodic background refresh
  useEffect(() => {
    if (!user) return;

    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Set up periodic refresh every 5 minutes
    const setupPeriodicRefresh = () => {
      refreshTimeoutRef.current = setTimeout(() => {
        if (user && isMountedRef.current) {
          performBackgroundRefresh(user.id);
          setupPeriodicRefresh(); // Schedule next refresh
        }
      }, 5 * 60 * 1000); // 5 minutes
    };

    setupPeriodicRefresh();

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [user, performBackgroundRefresh]);

  useEffect(() => {
    checkSubscription();
  }, [checkSubscription]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...status,
    refreshSubscription
  };
};
