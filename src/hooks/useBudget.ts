
import { useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { useBudgetsQuery, useCreateBudgetMutation } from '@/lib/api/production/queries';
import { validateFormData, formatValidationErrors, createBudgetSchema } from '@/features/production/validation/schemas';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import type { CreateBudgetInput } from '@/features/production/validation/schemas';

/**
 * Reusable hook for budget operations with React Query caching
 */
export const useBudget = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const budgetsQuery = useBudgetsQuery();
  const createMutation = useCreateBudgetMutation();

  const budgets = useMemo(() => budgetsQuery.data || [], [budgetsQuery.data]);

  const createBudget = async (budgetData: CreateBudgetInput) => {
    if (!currentOrganization || !user) {
      toast({
        title: "Error",
        description: "Organization and user context required",
        variant: "destructive"
      });
      return { success: false, error: "Missing context" };
    }

    const validation = validateFormData(createBudgetSchema, budgetData);
    
    if (!validation.success) {
      const errors = formatValidationErrors(validation.error);
      toast({
        title: "Validation Error",
        description: "Please check your input and try again",
        variant: "destructive"
      });
      return { success: false, errors };
    }

    try {
      const result = await createMutation.mutateAsync({
        ...validation.data,
        org_id: currentOrganization.id,
        user_id: user.id,
        status: validation.data.status || 'draft',
        currency: validation.data.currency || 'USD',
        total_budget: validation.data.total_budget || 0,
        title: validation.data.title || 'Untitled Budget',
        description: validation.data.description || ''
      });
      
      toast({
        title: "Budget created",
        description: `"${result?.title}" has been created successfully`,
      });
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create budget';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return { success: false, error: errorMessage };
    }
  };

  const totalBudgetAmount = useMemo(() => {
    return budgets.reduce((total, budget) => total + (budget.total_budget || 0), 0);
  }, [budgets]);

  const budgetsByStatus = useMemo(() => {
    return budgets.reduce((acc, budget) => {
      const status = budget.status;
      if (!acc[status]) acc[status] = [];
      acc[status].push(budget);
      return acc;
    }, {} as Record<string, typeof budgets>);
  }, [budgets]);

  return {
    budgets,
    totalBudgetAmount,
    budgetsByStatus,
    loading: budgetsQuery.isLoading,
    error: budgetsQuery.error,
    isCreating: createMutation.isPending,
    createBudget,
    refetch: budgetsQuery.refetch,
    // Query status helpers
    isStale: budgetsQuery.isStale,
    isFetching: budgetsQuery.isFetching,
  };
};
