import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { dashboardApi } from '@/lib/api/dashboard';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useToast } from '@/hooks/use-toast';

export const useDashboard = () => {
  const { currentOrganization } = useOrganization();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const orgId = currentOrganization?.id || '';

  // Projects query
  const projectsQuery = useQuery({
    queryKey: ['dashboard', 'projects', orgId],
    queryFn: () => dashboardApi.getUserProjects(orgId),
    enabled: !!orgId,
  });

  // Usage stats query
  const usageStatsQuery = useQuery({
    queryKey: ['dashboard', 'usage-stats', orgId],
    queryFn: () => dashboardApi.getUsageStats(orgId),
    enabled: !!orgId,
  });

  // Tool usage analytics query
  const toolUsageQuery = useQuery({
    queryKey: ['dashboard', 'tool-usage', orgId],
    queryFn: () => dashboardApi.getToolUsageAnalytics(orgId),
    enabled: !!orgId,
  });

  // Recent activity query
  const recentActivityQuery = useQuery({
    queryKey: ['dashboard', 'recent-activity', orgId],
    queryFn: () => dashboardApi.getRecentActivity(orgId),
    enabled: !!orgId,
  });

  // Tier limits query
  const tierLimitsQuery = useQuery({
    queryKey: ['dashboard', 'tier-limits', orgId],
    queryFn: () => dashboardApi.getTierLimits(orgId),
    enabled: !!orgId,
  });

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: (projectData: any) => dashboardApi.createProject(orgId, projectData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'projects', orgId] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'usage-stats', orgId] });
      toast({
        title: "Success",
        description: "Project created successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create project",
        variant: "destructive",
      });
    },
  });

  // Update project mutation
  const updateProjectMutation = useMutation({
    mutationFn: ({ projectId, updates }: { projectId: string; updates: any }) => 
      dashboardApi.updateProject(projectId, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'projects', orgId] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'usage-stats', orgId] });
      toast({
        title: "Success",
        description: "Project updated successfully!",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update project",
        variant: "destructive",
      });
    },
  });

  // Track tool usage mutation
  const trackToolUsageMutation = useMutation({
    mutationFn: ({ toolName, eventData }: { toolName: string; eventData?: any }) => 
      dashboardApi.trackToolUsage(orgId, toolName, eventData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'tool-usage', orgId] });
      queryClient.invalidateQueries({ queryKey: ['dashboard', 'recent-activity', orgId] });
    },
  });

  // Check if user can create project
  const checkCanCreateProject = async () => {
    const result = await dashboardApi.canCreateProject(orgId);
    return result.data || false;
  };

  return {
    // Data
    projects: projectsQuery.data?.data || [],
    usageStats: usageStatsQuery.data?.data,
    toolUsage: toolUsageQuery.data?.data || [],
    recentActivity: recentActivityQuery.data?.data || [],
    tierLimits: tierLimitsQuery.data?.data,

    // Loading states
    isLoading: projectsQuery.isLoading || usageStatsQuery.isLoading,
    isToolUsageLoading: toolUsageQuery.isLoading,
    isActivityLoading: recentActivityQuery.isLoading,

    // Mutations
    createProject: createProjectMutation.mutate,
    updateProject: updateProjectMutation.mutate,
    trackToolUsage: trackToolUsageMutation.mutate,
    
    // Utilities
    checkCanCreateProject,
    
    // Mutation states
    isCreatingProject: createProjectMutation.isPending,
    isUpdatingProject: updateProjectMutation.isPending,

    // Refetch functions
    refetchProjects: projectsQuery.refetch,
    refetchUsageStats: usageStatsQuery.refetch,
  };
};
