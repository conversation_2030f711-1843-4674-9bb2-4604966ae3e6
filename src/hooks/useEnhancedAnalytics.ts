
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { enhancedAnalyticsApi, type AnalyticsData, type ScreenplayAnalyticsSummary } from '@/lib/api/enhanced-analytics';

export const useScreenplayAnalytics = (screenplayId: string) => {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [summary, setSummary] = useState<ScreenplayAnalyticsSummary | null>(null);
  
  const { execute: executeGetAnalytics, loading } = useAsyncOperation<AnalyticsData>({
    errorMessage: 'Failed to fetch analytics'
  });

  const { execute: executeGetSummary } = useAsyncOperation<ScreenplayAnalyticsSummary | null>({
    errorMessage: 'Failed to fetch analytics summary'
  });

  const fetchAnalytics = async () => {
    if (!screenplayId) return;

    const { data: analyticsData, success: analyticsSuccess } = await executeGetAnalytics(async () => {
      const result = await enhancedAnalyticsApi.getDetailedAnalytics(screenplayId);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch analytics');
    });

    const { data: summaryData, success: summarySuccess } = await executeGetSummary(async () => {
      const result = await enhancedAnalyticsApi.getScreenplayAnalytics(screenplayId);
      if (result.success) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch analytics summary');
    });

    if (analyticsSuccess && analyticsData) {
      setAnalytics(analyticsData);
    }

    if (summarySuccess) {
      setSummary(summaryData);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [screenplayId]);

  return {
    analytics,
    summary,
    loading,
    refreshAnalytics: fetchAnalytics
  };
};

export const useAnalyticsOverview = () => {
  const [overview, setOverview] = useState<{
    totalViews: number;
    totalOffers: number;
    totalRevenue: number;
    averageConversion: number;
    topPerforming: Array<{ title: string; views: number; revenue: number }>;
  } | null>(null);
  
  const { execute: executeGetOverview, loading } = useAsyncOperation<{
    totalViews: number;
    totalOffers: number;
    totalRevenue: number;
    averageConversion: number;
    topPerforming: Array<{ title: string; views: number; revenue: number }>;
  }>({
    errorMessage: 'Failed to fetch analytics overview'
  });

  const fetchOverview = async () => {
    const { data, success } = await executeGetOverview(async () => {
      const result = await enhancedAnalyticsApi.getMyAnalyticsOverview();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch analytics overview');
    });

    if (success && data) {
      setOverview(data);
    }
  };

  useEffect(() => {
    fetchOverview();
  }, []);

  return {
    overview,
    loading,
    refreshOverview: fetchOverview
  };
};
