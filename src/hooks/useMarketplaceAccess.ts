
import { useSubscriptionAccess } from './useSubscriptionAccess';

export interface MarketplaceAccess {
  canView: boolean;
  canSubmit: boolean;
  canBuy: boolean;
  tier: string | null;
  loading: boolean;
  subscribed: boolean;
}

export const useMarketplaceAccess = (): MarketplaceAccess => {
  const { subscribed, tier, loading } = useSubscriptionAccess();

  const getMarketplaceAccess = (): MarketplaceAccess => {
    if (loading) {
      return {
        canView: false,
        canSubmit: false,
        canBuy: false,
        tier,
        loading: true,
        subscribed: false
      };
    }

    // Only allow access if user has an active subscription
    if (!subscribed || !tier) {
      return {
        canView: false,
        canSubmit: false,
        canBuy: false,
        tier,
        loading: false,
        subscribed: false
      };
    }

    switch (tier) {
      case 'starter':
        return {
          canView: false,
          canSubmit: false,
          canBuy: false,
          tier,
          loading: false,
          subscribed: true
        };
      
      case 'pro-solo':
      case 'pro-team':
        return {
          canView: true,
          canSubmit: true,
          canBuy: false,
          tier,
          loading: false,
          subscribed: true
        };
      
      case 'studio':
      case 'enterprise':
        return {
          canView: true,
          canSubmit: false,
          canBuy: true,
          tier,
          loading: false,
          subscribed: true
        };
      
      default:
        return {
          canView: false,
          canSubmit: false,
          canBuy: false,
          tier,
          loading: false,
          subscribed: false
        };
    }
  };

  return getMarketplaceAccess();
};
