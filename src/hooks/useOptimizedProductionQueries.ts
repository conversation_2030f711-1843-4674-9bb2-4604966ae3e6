
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';

interface OptimizedQueryOptions {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export const useOptimizedProductionQueries = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const useOptimizedSchedules = (options: OptimizedQueryOptions = {}) => {
    const { page = 1, pageSize = 20, search, status, sortBy = 'created_at', sortOrder = 'desc' } = options;

    return useQuery({
      queryKey: ['optimized-schedules', currentOrganization?.id, page, pageSize, search, status, sortBy, sortOrder],
      queryFn: async () => {
        if (!currentOrganization?.id) return { data: [], count: 0 };

        let query = supabase
          .from('production_schedules')
          .select('*', { count: 'exact' })
          .eq('org_id', currentOrganization.id);

        // Use full-text search if search term provided
        if (search) {
          query = query.textSearch('search_vector', search);
        }

        // Use indexed status filter
        if (status) {
          query = query.eq('status', status);
        }

        // Use indexed sorting
        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        // Pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;
        if (error) throw error;

        return { data: data || [], count: count || 0 };
      },
      enabled: !!user && !!currentOrganization?.id,
      staleTime: 30 * 1000, // 30 seconds
    });
  };

  const useOptimizedBudgets = (options: OptimizedQueryOptions = {}) => {
    const { page = 1, pageSize = 20, search, status, sortBy = 'created_at', sortOrder = 'desc' } = options;

    return useQuery({
      queryKey: ['optimized-budgets', currentOrganization?.id, page, pageSize, search, status, sortBy, sortOrder],
      queryFn: async () => {
        if (!currentOrganization?.id) return { data: [], count: 0 };

        let query = supabase
          .from('production_budgets')
          .select('*', { count: 'exact' })
          .eq('org_id', currentOrganization.id);

        if (search) {
          query = query.textSearch('search_vector', search);
        }

        if (status) {
          query = query.eq('status', status);
        }

        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;
        if (error) throw error;

        return { data: data || [], count: count || 0 };
      },
      enabled: !!user && !!currentOrganization?.id,
      staleTime: 30 * 1000,
    });
  };

  const useOptimizedResources = (options: OptimizedQueryOptions = {}) => {
    const { page = 1, pageSize = 20, search, status, sortBy = 'created_at', sortOrder = 'desc' } = options;

    return useQuery({
      queryKey: ['optimized-resources', currentOrganization?.id, page, pageSize, search, status, sortBy, sortOrder],
      queryFn: async () => {
        if (!currentOrganization?.id) return { data: [], count: 0 };

        let query = supabase
          .from('production_resources')
          .select('*', { count: 'exact' })
          .eq('org_id', currentOrganization.id);

        if (search) {
          query = query.textSearch('search_vector', search);
        }

        // Use availability_status for resources
        if (status) {
          query = query.eq('availability_status', status);
        }

        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;
        if (error) throw error;

        return { data: data || [], count: count || 0 };
      },
      enabled: !!user && !!currentOrganization?.id,
      staleTime: 30 * 1000,
    });
  };

  const useOptimizedReports = (options: OptimizedQueryOptions = {}) => {
    const { page = 1, pageSize = 20, search, status, sortBy = 'date', sortOrder = 'desc' } = options;

    return useQuery({
      queryKey: ['optimized-reports', currentOrganization?.id, page, pageSize, search, status, sortBy, sortOrder],
      queryFn: async () => {
        if (!currentOrganization?.id) return { data: [], count: 0 };

        let query = supabase
          .from('production_reports')
          .select('*', { count: 'exact' })
          .eq('org_id', currentOrganization.id);

        if (search) {
          query = query.textSearch('search_vector', search);
        }

        if (status) {
          query = query.eq('status', status);
        }

        query = query.order(sortBy, { ascending: sortOrder === 'asc' });

        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        query = query.range(from, to);

        const { data, error, count } = await query;
        if (error) throw error;

        return { data: data || [], count: count || 0 };
      },
      enabled: !!user && !!currentOrganization?.id,
      staleTime: 30 * 1000,
    });
  };

  // Use the optimized search function from the database
  const useProductionSearch = (searchQuery: string, entityType: string = 'all') => {
    return useQuery({
      queryKey: ['production-search', currentOrganization?.id, searchQuery, entityType],
      queryFn: async () => {
        if (!currentOrganization?.id || !searchQuery.trim()) return [];

        const { data, error } = await supabase.rpc('search_production_data', {
          org_id_param: currentOrganization.id,
          search_query: searchQuery.trim(),
          entity_type_filter: entityType,
          page_num: 1,
          page_size: 50
        });

        if (error) throw error;
        return data || [];
      },
      enabled: !!user && !!currentOrganization?.id && !!searchQuery.trim(),
      staleTime: 10 * 1000, // 10 seconds for search results
    });
  };

  return {
    useOptimizedSchedules,
    useOptimizedBudgets,
    useOptimizedResources,
    useOptimizedReports,
    useProductionSearch,
  };
};
