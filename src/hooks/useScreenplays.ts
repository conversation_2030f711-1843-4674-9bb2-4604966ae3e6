
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { screenplaysApi, type Screenplay, type ScreenplayPurchase, type ScreenplayReview } from '@/lib/api/screenplays';

export const useScreenplays = () => {
  const [publishedScreenplays, setPublishedScreenplays] = useState<Screenplay[]>([]);
  const [myScreenplays, setMyScreenplays] = useState<Screenplay[]>([]);
  
  const { execute: executeGetPublished, loading: loadingPublished } = useAsyncOperation<Screenplay[]>({
    errorMessage: 'Failed to fetch published screenplays'
  });

  const { execute: executeGetMy, loading: loadingMy } = useAsyncOperation<Screenplay[]>({
    errorMessage: 'Failed to fetch your screenplays'
  });

  const fetchPublishedScreenplays = async () => {
    const { data, success } = await executeGetPublished(async () => {
      const result = await screenplaysApi.getPublishedScreenplays();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch published screenplays');
    });

    if (success && data) {
      setPublishedScreenplays(data);
    }
  };

  const fetchMyScreenplays = async () => {
    const { data, success } = await executeGetMy(async () => {
      const result = await screenplaysApi.getMyScreenplays();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch your screenplays');
    });

    if (success && data) {
      setMyScreenplays(data);
    }
  };

  useEffect(() => {
    fetchPublishedScreenplays();
  }, []);

  return {
    publishedScreenplays,
    myScreenplays,
    loadingPublished,
    loadingMy,
    refreshPublishedScreenplays: fetchPublishedScreenplays,
    refreshMyScreenplays: fetchMyScreenplays
  };
};

export const useScreenplayPurchases = () => {
  const [purchases, setPurchases] = useState<(ScreenplayPurchase & { screenplay: Screenplay })[]>([]);
  
  const { execute: executeGetPurchases, loading } = useAsyncOperation<(ScreenplayPurchase & { screenplay: Screenplay })[]>({
    errorMessage: 'Failed to fetch your purchases'
  });

  const fetchPurchases = async () => {
    const { data, success } = await executeGetPurchases(async () => {
      const result = await screenplaysApi.getMyPurchases();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch purchases');
    });

    if (success && data) {
      setPurchases(data);
    }
  };

  useEffect(() => {
    fetchPurchases();
  }, []);

  return {
    purchases,
    loading,
    refreshPurchases: fetchPurchases
  };
};

export const useScreenplayReviews = (screenplayId: string) => {
  const [reviews, setReviews] = useState<ScreenplayReview[]>([]);
  
  const { execute: executeGetReviews, loading } = useAsyncOperation<ScreenplayReview[]>({
    errorMessage: 'Failed to fetch reviews'
  });

  const fetchReviews = async () => {
    if (!screenplayId) return;

    const { data, success } = await executeGetReviews(async () => {
      const result = await screenplaysApi.getScreenplayReviews(screenplayId);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch reviews');
    });

    if (success && data) {
      setReviews(data);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, [screenplayId]);

  return {
    reviews,
    loading,
    refreshReviews: fetchReviews
  };
};
