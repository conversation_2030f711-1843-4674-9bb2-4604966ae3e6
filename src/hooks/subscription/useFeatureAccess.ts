
import { useCallback } from 'react';
import { UserSubscription } from './useSubscriptionData';

export interface FeatureAccess {
  has_access: boolean;
  access_level: string;
  usage_count: number;
  usage_limit?: number;
  remaining_usage: number;
}

export const useFeatureAccess = (subscription: UserSubscription | null) => {
  // Check if user has access to a feature
  const hasFeature = useCallback((featureKey: string): boolean => {
    if (!subscription) return false;
    return subscription.features[featureKey] === true;
  }, [subscription]);

  // Get usage limit for a feature
  const getUsageLimit = useCallback((limitKey: string): number => {
    if (!subscription) return 0;
    const limit = subscription.limits[limitKey];
    return typeof limit === 'number' ? limit : 0;
  }, [subscription]);

  // Get current usage for a feature
  const getCurrentUsage = useCallback((featureKey: string): number => {
    if (!subscription) return 0;
    return subscription.usage_summary[featureKey] || 0;
  }, [subscription]);

  // Check if feature usage limit is exceeded
  const isUsageLimitExceeded = useCallback((featureKey: string): boolean => {
    const limit = getUsageLimit(featureKey);
    const usage = getCurrentUsage(featureKey);
    return limit > 0 && usage >= limit;
  }, [getUsageLimit, getCurrentUsage]);

  // Get remaining usage for a feature
  const getRemainingUsage = useCallback((featureKey: string): number => {
    const limit = getUsageLimit(featureKey);
    const usage = getCurrentUsage(featureKey);
    if (limit === -1) return Infinity; // Unlimited
    return Math.max(0, limit - usage);
  }, [getUsageLimit, getCurrentUsage]);

  return {
    hasFeature,
    getUsageLimit,
    getCurrentUsage,
    isUsageLimitExceeded,
    getRemainingUsage
  };
};
