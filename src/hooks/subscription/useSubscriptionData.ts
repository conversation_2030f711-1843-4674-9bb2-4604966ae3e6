
import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/lib/supabase';

export interface SubscriptionPlan {
  id: string;
  plan_id: string;
  name: string;
  display_name: string;
  description: string;
  price_monthly: number;
  price_yearly?: number;
  currency: string;
  stripe_price_id_monthly?: string;
  stripe_price_id_yearly?: string;
  features: Record<string, any>;
  limits: Record<string, any>;
  is_popular: boolean;
  is_active: boolean;
  sort_order: number;
}

export interface UserSubscription {
  subscribed: boolean;
  plan_id: string;
  plan_name: string;
  status: string;
  current_period_end?: string;
  trial_end?: string;
  cancel_at_period_end: boolean;
  features: Record<string, any>;
  limits: Record<string, any>;
  addons: Array<{
    addon_key: string;
    name: string;
    features: Record<string, any>;
    limits: Record<string, any>;
  }>;
  usage_summary: Record<string, number>;
  seats_info: {
    type: 'individual' | 'organization';
    total: number;
    used: number;
    available?: number;
  };
}

export const useSubscriptionData = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch available subscription plans
  const fetchPlans = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      setPlans(data || []);
    } catch (err: any) {
      console.error('Error fetching subscription plans:', err);
      setError(err.message);
    }
  }, []);

  // Fetch organization subscription with enhanced features
  const fetchSubscription = useCallback(async () => {
    if (!user || !currentOrganization) {
      setSubscription(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error } = await supabase
        .rpc('get_organization_subscription_with_features', {
          target_org_id: currentOrganization.id
        });

      if (error) throw error;

      if (data && data.length > 0) {
        const subData = data[0];
        setSubscription({
          subscribed: subData.has_subscription,
          plan_id: subData.plan_id,
          plan_name: subData.plan_name,
          status: subData.status,
          current_period_end: subData.current_period_end,
          trial_end: subData.trial_end,
          cancel_at_period_end: subData.cancel_at_period_end,
          features: subData.features || {},
          limits: subData.limits || {},
          addons: subData.addons || [],
          usage_summary: subData.usage_summary || {},
          seats_info: subData.seats_info || { type: 'individual', total: 1, used: 1 }
        });
      } else {
        // Set default free subscription
        setSubscription({
          subscribed: false,
          plan_id: 'free',
          plan_name: 'Free',
          status: 'inactive',
          cancel_at_period_end: false,
          features: {},
          limits: { active_projects: 1, ai_generations_per_day: 0 },
          addons: [],
          usage_summary: {},
          seats_info: { type: 'individual', total: 1, used: 1 }
        });
      }
    } catch (err: any) {
      console.error('Error fetching subscription:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [user, currentOrganization]);

  useEffect(() => {
    fetchPlans();
  }, [fetchPlans]);

  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  return {
    subscription,
    plans,
    loading,
    error,
    fetchSubscription,
    fetchPlans
  };
};
