
import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { withRetry } from '@/lib/supabase';
import { supabase } from '@/lib/supabase';

export const useUsageTracking = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // Track feature usage with retry logic
  const trackFeatureUsage = useCallback(async (
    featureKey: string,
    usageAmount: number = 1,
    usageType: string = 'count'
  ) => {
    if (!user || !currentOrganization) return null;

    try {
      const result = await withRetry(async () => {
        const { data, error } = await supabase
          .rpc('track_feature_usage', {
            target_org_id: currentOrganization.id,
            feature_key: featureKey,
            usage_amount: usageAmount,
            usage_type: usageType
          });

        if (error) throw error;
        return data && data.length > 0 ? data[0] : null;
      });

      return result;
    } catch (err: any) {
      console.error('Error tracking feature usage:', err);
      return null;
    }
  }, [user, currentOrganization]);

  return {
    trackFeatureUsage
  };
};
