
import { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { SubscriptionPlan } from './useSubscriptionData';

export const useCheckoutOperations = (plans: SubscriptionPlan[]) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  // Enhanced checkout session creation with promo code support
  const createCheckoutSession = useCallback(async (
    planId: string, 
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    promoCode?: string
  ) => {
    if (!user) {
      toast.error('Please sign in to subscribe');
      return null;
    }

    try {
      const plan = plans.find(p => p.plan_id === planId);
      if (!plan) {
        toast.error('Invalid subscription plan');
        return null;
      }

      const price = billingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly;
      const stripeProductId = billingCycle === 'yearly' ? plan.stripe_price_id_yearly : plan.stripe_price_id_monthly;

      if (!price) {
        toast.error('Pricing not available for selected billing cycle');
        return null;
      }

      // Track checkout start
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'begin_checkout', {
          currency: 'USD',
          plan_id: planId,
          billing_cycle: billingCycle,
          promo_code: promoCode
        });
      }

      const { data, error } = await supabase.functions.invoke('create-enhanced-checkout', {
        body: {
          planId,
          planName: plan.display_name,
          priceAmount: Math.round(price * 100), // Convert to cents
          billingCycle,
          stripeProductId,
          promoCode,
          metadata: {
            plan_id: planId,
            billing_cycle: billingCycle,
            user_id: user.id,
            org_id: currentOrganization?.id,
            promo_code: promoCode
          }
        }
      });

      if (error) throw error;
      return data;
    } catch (err: any) {
      console.error('Error creating checkout session:', err);
      toast.error('Failed to create checkout session');
      return null;
    }
  }, [user, currentOrganization, plans]);

  // Open customer portal
  const openCustomerPortal = useCallback(async () => {
    if (!user) {
      toast.error('Please sign in to manage your subscription');
      return;
    }

    try {
      const { data, error } = await supabase.functions.invoke('customer-portal');
      if (error) throw error;

      if (data?.url) {
        window.location.href = data.url;
      }
    } catch (err: any) {
      console.error('Error opening customer portal:', err);
      toast.error('Failed to open customer portal');
    }
  }, [user]);

  return {
    createCheckoutSession,
    openCustomerPortal
  };
};
