
import { useCallback } from 'react';
import { SubscriptionPlan } from './useSubscriptionData';

export const usePlanUtilities = (plans: SubscriptionPlan[]) => {
  // Get plan by ID
  const getPlanById = useCallback((planId: string) => {
    return plans.find(plan => plan.plan_id === planId);
  }, [plans]);

  // Get plan display information
  const getPlanDisplayInfo = useCallback((planId: string) => {
    const plan = getPlanById(planId);
    if (!plan) return null;

    return {
      name: plan.display_name,
      description: plan.description,
      features: plan.features,
      limits: plan.limits,
      isPopular: plan.is_popular
    };
  }, [getPlanById]);

  // Compare two plans
  const comparePlans = useCallback((planId1: string, planId2: string) => {
    const plan1 = getPlanById(planId1);
    const plan2 = getPlanById(planId2);
    
    if (!plan1 || !plan2) return null;

    return {
      plan1: {
        id: plan1.plan_id,
        name: plan1.display_name,
        monthlyPrice: plan1.price_monthly,
        yearlyPrice: plan1.price_yearly,
        features: plan1.features,
        limits: plan1.limits
      },
      plan2: {
        id: plan2.plan_id,
        name: plan2.display_name,
        monthlyPrice: plan2.price_monthly,
        yearlyPrice: plan2.price_yearly,
        features: plan2.features,
        limits: plan2.limits
      }
    };
  }, [getPlanById]);

  return {
    getPlanById,
    getPlanDisplayInfo,
    comparePlans
  };
};
