
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';

interface SubscriptionAnalytics {
  subscription_tier: string;
  subscriber_count: number;
  active_subscribers: number;
  avg_days_subscribed: number;
  signup_month: string;
}

interface ProductionDashboardAnalytics {
  org_id: string;
  total_schedules: number;
  total_budgets: number;
  total_resources: number;
  total_reports: number;
  active_schedules: number;
  approved_budgets: number;
  available_resources: number;
  total_budget_amount: number;
  last_activity: string;
}

export const useAnalytics = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();

  const { data: subscriptionAnalytics, isLoading: subscriptionLoading } = useQuery({
    queryKey: ['subscription-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscription_analytics')
        .select('*')
        .order('signup_month', { ascending: false });

      if (error) throw error;
      return data as SubscriptionAnalytics[];
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: productionAnalytics, isLoading: productionLoading } = useQuery({
    queryKey: ['production-analytics', currentOrganization?.id],
    queryFn: async () => {
      if (!currentOrganization?.id) return null;

      const { data, error } = await supabase
        .from('production_dashboard_analytics')
        .select('*')
        .eq('org_id', currentOrganization.id)
        .single();

      if (error) throw error;
      return data as ProductionDashboardAnalytics;
    },
    enabled: !!user && !!currentOrganization?.id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const refreshAnalytics = async () => {
    try {
      const { error } = await supabase.rpc('refresh_analytics_views');
      if (error) throw error;
      
      // Invalidate queries to refetch fresh data
      return Promise.all([
        supabase.from('subscription_analytics').select('*').then(() => {}),
        supabase.from('production_dashboard_analytics').select('*').then(() => {})
      ]);
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
      throw error;
    }
  };

  return {
    subscriptionAnalytics: subscriptionAnalytics || [],
    productionAnalytics,
    isLoading: subscriptionLoading || productionLoading,
    refreshAnalytics,
  };
};
