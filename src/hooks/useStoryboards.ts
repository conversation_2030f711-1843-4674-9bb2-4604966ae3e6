
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { storyboardsApi, type Storyboard } from '@/lib/api/storyboards';

export const useStoryboards = () => {
  const { currentOrganization } = useOrganization();
  const { handleError } = useErrorHandler();
  const [storyboards, setStoryboards] = useState<Storyboard[]>([]);
  const [loading, setLoading] = useState(false);

  const refreshStoryboards = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const result = await storyboardsApi.getStoryboards();
      if (result.success && result.data) {
        setStoryboards(result.data);
      } else if (result.error) {
        handleError(result.error, 'Failed to fetch storyboards');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch storyboards');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentOrganization) {
      refreshStoryboards();
    } else {
      setStoryboards([]);
    }
  }, [currentOrganization]);

  return {
    storyboards,
    loading,
    refreshStoryboards
  };
};
