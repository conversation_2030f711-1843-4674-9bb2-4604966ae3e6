
import { useForm, UseFormProps, FieldValues, DefaultValues } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ZodSchema, TypeOf, ZodObject } from 'zod';
import { useCallback } from 'react';

interface UseFormValidationProps<T extends FieldValues> extends Omit<UseFormProps<T>, 'resolver'> {
  schema: ZodSchema<T>;
  onSubmit: (data: T) => void | Promise<void>;
}

export const useFormValidation = <T extends FieldValues>({
  schema,
  onSubmit,
  defaultValues,
  ...formProps
}: UseFormValidationProps<T>) => {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as DefaultValues<T>,
    ...formProps
  });

  const handleSubmit = useCallback(
    form.handleSubmit(async (data: T) => {
      try {
        await onSubmit(data);
      } catch (error) {
        console.error('Form submission error:', error);
        
        // Set form errors if validation fails
        if (error instanceof Error) {
          form.setError('root', {
            type: 'manual',
            message: error.message
          });
        }
      }
    }),
    [form, onSubmit]
  );

  const validateField = useCallback(
    (fieldName: keyof T, value: any) => {
      try {
        // Check if schema is a ZodObject and has shape property
        if (schema instanceof ZodObject && 'shape' in schema) {
          const fieldSchema = (schema as any).shape[fieldName as string];
          if (fieldSchema) {
            fieldSchema.parse(value);
            form.clearErrors(fieldName as any);
            return true;
          }
        }
        // Fallback: validate the entire object and check if field is valid
        const result = schema.safeParse({ [fieldName]: value });
        if (result.success) {
          form.clearErrors(fieldName as any);
          return true;
        }
      } catch (error) {
        if (error instanceof Error) {
          form.setError(fieldName as any, {
            type: 'validation',
            message: error.message
          });
        }
        return false;
      }
      return true;
    },
    [schema, form]
  );

  return {
    ...form,
    handleSubmit,
    validateField,
    isValid: form.formState.isValid,
    isDirty: form.formState.isDirty,
    isSubmitting: form.formState.isSubmitting
  };
};
