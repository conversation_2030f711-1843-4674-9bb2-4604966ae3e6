
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useEnhancedSubscription } from "./useEnhancedSubscription";

export const usePlanSelection = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { createCheckoutSession } = useEnhancedSubscription();
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);

  const handlePlanSelection = async (
    planId: string, 
    billingCycle: 'monthly' | 'yearly' = 'monthly',
    promoCode?: string
  ) => {
    setLoadingPlan(planId);
    
    toast.success("Processing...", {
      description: `Starting checkout for ${planId} plan`,
    });

    try {
      if (!user) {
        // Store plan selection for after authentication
        localStorage.setItem('selectedPlan', JSON.stringify({
          planId,
          billingCycle,
          promoCode
        }));
        
        toast.info("Sign in required", {
          description: "Please sign in to continue with your plan selection",
        });
        
        navigate('/auth');
        return;
      }

      const result = await createCheckoutSession(planId, billingCycle, promoCode);
      
      if (result?.url) {
        toast.success("Checkout ready!", {
          description: "Redirecting to secure checkout...",
        });
        
        // Track conversion
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'begin_checkout', {
            currency: 'USD',
            plan_id: planId,
            billing_cycle: billingCycle
          });
        }
        
        // Redirect to checkout
        window.location.href = result.url;
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast.error("Error", {
        description: "Failed to start checkout process. Please try again.",
      });
    } finally {
      setTimeout(() => {
        setLoadingPlan(null);
      }, 1000);
    }
  };

  return {
    handlePlanSelection,
    loadingPlan
  };
};
