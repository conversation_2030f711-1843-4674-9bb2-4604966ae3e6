
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { supabase } from '@/integrations/supabase/client';

interface Favorite {
  id: string;
  screenplay_id: string;
  created_at: string;
  screenplay?: {
    id: string;
    title: string;
    genre: string;
    price: number;
    logline?: string;
    writer_id: string;
    script_content?: string;
    synopsis?: string;
    cover_image_url?: string;
    page_count?: number;
    status?: string;
    profiles?: {
      full_name?: string;
      username?: string;
    };
  };
}

export const useFavorites = () => {
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [favoriteIds, setFavoriteIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);

  const { execute: executeToggleFavorite } = useAsyncOperation({
    errorMessage: 'Failed to update favorites'
  });

  const fetchFavorites = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('favorites')
        .select(`
          *,
          screenplays:screenplay_id (
            id,
            title,
            genre,
            price,
            logline,
            writer_id,
            script_content,
            synopsis,
            cover_image_url,
            page_count,
            status,
            profiles:writer_id (
              full_name,
              username
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const favoritesData = data || [];
      setFavorites(favoritesData);
      
      // Create a set of screenplay IDs for quick lookup
      const ids = new Set(favoritesData.map(fav => fav.screenplay_id));
      setFavoriteIds(ids);
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleFavorite = async (screenplayId: string) => {
    const isFavorited = favoriteIds.has(screenplayId);

    const { success } = await executeToggleFavorite(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      if (isFavorited) {
        // Remove from favorites
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('screenplay_id', screenplayId)
          .eq('user_id', user.id);

        if (error) throw error;
      } else {
        // Add to favorites
        const { error } = await supabase
          .from('favorites')
          .insert({
            screenplay_id: screenplayId,
            user_id: user.id
          });

        if (error) throw error;
      }
    });

    if (success) {
      // Optimistically update the UI
      if (isFavorited) {
        setFavoriteIds(prev => {
          const newSet = new Set(prev);
          newSet.delete(screenplayId);
          return newSet;
        });
        setFavorites(prev => prev.filter(fav => fav.screenplay_id !== screenplayId));
      } else {
        setFavoriteIds(prev => new Set([...prev, screenplayId]));
        // We'll refresh to get the full data
        await fetchFavorites();
      }
    }
    return success;
  };

  const isFavorited = (screenplayId: string) => {
    return favoriteIds.has(screenplayId);
  };

  useEffect(() => {
    fetchFavorites();
  }, []);

  return {
    favorites,
    loading,
    toggleFavorite,
    isFavorited,
    refreshFavorites: fetchFavorites
  };
};
