
import { useState, useEffect } from 'react';
import { useE<PERSON>r<PERSON><PERSON><PERSON> } from '@/hooks/useErrorHandler';
import { promptsApi, type PromptLibraryItem } from '@/lib/api/prompts';

export const usePrompts = () => {
  const { handleError } = useErrorHandler();
  const [prompts, setPrompts] = useState<PromptLibraryItem[]>([]);
  const [loading, setLoading] = useState(false);

  const refreshPrompts = async () => {
    setLoading(true);
    try {
      const result = await promptsApi.getPrompts();
      if (result.success && result.data) {
        setPrompts(result.data);
      } else if (result.error) {
        handleError(result.error, 'Failed to fetch prompts');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch prompts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshPrompts();
  }, []);

  return {
    prompts,
    loading,
    refreshPrompts
  };
};
