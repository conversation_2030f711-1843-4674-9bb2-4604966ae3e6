import { useState, useCallback } from 'react';
import { 
  promoCodeService, 
  PromoCodeValidation, 
  PromoCodeApplication,
  TIER_PRICING 
} from '@/lib/promo/promoCodeService';
import { useToast } from './use-toast';

type TierType = keyof typeof TIER_PRICING;

interface UsePromoCodeReturn {
  // State
  promoCode: string;
  isValidating: boolean;
  isApplying: boolean;
  validation: PromoCodeValidation | null;
  application: PromoCodeApplication | null;
  
  // Actions
  setPromoCode: (code: string) => void;
  validateCode: (tier?: TierType) => Promise<boolean>;
  applyCode: (tier: TierType) => Promise<PromoCodeApplication | null>;
  clearCode: () => void;
  
  // Utilities
  formatPrice: (priceInCents: number) => string;
  getTierPrice: (tier: TierType) => number;
  calculateSavings: (originalPrice: number, finalPrice: number) => {
    savingsAmount: number;
    savingsPercentage: number;
    formattedSavings: string;
  };
}

export function usePromoCode(): UsePromoCodeReturn {
  const [promoCode, setPromoCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isApplying, setIsApplying] = useState(false);
  const [validation, setValidation] = useState<PromoCodeValidation | null>(null);
  const [application, setApplication] = useState<PromoCodeApplication | null>(null);
  const { toast } = useToast();

  const validateCode = useCallback(async (tier?: TierType): Promise<boolean> => {
    if (!promoCode.trim()) {
      setValidation(null);
      return false;
    }

    setIsValidating(true);
    try {
      const result = await promoCodeService.validatePromoCode(promoCode.trim(), tier);
      setValidation(result);

      if (!result.valid) {
        toast({
          title: "Invalid Promo Code",
          description: result.error || "This promo code is not valid",
          variant: "destructive",
        });
        return false;
      }

      return true;
    } catch (error) {
      console.error('Promo code validation failed:', error);
      setValidation({
        valid: false,
        error: 'Validation failed. Please try again.',
      });
      
      toast({
        title: "Validation Error",
        description: "Failed to validate promo code. Please try again.",
        variant: "destructive",
      });
      
      return false;
    } finally {
      setIsValidating(false);
    }
  }, [promoCode, toast]);

  const applyCode = useCallback(async (tier: TierType): Promise<PromoCodeApplication | null> => {
    if (!promoCode.trim()) {
      toast({
        title: "No Promo Code",
        description: "Please enter a promo code first",
        variant: "destructive",
      });
      return null;
    }

    setIsApplying(true);
    try {
      const result = await promoCodeService.applyPromoCode(promoCode.trim(), tier);
      setApplication(result);

      const savings = promoCodeService.calculateSavings(result.originalPrice, result.finalPrice);
      
      toast({
        title: "Promo Code Applied!",
        description: `You saved ${savings.formattedSavings} (${savings.savingsPercentage}% off)`,
      });

      return result;
    } catch (error) {
      console.error('Promo code application failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to apply promo code';
      
      toast({
        title: "Application Error",
        description: errorMessage,
        variant: "destructive",
      });
      
      return null;
    } finally {
      setIsApplying(false);
    }
  }, [promoCode, toast]);

  const clearCode = useCallback(() => {
    setPromoCode('');
    setValidation(null);
    setApplication(null);
  }, []);

  const formatPrice = useCallback((priceInCents: number): string => {
    return promoCodeService.formatPrice(priceInCents);
  }, []);

  const getTierPrice = useCallback((tier: TierType): number => {
    return promoCodeService.getTierPrice(tier);
  }, []);

  const calculateSavings = useCallback((originalPrice: number, finalPrice: number) => {
    return promoCodeService.calculateSavings(originalPrice, finalPrice);
  }, []);

  return {
    // State
    promoCode,
    isValidating,
    isApplying,
    validation,
    application,
    
    // Actions
    setPromoCode,
    validateCode,
    applyCode,
    clearCode,
    
    // Utilities
    formatPrice,
    getTierPrice,
    calculateSavings,
  };
}

export default usePromoCode;
