
import { useState, useEffect } from 'react';
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler';
import { storyboardCommentsApi, type StoryboardPanelComment, type CreateCommentData } from '@/lib/api/storyboard-comments';
import { useToast } from '@/hooks/use-toast';

export const useStoryboardComments = (panelId: string) => {
  const [comments, setComments] = useState<StoryboardPanelComment[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const { handleError } = useErrorHandler();
  const { toast } = useToast();

  const fetchComments = async () => {
    if (!panelId) return;
    
    setLoading(true);
    try {
      const result = await storyboardCommentsApi.getComments(panelId);
      if (result.success && result.data) {
        setComments(result.data);
      } else if (result.error) {
        handleError(result.error, 'Failed to fetch comments');
      }
    } catch (error) {
      handleError(error, 'Failed to fetch comments');
    } finally {
      setLoading(false);
    }
  };

  const createComment = async (commentData: CreateCommentData) => {
    setCreating(true);
    try {
      const result = await storyboardCommentsApi.createComment(commentData);
      if (result.success && result.data) {
        setComments(prev => [...prev, result.data!]);
        toast({
          title: "Comment Added",
          description: "Your comment has been added successfully.",
        });
        return result.data;
      } else if (result.error) {
        handleError(result.error, 'Failed to create comment');
        return null;
      }
    } catch (error) {
      handleError(error, 'Failed to create comment');
      return null;
    } finally {
      setCreating(false);
    }
  };

  const updateComment = async (id: string, updates: { content?: string; resolved?: boolean }) => {
    try {
      const result = await storyboardCommentsApi.updateComment(id, updates);
      if (result.success && result.data) {
        setComments(prev => prev.map(comment => 
          comment.id === id ? result.data! : comment
        ));
        toast({
          title: "Comment Updated",
          description: "Comment has been updated successfully.",
        });
      } else if (result.error) {
        handleError(result.error, 'Failed to update comment');
      }
    } catch (error) {
      handleError(error, 'Failed to update comment');
    }
  };

  const deleteComment = async (id: string) => {
    try {
      const result = await storyboardCommentsApi.deleteComment(id);
      if (result.success) {
        setComments(prev => prev.filter(comment => comment.id !== id));
        toast({
          title: "Comment Deleted",
          description: "Comment has been deleted successfully.",
        });
      } else if (result.error) {
        handleError(result.error, 'Failed to delete comment');
      }
    } catch (error) {
      handleError(error, 'Failed to delete comment');
    }
  };

  useEffect(() => {
    fetchComments();
  }, [panelId]);

  return {
    comments,
    loading,
    creating,
    createComment,
    updateComment,
    deleteComment,
    refetch: fetchComments
  };
};
