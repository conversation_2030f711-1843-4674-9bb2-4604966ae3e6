
import { budgetsApi } from '@/lib/api/production';
import { useProductionDataManager } from './useProductionDataManager';
import { createBudgetSchema, type CreateBudgetInput } from '@/features/production/validation/schemas';
import type { ProductionBudget } from '@/lib/api/production';

export const useProductionBudgets = () => {
  const baseHook = useProductionDataManager<ProductionBudget, CreateBudgetInput>({
    entityName: 'budget',
    apiService: {
      getItems: budgetsApi.getBudgets,
      createItem: budgetsApi.createBudget
    },
    validationSchema: createBudgetSchema,
    getDisplayName: (budget) => budget.title
  });

  return {
    budgets: baseHook.items,
    loading: baseHook.loading,
    creating: baseHook.creating,
    errors: baseHook.errors,
    createBudget: baseHook.createItem,
    fetchBudgets: baseHook.fetchItems,
    clearErrors: baseHook.clearErrors
  };
};
