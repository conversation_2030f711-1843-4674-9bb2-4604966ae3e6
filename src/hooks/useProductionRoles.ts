
import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

type ProductionRole = 'producer' | 'coordinator' | 'crew' | 'viewer';

interface ProductionPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canManageRoles: boolean;
  canViewAudit: boolean;
  canCreateTemplates: boolean;
}

export const useProductionRoles = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [userRole, setUserRole] = useState<ProductionRole>('viewer');
  const [permissions, setPermissions] = useState<ProductionPermissions>({
    canEdit: false,
    canDelete: false,
    canManageRoles: false,
    canViewAudit: false,
    canCreateTemplates: false
  });
  const [loading, setLoading] = useState(true);

  const fetchUserRole = async () => {
    if (!user || !currentOrganization) {
      setLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('production_user_roles')
        .select('role, permissions')
        .eq('org_id', currentOrganization.id)
        .eq('user_id', user.id)
        .maybeSingle();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      const role = data?.role || 'viewer';
      setUserRole(role);

      // Set permissions based on role
      const rolePermissions: ProductionPermissions = {
        canEdit: role !== 'viewer',
        canDelete: role === 'producer' || role === 'coordinator',
        canManageRoles: role === 'producer',
        canViewAudit: role === 'producer' || role === 'coordinator',
        canCreateTemplates: role !== 'viewer'
      };

      // Merge with custom permissions if any
      if (data?.permissions) {
        Object.assign(rolePermissions, data.permissions);
      }

      setPermissions(rolePermissions);
    } catch (error) {
      console.error('Error fetching user role:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user permissions",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const hasPermission = (permission: keyof ProductionPermissions): boolean => {
    return permissions[permission];
  };

  const requirePermission = (permission: keyof ProductionPermissions): boolean => {
    if (!hasPermission(permission)) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to perform this action",
        variant: "destructive"
      });
      return false;
    }
    return true;
  };

  useEffect(() => {
    fetchUserRole();
  }, [user, currentOrganization]);

  return {
    userRole,
    permissions,
    loading,
    hasPermission,
    requirePermission,
    refetch: fetchUserRole
  };
};
