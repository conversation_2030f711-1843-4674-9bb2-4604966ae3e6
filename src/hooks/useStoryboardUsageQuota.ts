
import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { useOrganization } from "@/contexts/OrganizationContext";

// Usage quota type
export type UsageQuota = {
  dailyLimit: number;
  remaining: number;
  lastChecked: Date;
};

interface UseStoryboardUsageQuotaProps {
  enabled?: boolean;
}

export function useStoryboardUsageQuota({ enabled = true }: UseStoryboardUsageQuotaProps = {}) {
  const { currentOrganization } = useOrganization();
  const { toast } = useToast();
  const [usage, setUsage] = useState<UsageQuota | null>(null);
  const [usageLoading, setUsageLoading] = useState(false);

  const fetchUsageQuota = useCallback(async () => {
    if (!currentOrganization || !enabled) return;
    setUsageLoading(true);
    try {
      // Get Supabase access token for RLS policies
      const { data, error } = (window as any).supabase.auth.getSession
        ? await (window as any).supabase.auth.getSession()
        : { data: { session: null }, error: null };
      let accessToken = data?.session?.access_token;

      // AI storyboard usage endpoint
      const resp = await fetch(
        `${import.meta.env.VITE_SUPABASE_FUNCTIONS_URL || "/functions/v1"}/ai-generate-storyboard-image/usage`, 
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {}),
          },
          body: JSON.stringify({ org_id: currentOrganization.id }),
        }
      );
      let usageResult = null;
      if (!resp.ok) {
        const data = await resp.json();
        throw new Error(data.error || "Failed to check usage info");
      }
      usageResult = await resp.json();

      if (Array.isArray(usageResult) && usageResult.length > 0) {
        setUsage({
          dailyLimit: usageResult[0].daily_limit,
          remaining: usageResult[0].remaining_generations,
          lastChecked: new Date(),
        });
      }
    } catch (err: any) {
      setUsage(null);
      // No annoying toast spam, just warn in dev
      if (process.env.NODE_ENV === "development") {
        // eslint-disable-next-line no-console
        console.warn("Failed to fetch storyboard usage", err);
      }
    } finally {
      setUsageLoading(false);
    }
  }, [currentOrganization, enabled]);

  useEffect(() => {
    if (currentOrganization && enabled) {
      fetchUsageQuota();
    }
  }, [currentOrganization, enabled, fetchUsageQuota]);

  return {
    usage,
    usageLoading,
    fetchUsageQuota,
    currentOrganization,
  };
}
