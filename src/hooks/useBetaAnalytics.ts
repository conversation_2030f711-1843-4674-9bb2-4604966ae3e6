
import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export function useBetaAnalytics() {
  const { user } = useAuth();

  const trackEvent = async (
    eventType: string,
    eventName: string,
    metadata: Record<string, any> = {}
  ) => {
    if (!user) return;

    try {
      await supabase.rpc('track_beta_event', {
        event_type_param: eventType,
        event_name_param: eventName,
        metadata_param: {
          ...metadata,
          user_agent: navigator.userAgent,
          session_id: sessionStorage.getItem('beta_session_id') || generateSessionId(),
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error('Error tracking beta event:', error);
    }
  };

  const trackPageView = (pagePath: string) => {
    trackEvent('page_view', 'page_viewed', {
      page_path: pagePath,
      referrer: document.referrer,
    });
  };

  const trackFeatureUsage = (featureName: string, metadata: Record<string, any> = {}) => {
    trackEvent('feature_usage', 'feature_used', {
      feature_name: featureName,
      ...metadata,
    });
  };

  const trackConversion = (conversionType: string, metadata: Record<string, any> = {}) => {
    trackEvent('conversion', 'conversion_completed', {
      conversion_type: conversionType,
      ...metadata,
    });
  };

  const trackError = (errorMessage: string, errorContext: Record<string, any> = {}) => {
    trackEvent('error', 'error_occurred', {
      error_message: errorMessage,
      ...errorContext,
    });
  };

  // Generate session ID
  const generateSessionId = () => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('beta_session_id', sessionId);
    return sessionId;
  };

  // Track page views automatically
  useEffect(() => {
    if (user) {
      trackPageView(window.location.pathname);
    }
  }, [user, window.location.pathname]);

  return {
    trackEvent,
    trackPageView,
    trackFeatureUsage,
    trackConversion,
    trackError,
  };
}
