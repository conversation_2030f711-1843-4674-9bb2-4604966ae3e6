
import { useProductionDataComposer } from './useProductionDataComposer';
import { useProductionRetryManager } from './useProductionRetryManager';
import { useProductionLoadingManager } from './useProductionLoadingManager';
import { useEnhancedProductionErrorHandling } from './useEnhancedProductionErrorHandling';
import { useEnhancedProductionOperations } from './useEnhancedProductionOperations';
import { useOptimizedProductionQueries } from './useOptimizedProductionQueries';
import { useAnalytics } from './useAnalytics';

/**
 * Enhanced production hook with optimized data fetching and smart loading states
 */
export const useEnhancedProduction = () => {
  const optimizedQueries = useOptimizedProductionQueries();
  const analytics = useAnalytics();
  const dataComposer = useProductionDataComposer();
  const retryManager = useProductionRetryManager();
  const loadingManager = useProductionLoadingManager({
    scheduleLoading: dataComposer.scheduleHook.loading,
    budgetLoading: dataComposer.budgetHook.loading,
    resourceLoading: dataComposer.resourceHook.loading,
    reportLoading: dataComposer.reportHook.loading,
    scheduleCreating: dataComposer.scheduleHook.isCreating,
    budgetCreating: dataComposer.budgetHook.isCreating,
    resourceCreating: dataComposer.resourceHook.isCreating,
    reportCreating: dataComposer.reportHook.isCreating
  });

  const errorHandling = useEnhancedProductionErrorHandling({
    dataError: dataComposer.error
  });

  const operations = useEnhancedProductionOperations({
    createSchedule: dataComposer.createSchedule,
    createBudget: dataComposer.createBudget,
    createResource: dataComposer.createResource,
    createReport: dataComposer.createReport,
    refetch: dataComposer.refetch
  });

  // Enhanced loading states with smart optimization
  const getOptimizedLoadingState = (entityType: string) => {
    const isLoading = loadingManager.getEntityLoadingState(entityType as any);
    const hasError = errorHandling.hasErrors;
    
    // Fix: Safely access length property with proper type checking
    let isEmpty = true;
    
    // Use direct access to the known array properties instead of dynamic access
    switch (entityType) {
      case 'schedule':
        isEmpty = dataComposer.schedules.length === 0;
        break;
      case 'budget':
        isEmpty = dataComposer.budgets.length === 0;
        break;
      case 'resource':
        isEmpty = dataComposer.resources.length === 0;
        break;
      case 'report':
        isEmpty = dataComposer.reports.length === 0;
        break;
      default:
        isEmpty = true;
    }
    
    return {
      isLoading,
      hasError,
      isEmpty,
      variant: entityType === 'schedule' ? 'table' as const : 'card' as const
    };
  };

  return {
    // Data from composer (for backward compatibility)
    schedules: dataComposer.schedules,
    budgets: dataComposer.budgets,
    resources: dataComposer.resources,
    reports: dataComposer.reports,
    
    // Computed data
    totalBudgetAmount: dataComposer.totalBudgetAmount,
    availableResources: dataComposer.availableResources,
    recentReports: dataComposer.recentReports,
    
    // Enhanced loading states
    loading: loadingManager.loading,
    creating: loadingManager.creating,
    isAnyCreating: loadingManager.isAnyCreating,
    isAnyLoading: loadingManager.isAnyLoading,
    
    // Enhanced error handling
    error: errorHandling.combinedError,
    errors: errorHandling.errors,
    hasErrors: errorHandling.hasErrors,
    clearErrors: errorHandling.clearErrors,
    clearError: errorHandling.clearError,
    setError: errorHandling.setError,
    
    // Retry states from retry manager
    isRetrying: retryManager.isRetrying,
    retryCount: retryManager.retryCount,
    
    // Enhanced operations
    createSchedule: operations.createSchedule,
    createBudget: operations.createBudget,
    createResource: operations.createResource,
    createReport: operations.createReport,
    refetch: operations.refetch,
    
    // Individual hook access for advanced usage
    scheduleHook: dataComposer.scheduleHook,
    budgetHook: dataComposer.budgetHook,
    resourceHook: dataComposer.resourceHook,
    reportHook: dataComposer.reportHook,
    
    // Optimized loading state helpers
    getOptimizedLoadingState,
    getEntityLoadingState: loadingManager.getEntityLoadingState,

    // NEW: Optimized query hooks
    ...optimizedQueries,
    
    // NEW: Analytics functionality
    analytics,
  };
};
