
import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import type { Subscription } from '@/types/database';

export const useSubscription = () => {
  const { user } = useAuth();
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscription = async () => {
    if (!user) {
      setSubscription(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw fetchError;
      }

      setSubscription(data || null);
    } catch (err: any) {
      console.error('Error fetching subscription:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const createSubscription = async (subscriptionData: Omit<Subscription, 'id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .insert({
          ...subscriptionData,
          user_id: user.id,
        })
        .select()
        .single();

      if (error) throw error;

      setSubscription(data as Subscription);
      return data as Subscription;
    } catch (err: any) {
      console.error('Error creating subscription:', err);
      throw err;
    }
  };

  const updateSubscription = async (updates: Partial<Subscription>) => {
    if (!user || !subscription) {
      throw new Error('No user logged in or no subscription found');
    }

    try {
      const { data, error } = await supabase
        .from('subscriptions')
        .update(updates)
        .eq('id', subscription.id)
        .select()
        .single();

      if (error) throw error;

      setSubscription(data as Subscription);
      return data as Subscription;
    } catch (err: any) {
      console.error('Error updating subscription:', err);
      throw err;
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [user]);

  return {
    subscription,
    loading,
    error,
    createSubscription,
    updateSubscription,
    refreshSubscription: fetchSubscription,
  };
};
