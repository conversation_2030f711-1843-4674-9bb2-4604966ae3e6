
import { schedulesApi } from '@/lib/api/production';
import { useProductionDataManager } from './useProductionDataManager';
import { createScheduleSchema, type CreateScheduleInput } from '@/features/production/validation/schemas';
import type { ProductionSchedule } from '@/lib/api/production';

export const useProductionSchedules = () => {
  return useProductionDataManager<ProductionSchedule, CreateScheduleInput>({
    entityName: 'schedule',
    apiService: {
      getItems: schedulesApi.getSchedules,
      createItem: schedulesApi.createSchedule
    },
    validationSchema: createScheduleSchema,
    getDisplayName: (schedule) => schedule.title
  });
};
