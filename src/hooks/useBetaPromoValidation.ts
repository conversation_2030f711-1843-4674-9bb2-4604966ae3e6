
import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface BetaPromoValidation {
  isEligible: boolean;
  discountAmount: number;
  campaignId?: string;
  errorMessage?: string;
}

export const useBetaPromoValidation = () => {
  const [loading, setLoading] = useState(false);

  const validateBetaEligibility = async (): Promise<BetaPromoValidation> => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return { isEligible: false, discountAmount: 0, errorMessage: 'User not authenticated' };
      }

      // Check if user is in an active beta cohort
      const { data: cohort, error: cohortError } = await supabase
        .from('beta_cohorts')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .single();

      if (cohortError || !cohort) {
        return { isEligible: false, discountAmount: 0, errorMessage: 'Not a beta user' };
      }

      // Check if beta promo campaign is active
      const { data: campaign, error: campaignError } = await supabase
        .from('promo_campaigns')
        .select('*')
        .eq('campaign_code', 'BETA-LIFETIME-90')
        .eq('status', 'active')
        .eq('is_active', true)
        .single();

      if (campaignError || !campaign) {
        return { isEligible: false, discountAmount: 0, errorMessage: 'Beta promo not available' };
      }

      // Check if user has already used this promo
      const { data: usage, error: usageError } = await supabase
        .from('promo_campaign_usage')
        .select('*')
        .eq('campaign_id', campaign.id)
        .eq('user_id', user.id);

      if (usageError) {
        return { isEligible: false, discountAmount: 0, errorMessage: 'Error checking promo usage' };
      }

      if (usage && usage.length > 0) {
        return { isEligible: false, discountAmount: 0, errorMessage: 'Beta promo already used' };
      }

      return {
        isEligible: true,
        discountAmount: campaign.discount_value,
        campaignId: campaign.id
      };
    } catch (error) {
      console.error('Error validating beta eligibility:', error);
      return { isEligible: false, discountAmount: 0, errorMessage: 'Validation error' };
    } finally {
      setLoading(false);
    }
  };

  const applyBetaPromo = async (planId: string, originalAmount: number) => {
    setLoading(true);
    try {
      const validation = await validateBetaEligibility();
      if (!validation.isEligible) {
        toast({
          title: 'Not Eligible',
          description: validation.errorMessage || 'You are not eligible for the beta promo',
          variant: 'destructive',
        });
        return null;
      }

      const { data, error } = await supabase.rpc('apply_promo_code', {
        code: 'BETA-LIFETIME-90',
        user_id_param: (await supabase.auth.getUser()).data.user?.id,
        original_amount_param: originalAmount,
        subscription_id_param: null,
        payment_intent_id_param: null
      });

      if (error) throw error;

      const result = data?.[0];
      if (result?.success) {
        toast({
          title: 'Beta Promo Applied!',
          description: `90% lifetime discount applied! You save $${result.discount_amount}`,
        });
        return result;
      } else {
        toast({
          title: 'Error',
          description: result?.error_message || 'Failed to apply beta promo',
          variant: 'destructive',
        });
        return null;
      }
    } catch (error) {
      console.error('Error applying beta promo:', error);
      toast({
        title: 'Error',
        description: 'Failed to apply beta promo',
        variant: 'destructive',
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    validateBetaEligibility,
    applyBetaPromo,
    loading
  };
};
