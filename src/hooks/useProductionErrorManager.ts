
import { useState, useCallback } from 'react';

interface ProductionErrorManager {
  errors: Record<string, string | string[]>;
  hasErrors: boolean;
  setError: (field: string, message: string | string[]) => void;
  clearError: (field: string) => void;
  clearAllErrors: () => void;
  setErrors: (errors: Record<string, string | string[]>) => void;
}

/**
 * Hook for managing production-related errors across different entities
 */
export const useProductionErrorManager = (): ProductionErrorManager => {
  const [errors, setErrorsState] = useState<Record<string, string | string[]>>({});

  const setError = useCallback((field: string, message: string | string[]) => {
    setErrorsState(prev => ({
      ...prev,
      [field]: message
    }));
  }, []);

  const clearError = useCallback((field: string) => {
    setErrorsState(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrorsState({});
  }, []);

  const setErrors = useCallback((newErrors: Record<string, string | string[]>) => {
    setErrorsState(newErrors);
  }, []);

  const hasErrors = Object.keys(errors).length > 0;

  return {
    errors,
    hasErrors,
    setError,
    clearError,
    clearAllErrors,
    setErrors
  };
};
