
import { useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { useReportsQuery, useCreateReportMutation } from '@/lib/api/production/queries';
import { validateFormData, formatValidationErrors, createReportSchema } from '@/features/production/validation/schemas';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import type { CreateReportInput } from '@/features/production/validation/schemas';
import type { ProductionReport } from '@/lib/api/production';

/**
 * Reusable hook for report operations with React Query caching
 */
export const useReport = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const reportsQuery = useReportsQuery();
  const createMutation = useCreateReportMutation();

  const reports = useMemo(() => reportsQuery.data || [], [reportsQuery.data]);

  const createReport = async (reportData: CreateReportInput) => {
    if (!currentOrganization || !user) {
      toast({
        title: "Error",
        description: "Organization and user context required",
        variant: "destructive"
      });
      return { success: false, error: "Missing context" };
    }

    const validation = validateFormData(createReportSchema, reportData);
    
    if (!validation.success) {
      const errors = formatValidationErrors(validation.error);
      toast({
        title: "Validation Error",
        description: "Please check your input and try again",
        variant: "destructive"
      });
      return { success: false, errors };
    }

    try {
      const result = await createMutation.mutateAsync({
        ...validation.data,
        org_id: currentOrganization.id,
        user_id: user.id,
        status: validation.data.status || 'draft',
        title: validation.data.title || '',
        report_type: validation.data.report_type || 'daily',
        date: validation.data.date || new Date().toISOString().split('T')[0],
        content: validation.data.content || {}
      });
      
      toast({
        title: "Report created",
        description: `"${result?.title}" has been created successfully`,
      });
      
      return { success: true, data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create report';
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });
      return { success: false, error: errorMessage };
    }
  };

  const reportsByType = useMemo(() => {
    return reports.reduce((acc, report) => {
      const type = report.report_type;
      if (!acc[type]) acc[type] = [];
      acc[type].push(report);
      return acc;
    }, {} as Record<ProductionReport['report_type'], ProductionReport[]>);
  }, [reports]);

  const reportsByStatus = useMemo(() => {
    return reports.reduce((acc, report) => {
      const status = report.status;
      if (!acc[status]) acc[status] = [];
      acc[status].push(report);
      return acc;
    }, {} as Record<string, typeof reports>);
  }, [reports]);

  const recentReports = useMemo(() => {
    return [...reports]
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 10);
  }, [reports]);

  return {
    reports,
    reportsByType,
    reportsByStatus,
    recentReports,
    loading: reportsQuery.isLoading,
    error: reportsQuery.error,
    isCreating: createMutation.isPending,
    createReport,
    refetch: reportsQuery.refetch,
    // Query status helpers
    isStale: reportsQuery.isStale,
    isFetching: reportsQuery.isFetching,
  };
};
