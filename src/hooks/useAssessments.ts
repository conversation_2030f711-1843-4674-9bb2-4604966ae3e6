
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { assessmentsApi, type ScreenplayAssessment } from '@/lib/api/assessments';

export const useAssessments = () => {
  const [assessments, setAssessments] = useState<(ScreenplayAssessment & { screenplay: { title: string } })[]>([]);
  
  const { execute: executeGetAssessments, loading } = useAsyncOperation<(ScreenplayAssessment & { screenplay: { title: string } })[]>({
    errorMessage: 'Failed to fetch assessments'
  });

  const { execute: executeRequestAssessment, loading: requesting } = useAsyncOperation<ScreenplayAssessment>({
    errorMessage: 'Failed to request assessment'
  });

  const fetchAssessments = async () => {
    const { data, success } = await executeGetAssessments(async () => {
      const result = await assessmentsApi.getMyAssessments();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch assessments');
    });

    if (success && data) {
      setAssessments(data);
    }
  };

  const requestAssessment = async (screenplayId: string, type: 'quick' | 'standard' | 'full' = 'standard') => {
    const { success } = await executeRequestAssessment(async () => {
      const result = await assessmentsApi.requestAssessment(screenplayId, type);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to request assessment');
    });

    if (success) {
      fetchAssessments(); // Refresh the list
    }

    return success;
  };

  useEffect(() => {
    fetchAssessments();
  }, []);

  return {
    assessments,
    loading,
    requesting,
    requestAssessment,
    refreshAssessments: fetchAssessments
  };
};

export const useScreenplayAssessment = (screenplayId: string) => {
  const [assessment, setAssessment] = useState<ScreenplayAssessment | null>(null);
  
  const { execute: executeGetAssessment, loading } = useAsyncOperation<ScreenplayAssessment | null>({
    errorMessage: 'Failed to fetch assessment'
  });

  const fetchAssessment = async () => {
    if (!screenplayId) return;

    const { data, success } = await executeGetAssessment(async () => {
      const result = await assessmentsApi.getAssessment(screenplayId);
      if (result.success) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch assessment');
    });

    if (success) {
      setAssessment(data);
    }
  };

  useEffect(() => {
    fetchAssessment();
  }, [screenplayId]);

  return {
    assessment,
    loading,
    refreshAssessment: fetchAssessment
  };
};
