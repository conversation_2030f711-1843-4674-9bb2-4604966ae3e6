
import { useState, useEffect } from 'react';

interface NetworkState {
  online: boolean;
  connection: {
    effectiveType: '4g' | '3g' | '2g' | 'slow-2g' | 'unknown';
    saveData: boolean;
    downlink: number;
    rtt: number;
  } | null;
  isSlowConnection: boolean;
}

export function useMobileNetwork() {
  const [networkState, setNetworkState] = useState<NetworkState>({
    online: navigator.onLine,
    connection: null,
    isSlowConnection: false
  });

  useEffect(() => {
    const updateNetworkState = () => {
      const connection = (navigator as any).connection || 
                        (navigator as any).mozConnection || 
                        (navigator as any).webkitConnection;

      let connectionInfo = null;
      let isSlowConnection = false;

      if (connection) {
        connectionInfo = {
          effectiveType: connection.effectiveType || 'unknown',
          saveData: connection.saveData || false,
          downlink: connection.downlink || 0,
          rtt: connection.rtt || 0
        };

        // Determine if connection is slow
        isSlowConnection = 
          connectionInfo.effectiveType === '2g' || 
          connectionInfo.effectiveType === 'slow-2g' ||
          connectionInfo.saveData ||
          connectionInfo.downlink < 1.5;
      }

      setNetworkState({
        online: navigator.onLine,
        connection: connectionInfo,
        isSlowConnection
      });
    };

    // Initial state
    updateNetworkState();

    // Event listeners
    window.addEventListener('online', updateNetworkState);
    window.addEventListener('offline', updateNetworkState);

    const connection = (navigator as any).connection;
    if (connection) {
      connection.addEventListener('change', updateNetworkState);
    }

    return () => {
      window.removeEventListener('online', updateNetworkState);
      window.removeEventListener('offline', updateNetworkState);
      if (connection) {
        connection.removeEventListener('change', updateNetworkState);
      }
    };
  }, []);

  return networkState;
}
