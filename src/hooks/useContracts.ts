
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { contractsApi, type ContractWithDetails, type ScreenplayContract } from '@/lib/api/contracts';

export const useContracts = () => {
  const [contracts, setContracts] = useState<ContractWithDetails[]>([]);
  
  const { execute: executeGetContracts, loading } = useAsyncOperation<ContractWithDetails[]>({
    errorMessage: 'Failed to fetch contracts'
  });

  const { execute: executeCreateContract, loading: creating } = useAsyncOperation<ScreenplayContract>({
    errorMessage: 'Failed to create contract'
  });

  const { execute: executeSignContract, loading: signing } = useAsyncOperation<ScreenplayContract>({
    errorMessage: 'Failed to sign contract'
  });

  const fetchContracts = async () => {
    const { data, success } = await executeGetContracts(async () => {
      const result = await contractsApi.getMyContracts();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch contracts');
    });

    if (success && data) {
      setContracts(data);
    }
  };

  const createContract = async (
    screenplayId: string,
    buyerId: string,
    sellerId: string,
    contractType: 'purchase' | 'option' | 'licensing',
    terms: Record<string, any>,
    offerId?: string
  ) => {
    const { success } = await executeCreateContract(async () => {
      const result = await contractsApi.createContract(
        screenplayId,
        buyerId,
        sellerId,
        contractType,
        terms,
        offerId
      );
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to create contract');
    });

    if (success) {
      fetchContracts(); // Refresh the list
    }

    return success;
  };

  const signContract = async (contractId: string, role: 'buyer' | 'seller') => {
    const { success } = await executeSignContract(async () => {
      const result = await contractsApi.signContract(contractId, role);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to sign contract');
    });

    if (success) {
      fetchContracts(); // Refresh the list
    }

    return success;
  };

  useEffect(() => {
    fetchContracts();
  }, []);

  return {
    contracts,
    loading,
    creating,
    signing,
    createContract,
    signContract,
    refreshContracts: fetchContracts
  };
};
