
import { useCallback } from 'react';
import { ScreenplayElement } from '@/lib/pdf/pdfProcessor';
import { toast } from 'sonner';

interface UsePDFImportProps {
  onContentChange?: (content: string) => void;
  onElementsChange?: (elements: ScreenplayElement[]) => void;
}

export const usePDFImport = ({ onContentChange, onElementsChange }: UsePDFImportProps = {}) => {
  const handlePDFImport = useCallback((content: string, elements: ScreenplayElement[]) => {
    try {
      // Trigger content and elements change handlers
      onContentChange?.(content);
      onElementsChange?.(elements);
      
      // Log successful import
      console.log(`PDF imported successfully: ${elements.length} elements processed`);
      
      // Show success notification
      toast.success(`Imported screenplay with ${elements.length} elements`);
    } catch (error) {
      console.error('Error handling PDF import:', error);
      toast.error('Failed to import PDF content');
    }
  }, [onContentChange, onElementsChange]);

  return {
    handlePDFImport
  };
};
