
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { paymentsApi, type Transaction, type SellerAccount, type EarningsSummary } from '@/lib/api/payments';

export const useSellerAccount = () => {
  const [sellerAccount, setSellerAccount] = useState<SellerAccount | null>(null);
  
  const { execute: executeGetAccount, loading } = useAsyncOperation<SellerAccount>({
    errorMessage: 'Failed to fetch seller account'
  });

  const { execute: executeCreateAccount, loading: creating } = useAsyncOperation<{ accountLink: string }>({
    errorMessage: 'Failed to create seller account'
  });

  const fetchSellerAccount = async () => {
    const { data, success } = await executeGetAccount(async () => {
      const result = await paymentsApi.getSellerAccount();
      if (result.success && result.data) {
        return result.data;
      }
      if (result.error?.includes('No rows')) {
        return null;
      }
      throw new Error(result.error || 'Failed to fetch seller account');
    });

    if (success) {
      setSellerAccount(data);
    }
  };

  const createSellerAccount = async () => {
    const { data, success } = await executeCreateAccount(async () => {
      const result = await paymentsApi.createSellerAccount();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to create seller account');
    });

    if (success && data) {
      // Redirect to Stripe onboarding
      window.open(data.accountLink, '_blank');
      // Refresh account status after a delay
      setTimeout(fetchSellerAccount, 2000);
    }
  };

  useEffect(() => {
    fetchSellerAccount();
  }, []);

  return {
    sellerAccount,
    loading,
    creating,
    createSellerAccount,
    refreshAccount: fetchSellerAccount
  };
};

export const useTransactions = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  
  const { execute: executeGetTransactions, loading } = useAsyncOperation<Transaction[]>({
    errorMessage: 'Failed to fetch transactions'
  });

  const fetchTransactions = async () => {
    const { data, success } = await executeGetTransactions(async () => {
      const result = await paymentsApi.getMyTransactions();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch transactions');
    });

    if (success && data) {
      setTransactions(data);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, []);

  return {
    transactions,
    loading,
    refreshTransactions: fetchTransactions
  };
};

export const useEarnings = () => {
  const [earnings, setEarnings] = useState<EarningsSummary | null>(null);
  
  const { execute: executeGetEarnings, loading } = useAsyncOperation<EarningsSummary>({
    errorMessage: 'Failed to fetch earnings'
  });

  const fetchEarnings = async () => {
    const { data, success } = await executeGetEarnings(async () => {
      const result = await paymentsApi.getEarningsSummary();
      if (result.success && result.data) {
        return result.data;
      }
      if (result.error?.includes('No rows')) {
        return null;
      }
      throw new Error(result.error || 'Failed to fetch earnings');
    });

    if (success) {
      setEarnings(data);
    }
  };

  useEffect(() => {
    fetchEarnings();
  }, []);

  return {
    earnings,
    loading,
    refreshEarnings: fetchEarnings
  };
};
