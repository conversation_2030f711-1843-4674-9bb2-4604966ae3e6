
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface BetaFeedback {
  id: string;
  title: string;
  description: string;
  feedback_type: string;
  category: string;
  severity: string;
  status: string;
  page_url?: string;
  steps_to_reproduce?: string;
  expected_behavior?: string;
  actual_behavior?: string;
  created_at: string;
  user_id: string;
}

export function useBetaFeedback() {
  const [feedback, setFeedback] = useState<BetaFeedback[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchFeedback = async () => {
    try {
      const { data, error } = await supabase
        .from('beta_feedback')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFeedback(data || []);
    } catch (error) {
      console.error('Error fetching beta feedback:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch beta feedback',
        variant: 'destructive',
      });
    }
  };

  const updateFeedbackStatus = async (feedbackId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('beta_feedback')
        .update({ 
          status,
          resolved_at: status === 'resolved' ? new Date().toISOString() : null
        })
        .eq('id', feedbackId);

      if (error) throw error;
      
      await fetchFeedback();
      toast({
        title: 'Success',
        description: 'Feedback status updated successfully',
      });
    } catch (error) {
      console.error('Error updating feedback status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update feedback status',
        variant: 'destructive',
      });
    }
  };

  const submitFeedback = async (feedbackData: {
    title: string;
    description: string;
    feedback_type: string;
    category: string;
    severity: string;
    page_url?: string;
    steps_to_reproduce?: string;
    expected_behavior?: string;
    actual_behavior?: string;
  }) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('beta_feedback')
        .insert({
          ...feedbackData,
          user_id: user.id,
          user_agent: navigator.userAgent,
          browser_info: {
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
          },
        });

      if (error) throw error;
      
      toast({
        title: 'Success',
        description: 'Thank you for your feedback!',
      });
      
      return true;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit feedback',
        variant: 'destructive',
      });
      return false;
    }
  };

  useEffect(() => {
    fetchFeedback();
  }, []);

  return {
    feedback,
    updateFeedbackStatus,
    submitFeedback,
    loading,
    refetch: fetchFeedback,
  };
}
