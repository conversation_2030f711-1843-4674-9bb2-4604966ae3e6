
import { useMemo } from 'react';
import { useSchedule } from './useSchedule';
import { useBudget } from './useBudget';
import { useResource } from './useResource';
import { useReport } from './useReport';

/**
 * Enhanced composer hook using React Query for caching and performance
 */
export const useProductionDataComposer = () => {
  const scheduleHook = useSchedule();
  const budgetHook = useBudget();
  const resourceHook = useResource();
  const reportHook = useReport();

  // Combined loading state
  const loading = useMemo(() => (
    scheduleHook.loading || 
    budgetHook.loading || 
    resourceHook.loading || 
    reportHook.loading
  ), [scheduleHook.loading, budgetHook.loading, resourceHook.loading, reportHook.loading]);

  // Combined error state
  const error = useMemo(() => (
    scheduleHook.error || 
    budgetHook.error || 
    resourceHook.error || 
    reportHook.error
  ), [scheduleHook.error, budgetHook.error, resourceHook.error, reportHook.error]);

  // Combined creating states
  const creating = useMemo(() => ({
    schedule: scheduleHook.isCreating,
    budget: budgetHook.isCreating,
    resource: resourceHook.isCreating,
    report: reportHook.isCreating
  }), [
    scheduleHook.isCreating, 
    budgetHook.isCreating, 
    resourceHook.isCreating, 
    reportHook.isCreating
  ]);

  // Refetch all data
  const refetchAll = async () => {
    await Promise.all([
      scheduleHook.refetch(),
      budgetHook.refetch(),
      resourceHook.refetch(),
      reportHook.refetch()
    ]);
  };

  return {
    // Data
    schedules: scheduleHook.schedules,
    budgets: budgetHook.budgets,
    resources: resourceHook.resources,
    reports: reportHook.reports,
    
    // Computed data
    totalBudgetAmount: budgetHook.totalBudgetAmount,
    availableResources: resourceHook.availableResources,
    recentReports: reportHook.recentReports,
    
    // States
    loading,
    error,
    creating,
    
    // Actions
    createSchedule: scheduleHook.createSchedule,
    createBudget: budgetHook.createBudget,
    createResource: resourceHook.createResource,
    createReport: reportHook.createReport,
    refetch: refetchAll,
    
    // Individual hook access for advanced usage
    scheduleHook,
    budgetHook,
    resourceHook,
    reportHook,
  };
};
