
import { useState, useEffect } from 'react';
import { HealthChecker, HealthStatus } from '@/utils/healthCheck';
import { productionConfig } from '@/utils/productionConfig';

export function useHealthCheck(autoCheck = true) {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkHealth = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const healthChecker = HealthChecker.getInstance();
      const status = await healthChecker.checkHealth();
      setHealthStatus(status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Health check failed');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!autoCheck) return;

    const config = productionConfig.getConfig();
    const interval = config.monitoring.healthCheckInterval;

    // Initial check
    checkHealth();

    // Set up periodic checks
    const intervalId = setInterval(checkHealth, interval);

    return () => clearInterval(intervalId);
  }, [autoCheck]);

  return {
    healthStatus,
    isLoading,
    error,
    checkHealth
  };
}
