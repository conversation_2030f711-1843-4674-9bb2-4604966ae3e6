
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface BetaInvitation {
  id: string;
  email: string;
  invitation_code: string;
  status: string;
  phase: number;
  invited_at: string;
  accepted_at?: string;
  expires_at: string;
}

export function useBetaInvitations() {
  const [invitations, setInvitations] = useState<BetaInvitation[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchInvitations = async () => {
    try {
      const { data, error } = await supabase
        .from('beta_invitations')
        .select('*')
        .order('invited_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setInvitations(data || []);
    } catch (error) {
      console.error('Error fetching beta invitations:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch beta invitations',
        variant: 'destructive',
      });
    }
  };

  const sendInvitation = async (email: string, phase: number) => {
    setLoading(true);
    try {
      const { error } = await supabase.rpc('send_beta_invitation', {
        email_param: email,
        phase_param: phase,
      });

      if (error) throw error;
      
      await fetchInvitations();
      return true;
    } catch (error) {
      console.error('Error sending beta invitation:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const bulkSendInvitations = async (emails: string[], phase: number) => {
    setLoading(true);
    try {
      const promises = emails.map(email => 
        supabase.rpc('send_beta_invitation', {
          email_param: email,
          phase_param: phase,
        })
      );

      const results = await Promise.allSettled(promises);
      const failures = results.filter(result => result.status === 'rejected');
      
      if (failures.length > 0) {
        console.warn(`${failures.length} invitations failed to send`);
      }

      await fetchInvitations();
      return true;
    } catch (error) {
      console.error('Error sending bulk invitations:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const revokeInvitation = async (invitationId: string) => {
    try {
      const { error } = await supabase
        .from('beta_invitations')
        .update({ status: 'revoked' })
        .eq('id', invitationId);

      if (error) throw error;
      
      await fetchInvitations();
      toast({
        title: 'Success',
        description: 'Invitation revoked successfully',
      });
    } catch (error) {
      console.error('Error revoking invitation:', error);
      toast({
        title: 'Error',
        description: 'Failed to revoke invitation',
        variant: 'destructive',
      });
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, []);

  return {
    invitations,
    sendInvitation,
    bulkSendInvitations,
    revokeInvitation,
    loading,
    refetch: fetchInvitations,
  };
}
