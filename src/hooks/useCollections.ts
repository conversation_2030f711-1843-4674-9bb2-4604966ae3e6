
import { useState, useEffect } from 'react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { supabase } from '@/integrations/supabase/client';

interface Collection {
  id: string;
  name: string;
  description?: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  item_count?: number;
}

interface CollectionItem {
  id: string;
  collection_id: string;
  screenplay_id: string;
  added_at: string;
  screenplay?: {
    title: string;
    genre: string;
    price: number;
    logline?: string;
  };
}

export const useCollections = () => {
  const [collections, setCollections] = useState<Collection[]>([]);
  const [loading, setLoading] = useState(true);

  const { execute: executeCreateCollection } = useAsyncOperation({
    errorMessage: 'Failed to create collection'
  });

  const { execute: executeUpdateCollection } = useAsyncOperation({
    errorMessage: 'Failed to update collection'
  });

  const { execute: executeDeleteCollection } = useAsyncOperation({
    errorMessage: 'Failed to delete collection'
  });

  const fetchCollections = async () => {
    try {
      setLoading(true);
      
      // First get all collections
      const { data: collectionsData, error: collectionsError } = await supabase
        .from('collections')
        .select('*')
        .order('updated_at', { ascending: false });

      if (collectionsError) throw collectionsError;

      // Get item counts for each collection
      const collectionsWithCounts = await Promise.all(
        (collectionsData || []).map(async (collection) => {
          const { count } = await supabase
            .from('collection_items')
            .select('*', { count: 'exact', head: true })
            .eq('collection_id', collection.id);

          return {
            ...collection,
            item_count: count || 0
          };
        })
      );

      setCollections(collectionsWithCounts);
    } catch (error) {
      console.error('Error fetching collections:', error);
    } finally {
      setLoading(false);
    }
  };

  const createCollection = async (name: string, description?: string, isPublic = false) => {
    const { success } = await executeCreateCollection(async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('collections')
        .insert({
          name,
          description,
          is_public: isPublic,
          user_id: user.id
        });

      if (error) throw error;
    });

    if (success) {
      await fetchCollections();
    }
    return success;
  };

  const updateCollection = async (id: string, updates: Partial<Pick<Collection, 'name' | 'description' | 'is_public'>>) => {
    const { success } = await executeUpdateCollection(async () => {
      const { error } = await supabase
        .from('collections')
        .update(updates)
        .eq('id', id);

      if (error) throw error;
    });

    if (success) {
      await fetchCollections();
    }
  };

  const deleteCollection = async (id: string) => {
    const { success } = await executeDeleteCollection(async () => {
      const { error } = await supabase
        .from('collections')
        .delete()
        .eq('id', id);

      if (error) throw error;
    });

    if (success) {
      await fetchCollections();
    }
  };

  useEffect(() => {
    fetchCollections();
  }, []);

  return {
    collections,
    loading,
    createCollection,
    updateCollection,
    deleteCollection,
    refreshCollections: fetchCollections
  };
};

export const useCollectionItems = (collectionId: string | null) => {
  const [items, setItems] = useState<CollectionItem[]>([]);
  const [loading, setLoading] = useState(false);

  const { execute: executeAddItem } = useAsyncOperation({
    errorMessage: 'Failed to add item to collection'
  });

  const { execute: executeRemoveItem } = useAsyncOperation({
    errorMessage: 'Failed to remove item from collection'
  });

  const fetchCollectionItems = async () => {
    if (!collectionId) return;

    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('collection_items')
        .select(`
          *,
          screenplays:screenplay_id (
            title,
            genre,
            price,
            logline
          )
        `)
        .eq('collection_id', collectionId)
        .order('added_at', { ascending: false });

      if (error) throw error;
      setItems(data || []);
    } catch (error) {
      console.error('Error fetching collection items:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCollection = async (screenplayId: string) => {
    if (!collectionId) return false;

    const { success } = await executeAddItem(async () => {
      const { error } = await supabase
        .from('collection_items')
        .insert({
          collection_id: collectionId,
          screenplay_id: screenplayId
        });

      if (error) throw error;
    });

    if (success) {
      await fetchCollectionItems();
    }
    return success;
  };

  const removeFromCollection = async (itemId: string) => {
    const { success } = await executeRemoveItem(async () => {
      const { error } = await supabase
        .from('collection_items')
        .delete()
        .eq('id', itemId);

      if (error) throw error;
    });

    if (success) {
      await fetchCollectionItems();
    }
  };

  useEffect(() => {
    fetchCollectionItems();
  }, [collectionId]);

  return {
    items,
    loading,
    addToCollection,
    removeFromCollection,
    refreshItems: fetchCollectionItems
  };
};
