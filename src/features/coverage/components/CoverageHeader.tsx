
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Sparkles, FileText, Clock, Zap } from 'lucide-react';
import type { CoverageAccess } from '@/hooks/useCoverageAccess';
import { useCoverageUsage } from '@/hooks/useCoverageUsage';

interface CoverageHeaderProps {
  coverageAccess?: CoverageAccess;
}

const CoverageHeader: React.FC<CoverageHeaderProps> = ({ coverageAccess }) => {
  const { usage } = useCoverageUsage();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-cinema-700 rounded-lg">
            <Sparkles className="h-6 w-6 text-gold-400" />
          </div>
          <div>
            <h1 className="text-2xl font-playfair font-bold">Coverage Generator</h1>
            <p className="text-muted-foreground">
              Professional screenplay analysis and coverage reports
            </p>
          </div>
        </div>
        
        {coverageAccess && (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-gold-400 border-gold-400/30">
              {coverageAccess.tier?.replace('-', ' ').toUpperCase()}
            </Badge>
            {!usage.loading && (
              <Badge 
                variant="secondary" 
                className={`flex items-center gap-1 ${
                  usage.remaining_generations <= 1 ? 'bg-red-500/10 text-red-400 border-red-400/30' :
                  usage.remaining_generations <= 3 ? 'bg-yellow-500/10 text-yellow-400 border-yellow-400/30' :
                  'bg-green-500/10 text-green-400 border-green-400/30'
                }`}
              >
                <Zap className="h-3 w-3" />
                {usage.remaining_generations}/{usage.daily_limit} left today
              </Badge>
            )}
          </div>
        )}
      </div>

      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <FileText className="h-4 w-4" />
          <span>20-120 pages required</span>
        </div>
        <div className="flex items-center gap-1">
          <Sparkles className="h-4 w-4" />
          <span>Multiple analysis levels</span>
        </div>
        <div className="flex items-center gap-1">
          <Clock className="h-4 w-4" />
          <span>Daily limits reset at midnight</span>
        </div>
      </div>

      {usage.error && (
        <div className="text-sm text-red-400 bg-red-500/10 px-3 py-2 rounded-md border border-red-400/30">
          Unable to load usage data: {usage.error}
        </div>
      )}
    </div>
  );
};

export default CoverageHeader;
