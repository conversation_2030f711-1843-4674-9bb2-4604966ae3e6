
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { Sparkles, AlertTriangle, FileText } from 'lucide-react';
import { useScenes } from '@/hooks/useScenes';
import { validateScreenplayForCoverage } from '@/lib/validation/screenplayValidation';
import type { CoverageAccess } from '@/hooks/useCoverageAccess';

interface FidelityOption {
  value: string;
  label: string;
  description: string;
  color: string;
  disabled?: boolean;
}

interface CoverageGenerationFormProps {
  onGenerate: (sceneId: string, fidelityLevel: string) => void;
  isGenerating: boolean;
  coverageAccess: CoverageAccess;
}

const CoverageGenerationForm: React.FC<CoverageGenerationFormProps> = ({
  onGenerate,
  isGenerating,
  coverageAccess
}) => {
  const { scenes } = useScenes();
  const [selectedScene, setSelectedScene] = useState<string>('');
  const [fidelityLevel, setFidelityLevel] = useState<string>('Standard');
  const [validationError, setValidationError] = useState<string>('');

  const fidelityOptions: FidelityOption[] = [
    { 
      value: 'Basic', 
      label: 'Basic', 
      description: 'Quick overview and key points', 
      color: 'bg-blue-500/20 text-blue-300',
      disabled: !coverageAccess.fidelityAccess.basic
    },
    { 
      value: 'Standard', 
      label: 'Standard', 
      description: 'Comprehensive analysis with recommendations', 
      color: 'bg-green-500/20 text-green-300',
      disabled: !coverageAccess.fidelityAccess.standard
    },
    { 
      value: 'Premium', 
      label: 'Premium', 
      description: 'Deep dive with market insights and comparisons', 
      color: 'bg-purple-500/20 text-purple-300',
      disabled: !coverageAccess.fidelityAccess.premium
    }
  ];

  const handleSceneSelect = (sceneId: string) => {
    setSelectedScene(sceneId);
    setValidationError('');
    
    // Validate screenplay content when scene is selected
    const scene = scenes.find(s => s.id === sceneId);
    if (scene && scene.content) {
      const validation = validateScreenplayForCoverage(scene.content);
      if (!validation.isValid) {
        setValidationError(validation.error || 'Invalid screenplay content');
      }
    }
  };

  const handleGenerate = () => {
    if (selectedScene && !validationError) {
      onGenerate(selectedScene, fidelityLevel);
      setSelectedScene('');
    }
  };

  return (
    <Card className="p-4 border-cinema-700">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center">
          <Sparkles className="h-5 w-5 mr-2" />
          Generate New Coverage
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label className="text-sm font-medium mb-2 block">Select Screenplay</Label>
            <Select value={selectedScene} onValueChange={handleSceneSelect}>
              <SelectTrigger className="bg-cinema-800 border-cinema-700">
                <SelectValue placeholder="Choose a screenplay to analyze..." />
              </SelectTrigger>
              <SelectContent>
                {scenes.map((scene) => {
                  const validation = scene.content ? validateScreenplayForCoverage(scene.content) : null;
                  const isValid = validation?.isValid ?? false;
                  
                  return (
                    <SelectItem 
                      key={scene.id} 
                      value={scene.id}
                      disabled={!isValid}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <div className="font-medium">{scene.title}</div>
                          {scene.act && (
                            <div className="text-xs text-muted-foreground">Act {scene.act}</div>
                          )}
                          {validation?.pageCount && (
                            <div className="text-xs text-muted-foreground">
                              ~{Math.round(validation.pageCount)} pages
                            </div>
                          )}
                        </div>
                        {!isValid && (
                          <Badge variant="destructive" className="ml-2 text-xs">
                            {validation?.pageCount 
                              ? `${Math.round(validation.pageCount)} pages`
                              : 'Invalid'
                            }
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            
            {validationError && (
              <Alert className="mt-2 border-red-600 bg-red-900/20">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-sm text-red-400">
                  {validationError}
                </AlertDescription>
              </Alert>
            )}
          </div>

          <div>
            <Label className="text-sm font-medium mb-2 block">Analysis Depth</Label>
            <div className="flex flex-wrap gap-2">
              {fidelityOptions.map((option) => (
                <Badge
                  key={option.value}
                  className={cn(
                    "cursor-pointer transition-all hover:scale-105",
                    option.disabled && "opacity-50 cursor-not-allowed",
                    fidelityLevel === option.value && !option.disabled
                      ? option.color 
                      : "bg-cinema-700 text-gray-300 hover:bg-cinema-600"
                  )}
                  onClick={() => !option.disabled && setFidelityLevel(option.value)}
                >
                  {option.label}
                  {option.disabled && (
                    <span className="ml-1 text-xs">🔒</span>
                  )}
                </Badge>
              ))}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {fidelityOptions.find(opt => opt.value === fidelityLevel)?.description}
            </p>
            
            {!coverageAccess.fidelityAccess.premium && (
              <p className="text-xs text-orange-400 mt-1">
                Premium analysis requires Pro Team, Studio, or Enterprise plan
              </p>
            )}
          </div>
        </div>

        <div className="bg-cinema-800/50 border border-cinema-700 rounded-lg p-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <FileText className="h-4 w-4" />
            <span>Screenplay Requirements:</span>
          </div>
          <ul className="text-xs text-muted-foreground mt-1 ml-6 space-y-1">
            <li>• Minimum 20 pages (short film)</li>
            <li>• Maximum 120 pages (feature film)</li>
            <li>• Standard screenplay formatting</li>
            <li>• Daily limit: {coverageAccess.dailyLimit} generations</li>
          </ul>
        </div>

        <Button 
          onClick={handleGenerate} 
          disabled={!selectedScene || isGenerating || !!validationError}
          className="w-full btn-glow"
        >
          {isGenerating ? (
            <>
              <Sparkles className="h-4 w-4 mr-2 animate-spin" />
              Generating Coverage...
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              Generate Coverage Report
            </>
          )}
        </Button>
      </div>
    </Card>
  );
};

export default CoverageGenerationForm;
