
import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { FileText } from 'lucide-react';
import CoverageReportCard from './CoverageReportCard';
import type { CoverageReport } from '@/lib/api/coverage';

interface CoverageReportsListProps {
  reports: CoverageReport[];
  scenes: Array<{ id: string; title: string }>;
  onDeleteReport: (id: string) => void;
  onCopyReport: (report: CoverageReport, sceneTitle?: string) => void;
  isLoading: boolean;
}

const CoverageReportsList: React.FC<CoverageReportsListProps> = ({
  reports,
  scenes,
  onDeleteReport,
  onCopyReport,
  isLoading
}) => {
  const [expandedReport, setExpandedReport] = useState<string | null>(null);

  const getSceneTitle = (sceneId: string) => {
    return scenes.find(s => s.id === sceneId)?.title;
  };

  const handleCopyReport = (report: CoverageReport) => {
    const sceneTitle = getSceneTitle(report.scene_id);
    onCopyReport(report, sceneTitle);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Recent Coverage Reports</h3>
        <Badge variant="secondary" className="text-xs">
          {reports.length} report{reports.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <ScrollArea className="h-96">
        <div className="space-y-4">
          {reports.map((report) => (
            <CoverageReportCard
              key={report.id}
              report={report}
              sceneTitle={getSceneTitle(report.scene_id)}
              isExpanded={expandedReport === report.id}
              onToggleExpand={() => setExpandedReport(expandedReport === report.id ? null : report.id)}
              onDelete={() => onDeleteReport(report.id)}
              onCopy={() => handleCopyReport(report)}
            />
          ))}

          {reports.length === 0 && !isLoading && (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No coverage reports yet</p>
              <p className="text-xs">Generate your first coverage report to get AI-powered script analysis</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default CoverageReportsList;
