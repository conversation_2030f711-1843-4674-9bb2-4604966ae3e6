
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { CheckCircle, AlertCircle, TrendingUp, TrendingDown, Trash2, Copy } from 'lucide-react';
import type { CoverageReport } from '@/lib/api/coverage';

interface CoverageReportCardProps {
  report: CoverageReport;
  sceneTitle?: string;
  isExpanded: boolean;
  onToggleExpand: () => void;
  onDelete: () => void;
  onCopy: () => void;
}

const fidelityColors = {
  'Basic': 'bg-blue-500/20 text-blue-300',
  'Standard': 'bg-green-500/20 text-green-300',
  'Premium': 'bg-purple-500/20 text-purple-300'
};

const getVerdictIcon = (verdict?: string) => {
  if (!verdict) return <AlertCircle className="h-4 w-4" />;
  
  const lowerVerdict = verdict.toLowerCase();
  if (lowerVerdict.includes('recommend') || lowerVerdict.includes('strong')) {
    return <CheckCircle className="h-4 w-4 text-green-400" />;
  } else if (lowerVerdict.includes('consider') || lowerVerdict.includes('potential')) {
    return <AlertCircle className="h-4 w-4 text-yellow-400" />;
  } else {
    return <AlertCircle className="h-4 w-4 text-red-400" />;
  }
};

const getVerdictColor = (verdict?: string) => {
  if (!verdict) return 'bg-gray-500/20 text-gray-300';
  
  const lowerVerdict = verdict.toLowerCase();
  if (lowerVerdict.includes('recommend') || lowerVerdict.includes('strong')) {
    return 'bg-green-500/20 text-green-300';
  } else if (lowerVerdict.includes('consider') || lowerVerdict.includes('potential')) {
    return 'bg-yellow-500/20 text-yellow-300';
  } else {
    return 'bg-red-500/20 text-red-300';
  }
};

const CoverageReportCard: React.FC<CoverageReportCardProps> = ({
  report,
  sceneTitle,
  isExpanded,
  onToggleExpand,
  onDelete,
  onCopy
}) => {
  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-start justify-between">
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <h4 className="font-medium">{sceneTitle || 'Unknown Scene'}</h4>
            <Badge 
              className={fidelityColors[report.fidelity_level as keyof typeof fidelityColors] || "bg-gray-500/20 text-gray-300"}
            >
              {report.fidelity_level}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground">
            Generated {new Date(report.created_at).toLocaleDateString()}
          </p>
        </div>
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="ghost"
            onClick={onCopy}
            className="text-blue-400 hover:text-blue-300"
          >
            <Copy className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onToggleExpand}
            className="text-gray-400 hover:text-gray-300"
          >
            {isExpanded ? 'Collapse' : 'Expand'}
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={onDelete}
            className="text-red-400 hover:text-red-300"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {report.synopsis && (
        <div className="space-y-2">
          <h5 className="text-sm font-medium">Synopsis</h5>
          <p className="text-sm text-muted-foreground">{report.synopsis}</p>
        </div>
      )}

      {isExpanded && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {report.strengths && (
              <div className="space-y-2">
                <h5 className="text-sm font-medium flex items-center text-green-400">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Strengths
                </h5>
                <Textarea
                  value={report.strengths}
                  readOnly
                  className="text-sm bg-cinema-800 border-cinema-700"
                  rows={4}
                />
              </div>
            )}

            {report.weaknesses && (
              <div className="space-y-2">
                <h5 className="text-sm font-medium flex items-center text-red-400">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  Areas for Improvement
                </h5>
                <Textarea
                  value={report.weaknesses}
                  readOnly
                  className="text-sm bg-cinema-800 border-cinema-700"
                  rows={4}
                />
              </div>
            )}
          </div>

          {report.coverage_report && (
            <div className="space-y-2">
              <h5 className="text-sm font-medium">Full Coverage Report</h5>
              <Textarea
                value={report.coverage_report}
                readOnly
                className="text-sm bg-cinema-800 border-cinema-700 font-mono"
                rows={8}
              />
            </div>
          )}
        </>
      )}

      {report.verdict && (
        <>
          <Separator />
          <div className="space-y-2">
            <h5 className="text-sm font-medium flex items-center">
              {getVerdictIcon(report.verdict)}
              <span className="ml-1">Overall Assessment</span>
            </h5>
            <Badge className={cn("text-xs", getVerdictColor(report.verdict))}>
              {report.verdict}
            </Badge>
          </div>
        </>
      )}
    </Card>
  );
};

export default CoverageReportCard;
