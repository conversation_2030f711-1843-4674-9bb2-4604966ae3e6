
import React from 'react';
import { Card } from '@/components/ui/card';
import { ProgressIndicator } from '@/components/ui/progress-indicator';
import { Button } from '@/components/ui/button';
import { X, RefreshCw } from 'lucide-react';
import type { ProcessingProgress } from '../hooks/useCoverageOperations';

interface ProcessingStatusProps {
  processingJobs: Map<string, ProcessingProgress>;
  onDismiss?: (jobId: string) => void;
  onRefresh?: () => void;
}

const ProcessingStatus: React.FC<ProcessingStatusProps> = ({
  processingJobs,
  onDismiss,
  onRefresh
}) => {
  if (processingJobs.size === 0) return null;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Processing Status</h3>
        <div className="flex items-center gap-2">
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={onRefresh}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <div className="space-y-3">
        {Array.from(processingJobs.values()).map((job) => (
          <div key={job.jobId} className="relative">
            <ProgressIndicator
              progress={job.progress}
              status={job.status}
              jobId={job.jobId}
              estimatedTimeRemaining={job.estimatedTimeRemaining}
            />
            
            {onDismiss && job.progress === 100 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDismiss(job.jobId)}
                className="absolute top-2 right-2 h-6 w-6 p-0 hover:bg-cinema-700"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        ))}
      </div>

      {processingJobs.size > 0 && (
        <div className="text-xs text-muted-foreground text-center">
          {processingJobs.size} job{processingJobs.size !== 1 ? 's' : ''} in progress
        </div>
      )}
    </div>
  );
};

export default ProcessingStatus;
