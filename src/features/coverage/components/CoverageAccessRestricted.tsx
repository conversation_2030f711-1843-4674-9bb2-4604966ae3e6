
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Lock, Crown, Users, Building, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { CoverageAccess } from '@/hooks/useCoverageAccess';

interface CoverageAccessRestrictedProps {
  coverageAccess: CoverageAccess;
  className?: string;
}

const CoverageAccessRestricted: React.FC<CoverageAccessRestrictedProps> = ({ 
  coverageAccess, 
  className 
}) => {
  const getTierIcon = (tierName: string) => {
    switch (tierName) {
      case 'pro-solo': return <Crown className="h-5 w-5" />;
      case 'pro-team': return <Users className="h-5 w-5" />;
      case 'studio': return <Building className="h-5 w-5" />;
      case 'enterprise': return <Zap className="h-5 w-5" />;
      default: return <Lock className="h-5 w-5" />;
    }
  };

  const supportedTiers = [
    { name: 'Pro Solo', key: 'pro-solo', limit: '5/day', fidelity: 'Basic + Standard' },
    { name: 'Pro Team', key: 'pro-team', limit: '15/day', fidelity: 'All Levels' },
    { name: 'Studio', key: 'studio', limit: '50/day', fidelity: 'All Levels' },
    { name: 'Enterprise', key: 'enterprise', limit: '200/day', fidelity: 'All Levels' }
  ];

  return (
    <Card className={cn("cinema-card p-8 text-center", className)}>
      <div className="space-y-6">
        <div className="flex justify-center">
          <div className="p-4 bg-cinema-700 rounded-full">
            <Lock className="h-8 w-8 text-gold-400" />
          </div>
        </div>

        <div>
          <h2 className="text-2xl font-playfair font-bold mb-2">Coverage Generator</h2>
          <p className="text-muted-foreground">
            Professional screenplay analysis requires a Pro subscription or higher
          </p>
        </div>

        {coverageAccess.loading ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-400 mx-auto"></div>
            <p className="text-sm text-muted-foreground mt-2">Checking subscription...</p>
          </div>
        ) : (
          <div className="space-y-4">
            {coverageAccess.subscribed ? (
              <div className="p-4 bg-orange-900/20 border border-orange-700 rounded-lg">
                <p className="text-orange-400 font-medium">
                  Your current plan ({coverageAccess.tier}) doesn't include Coverage Generator access.
                </p>
                <p className="text-sm text-orange-300 mt-1">
                  Upgrade to unlock professional screenplay analysis tools.
                </p>
              </div>
            ) : (
              <div className="p-4 bg-cinema-700/50 border border-cinema-600 rounded-lg">
                <p className="text-muted-foreground">
                  You need an active subscription to access the Coverage Generator.
                </p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {supportedTiers.map((tier) => (
                <div key={tier.key} className="p-4 bg-cinema-800 border border-cinema-700 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getTierIcon(tier.key)}
                      <span className="font-semibold">{tier.name}</span>
                    </div>
                    <Badge variant="outline" className="text-gold-400 border-gold-400/30">
                      {tier.limit}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {tier.fidelity} analysis
                  </p>
                </div>
              ))}
            </div>

            <div className="space-y-2 text-sm text-muted-foreground">
              <p>✨ Professional screenplay analysis</p>
              <p>📄 Supports 20-120 page screenplays</p>
              <p>🎯 Multiple fidelity levels</p>
              <p>📊 Detailed coverage reports</p>
            </div>

            <Button 
              className="w-full btn-glow" 
              onClick={() => window.location.href = '/pricing'}
            >
              Upgrade Your Plan
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default CoverageAccessRestricted;
