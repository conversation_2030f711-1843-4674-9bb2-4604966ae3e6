
import { useOrganization } from '@/contexts/OrganizationContext';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { type CoverageReport } from '@/lib/api/coverage';
import { useToast } from '@/hooks/use-toast';
import { useCoverageModeration } from './useCoverageModeration';
import { useCoverageBackgroundProcessing, type ProcessingProgress } from './useCoverageBackgroundProcessing';
import { useCoverageCache } from './useCoverageCache';
import { useCoverageGeneration } from './useCoverageGeneration';
import { useCoverageReportOperations } from './useCoverageReportOperations';

export { type ProcessingProgress };

export const useCoverageOperations = () => {
  const { currentOrganization } = useOrganization();
  const { handleError } = useErrorHandler();
  const { toast } = useToast();
  
  // Sub-hooks for different concerns
  const { moderateContent } = useCoverageModeration();
  const { 
    processingJobs, 
    startBackgroundProcessing, 
    pollForCompletion, 
    dismissJob 
  } = useCoverageBackgroundProcessing();
  const { getCachedReport, setCachedReport, getCacheStats, clearCache } = useCoverageCache();
  const { 
    generating, 
    setGenerating, 
    checkSubscriptionAccess, 
    getSceneContent, 
    processRegularGeneration 
  } = useCoverageGeneration();
  const { deleteCoverageReport, copyReportToClipboard } = useCoverageReportOperations();

  const generateCoverage = async (
    selectedScene: string,
    fidelityLevel: string,
    onSuccess: (report: CoverageReport) => void
  ) => {
    if (!currentOrganization || !selectedScene) return;

    // Check subscription access
    if (!checkSubscriptionAccess(fidelityLevel)) {
      return;
    }

    setGenerating(true);
    
    try {
      // Get scene content for moderation and caching
      const content = await getSceneContent(selectedScene);

      // Content moderation
      try {
        await moderateContent(content);
      } catch (moderationError) {
        toast({
          title: "Content Moderation Failed",
          description: moderationError instanceof Error ? moderationError.message : 'Content violates community guidelines',
          variant: "destructive",
        });
        return;
      }

      // Check cache first
      const cachedReport = getCachedReport(selectedScene, fidelityLevel, content);
      if (cachedReport) {
        console.log('Using cached coverage report');
        onSuccess(cachedReport);
        toast({
          title: "Coverage Retrieved",
          description: "Your coverage report was retrieved from cache instantly.",
        });
        return;
      }

      // Determine if we need background processing (large content)
      const isLargeContent = content.length > 50000; // ~20 pages

      if (isLargeContent) {
        // Use background processing for large content
        const jobId = startBackgroundProcessing(
          selectedScene,
          fidelityLevel,
          content,
          currentOrganization.id
        );

        // Poll for completion
        pollForCompletion(
          jobId,
          async () => {
            // Continue with regular generation process
            await handleRegularGeneration(selectedScene, fidelityLevel, onSuccess, content);
          },
          (error) => {
            throw error;
          }
        );

      } else {
        // Process immediately for smaller content
        await handleRegularGeneration(selectedScene, fidelityLevel, onSuccess, content);
      }

    } catch (error) {
      handleError(error, 'Failed to generate coverage');
    } finally {
      setGenerating(false);
    }
  };

  const handleRegularGeneration = async (
    selectedScene: string,
    fidelityLevel: string,
    onSuccess: (report: CoverageReport) => void,
    content: string
  ) => {
    await processRegularGeneration(selectedScene, fidelityLevel, (report) => {
      // Cache the result
      setCachedReport(selectedScene, fidelityLevel, content, report);
      onSuccess(report);
    });
  };

  const handleDismissJob = (jobId: string) => {
    dismissJob(jobId);
    console.log('Dismissing job:', jobId);
  };

  return {
    generating,
    processingJobs,
    generateCoverage,
    deleteCoverageReport,
    copyReportToClipboard,
    getCacheStats,
    clearCache,
    dismissJob: handleDismissJob,
  };
};
