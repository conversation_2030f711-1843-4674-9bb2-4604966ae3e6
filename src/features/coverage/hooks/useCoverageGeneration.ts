
import { useState } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { coverageApi, type CoverageReport } from '@/lib/api/coverage';
import { useToast } from '@/hooks/use-toast';
import { useCoverageAccess } from '@/hooks/useCoverageAccess';
import { supabase } from '@/integrations/supabase/client';

export const useCoverageGeneration = () => {
  const { currentOrganization } = useOrganization();
  const { handleError } = useErrorHandler();
  const { toast } = useToast();
  const coverageAccess = useCoverageAccess();
  const [generating, setGenerating] = useState(false);

  const checkSubscriptionAccess = (fidelityLevel: string): boolean => {
    if (!coverageAccess.canAccess) {
      toast({
        title: "Access Denied",
        description: "Coverage Generator requires a Pro subscription or higher.",
        variant: "destructive",
      });
      return false;
    }

    const fidelityAccess = coverageAccess.fidelityAccess;
    if (
      (fidelityLevel === 'Basic' && !fidelityAccess.basic) ||
      (fidelityLevel === 'Standard' && !fidelityAccess.standard) ||
      (fidelityLevel === 'Premium' && !fidelityAccess.premium)
    ) {
      toast({
        title: "Fidelity Level Restricted",
        description: `${fidelityLevel} analysis is not available on your current plan.`,
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const getSceneContent = async (sceneId: string) => {
    const { data: scene, error: sceneError } = await supabase
      .from('scenes')
      .select('content')
      .eq('id', sceneId)
      .single();

    if (sceneError || !scene?.content) {
      throw new Error('Failed to fetch scene content');
    }

    return scene.content;
  };

  const processRegularGeneration = async (
    selectedScene: string,
    fidelityLevel: string,
    onSuccess: (report: CoverageReport) => void
  ) => {
    // Check current usage and increment atomically
    const { data: usageResult, error: usageError } = await supabase
      .rpc('increment_coverage_usage', {
        target_org_id: currentOrganization!.id
      });

    if (usageError) {
      console.error('Usage tracking error:', usageError);
      throw new Error('Failed to track usage');
    }

    if (!usageResult || usageResult.length === 0) {
      throw new Error('No usage tracking response');
    }

    const usage = usageResult[0];
    if (!usage.success) {
      toast({
        title: "Generation Failed",
        description: usage.error_message || 'Unable to generate coverage',
        variant: "destructive",
      });
      return;
    }

    // Proceed with coverage generation
    const result = await coverageApi.generateCoverage({
      scene_id: selectedScene,
      fidelity_level: fidelityLevel,
      org_id: currentOrganization!.id
    });

    if (result.success && result.data) {
      onSuccess(result.data);
      const remainingText = usage.remaining_generations === 1 
        ? '1 generation remaining today'
        : `${usage.remaining_generations} generations remaining today`;
      
      toast({
        title: "Coverage Generated",
        description: `Your ${fidelityLevel.toLowerCase()} coverage report has been successfully generated. ${remainingText}.`,
      });
    } else if (result.error) {
      handleError(result.error, 'Failed to generate coverage');
    }
  };

  return {
    generating,
    setGenerating,
    checkSubscriptionAccess,
    getSceneContent,
    processRegularGeneration,
  };
};
