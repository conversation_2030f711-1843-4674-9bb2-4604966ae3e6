
import { useCallback } from 'react';
import { ContentModerator } from '@/lib/security/contentModerator';

export const useCoverageModeration = () => {
  const moderateContent = useCallback(async (content: string) => {
    const moderation = ContentModerator.moderateContent(content, {
      enableProfanityFilter: true,
      enableSpamDetection: true,
      enableToxicityDetection: true,
      strictMode: false,
    });

    if (!moderation.isAllowed) {
      throw new Error(`Content moderation failed: ${moderation.flaggedContent.join(', ')}`);
    }

    return content; // Return original content if moderation passes
  }, []);

  return { moderateContent };
};
