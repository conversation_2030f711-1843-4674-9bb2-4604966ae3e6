
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler';
import { coverageApi, type CoverageReport } from '@/lib/api/coverage';
import { useToast } from '@/hooks/use-toast';

export const useCoverageReportOperations = () => {
  const { handleError } = useErrorHandler();
  const { toast } = useToast();

  const deleteCoverageReport = async (
    id: string,
    onSuccess: (id: string) => void
  ) => {
    try {
      const result = await coverageApi.deleteCoverageReport(id);
      if (result.success) {
        onSuccess(id);
        toast({
          title: "Report Deleted",
          description: "Coverage report has been removed.",
        });
      } else if (result.error) {
        handleError(result.error, 'Failed to delete coverage report');
      }
    } catch (error) {
      handleError(error, 'Failed to delete coverage report');
    }
  };

  const copyReportToClipboard = async (report: CoverageReport, sceneTitle?: string) => {
    const content = `
COVERAGE REPORT - ${sceneTitle || 'Unknown Scene'}
Generated: ${new Date(report.created_at).toLocaleDateString()}
Fidelity: ${report.fidelity_level}

SYNOPSIS:
${report.synopsis || 'N/A'}

STRENGTHS:
${report.strengths || 'N/A'}

WEAKNESSES:
${report.weaknesses || 'N/A'}

VERDICT:
${report.verdict || 'N/A'}
    `.trim();

    try {
      await navigator.clipboard.writeText(content);
      toast({
        title: "Copied to Clipboard",
        description: "Coverage report has been copied to your clipboard.",
      });
    } catch (error) {
      handleError(error, 'Failed to copy to clipboard');
    }
  };

  return {
    deleteCoverageReport,
    copyReportToClipboard,
  };
};
