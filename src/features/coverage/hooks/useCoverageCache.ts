
import { useCallback } from 'react';
import { coverageCache } from '@/lib/cache/coverageCache';
import { useToast } from '@/hooks/use-toast';

export const useCoverageCache = () => {
  const { toast } = useToast();

  const getCacheStats = useCallback(() => {
    return coverageCache.getStats();
  }, []);

  const clearCache = useCallback(() => {
    coverageCache.clear();
    toast({
      title: "Cache Cleared",
      description: "Coverage cache has been cleared.",
    });
  }, [toast]);

  const getCachedReport = useCallback((sceneId: string, fidelityLevel: string, content: string) => {
    return coverageCache.get(sceneId, fidelityLevel, content);
  }, []);

  const setCachedReport = useCallback((sceneId: string, fidelityLevel: string, content: string, report: any) => {
    coverageCache.set(sceneId, fidelityLevel, content, report);
  }, []);

  return {
    getCacheStats,
    clearCache,
    getCachedReport,
    setCachedReport,
  };
};
