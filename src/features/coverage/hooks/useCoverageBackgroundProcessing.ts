
import { useState, useCallback } from 'react';
import { backgroundProcessor } from '@/lib/processing/backgroundProcessor';
import { useToast } from '@/hooks/use-toast';

export interface ProcessingProgress {
  jobId: string;
  progress: number;
  status: string;
  estimatedTimeRemaining?: number;
}

export const useCoverageBackgroundProcessing = () => {
  const [processingJobs, setProcessingJobs] = useState<Map<string, ProcessingProgress>>(new Map());
  const { toast } = useToast();

  const startBackgroundProcessing = useCallback((
    sceneId: string,
    fidelityLevel: string,
    content: string,
    orgId: string
  ) => {
    const jobId = backgroundProcessor.createJob(
      sceneId,
      fidelityLevel,
      content,
      orgId
    );

    // Set up progress tracking
    backgroundProcessor.onProgress(jobId, (progress, status) => {
      setProcessingJobs(prev => new Map(prev.set(jobId, {
        jobId,
        progress,
        status,
        estimatedTimeRemaining: progress > 0 ? ((100 - progress) / progress) * 30 : undefined,
      })));
    });

    toast({
      title: "Processing Started",
      description: "Your large screenplay is being processed in the background. You'll be notified when complete.",
    });

    return jobId;
  }, [toast]);

  const pollForCompletion = useCallback((jobId: string, onComplete: () => void, onError: (error: Error) => void) => {
    const pollInterval = setInterval(async () => {
      const job = backgroundProcessor.getJob(jobId);
      if (job && job.status === 'completed') {
        clearInterval(pollInterval);
        setProcessingJobs(prev => {
          const newMap = new Map(prev);
          newMap.delete(jobId);
          return newMap;
        });
        onComplete();
      } else if (job && job.status === 'failed') {
        clearInterval(pollInterval);
        setProcessingJobs(prev => {
          const newMap = new Map(prev);
          newMap.delete(jobId);
          return newMap;
        });
        onError(new Error('Background processing failed'));
      }
    }, 2000);

    return pollInterval;
  }, []);

  const dismissJob = useCallback((jobId: string) => {
    setProcessingJobs(prev => {
      const newMap = new Map(prev);
      newMap.delete(jobId);
      return newMap;
    });
  }, []);

  return {
    processingJobs,
    startBackgroundProcessing,
    pollForCompletion,
    dismissJob,
  };
};
