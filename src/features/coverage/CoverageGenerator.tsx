
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useScenes } from '@/hooks/useScenes';
import { useCoverage } from '@/hooks/useCoverage';
import { useCoverageOperations } from './hooks/useCoverageOperations';
import { useCoverageAccess } from '@/hooks/useCoverageAccess';
import { Trash2, BarChart3 } from 'lucide-react';
import CoverageHeader from './components/CoverageHeader';
import CoverageGenerationForm from './components/CoverageGenerationForm';
import CoverageReportsList from './components/CoverageReportsList';
import CoverageAccessRestricted from './components/CoverageAccessRestricted';
import ProcessingStatus from './components/ProcessingStatus';

interface CoverageGeneratorProps {
  className?: string;
}

const CoverageGenerator: React.FC<CoverageGeneratorProps> = ({ className }) => {
  const { currentOrganization } = useOrganization();
  const { scenes } = useScenes();
  const { coverageReports, loading, refreshCoverageReports } = useCoverage();
  const { 
    generating, 
    processingJobs, 
    generateCoverage, 
    deleteCoverageReport, 
    copyReportToClipboard,
    getCacheStats,
    clearCache
  } = useCoverageOperations();
  const coverageAccess = useCoverageAccess();
  const [showCacheStats, setShowCacheStats] = useState(false);

  const handleGenerate = async (sceneId: string, fidelityLevel: string) => {
    await generateCoverage(sceneId, fidelityLevel, (newReport) => {
      refreshCoverageReports();
    });
  };

  const handleDeleteReport = async (id: string) => {
    await deleteCoverageReport(id, () => {
      refreshCoverageReports();
    });
  };

  const handleDismissJob = (jobId: string) => {
    // Implementation would remove the job from the processingJobs map
    console.log('Dismissing job:', jobId);
  };

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please select an organization to use Coverage Generator</p>
      </div>
    );
  }

  // Show access restriction if user doesn't have proper subscription tier
  if (!coverageAccess.canAccess) {
    return <CoverageAccessRestricted coverageAccess={coverageAccess} className={className} />;
  }

  const cacheStats = getCacheStats();

  return (
    <Card className={cn("cinema-card p-6", className)}>
      <div className="space-y-6">
        <CoverageHeader coverageAccess={coverageAccess} />
        
        {/* Processing Status */}
        <ProcessingStatus
          processingJobs={processingJobs}
          onDismiss={handleDismissJob}
          onRefresh={refreshCoverageReports}
        />
        
        {/* Cache Management */}
        <div className="flex items-center justify-between p-3 bg-cinema-800/30 rounded-lg border border-cinema-700">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-blue-400" />
            <span className="text-sm font-medium">Cache Status</span>
            <Badge variant="secondary" className="text-xs">
              {cacheStats.size}/{cacheStats.maxSize}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowCacheStats(!showCacheStats)}
              className="text-xs"
            >
              {showCacheStats ? 'Hide' : 'Show'} Stats
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearCache}
              className="text-xs"
              disabled={cacheStats.size === 0}
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Clear Cache
            </Button>
          </div>
        </div>

        {showCacheStats && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-cinema-900/50 rounded-lg border border-cinema-700">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-400">{cacheStats.size}</div>
              <div className="text-xs text-muted-foreground">Cached Reports</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-400">{cacheStats.totalAccesses}</div>
              <div className="text-xs text-muted-foreground">Total Hits</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-400">
                {Math.round(cacheStats.averageAge / 1000 / 60)}m
              </div>
              <div className="text-xs text-muted-foreground">Avg Age</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-400">
                {Math.round((cacheStats.size / cacheStats.maxSize) * 100)}%
              </div>
              <div className="text-xs text-muted-foreground">Cache Usage</div>
            </div>
          </div>
        )}
        
        <CoverageGenerationForm
          onGenerate={handleGenerate}
          isGenerating={generating}
          coverageAccess={coverageAccess}
        />

        <CoverageReportsList
          reports={coverageReports}
          scenes={scenes}
          onDeleteReport={handleDeleteReport}
          onCopyReport={copyReportToClipboard}
          isLoading={loading}
        />
      </div>
    </Card>
  );
};

export default CoverageGenerator;
