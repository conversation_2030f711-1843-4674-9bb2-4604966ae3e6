
import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';

interface ProductionTemplate {
  id: string;
  name: string;
  description?: string;
  template_type: string;
  template_data: any;
  is_public: boolean;
  usage_count: number;
  created_at: string;
  created_by: string;
}

interface NewTemplate {
  name: string;
  description: string;
  template_type: string;
  is_public: boolean;
}

export const useTemplateOperations = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [templates, setTemplates] = useState<ProductionTemplate[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchTemplates = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('production_templates')
        .select('*')
        .or(`org_id.eq.${currentOrganization.id},is_public.eq.true`)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast({
        title: "Error",
        description: "Failed to fetch templates",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const createTemplate = async (newTemplate: NewTemplate) => {
    if (!currentOrganization || !user) return false;

    try {
      const { error } = await supabase
        .from('production_templates')
        .insert({
          org_id: currentOrganization.id,
          created_by: user.id,
          name: newTemplate.name,
          description: newTemplate.description,
          template_type: newTemplate.template_type,
          is_public: newTemplate.is_public,
          template_data: {} // Would be populated based on template type
        });

      if (error) throw error;

      toast({
        title: "Template created",
        description: "Production template has been created successfully"
      });

      fetchTemplates();
      return true;
    } catch (error) {
      console.error('Error creating template:', error);
      toast({
        title: "Error",
        description: "Failed to create template",
        variant: "destructive"
      });
      return false;
    }
  };

  const useTemplate = async (template: ProductionTemplate) => {
    // This would create new production items based on the template
    toast({
      title: "Template applied",
      description: `Applied ${template.name} template to your production`
    });

    // Increment usage count
    await supabase
      .from('production_templates')
      .update({ usage_count: template.usage_count + 1 })
      .eq('id', template.id);

    fetchTemplates();
  };

  useEffect(() => {
    fetchTemplates();
  }, [currentOrganization]);

  return {
    templates,
    loading,
    createTemplate,
    useTemplate,
    fetchTemplates
  };
};
