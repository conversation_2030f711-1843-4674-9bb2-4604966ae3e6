
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';

interface AuditLog {
  id: string;
  user_id: string;
  entity_type: string;
  entity_id: string;
  action: string;
  changes_summary: string;
  created_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
  } | null;
}

export const useAuditLogs = () => {
  const { currentOrganization } = useOrganization();
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchAuditLogs = async (filterEntity?: string, filterAction?: string) => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      let query = supabase
        .from('production_audit_logs')
        .select(`
          *,
          profiles!inner (
            full_name,
            username
          )
        `)
        .eq('org_id', currentOrganization.id)
        .order('created_at', { ascending: false })
        .limit(100);

      if (filterEntity && filterEntity !== 'all') {
        query = query.eq('entity_type', filterEntity);
      }

      if (filterAction && filterAction !== 'all') {
        query = query.eq('action', filterAction);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      // Map the data to handle potential type mismatches
      const mappedLogs = (data || []).map((log: any) => ({
        ...log,
        profiles: log.profiles && typeof log.profiles === 'object' && !('message' in log.profiles) 
          ? log.profiles 
          : null
      }));
      
      setLogs(mappedLogs);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'create': return 'default';
      case 'update': return 'secondary';
      case 'delete': return 'destructive';
      default: return 'outline';
    }
  };

  const getEntityIcon = (entityType: string) => {
    // Could add specific icons for each entity type
    return '📄';
  };

  useEffect(() => {
    fetchAuditLogs();
  }, [currentOrganization]);

  return {
    logs,
    loading,
    fetchAuditLogs,
    getActionColor,
    getEntityIcon
  };
};
