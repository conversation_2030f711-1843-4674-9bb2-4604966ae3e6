import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { callSheetsApi, type CallSheet } from '@/lib/api/production/callSheetsApi';
import { toast } from '@/hooks/use-toast';

interface CallSheetFormData {
  title: string;
  call_date: string;
  schedule_id: string;
  general_notes: string;
  weather_info: Record<string, any>;
  emergency_contacts: Record<string, any>;
}

export const useCallSheets = () => {
  const { currentOrganization } = useOrganization();
  const [callSheets, setCallSheets] = useState<CallSheet[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CallSheetFormData>({
    title: '',
    call_date: new Date().toISOString().split('T')[0], // Default to today's date
    schedule_id: '',
    general_notes: '',
    weather_info: {},
    emergency_contacts: {}
  });

  useEffect(() => {
    if (currentOrganization) {
      fetchCallSheets();
    }
  }, [currentOrganization]);

  const fetchCallSheets = async () => {
    if (!currentOrganization) return;
    
    setLoading(true);
    try {
      const result = await callSheetsApi.getCallSheets(currentOrganization.id);
      if (result.success && result.data) {
        setCallSheets(result.data);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch call sheets",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const createCallSheet = async () => {
    if (!currentOrganization) return false;

    try {
      const result = await callSheetsApi.createCallSheet({
        ...formData,
        org_id: currentOrganization.id,
        user_id: 'temp_user_id', // Replace with actual user ID
        status: 'draft' // Default status
      });

      if (result.success && result.data) {
        setCallSheets(prev => [result.data!, ...prev]);
        resetForm();
        toast({
          title: "Success",
          description: "Call sheet created successfully"
        });
        return true;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create call sheet",
        variant: "destructive"
      });
    }
    return false;
  };

  const resetForm = () => {
    setFormData({
      title: '',
      call_date: new Date().toISOString().split('T')[0], // Default to today's date
      schedule_id: '',
      general_notes: '',
      weather_info: {},
      emergency_contacts: {}
    });
  };

  const updateFormData = (updates: Partial<CallSheetFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return {
    callSheets,
    loading,
    formData,
    updateFormData,
    createCallSheet,
    resetForm,
    getStatusColor,
    refreshCallSheets: fetchCallSheets
  };
};
