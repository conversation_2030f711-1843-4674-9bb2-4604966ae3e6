import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';

interface ProductionRole {
  id: string;
  user_id: string;
  role: 'producer' | 'coordinator' | 'crew' | 'viewer';
  permissions: any;
  assigned_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  } | null;
}

export const useProductionRolesManager = () => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [roles, setRoles] = useState<ProductionRole[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchRoles = async () => {
    if (!currentOrganization) return;

    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('production_user_roles')
        .select(`
          *,
          profiles!inner (
            full_name,
            username,
            avatar_url
          )
        `)
        .eq('org_id', currentOrganization.id);

      if (error) throw error;
      
      // Map the data to handle potential type mismatches
      const mappedRoles = (data || []).map((role: any) => ({
        ...role,
        permissions: role.permissions || {},
        profiles: role.profiles && typeof role.profiles === 'object' && !('message' in role.profiles) 
          ? role.profiles 
          : null
      }));
      
      setRoles(mappedRoles);
    } catch (error) {
      console.error('Error fetching roles:', error);
      toast({
        title: "Error",
        description: "Failed to fetch production roles",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateRole = async (userId: string, newRole: ProductionRole['role']) => {
    if (!currentOrganization || !user) return;

    try {
      const { error } = await supabase
        .from('production_user_roles')
        .upsert({
          org_id: currentOrganization.id,
          user_id: userId,
          role: newRole,
          assigned_by: user.id
        });

      if (error) throw error;

      toast({
        title: "Role updated",
        description: "Production role has been updated successfully"
      });

      fetchRoles();
    } catch (error) {
      console.error('Error updating role:', error);
      toast({
        title: "Error",
        description: "Failed to update role",
        variant: "destructive"
      });
    }
  };

  const getRoleColor = (role: string): "default" | "destructive" | "secondary" | "outline" => {
    switch (role) {
      case 'producer': return 'default';
      case 'coordinator': return 'secondary';
      case 'crew': return 'outline';
      case 'viewer': return 'destructive';
      default: return 'outline';
    }
  };

  useEffect(() => {
    fetchRoles();
  }, [currentOrganization]);

  return {
    roles,
    loading,
    fetchRoles,
    updateRole,
    getRoleColor
  };
};
