
import { useState, useEffect } from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { crewApi, type CrewMember } from '@/lib/api/production/crewApi';
import { toast } from '@/hooks/use-toast';

interface CrewMemberFormData {
  name: string;
  role: string;
  email: string;
  phone: string;
  daily_rate: string;
  overtime_rate: string;
  availability_status: CrewMember['availability_status'];
  union_status: string;
  skills: Record<string, any>;
  emergency_contact: Record<string, any>;
  notes: string;
}

export const useCrewManagement = () => {
  const { currentOrganization } = useOrganization();
  const [crewMembers, setCrewMembers] = useState<CrewMember[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CrewMemberFormData>({
    name: '',
    role: '',
    email: '',
    phone: '',
    daily_rate: '',
    overtime_rate: '',
    availability_status: 'available',
    union_status: '',
    skills: {},
    emergency_contact: {},
    notes: ''
  });

  useEffect(() => {
    if (currentOrganization) {
      fetchCrewMembers();
    }
  }, [currentOrganization]);

  const fetchCrewMembers = async () => {
    if (!currentOrganization) return;
    
    setLoading(true);
    try {
      const result = await crewApi.getCrewMembers(currentOrganization.id);
      if (result.success && result.data) {
        setCrewMembers(result.data);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch crew members",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const createCrewMember = async () => {
    if (!currentOrganization) return false;

    try {
      const result = await crewApi.createCrewMember({
        ...formData,
        org_id: currentOrganization.id,
        daily_rate: formData.daily_rate ? parseFloat(formData.daily_rate) : undefined,
        overtime_rate: formData.overtime_rate ? parseFloat(formData.overtime_rate) : undefined
      });

      if (result.success && result.data) {
        setCrewMembers(prev => [result.data!, ...prev]);
        resetForm();
        toast({
          title: "Success",
          description: "Crew member added successfully"
        });
        return true;
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add crew member",
        variant: "destructive"
      });
    }
    return false;
  };

  const resetForm = () => {
    setFormData({
      name: '',
      role: '',
      email: '',
      phone: '',
      daily_rate: '',
      overtime_rate: '',
      availability_status: 'available',
      union_status: '',
      skills: {},
      emergency_contact: {},
      notes: ''
    });
  };

  const updateFormData = (updates: Partial<CrewMemberFormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const getAvailabilityColor = (status: CrewMember['availability_status']) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'busy': return 'bg-red-100 text-red-800';
      case 'unavailable': return 'bg-gray-100 text-gray-800';
      case 'tentative': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const availableCrew = crewMembers.filter(member => member.availability_status === 'available');
  const busyCrew = crewMembers.filter(member => member.availability_status === 'busy');

  return {
    crewMembers,
    loading,
    formData,
    updateFormData,
    createCrewMember,
    resetForm,
    getAvailabilityColor,
    availableCrew,
    busyCrew,
    refreshCrewMembers: fetchCrewMembers
  };
};
