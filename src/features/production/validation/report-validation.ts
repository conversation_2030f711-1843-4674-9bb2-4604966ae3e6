
import { z } from 'zod';

// Base validation helpers
const requiredString = (message: string) => 
  z.string().min(1, message).trim();

const optionalString = () => 
  z.string().optional().or(z.literal(''));

const dateString = (message: string) => 
  z.string().min(1, message).refine(
    (date) => !isNaN(Date.parse(date)),
    { message: "Invalid date format" }
  );

// Production Report Validation
export const createReportSchema = z.object({
  title: requiredString("Report title is required"),
  report_type: z.enum(['daily', 'wrap', 'incident', 'progress']),
  content: z.object({
    summary: optionalString(),
    details: optionalString(),
    issues: optionalString(),
    next_steps: optionalString()
  }),
  date: dateString("Report date is required"),
  status: z.enum(['draft', 'submitted', 'approved']).default('draft')
});

export type CreateReportInput = z.infer<typeof createReportSchema>;
