
import { z } from 'zod';

// Base validation helpers
const requiredString = (message: string) => 
  z.string().min(1, message).trim();

const optionalString = () => 
  z.string().optional().or(z.literal(''));

// Production Resource Validation
export const createResourceSchema = z.object({
  name: requiredString("Resource name is required"),
  type: z.enum(['equipment', 'location', 'talent', 'crew', 'vehicle', 'other']),
  description: optionalString(),
  availability_status: z.enum(['available', 'booked', 'maintenance', 'unavailable']).default('available'),
  cost_per_day: z.number().min(0, "Cost must be a positive number").optional()
});

export type CreateResourceInput = z.infer<typeof createResourceSchema>;
