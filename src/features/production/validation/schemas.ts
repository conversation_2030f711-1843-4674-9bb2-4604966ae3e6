

// Re-export all validation schemas from focused modules
export * from './schedule-validation';
export * from './budget-validation';
export * from './resource-validation';
export * from './report-validation';
export * from './validation-helpers';

// For backward compatibility, also export the main lib schemas with proper type exports
export { createScheduleSchema } from './schedule-validation';
export type { CreateScheduleInput } from './schedule-validation';
export { createBudgetSchema } from './budget-validation';
export type { CreateBudgetInput } from './budget-validation';
export { createResourceSchema } from './resource-validation';
export type { CreateResourceInput } from './resource-validation';
export { createReportSchema } from './report-validation';
export type { CreateReportInput } from './report-validation';
export { validateFormData, formatValidationErrors } from './validation-helpers';

