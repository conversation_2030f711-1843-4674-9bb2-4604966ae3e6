
import { z } from 'zod';

// Base validation helpers
const requiredString = (message: string) => 
  z.string().min(1, message).trim();

const optionalString = () => 
  z.string().optional().or(z.literal(''));

// Production Budget Validation
export const createBudgetSchema = z.object({
  title: requiredString("Budget title is required"),
  description: optionalString(),
  total_budget: z.number().min(0, "Budget must be a positive number"),
  currency: requiredString("Currency is required").default("USD"),
  status: z.enum(['draft', 'approved', 'active', 'completed']).default('draft')
});

export type CreateBudgetInput = z.infer<typeof createBudgetSchema>;
