
import { z } from 'zod';

// Base validation helpers
const requiredString = (message: string) => 
  z.string().min(1, message).trim();

const dateString = (message: string) => 
  z.string().min(1, message).refine(
    (date) => !isNaN(Date.parse(date)),
    { message: "Invalid date format" }
  );

// Production Schedule Validation - Re-use the main validation schemas
export const createScheduleSchema = z.object({
  title: requiredString("Schedule title is required"),
  description: z.string().optional().or(z.literal('')),
  start_date: dateString("Start date is required"),
  end_date: dateString("End date is required"),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']).default('draft')
}).refine(data => new Date(data.end_date) > new Date(data.start_date), {
  message: 'End date must be after start date',
  path: ['end_date']
});

export type CreateScheduleInput = z.infer<typeof createScheduleSchema>;
