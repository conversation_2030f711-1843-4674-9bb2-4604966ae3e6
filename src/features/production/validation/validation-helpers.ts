
import { z } from 'zod';

// Fixed validation helper function with better type discrimination
export function validateFormData<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T; error?: never } | { success: false; error: z.ZodError; data?: never } {
  try {
    const validatedData = schema.parse(data);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
}

// Helper to format validation errors for UI display
export const formatValidationErrors = (error: z.ZodError): Record<string, string> => {
  return error.issues.reduce((acc, issue) => {
    const path = issue.path.join('.');
    acc[path] = issue.message;
    return acc;
  }, {} as Record<string, string>);
};
