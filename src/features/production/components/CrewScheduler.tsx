
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useCrewManagement } from '../hooks/useCrewManagement';
import { CrewMemberForm } from './CrewMemberForm';
import { CrewMemberList } from './CrewMemberList';

export const CrewScheduler: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const {
    crewMembers,
    loading,
    formData,
    updateFormData,
    createCrewMember,
    resetForm,
    getAvailabilityColor,
    availableCrew,
    busyCrew
  } = useCrewManagement();

  const handleCreateCrewMember = async () => {
    const success = await createCrewMember();
    if (success) {
      setShowForm(false);
    }
    return success;
  };

  const handleCancelForm = () => {
    setShowForm(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Crew Management</h2>
          <p className="text-muted-foreground">
            {availableCrew.length} available • {busyCrew.length} busy
          </p>
        </div>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Crew Member
        </Button>
      </div>

      {showForm && (
        <CrewMemberForm
          formData={formData}
          onFormDataChange={updateFormData}
          onSubmit={handleCreateCrewMember}
          onCancel={handleCancelForm}
        />
      )}

      <CrewMemberList
        crewMembers={crewMembers}
        loading={loading}
        getAvailabilityColor={getAvailabilityColor}
      />
    </div>
  );
};
