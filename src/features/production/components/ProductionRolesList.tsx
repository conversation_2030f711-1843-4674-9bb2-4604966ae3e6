
import React from 'react';
import { MemberRoleCard } from './MemberRoleCard';

interface OrganizationMember {
  id: string;
  user_id: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  } | null;
}

interface ProductionRole {
  id: string;
  user_id: string;
  role: 'producer' | 'coordinator' | 'crew' | 'viewer';
  permissions: any;
  assigned_at: string;
}

interface ProductionRolesListProps {
  members: OrganizationMember[];
  roles: ProductionRole[];
  onRoleChange: (userId: string, newRole: ProductionRole['role']) => void;
  getRoleColor: (role: string) => "default" | "destructive" | "secondary" | "outline";
}

export const ProductionRolesList: React.FC<ProductionRolesListProps> = ({
  members,
  roles,
  onRoleChange,
  getRoleColor
}) => {
  return (
    <div className="space-y-4">
      {members.map((member) => {
        const existingRole = roles.find(r => r.user_id === member.user_id);
        const currentRole = existingRole?.role || 'viewer';

        return (
          <MemberRoleCard
            key={member.id}
            member={member}
            currentRole={currentRole}
            onRoleChange={onRoleChange}
            getRoleColor={getRoleColor}
          />
        );
      })}
    </div>
  );
};
