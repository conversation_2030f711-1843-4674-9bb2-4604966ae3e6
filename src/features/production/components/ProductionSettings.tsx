
import React from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Settings } from 'lucide-react';
import { ProductionRoleManager } from './ProductionRoleManager';
import { ProductionTemplateManager } from './ProductionTemplateManager';
import { ProductionAuditLog } from './ProductionAuditLog';

export const ProductionSettings: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Settings className="h-6 w-6 mr-2" />
        <h2 className="text-2xl font-bold">Production Settings</h2>
      </div>

      <Tabs defaultValue="roles" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="roles">User Roles</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        <TabsContent value="roles">
          <ProductionRoleManager />
        </TabsContent>

        <TabsContent value="templates">
          <ProductionTemplateManager />
        </TabsContent>

        <TabsContent value="audit">
          <ProductionAuditLog />
        </TabsContent>
      </Tabs>
    </div>
  );
};
