
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, CheckCircle, XCircle } from 'lucide-react';
import type { ProductionSchedule } from '@/lib/api/production';

interface ScheduleStatusCardProps {
  schedules: ProductionSchedule[];
}

const ScheduleStatusCard: React.FC<ScheduleStatusCardProps> = ({ schedules }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Schedule Status
        </CardTitle>
        <CardDescription>
          Current production schedule overview
        </CardDescription>
      </CardHeader>
      <CardContent>
        {schedules.length === 0 ? (
          <p className="text-center text-muted-foreground py-4">
            No schedules available
          </p>
        ) : (
          <div className="space-y-3">
            {schedules.map((schedule) => (
              <div key={schedule.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <h4 className="font-semibold">{schedule.title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {new Date(schedule.start_date).toLocaleDateString()} - {new Date(schedule.end_date).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={
                    schedule.status === 'active' ? 'default' :
                    schedule.status === 'completed' ? 'secondary' :
                    schedule.status === 'cancelled' ? 'destructive' : 'outline'
                  }>
                    {schedule.status}
                  </Badge>
                  {schedule.status === 'active' && <CheckCircle className="h-4 w-4 text-green-500" />}
                  {schedule.status === 'cancelled' && <XCircle className="h-4 w-4 text-red-500" />}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ScheduleStatusCard;
