
import React from 'react';
import ErrorDisplay from '@/components/ErrorDisplay';

type ScheduleFormErrors = {
  title?: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
};

interface ScheduleErrorAlertProps {
  errors: ScheduleFormErrors;
}

const ScheduleErrorAlert: React.FC<ScheduleErrorAlertProps> = ({ errors }) => {
  return (
    <ErrorDisplay 
      errors={errors}
      title="Schedule Form Errors"
    />
  );
};

export default ScheduleErrorAlert;
