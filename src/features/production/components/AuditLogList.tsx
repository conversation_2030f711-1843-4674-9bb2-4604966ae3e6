
import React from 'react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ClipboardList } from 'lucide-react';

interface AuditLog {
  id: string;
  user_id: string;
  entity_type: string;
  entity_id: string;
  action: string;
  changes_summary: string;
  created_at: string;
  profiles?: {
    full_name?: string;
    username?: string;
  } | null;
}

interface AuditLogListProps {
  logs: AuditLog[];
  loading: boolean;
  getActionColor: (action: string) => "default" | "destructive" | "secondary" | "outline";
  getEntityIcon: (entityType: string) => string;
}

export const AuditLogList: React.FC<AuditLogListProps> = ({
  logs,
  loading,
  getActionColor,
  getEntityIcon
}) => {
  if (logs.length === 0 && !loading) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <ClipboardList className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No audit logs found</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {logs.map((log) => (
        <div key={log.id} className="flex items-start space-x-3 p-3 bg-muted rounded-lg">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs">
              {(log.profiles?.full_name || log.profiles?.username || 'U').charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-sm font-medium">
                {log.profiles?.full_name || log.profiles?.username || 'Unknown User'}
              </span>
              <Badge variant={getActionColor(log.action)} className="text-xs">
                {log.action}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {getEntityIcon(log.entity_type)} {log.entity_type}
              </span>
            </div>
            <p className="text-sm text-muted-foreground">{log.changes_summary}</p>
            <p className="text-xs text-muted-foreground mt-1">
              {new Date(log.created_at).toLocaleString()}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
};
