
import React from 'react';
import { DragDropScheduleCalendar } from './DragDropScheduleCalendar';
import type { ProductionSchedule, ProductionScheduleItem } from '@/lib/api/production';

interface ScheduleCalendarViewProps {
  schedules: ProductionSchedule[];
  scheduleItems: ProductionScheduleItem[];
  selectedDate: string;
  onDateChange: (date: string) => void;
  onItemMove: (itemId: string, newDate: string, newTime: string) => void;
}

export const ScheduleCalendarView: React.FC<ScheduleCalendarViewProps> = ({
  schedules,
  scheduleItems,
  selectedDate,
  onDateChange,
  onItemMove
}) => {
  return (
    <DragDropScheduleCalendar
      schedules={schedules}
      scheduleItems={scheduleItems}
      onItemMove={onItemMove}
      selectedDate={selectedDate}
      onDateChange={onDateChange}
    />
  );
};
