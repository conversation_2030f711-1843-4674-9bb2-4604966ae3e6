
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Edit3, CheckSquare } from 'lucide-react';

interface BulkOperationsPanelProps<T extends { id: string; status?: string }> {
  items: T[];
  selectedItems: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
  statusOptions: { value: string; label: string }[];
  getDisplayName: (item: T) => string;
}

export function BulkOperationsPanel<T extends { id: string; status?: string }>({
  items,
  selectedItems,
  onSelectionChange,
  onBulkStatusUpdate,
  onBulkDelete,
  statusOptions,
  getDisplayName
}: BulkOperationsPanelProps<T>) {
  const [bulkStatus, setBulkStatus] = useState<string>('');

  const handleSelectAll = () => {
    if (selectedItems.length === items.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(items.map(item => item.id));
    }
  };

  const handleItemSelect = (itemId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedItems, itemId]);
    } else {
      onSelectionChange(selectedItems.filter(id => id !== itemId));
    }
  };

  const handleBulkStatusUpdate = () => {
    if (bulkStatus) {
      onBulkStatusUpdate(bulkStatus);
      setBulkStatus('');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckSquare className="h-5 w-5" />
          Bulk Operations
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Selection Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedItems.length === items.length && items.length > 0}
              onCheckedChange={handleSelectAll}
            />
            <span className="text-sm">
              Select All ({selectedItems.length} of {items.length})
            </span>
          </div>
          {selectedItems.length > 0 && (
            <Badge variant="secondary">
              {selectedItems.length} selected
            </Badge>
          )}
        </div>

        {/* Item List */}
        <div className="max-h-48 overflow-y-auto space-y-2">
          {items.map(item => (
            <div key={item.id} className="flex items-center gap-2 p-2 border rounded">
              <Checkbox
                checked={selectedItems.includes(item.id)}
                onCheckedChange={(checked) => handleItemSelect(item.id, !!checked)}
              />
              <div className="flex-1">
                <div className="text-sm font-medium">{getDisplayName(item)}</div>
                {item.status && (
                  <Badge variant="outline" className="text-xs">
                    {item.status}
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="space-y-3 pt-3 border-t">
            <div className="flex items-center gap-2">
              <Select value={bulkStatus} onValueChange={setBulkStatus}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Update status..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={handleBulkStatusUpdate}
                disabled={!bulkStatus}
                size="sm"
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Update
              </Button>
            </div>

            <Button
              onClick={onBulkDelete}
              variant="destructive"
              size="sm"
              className="w-full"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete Selected ({selectedItems.length})
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
