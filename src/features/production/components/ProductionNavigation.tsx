
import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, Calendar, DollarSign, Users, FileText } from 'lucide-react';

const ProductionNavigation: React.FC = () => {
  return (
    <TabsList className="grid w-full grid-cols-5">
      <TabsTrigger value="dashboard" className="gap-2">
        <BarChart3 className="h-4 w-4" />
        Dashboard
      </TabsTrigger>
      <TabsTrigger value="scheduling" className="gap-2">
        <Calendar className="h-4 w-4" />
        Scheduling
      </TabsTrigger>
      <TabsTrigger value="budgeting" className="gap-2">
        <DollarSign className="h-4 w-4" />
        Budgeting
      </TabsTrigger>
      <TabsTrigger value="resources" className="gap-2">
        <Users className="h-4 w-4" />
        Resources
      </TabsTrigger>
      <TabsTrigger value="reports" className="gap-2">
        <FileText className="h-4 w-4" />
        Reports
      </TabsTrigger>
    </TabsList>
  );
};

export default ProductionNavigation;
