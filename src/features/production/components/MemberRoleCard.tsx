
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface OrganizationMember {
  id: string;
  user_id: string;
  profiles?: {
    full_name?: string;
    username?: string;
    avatar_url?: string;
  } | null;
}

interface ProductionRole {
  id: string;
  user_id: string;
  role: 'producer' | 'coordinator' | 'crew' | 'viewer';
  permissions: any;
  assigned_at: string;
}

interface MemberRoleCardProps {
  member: OrganizationMember;
  currentRole: ProductionRole['role'];
  onRoleChange: (userId: string, newRole: ProductionRole['role']) => void;
  getRoleColor: (role: string) => "default" | "destructive" | "secondary" | "outline";
}

export const MemberRoleCard: React.FC<MemberRoleCardProps> = ({
  member,
  currentRole,
  onRoleChange,
  getRoleColor
}) => {
  return (
    <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
      <div className="flex items-center space-x-3">
        <Avatar>
          <AvatarImage src={member.profiles?.avatar_url} />
          <AvatarFallback>
            {(member.profiles?.full_name || member.profiles?.username || 'U').charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        <div>
          <p className="font-medium">
            {member.profiles?.full_name || member.profiles?.username || 'Unknown User'}
          </p>
          <Badge variant={getRoleColor(currentRole)} className="text-xs">
            {currentRole}
          </Badge>
        </div>
      </div>
      
      <Select
        value={currentRole}
        onValueChange={(value) => onRoleChange(member.user_id, value as ProductionRole['role'])}
      >
        <SelectTrigger className="w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="producer">Producer</SelectItem>
          <SelectItem value="coordinator">Coordinator</SelectItem>
          <SelectItem value="crew">Crew</SelectItem>
          <SelectItem value="viewer">Viewer</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
