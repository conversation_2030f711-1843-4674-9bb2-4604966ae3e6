
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Calendar, Plus, Loader2 } from 'lucide-react';
import { useEnhancedProduction } from '@/hooks/useEnhancedProduction';
import type { CreateScheduleInput } from '@/features/production/validation/schemas';
import ScheduleForm from './ScheduleForm';
import ScheduleErrorAlert from './ScheduleErrorAlert';
import ScheduleList from './ScheduleList';
import ScheduleEmptyState from './ScheduleEmptyState';

// Define proper types for the form data
type ScheduleFormData = {
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
};

// Define proper types for form errors
type ScheduleFormErrors = {
  title?: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
};

const SchedulingTab: React.FC = () => {
  const { 
    schedules, 
    loading, 
    creating, 
    errors: rawErrors, 
    createSchedule, 
    clearErrors,
    isRetrying,
    retryCount
  } = useEnhancedProduction();
  
  // Type the errors properly
  const errors = rawErrors as ScheduleFormErrors;
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState<ScheduleFormData>({
    title: '',
    description: '',
    start_date: '',
    end_date: '',
    status: 'draft'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const scheduleInput: CreateScheduleInput = {
      title: formData.title,
      description: formData.description,
      start_date: formData.start_date,
      end_date: formData.end_date,
      status: formData.status
    };
    
    const newSchedule = await createSchedule(scheduleInput);
    if (newSchedule && newSchedule.success) {
      setIsCreateDialogOpen(false);
      setFormData({
        title: '',
        description: '',
        start_date: '',
        end_date: '',
        status: 'draft'
      });
    }
  };

  const handleDialogClose = () => {
    setIsCreateDialogOpen(false);
    clearErrors();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-y-2">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground mt-2">
                {isRetrying ? `Retrying... (${retryCount}/3)` : 'Loading production schedules...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Production Scheduling
            </CardTitle>
            <CardDescription>
              Manage shooting schedules, crew availability, and location bookings
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2" disabled={creating.schedule}>
                {creating.schedule ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                New Schedule
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Production Schedule</DialogTitle>
                <DialogDescription>
                  Set up a new production schedule for your project.
                </DialogDescription>
              </DialogHeader>
              
              <ScheduleErrorAlert errors={errors} />
              
              <ScheduleForm
                formData={formData}
                setFormData={setFormData}
                onSubmit={handleSubmit}
                onCancel={handleDialogClose}
                errors={errors}
                isCreating={creating.schedule}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {schedules.length === 0 ? (
          <ScheduleEmptyState />
        ) : (
          <ScheduleList schedules={schedules} />
        )}
      </CardContent>
    </Card>
  );
};

export default SchedulingTab;
