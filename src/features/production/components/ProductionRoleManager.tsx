
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield } from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useProductionRolesManager } from '../hooks/useProductionRolesManager';
import { ProductionRolesList } from './ProductionRolesList';

export const ProductionRoleManager: React.FC = () => {
  const { currentOrganization, members } = useOrganization();
  const { roles, updateRole, getRoleColor } = useProductionRolesManager();

  if (!currentOrganization) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shield className="h-5 w-5 mr-2" />
          Production Roles
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ProductionRolesList
          members={members}
          roles={roles}
          onRoleChange={updateRole}
          getRoleColor={getRoleColor}
        />
      </CardContent>
    </Card>
  );
};
