
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import type { ProductionReport } from '@/lib/api/production';

type ReportContent = {
  details?: string;
  summary?: string;
  issues?: string;
  next_steps?: string;
};

type ReportFormData = {
  title: string;
  date: string;
  status: 'draft' | 'approved' | 'submitted';
  report_type: 'progress' | 'wrap' | 'daily' | 'incident';
  content: ReportContent;
};

type ReportFormErrors = {
  title?: string;
  date?: string;
  status?: string;
  report_type?: string;
  content?: string;
};

interface ReportFormProps {
  formData: ReportFormData;
  setFormData: React.Dispatch<React.SetStateAction<ReportFormData>>;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  errors: ReportFormErrors;
  isCreating: boolean;
}

const ReportForm: React.FC<ReportFormProps> = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  errors,
  isCreating
}) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label htmlFor="title" className="text-sm font-medium">
          Title *
        </label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="e.g., Daily Report - Day 5"
          className={errors.title ? 'border-destructive' : ''}
          required
        />
        {errors.title && (
          <p className="text-sm text-destructive mt-1">{String(errors.title)}</p>
        )}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="report_type" className="text-sm font-medium">
            Report Type *
          </label>
          <Select
            value={formData.report_type}
            onValueChange={(value) => setFormData(prev => ({ 
              ...prev, 
              report_type: value as ProductionReport['report_type']
            }))}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select report type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily Report</SelectItem>
              <SelectItem value="wrap">Wrap Report</SelectItem>
              <SelectItem value="incident">Incident Report</SelectItem>
              <SelectItem value="progress">Progress Report</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label htmlFor="date" className="text-sm font-medium">
            Date *
          </label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
            className={errors.date ? 'border-destructive' : ''}
            required
          />
          {errors.date && (
            <p className="text-sm text-destructive mt-1">{String(errors.date)}</p>
          )}
        </div>
      </div>
      
      <div>
        <label htmlFor="content" className="text-sm font-medium">
          Report Content
        </label>
        <Textarea
          id="content"
          value={typeof formData.content === 'object' ? JSON.stringify(formData.content, null, 2) : String(formData.content)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value);
              setFormData(prev => ({ ...prev, content: parsed }));
            } catch {
              setFormData(prev => ({ 
                ...prev, 
                content: { 
                  ...prev.content,
                  details: e.target.value 
                }
              }));
            }
          }}
          placeholder="Enter report details..."
          rows={6}
        />
      </div>
      
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isCreating}>
          {isCreating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Creating...
            </>
          ) : (
            'Create Report'
          )}
        </Button>
      </div>
    </form>
  );
};

export default ReportForm;
