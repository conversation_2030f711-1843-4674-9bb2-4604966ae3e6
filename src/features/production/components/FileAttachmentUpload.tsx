
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, X, File } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { supabase } from '@/integrations/supabase/client';

interface FileAttachmentUploadProps {
  entityType: 'report' | 'budget' | 'schedule' | 'resource';
  entityId: string;
  onFileUploaded?: () => void;
}

export const FileAttachmentUpload: React.FC<FileAttachmentUploadProps> = ({
  entityType,
  entityId,
  onFileUploaded
}) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const [files, setFiles] = useState<File[]>([]);
  const [descriptions, setDescriptions] = useState<Record<string, string>>({});
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    setFiles(prev => [...prev, ...selectedFiles]);
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
    setDescriptions(prev => {
      const newDescriptions = { ...prev };
      delete newDescriptions[`file-${index}`];
      return newDescriptions;
    });
  };

  const updateDescription = (index: number, description: string) => {
    setDescriptions(prev => ({
      ...prev,
      [`file-${index}`]: description
    }));
  };

  const uploadFiles = async () => {
    if (!user || !currentOrganization || files.length === 0) return;

    setUploading(true);
    
    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const storagePath = `${currentOrganization.id}/${entityType}/${entityId}/${Date.now()}-${file.name}`;
        
        // Note: This would require setting up Supabase Storage first
        // For now, we'll just store the file metadata
        const { error } = await supabase
          .from('production_file_attachments')
          .insert({
            org_id: currentOrganization.id,
            user_id: user.id,
            entity_type: entityType,
            entity_id: entityId,
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            storage_path: storagePath,
            description: descriptions[`file-${i}`] || null
          });

        if (error) throw error;
      }

      toast({
        title: "Files uploaded",
        description: `${files.length} file(s) uploaded successfully`
      });

      setFiles([]);
      setDescriptions({});
      onFileUploaded?.();
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: "Failed to upload files. Please try again.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Upload className="h-5 w-5 mr-2" />
          Upload Files
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Label htmlFor="file-upload">Select Files</Label>
          <Input
            id="file-upload"
            type="file"
            multiple
            onChange={handleFileSelect}
            className="mt-1"
          />
        </div>

        {files.length > 0 && (
          <div className="space-y-3">
            {files.map((file, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-muted rounded-lg">
                <File className="h-5 w-5 mt-1 text-muted-foreground" />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{file.name}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {(file.size / 1024).toFixed(1)} KB
                  </p>
                  <Textarea
                    placeholder="Description (optional)"
                    value={descriptions[`file-${index}`] || ''}
                    onChange={(e) => updateDescription(index, e.target.value)}
                    className="text-sm"
                    rows={2}
                  />
                </div>
              </div>
            ))}
          </div>
        )}

        {files.length > 0 && (
          <Button onClick={uploadFiles} disabled={uploading} className="w-full">
            {uploading ? 'Uploading...' : `Upload ${files.length} file(s)`}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
