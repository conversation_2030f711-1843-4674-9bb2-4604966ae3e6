
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus } from 'lucide-react';

interface TemplateCreationDialogProps {
  onCreateTemplate: (template: {
    name: string;
    description: string;
    template_type: string;
    is_public: boolean;
  }) => Promise<boolean>;
}

export const TemplateCreationDialog: React.FC<TemplateCreationDialogProps> = ({
  onCreateTemplate
}) => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    template_type: 'schedule',
    is_public: false
  });

  const handleCreate = async () => {
    const success = await onCreateTemplate(newTemplate);
    if (success) {
      setDialogOpen(false);
      setNewTemplate({
        name: '',
        description: '',
        template_type: 'schedule',
        is_public: false
      });
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Create Template
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create Production Template</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="template-name">Template Name</Label>
            <Input
              id="template-name"
              value={newTemplate.name}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter template name"
            />
          </div>
          
          <div>
            <Label htmlFor="template-description">Description</Label>
            <Textarea
              id="template-description"
              value={newTemplate.description}
              onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe what this template includes"
            />
          </div>
          
          <div>
            <Label htmlFor="template-type">Template Type</Label>
            <Select
              value={newTemplate.template_type}
              onValueChange={(value) => setNewTemplate(prev => ({ ...prev, template_type: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="schedule">Schedule Template</SelectItem>
                <SelectItem value="budget">Budget Template</SelectItem>
                <SelectItem value="complete">Complete Production</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch
              id="public-template"
              checked={newTemplate.is_public}
              onCheckedChange={(checked) => setNewTemplate(prev => ({ ...prev, is_public: checked }))}
            />
            <Label htmlFor="public-template">Make template public</Label>
          </div>
          
          <Button onClick={handleCreate} className="w-full">
            Create Template
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
