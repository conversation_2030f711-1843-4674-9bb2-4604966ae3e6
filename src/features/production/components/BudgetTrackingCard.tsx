
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { TrendingUp, AlertTriangle } from 'lucide-react';
import { useBudgetTracking } from '@/hooks/useBudgetTracking';
import type { ProductionBudget } from '@/lib/api/production';

interface BudgetTrackingCardProps {
  selectedBudgetId?: string;
  budgets: ProductionBudget[];
}

const BudgetTrackingCard: React.FC<BudgetTrackingCardProps> = ({
  selectedBudgetId,
  budgets
}) => {
  const { tracking } = useBudgetTracking(selectedBudgetId);

  if (!selectedBudgetId) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Budget Tracking
        </CardTitle>
        <CardDescription>
          Real-time budget utilization and variance tracking
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Budget Alerts */}
        {tracking.alerts.length > 0 && (
          <div className="space-y-2">
            {tracking.alerts.map((alert, index) => (
              <Alert key={index} variant={alert.severity === 'error' ? 'destructive' : 'default'}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{alert.message}</AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Budget Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Budget Utilization</span>
            <span>{tracking.budgetUtilization.toFixed(1)}%</span>
          </div>
          <Progress 
            value={Math.min(tracking.budgetUtilization, 100)}
            className={tracking.isOverBudget ? 'bg-red-100' : ''}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Spent: ${tracking.totalActual.toLocaleString()}</span>
            <span>Budget: ${(budgets.find(b => b.id === selectedBudgetId)?.total_budget || 0).toLocaleString()}</span>
          </div>
        </div>

        {/* Budget Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-green-600">
              ${tracking.totalEstimated.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">Estimated</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              ${tracking.totalActual.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">Actual</div>
          </div>
          <div className="text-center p-4 border rounded-lg">
            <div className={`text-2xl font-bold ${tracking.budgetVariance > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {tracking.budgetVariance > 0 ? '+' : ''}${tracking.budgetVariance.toLocaleString()}
            </div>
            <div className="text-sm text-muted-foreground">Variance</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BudgetTrackingCard;
