
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useCallSheets } from '../hooks/useCallSheets';
import { CallSheetForm } from './CallSheetForm';
import { CallSheetList } from './CallSheetList';

export const CallSheetManager: React.FC = () => {
  const [showForm, setShowForm] = useState(false);
  const {
    callSheets,
    loading,
    formData,
    updateFormData,
    createCallSheet,
    resetForm,
    getStatusColor
  } = useCallSheets();

  const handleCreateCallSheet = async () => {
    const success = await createCallSheet();
    if (success) {
      setShowForm(false);
    }
    return success;
  };

  const handleCancelForm = () => {
    setShowForm(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Call Sheets</h2>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Call Sheet
        </Button>
      </div>

      {showForm && (
        <CallSheetForm
          formData={formData}
          onFormDataChange={updateFormData}
          onSubmit={handleCreateCallSheet}
          onCancel={handleCancelForm}
        />
      )}

      <CallSheetList
        callSheets={callSheets}
        loading={loading}
        getStatusColor={getStatusColor}
      />
    </div>
  );
};
