
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface CallSheetFormData {
  title: string;
  call_date: string;
  schedule_id: string;
  general_notes: string;
  weather_info: Record<string, any>;
  emergency_contacts: Record<string, any>;
}

interface CallSheetFormProps {
  formData: CallSheetFormData;
  onFormDataChange: (updates: Partial<CallSheetFormData>) => void;
  onSubmit: () => Promise<boolean>;
  onCancel: () => void;
}

export const CallSheetForm: React.FC<CallSheetFormProps> = ({
  formData,
  onFormDataChange,
  onSubmit,
  onCancel
}) => {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await onSubmit();
    if (success) {
      onCancel();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Create New Call Sheet</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => onFormDataChange({ title: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="call_date">Call Date</Label>
              <Input
                id="call_date"
                type="date"
                value={formData.call_date}
                onChange={(e) => onFormDataChange({ call_date: e.target.value })}
                required
              />
            </div>
          </div>
          <div>
            <Label htmlFor="general_notes">General Notes</Label>
            <Textarea
              id="general_notes"
              value={formData.general_notes}
              onChange={(e) => onFormDataChange({ general_notes: e.target.value })}
              rows={3}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">Create Call Sheet</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
