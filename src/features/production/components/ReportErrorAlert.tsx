
import React from 'react';
import ErrorDisplay from '@/components/ErrorDisplay';

type ReportFormErrors = {
  title?: string;
  date?: string;
  status?: string;
  report_type?: string;
  content?: string;
};

interface ReportErrorAlertProps {
  errors: ReportFormErrors;
}

const ReportErrorAlert: React.FC<ReportErrorAlertProps> = ({ errors }) => {
  return (
    <ErrorDisplay 
      errors={errors}
      title="Report Form Errors"
    />
  );
};

export default ReportErrorAlert;
