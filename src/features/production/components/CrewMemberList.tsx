
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Users, Calendar, Clock } from 'lucide-react';
import type { CrewMember } from '@/lib/api/production/crewApi';

interface CrewMemberListProps {
  crewMembers: CrewMember[];
  loading: boolean;
  getAvailabilityColor: (status: CrewMember['availability_status']) => string;
}

export const CrewMemberList: React.FC<CrewMemberListProps> = ({
  crewMembers,
  loading,
  getAvailabilityColor
}) => {
  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (crewMembers.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">
          No crew members found. Add your first crew member to get started.
        </div>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {crewMembers.map((member) => (
        <Card key={member.id}>
          <CardContent className="p-4">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold">{member.name}</h3>
                  <Badge className={getAvailabilityColor(member.availability_status)}>
                    {member.availability_status}
                  </Badge>
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <span className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    {member.role}
                  </span>
                  {member.daily_rate && (
                    <span>${member.daily_rate}/day</span>
                  )}
                </div>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  {member.email && <span>{member.email}</span>}
                  {member.phone && <span>{member.phone}</span>}
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <Calendar className="h-4 w-4 mr-1" />
                  Schedule
                </Button>
                <Button variant="outline" size="sm">
                  <Clock className="h-4 w-4 mr-1" />
                  Availability
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
