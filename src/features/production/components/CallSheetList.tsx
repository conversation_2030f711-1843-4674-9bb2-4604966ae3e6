
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, FileText, Download } from 'lucide-react';
import type { CallSheet } from '@/lib/api/production/callSheetsApi';

interface CallSheetListProps {
  callSheets: CallSheet[];
  loading: boolean;
  getStatusColor: (status: string) => string;
}

export const CallSheetList: React.FC<CallSheetListProps> = ({
  callSheets,
  loading,
  getStatusColor
}) => {
  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (callSheets.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">
          No call sheets found. Create your first call sheet to get started.
        </div>
      </Card>
    );
  }

  return (
    <div className="grid gap-4">
      {callSheets.map((callSheet) => (
        <Card key={callSheet.id}>
          <CardContent className="p-6">
            <div className="flex justify-between items-start">
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <h3 className="text-lg font-semibold">{callSheet.title}</h3>
                  <Badge className={getStatusColor(callSheet.status)}>
                    {callSheet.status}
                  </Badge>
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4 mr-1" />
                  {new Date(callSheet.call_date).toLocaleDateString()}
                </div>
                {callSheet.general_notes && (
                  <p className="text-sm text-muted-foreground">{callSheet.general_notes}</p>
                )}
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <FileText className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
