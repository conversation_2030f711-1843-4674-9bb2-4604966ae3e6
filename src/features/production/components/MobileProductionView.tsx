
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  DollarSign, 
  Users, 
  FileText, 
  Clock, 
  MapPin,
  ChevronRight,
  Smartphone
} from 'lucide-react';
import type { 
  ProductionSchedule, 
  ProductionBudget, 
  ProductionResource, 
  ProductionReport 
} from '@/lib/api/production';

interface MobileProductionViewProps {
  schedules: ProductionSchedule[];
  budgets: ProductionBudget[];
  resources: ProductionResource[];
  reports: ProductionReport[];
}

export const MobileProductionView: React.FC<MobileProductionViewProps> = ({
  schedules,
  budgets,
  resources,
  reports
}) => {
  const [selectedTab, setSelectedTab] = useState('today');

  const todaysSchedules = schedules.filter(schedule => {
    const today = new Date().toISOString().split('T')[0];
    return schedule.start_date <= today && schedule.end_date >= today;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'in_progress':
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="lg:hidden space-y-4 p-4">
      <div className="flex items-center gap-2 mb-4">
        <Smartphone className="h-5 w-5" />
        <h2 className="text-lg font-semibold">Production Dashboard</h2>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="today" className="text-xs">Today</TabsTrigger>
          <TabsTrigger value="budget" className="text-xs">Budget</TabsTrigger>
          <TabsTrigger value="crew" className="text-xs">Crew</TabsTrigger>
          <TabsTrigger value="reports" className="text-xs">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="today" className="space-y-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Today's Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {todaysSchedules.length === 0 ? (
                <p className="text-sm text-muted-foreground">No schedules for today</p>
              ) : (
                todaysSchedules.map(schedule => (
                  <div key={schedule.id} className="border rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium text-sm">{schedule.title}</h3>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge className={getStatusColor(schedule.status)}>
                            {schedule.status}
                          </Badge>
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {new Date(schedule.start_date).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <ChevronRight className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="budget" className="space-y-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Budget Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {budgets.map(budget => (
                <div key={budget.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-sm">{budget.title}</h3>
                      <p className="text-lg font-bold text-green-600">
                        {budget.currency} {budget.total_budget.toLocaleString()}
                      </p>
                    </div>
                    <Badge className={getStatusColor(budget.status)}>
                      {budget.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="crew" className="space-y-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <Users className="h-4 w-4" />
                Resources & Crew
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {resources.map(resource => (
                <div key={resource.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-sm">{resource.name}</h3>
                      <p className="text-xs text-muted-foreground">{resource.type}</p>
                    </div>
                    <Badge className={getStatusColor(resource.availability_status)}>
                      {resource.availability_status}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-3">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Recent Reports
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {reports.slice(0, 5).map(report => (
                <div key={report.id} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-sm">{report.title}</h3>
                      <p className="text-xs text-muted-foreground">
                        {new Date(report.date).toLocaleDateString()}
                      </p>
                    </div>
                    <Badge className={getStatusColor(report.status)}>
                      {report.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
