
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ClipboardList } from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useAuditLogs } from '../hooks/useAuditLogs';
import { AuditLogFilters } from './AuditLogFilters';
import { AuditLogList } from './AuditLogList';

export const ProductionAuditLog: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { logs, loading, fetchAuditLogs, getActionColor, getEntityIcon } = useAuditLogs();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterEntity, setFilterEntity] = useState<string>('all');
  const [filterAction, setFilterAction] = useState<string>('all');

  const filteredLogs = logs.filter(log =>
    log.changes_summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.entity_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    fetchAuditLogs(filterEntity, filterAction);
  }, [filterEntity, filterAction]);

  if (!currentOrganization) return null;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <ClipboardList className="h-5 w-5 mr-2" />
          Audit Log
        </CardTitle>
        <AuditLogFilters
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          filterEntity={filterEntity}
          onEntityFilterChange={setFilterEntity}
          filterAction={filterAction}
          onActionFilterChange={setFilterAction}
        />
      </CardHeader>
      <CardContent>
        <AuditLogList
          logs={filteredLogs}
          loading={loading}
          getActionColor={getActionColor}
          getEntityIcon={getEntityIcon}
        />
      </CardContent>
    </Card>
  );
};
