
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { FileText, Plus, Loader2 } from 'lucide-react';
import { useEnhancedProduction } from '@/hooks/useEnhancedProduction';
import type { CreateReportInput } from '@/features/production/validation/schemas';
import ReportForm from './ReportForm';
import ReportErrorAlert from './ReportErrorAlert';
import ReportList from './ReportList';
import ReportEmptyState from './ReportEmptyState';

// Define proper types for the form data
type ReportContent = {
  details?: string;
  summary?: string;
  issues?: string;
  next_steps?: string;
};

type ReportFormData = {
  title: string;
  date: string;
  status: 'draft' | 'approved' | 'submitted';
  report_type: 'progress' | 'wrap' | 'daily' | 'incident';
  content: ReportContent;
};

// Define proper types for form errors
type ReportFormErrors = {
  title?: string;
  date?: string;
  status?: string;
  report_type?: string;
  content?: string;
};

const ReportsTab: React.FC = () => {
  const { 
    reports, 
    loading, 
    creating, 
    errors: rawErrors, 
    createReport, 
    clearErrors,
    isRetrying,
    retryCount
  } = useEnhancedProduction();
  
  // Type the errors properly
  const errors = rawErrors as ReportFormErrors;
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState<ReportFormData>({
    title: '',
    report_type: 'daily',
    content: {},
    date: new Date().toISOString().split('T')[0],
    status: 'draft'
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const reportInput: CreateReportInput = {
      title: formData.title,
      report_type: formData.report_type,
      content: formData.content,
      date: formData.date,
      status: formData.status
    };
    
    const newReport = await createReport(reportInput);
    if (newReport && newReport.success) {
      setIsCreateDialogOpen(false);
      setFormData({
        title: '',
        report_type: 'daily',
        content: {},
        date: new Date().toISOString().split('T')[0],
        status: 'draft'
      });
    }
  };

  const handleDialogClose = () => {
    setIsCreateDialogOpen(false);
    clearErrors();
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-y-2">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground mt-2">
                {isRetrying ? `Retrying... (${retryCount}/3)` : 'Loading production reports...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Production Reports
            </CardTitle>
            <CardDescription>
              Create and manage daily reports, wrap reports, and incident documentation
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2" disabled={creating.report}>
                {creating.report ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                New Report
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Production Report</DialogTitle>
                <DialogDescription>
                  Document daily activities, wrap details, or incidents.
                </DialogDescription>
              </DialogHeader>
              
              <ReportErrorAlert errors={errors} />
              
              <ReportForm
                formData={formData}
                setFormData={setFormData}
                onSubmit={handleSubmit}
                onCancel={handleDialogClose}
                errors={errors}
                isCreating={creating.report}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {reports.length === 0 ? (
          <ReportEmptyState />
        ) : (
          <ReportList reports={reports} />
        )}
      </CardContent>
    </Card>
  );
};

export default ReportsTab;
