
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, Download, Filter } from 'lucide-react';
import { useProductionSearch } from '@/hooks/useProductionSearch';
import { useProductionExport } from '@/hooks/useProductionExport';
import { usePagination } from '@/hooks/usePagination';
import { Card, CardContent } from '@/components/ui/card';
import { LoadingSpinner } from '@/components/LoadingSpinner';

export const ProductionSearchBar: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [entityFilter, setEntityFilter] = useState('all');
  const { results, loading, search } = useProductionSearch();
  const { exporting, exportData } = useProductionExport();
  const pagination = usePagination(10);

  const handleSearch = () => {
    search(searchQuery, entityFilter, pagination.page, pagination.pageSize);
  };

  const handleExport = () => {
    exportData();
  };

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery.length > 2 || searchQuery.length === 0) {
        handleSearch();
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, entityFilter, pagination.page]);

  return (
    <div className="space-y-4">
      {/* Search Controls */}
      <div className="flex gap-2 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search schedules, budgets, resources, reports..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={entityFilter} onValueChange={setEntityFilter}>
          <SelectTrigger className="w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="schedules">Schedules</SelectItem>
            <SelectItem value="budgets">Budgets</SelectItem>
            <SelectItem value="resources">Resources</SelectItem>
            <SelectItem value="reports">Reports</SelectItem>
          </SelectContent>
        </Select>

        <Button onClick={handleExport} disabled={exporting} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          {exporting ? 'Exporting...' : 'Export'}
        </Button>
      </div>

      {/* Search Results */}
      {loading && (
        <div className="flex justify-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {!loading && results.length > 0 && (
        <div className="space-y-2">
          <p className="text-sm text-gray-600">Found {results.length} results</p>
          {results.map((result) => (
            <Card key={`${result.entity_type}-${result.entity_id}`} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {result.entity_type}
                      </span>
                      <h3 className="font-medium">{result.title}</h3>
                    </div>
                    {result.description && (
                      <p className="text-sm text-gray-600 line-clamp-2">{result.description}</p>
                    )}
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>Status: {result.status}</span>
                      <span>Created: {new Date(result.created_at).toLocaleDateString()}</span>
                      <span>Relevance: {(result.relevance_rank * 100).toFixed(0)}%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {!loading && searchQuery && results.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No results found for "{searchQuery}"
        </div>
      )}
    </div>
  );
};
