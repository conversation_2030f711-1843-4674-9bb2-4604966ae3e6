
import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { CrewMember } from '@/lib/api/production/crewApi';

interface CrewMemberFormData {
  name: string;
  role: string;
  email: string;
  phone: string;
  daily_rate: string;
  overtime_rate: string;
  availability_status: CrewMember['availability_status'];
  union_status: string;
  skills: Record<string, any>;
  emergency_contact: Record<string, any>;
  notes: string;
}

interface CrewMemberFormProps {
  formData: CrewMemberFormData;
  onFormDataChange: (updates: Partial<CrewMemberFormData>) => void;
  onSubmit: () => Promise<boolean>;
  onCancel: () => void;
}

export const CrewMemberForm: React.FC<CrewMemberFormProps> = ({
  formData,
  onFormDataChange,
  onSubmit,
  onCancel
}) => {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await onSubmit();
    if (success) {
      onCancel();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Crew Member</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => onFormDataChange({ name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="role">Role</Label>
              <Select onValueChange={(value) => onFormDataChange({ role: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="director">Director</SelectItem>
                  <SelectItem value="producer">Producer</SelectItem>
                  <SelectItem value="cinematographer">Cinematographer</SelectItem>
                  <SelectItem value="gaffer">Gaffer</SelectItem>
                  <SelectItem value="sound-mixer">Sound Mixer</SelectItem>
                  <SelectItem value="script-supervisor">Script Supervisor</SelectItem>
                  <SelectItem value="assistant-director">Assistant Director</SelectItem>
                  <SelectItem value="camera-operator">Camera Operator</SelectItem>
                  <SelectItem value="grip">Grip</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => onFormDataChange({ email: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => onFormDataChange({ phone: e.target.value })}
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="daily_rate">Daily Rate ($)</Label>
              <Input
                id="daily_rate"
                type="number"
                step="0.01"
                value={formData.daily_rate}
                onChange={(e) => onFormDataChange({ daily_rate: e.target.value })}
              />
            </div>
            <div>
              <Label htmlFor="overtime_rate">Overtime Rate ($)</Label>
              <Input
                id="overtime_rate"
                type="number"
                step="0.01"
                value={formData.overtime_rate}
                onChange={(e) => onFormDataChange({ overtime_rate: e.target.value })}
              />
            </div>
          </div>
          <div>
            <Label htmlFor="availability_status">Availability Status</Label>
            <Select onValueChange={(value: CrewMember['availability_status']) => 
              onFormDataChange({ availability_status: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select availability" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="available">Available</SelectItem>
                <SelectItem value="busy">Busy</SelectItem>
                <SelectItem value="unavailable">Unavailable</SelectItem>
                <SelectItem value="tentative">Tentative</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit">Add Crew Member</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
