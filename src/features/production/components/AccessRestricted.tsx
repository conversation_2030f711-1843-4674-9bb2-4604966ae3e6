
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Lock, CreditCard, Clapperboard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AccessRestrictedProps {
  tier: string | null;
  subscribed?: boolean;
}

const AccessRestricted: React.FC<AccessRestrictedProps> = ({ tier, subscribed = false }) => {
  const navigate = useNavigate();

  const getAccessMessage = () => {
    if (!tier) {
      return 'Please sign in and subscribe to access production tools.';
    }
    
    if (!subscribed) {
      return `You have a ${tier} account but no active subscription. Please subscribe to access production tools.`;
    }
    
    return 'Production tools are available for Studio and Enterprise subscribers only.';
  };

  const getUpgradeAction = () => {
    if (!tier || !subscribed) {
      return 'Subscribe Now';
    }
    return 'Upgrade to Studio';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-3 mb-8">
        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg flex items-center justify-center">
          <Clapperboard className="h-6 w-6 text-white" />
        </div>
        <div>
          <h1 className="text-3xl font-playfair font-bold">Production Tools</h1>
          <p className="text-muted-foreground">Professional production management for Studio subscribers</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {subscribed ? <Lock className="h-5 w-5" /> : <CreditCard className="h-5 w-5" />}
            {subscribed ? 'Access Restricted' : 'Subscription Required'}
          </CardTitle>
          <CardDescription>
            {subscribed 
              ? 'Production tools require a Studio or Enterprise subscription.'
              : 'An active subscription is required to access production tools.'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              {getAccessMessage()}
            </AlertDescription>
          </Alert>
          
          {tier && subscribed && (
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                Current Plan: <span className="font-medium capitalize">{tier}</span>
              </p>
              <p className="text-sm text-muted-foreground">
                Status: <span className="font-medium text-green-600">Active Subscription</span>
              </p>
            </div>
          )}
          
          <div className="flex gap-4">
            <Button onClick={() => navigate('/#pricing')}>
              {getUpgradeAction()}
            </Button>
            <Button variant="outline" onClick={() => navigate('/')}>
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccessRestricted;
