
import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { DraggableScheduleItem } from './DraggableScheduleItem';
import type { ProductionScheduleItem } from '@/lib/api/production';

interface DroppableTimeSlotProps {
  timeSlot: string;
  items: ProductionScheduleItem[];
}

export const DroppableTimeSlot: React.FC<DroppableTimeSlotProps> = ({ timeSlot, items }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: timeSlot,
  });

  return (
    <div
      ref={setNodeRef}
      className={`min-h-16 border rounded-lg p-2 transition-colors ${
        isOver ? 'bg-blue-50 border-blue-300' : 'bg-gray-50 border-gray-200'
      }`}
    >
      <div className="text-xs font-medium text-gray-600 mb-2">{timeSlot}</div>
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <div className="space-y-1">
          {items.map(item => (
            <DraggableScheduleItem key={item.id} item={item} />
          ))}
        </div>
      </SortableContext>
    </div>
  );
};
