
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Users } from 'lucide-react';
import type { ProductionResource } from '@/lib/api/production';

interface ResourceStatusCardProps {
  resources: ProductionResource[];
}

const ResourceStatusCard: React.FC<ResourceStatusCardProps> = ({ resources }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Resource Status
        </CardTitle>
        <CardDescription>
          Equipment and crew availability overview
        </CardDescription>
      </CardHeader>
      <CardContent>
        {resources.length === 0 ? (
          <p className="text-center text-muted-foreground py-4">
            No resources available
          </p>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {['available', 'booked', 'maintenance', 'unavailable'].map((status) => {
              const count = resources.filter(r => r.availability_status === status).length;
              const percentage = resources.length > 0 ? (count / resources.length) * 100 : 0;
              
              return (
                <div key={status} className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold mb-1">{count}</div>
                  <div className="text-sm text-muted-foreground capitalize mb-2">{status}</div>
                  <Progress value={percentage} className="h-2" />
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ResourceStatusCard;
