
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Globe, Lock } from 'lucide-react';

interface ProductionTemplate {
  id: string;
  name: string;
  description?: string;
  template_type: string;
  template_data: any;
  is_public: boolean;
  usage_count: number;
  created_at: string;
  created_by: string;
}

interface TemplateListProps {
  templates: ProductionTemplate[];
  onUseTemplate: (template: ProductionTemplate) => void;
}

export const TemplateList: React.FC<TemplateListProps> = ({
  templates,
  onUseTemplate
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {templates.map((template) => (
        <Card key={template.id}>
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-lg">{template.name}</CardTitle>
                <div className="flex items-center space-x-2 mt-1">
                  <Badge variant="outline">{template.template_type}</Badge>
                  {template.is_public ? (
                    <Globe className="h-4 w-4 text-green-600" />
                  ) : (
                    <Lock className="h-4 w-4 text-gray-500" />
                  )}
                </div>
              </div>
              <FileText className="h-5 w-5 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            {template.description && (
              <p className="text-sm text-muted-foreground mb-3">
                {template.description}
              </p>
            )}
            <div className="flex items-center justify-between text-xs text-muted-foreground mb-3">
              <span>Used {template.usage_count} times</span>
              <span>{new Date(template.created_at).toLocaleDateString()}</span>
            </div>
            <Button onClick={() => onUseTemplate(template)} className="w-full" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Use Template
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
