
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { CardDescription, CardTitle } from '@/components/ui/card';
import { Calendar, Plus, Loader2 } from 'lucide-react';
import { ProductionNotifications } from './ProductionNotifications';

interface ScheduleHeaderProps {
  creating: boolean;
  onCreateClick: () => void;
}

export const ScheduleHeader: React.FC<ScheduleHeaderProps> = ({
  creating,
  onCreateClick
}) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Enhanced Production Scheduling
        </CardTitle>
        <CardDescription>
          Drag-and-drop scheduling with calendar view and bulk operations
        </CardDescription>
      </div>
      <div className="flex items-center gap-2">
        <ProductionNotifications />
        <Button className="gap-2" disabled={creating} onClick={onCreateClick}>
          {creating ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Plus className="h-4 w-4" />
          )}
          New Schedule
        </Button>
      </div>
    </div>
  );
};
