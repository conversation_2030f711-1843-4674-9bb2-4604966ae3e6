
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Bell, X, Clock, DollarSign, Users, Calendar } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useOrganization } from '@/contexts/OrganizationContext';

interface ProductionNotification {
  id: string;
  type: 'schedule_change' | 'budget_update' | 'resource_change' | 'report_ready';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  data?: any;
}

export const ProductionNotifications: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [notifications, setNotifications] = useState<ProductionNotification[]>([]);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (!currentOrganization) return;

    // Set up real-time subscription for production notifications
    const channel = supabase
      .channel('production_notifications')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'production_schedules',
          filter: `org_id=eq.${currentOrganization.id}`
        },
        (payload) => {
          handleScheduleChange(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'production_budgets',
          filter: `org_id=eq.${currentOrganization.id}`
        },
        (payload) => {
          handleBudgetChange(payload);
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'production_resources',
          filter: `org_id=eq.${currentOrganization.id}`
        },
        (payload) => {
          handleResourceChange(payload);
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [currentOrganization]);

  const handleScheduleChange = (payload: any) => {
    const newNotification: ProductionNotification = {
      id: `schedule_${payload.new?.id || payload.old?.id}_${Date.now()}`,
      type: 'schedule_change',
      title: 'Schedule Updated',
      message: payload.eventType === 'DELETE' 
        ? 'A schedule has been deleted'
        : `Schedule "${payload.new?.title}" has been ${payload.eventType.toLowerCase()}d`,
      timestamp: new Date().toISOString(),
      read: false,
      data: payload
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 19)]);
  };

  const handleBudgetChange = (payload: any) => {
    const newNotification: ProductionNotification = {
      id: `budget_${payload.new?.id || payload.old?.id}_${Date.now()}`,
      type: 'budget_update',
      title: 'Budget Updated',
      message: payload.eventType === 'DELETE'
        ? 'A budget has been deleted'
        : `Budget "${payload.new?.title}" has been ${payload.eventType.toLowerCase()}d`,
      timestamp: new Date().toISOString(),
      read: false,
      data: payload
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 19)]);
  };

  const handleResourceChange = (payload: any) => {
    const newNotification: ProductionNotification = {
      id: `resource_${payload.new?.id || payload.old?.id}_${Date.now()}`,
      type: 'resource_change',
      title: 'Resource Updated',
      message: payload.eventType === 'DELETE'
        ? 'A resource has been deleted'
        : `Resource "${payload.new?.name}" has been ${payload.eventType.toLowerCase()}d`,
      timestamp: new Date().toISOString(),
      read: false,
      data: payload
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 19)]);
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const getNotificationIcon = (type: ProductionNotification['type']) => {
    switch (type) {
      case 'schedule_change':
        return <Calendar className="h-4 w-4" />;
      case 'budget_update':
        return <DollarSign className="h-4 w-4" />;
      case 'resource_change':
        return <Users className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="relative">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        <Bell className="h-4 w-4" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs">
            {unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <Card className="absolute right-0 top-full mt-2 w-80 max-h-96 overflow-hidden z-50 shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Production Updates</CardTitle>
              <div className="flex items-center gap-1">
                {unreadCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                    Mark all read
                  </Button>
                )}
                <Button variant="ghost" size="sm" onClick={() => setIsOpen(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            <div className="max-h-64 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No notifications yet
                </div>
              ) : (
                notifications.map(notification => (
                  <div
                    key={notification.id}
                    className={`p-3 border-b cursor-pointer hover:bg-gray-50 ${
                      !notification.read ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => markAsRead(notification.id)}
                  >
                    <div className="flex items-start gap-2">
                      <div className="mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium">{notification.title}</h4>
                        <p className="text-xs text-muted-foreground">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-1 mt-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">
                            {new Date(notification.timestamp).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      {!notification.read && (
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
