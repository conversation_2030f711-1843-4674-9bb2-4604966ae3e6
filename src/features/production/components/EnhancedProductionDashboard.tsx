
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProductionSearchBar } from './ProductionSearchBar';
import OverviewCards from './OverviewCards';
import EnhancedSchedulingTab from './EnhancedSchedulingTab';
import BudgetingTab from './BudgetingTab';
import ResourceManagementTab from './ResourceManagementTab';
import ReportsTab from './ReportsTab';
import { ProductionAuditLog } from './ProductionAuditLog';
import { Search, Calendar, DollarSign, Package, FileText, ClipboardList } from 'lucide-react';
import { useEnhancedProduction } from '@/hooks/useEnhancedProduction';

export const EnhancedProductionDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const {
    schedules,
    budgets,
    resources,
    reports,
    loading,
    error
  } = useEnhancedProduction();

  return (
    <div className="space-y-6">
      {/* Global Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Global Search & Export
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ProductionSearchBar />
        </CardContent>
      </Card>

      {/* Main Dashboard */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="scheduling" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Scheduling
          </TabsTrigger>
          <TabsTrigger value="budgeting" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Budgeting
          </TabsTrigger>
          <TabsTrigger value="resources" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Resources
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Reports
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            Audit Log
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="text-muted-foreground">Loading overview data...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center p-8">
              <div className="text-destructive">Error loading data: {error.message || 'An unexpected error occurred'}</div>
            </div>
          ) : (
            <OverviewCards 
              schedules={schedules}
              budgets={budgets}
              resources={resources}
              reports={reports}
            />
          )}
        </TabsContent>

        <TabsContent value="scheduling">
          <EnhancedSchedulingTab />
        </TabsContent>

        <TabsContent value="budgeting">
          <BudgetingTab />
        </TabsContent>

        <TabsContent value="resources">
          <ResourceManagementTab />
        </TabsContent>

        <TabsContent value="reports">
          <ReportsTab />
        </TabsContent>

        <TabsContent value="audit">
          <ProductionAuditLog />
        </TabsContent>
      </Tabs>
    </div>
  );
};
