
import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertCircle } from 'lucide-react';
import { useEnhancedProduction } from '@/hooks/useEnhancedProduction';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { schedulesApi } from '@/lib/api/production';
import { MobileProductionView } from './MobileProductionView';
import { ScheduleHeader } from './ScheduleHeader';
import { ScheduleCalendarView } from './ScheduleCalendarView';
import { BulkOperationsView } from './BulkOperationsView';
import { ScheduleListView } from './ScheduleListView';
import type { ProductionScheduleItem } from '@/lib/api/production';

const EnhancedSchedulingTab: React.FC = () => {
  const { 
    schedules, 
    loading, 
    creating, 
    isRetrying,
    retryCount,
    budgets,
    resources,
    reports
  } = useEnhancedProduction();
  
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [scheduleItems, setScheduleItems] = useState<ProductionScheduleItem[]>([]);
  const [activeTab, setActiveTab] = useState('calendar');

  // Fetch schedule items for the active schedules
  React.useEffect(() => {
    const fetchScheduleItems = async () => {
      if (schedules.length === 0) return;
      
      try {
        const allItems: ProductionScheduleItem[] = [];
        
        for (const schedule of schedules) {
          const result = await schedulesApi.getScheduleItems(schedule.id);
          if (result.success && result.data) {
            allItems.push(...result.data);
          }
        }
        
        setScheduleItems(allItems);
      } catch (error) {
        console.error('Failed to fetch schedule items:', error);
      }
    };

    fetchScheduleItems();
  }, [schedules]);

  const handleItemMove = async (itemId: string, newDate: string, newTime: string) => {
    // Update the schedule item with new date/time
    setScheduleItems(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, scheduled_date: newDate, start_time: newTime }
          : item
      )
    );
    
    // Here you would also call the API to update the item
    console.log(`Moving item ${itemId} to ${newDate} at ${newTime}`);
  };

  const handleBulkStatusUpdate = (status: string) => {
    console.log('Updating status for items:', selectedItems, 'to:', status);
    // Implement bulk status update logic
    setSelectedItems([]);
  };

  const handleBulkDelete = () => {
    console.log('Deleting items:', selectedItems);
    // Implement bulk delete logic
    setSelectedItems([]);
  };

  const handleCreateClick = () => {
    // Implement create schedule logic
    console.log('Create new schedule clicked');
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-y-2">
            <div className="flex flex-col items-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground mt-2">
                {isRetrying ? `Retrying... (${retryCount}/3)` : 'Loading production schedules...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      {/* Mobile View */}
      <MobileProductionView 
        schedules={schedules}
        budgets={budgets}
        resources={resources}
        reports={reports}
      />

      {/* Desktop View */}
      <div className="hidden lg:block">
        <Card>
          <CardHeader>
            <ScheduleHeader 
              creating={creating.schedule}
              onCreateClick={handleCreateClick}
            />
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-4">
                <TabsTrigger value="calendar">Calendar View</TabsTrigger>
                <TabsTrigger value="bulk">Bulk Operations</TabsTrigger>
                <TabsTrigger value="list">List View</TabsTrigger>
              </TabsList>

              <TabsContent value="calendar">
                <ScheduleCalendarView
                  schedules={schedules}
                  scheduleItems={scheduleItems}
                  selectedDate={selectedDate}
                  onDateChange={setSelectedDate}
                  onItemMove={handleItemMove}
                />
              </TabsContent>

              <TabsContent value="bulk">
                <BulkOperationsView
                  schedules={schedules}
                  selectedItems={selectedItems}
                  onSelectionChange={setSelectedItems}
                  onBulkStatusUpdate={handleBulkStatusUpdate}
                  onBulkDelete={handleBulkDelete}
                />
              </TabsContent>

              <TabsContent value="list">
                <ScheduleListView schedules={schedules} />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </>
  );
};

export default EnhancedSchedulingTab;
