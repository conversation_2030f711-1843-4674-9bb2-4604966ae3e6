
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Users, Plus, Camera, MapPin, Car, Wrench } from 'lucide-react';
import { useProduction } from '@/hooks/useProduction';
import type { ProductionResource } from '@/lib/api/production';

const ResourceManagementTab: React.FC = () => {
  const { resources, loading, createResource } = useProduction();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    type: 'equipment' as ProductionResource['type'],
    description: '',
    availability_status: 'available' as ProductionResource['availability_status'],
    cost_per_day: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newResource = await createResource({
      ...formData,
      cost_per_day: formData.cost_per_day ? parseFloat(formData.cost_per_day) : undefined
    });
    
    if (newResource) {
      setIsCreateDialogOpen(false);
      setFormData({
        name: '',
        type: 'equipment',
        description: '',
        availability_status: 'available',
        cost_per_day: ''
      });
    }
  };

  const getTypeIcon = (type: ProductionResource['type']) => {
    switch (type) {
      case 'equipment':
        return <Camera className="h-4 w-4" />;
      case 'location':
        return <MapPin className="h-4 w-4" />;
      case 'talent':
      case 'crew':
        return <Users className="h-4 w-4" />;
      case 'vehicle':
        return <Car className="h-4 w-4" />;
      default:
        return <Wrench className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: ProductionResource['availability_status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'booked':
        return 'bg-blue-100 text-blue-800';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800';
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const resourcesByType = resources.reduce((acc, resource) => {
    if (!acc[resource.type]) {
      acc[resource.type] = [];
    }
    acc[resource.type].push(resource);
    return acc;
  }, {} as Record<string, ProductionResource[]>);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Resource Management
            </CardTitle>
            <CardDescription>
              Manage crew, equipment, vehicles, and other production resources
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add Resource
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add New Resource</DialogTitle>
                <DialogDescription>
                  Add a new resource to your production inventory.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="text-sm font-medium">
                    Resource Name
                  </label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="e.g., RED Camera Package, Studio Location"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="type" className="text-sm font-medium">
                    Type
                  </label>
                  <Select value={formData.type} onValueChange={(value: ProductionResource['type']) => setFormData(prev => ({ ...prev, type: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select resource type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="equipment">Equipment</SelectItem>
                      <SelectItem value="location">Location</SelectItem>
                      <SelectItem value="talent">Talent</SelectItem>
                      <SelectItem value="crew">Crew</SelectItem>
                      <SelectItem value="vehicle">Vehicle</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label htmlFor="description" className="text-sm font-medium">
                    Description
                  </label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of the resource..."
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="availability_status" className="text-sm font-medium">
                      Availability
                    </label>
                    <Select 
                      value={formData.availability_status} 
                      onValueChange={(value: ProductionResource['availability_status']) => 
                        setFormData(prev => ({ ...prev, availability_status: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="booked">Booked</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="unavailable">Unavailable</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label htmlFor="cost_per_day" className="text-sm font-medium">
                      Cost per Day (USD)
                    </label>
                    <Input
                      id="cost_per_day"
                      type="number"
                      step="0.01"
                      value={formData.cost_per_day}
                      onChange={(e) => setFormData(prev => ({ ...prev, cost_per_day: e.target.value }))}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Add Resource
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {resources.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No production resources added yet</p>
            <p className="text-sm mt-2">
              Add equipment, crew, locations, and other resources to your inventory
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {Object.entries(resourcesByType).map(([type, typeResources]) => (
              <div key={type}>
                <h3 className="text-lg font-semibold mb-3 capitalize flex items-center gap-2">
                  {getTypeIcon(type as ProductionResource['type'])}
                  {type} ({typeResources.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {typeResources.map((resource) => (
                    <div key={resource.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h4 className="font-semibold">{resource.name}</h4>
                        <Badge className={getStatusColor(resource.availability_status)}>
                          {resource.availability_status}
                        </Badge>
                      </div>
                      {resource.description && (
                        <p className="text-muted-foreground text-sm mb-3">
                          {resource.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-sm">
                        {resource.cost_per_day && (
                          <span className="font-medium text-green-600">
                            ${resource.cost_per_day}/day
                          </span>
                        )}
                        <span className="text-muted-foreground">
                          Added {new Date(resource.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ResourceManagementTab;
