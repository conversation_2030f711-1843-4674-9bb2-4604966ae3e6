
import React, { useState, useMemo } from 'react';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import { DraggableScheduleItem } from './DraggableScheduleItem';
import { DroppableTimeSlot } from './DroppableTimeSlot';
import type { ProductionSchedule, ProductionScheduleItem } from '@/lib/api/production';

interface DragDropScheduleCalendarProps {
  schedules: ProductionSchedule[];
  scheduleItems: ProductionScheduleItem[];
  onItemMove: (itemId: string, newDate: string, newTime: string) => void;
  selectedDate: string;
  onDateChange: (date: string) => void;
}

export const DragDropScheduleCalendar: React.FC<DragDropScheduleCalendarProps> = ({
  schedules,
  scheduleItems,
  onItemMove,
  selectedDate,
  onDateChange
}) => {
  const [activeItem, setActiveItem] = useState<ProductionScheduleItem | null>(null);

  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 6; hour < 22; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
      slots.push(`${hour.toString().padStart(2, '0')}:30`);
    }
    return slots;
  }, []);

  const itemsForDate = useMemo(() => {
    return scheduleItems.filter(item => item.scheduled_date === selectedDate);
  }, [scheduleItems, selectedDate]);

  const handleDragStart = (event: DragStartEvent) => {
    const item = scheduleItems.find(item => item.id === event.active.id);
    setActiveItem(item || null);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const timeSlot = over.id as string;
      if (timeSlot.includes(':')) {
        onItemMove(active.id as string, selectedDate, timeSlot);
      }
    }
    
    setActiveItem(null);
  };

  const getItemsForTimeSlot = (timeSlot: string) => {
    return itemsForDate.filter(item => item.start_time === timeSlot);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Production Calendar
        </h3>
        <input
          type="date"
          value={selectedDate}
          onChange={(e) => onDateChange(e.target.value)}
          className="px-3 py-2 border rounded-md"
        />
      </div>

      <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Available Items */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Unscheduled Items</CardTitle>
            </CardHeader>
            <CardContent>
              <SortableContext items={itemsForDate.filter(item => !item.start_time)} strategy={verticalListSortingStrategy}>
                <div className="space-y-2">
                  {itemsForDate
                    .filter(item => !item.start_time)
                    .map(item => (
                      <DraggableScheduleItem key={item.id} item={item} />
                    ))}
                </div>
              </SortableContext>
            </CardContent>
          </Card>

          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Timeline - {selectedDate}</CardTitle>
            </CardHeader>
            <CardContent className="max-h-96 overflow-y-auto">
              <div className="space-y-1">
                {timeSlots.map(timeSlot => (
                  <DroppableTimeSlot
                    key={timeSlot}
                    timeSlot={timeSlot}
                    items={getItemsForTimeSlot(timeSlot)}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <DragOverlay>
          {activeItem ? (
            <div className="bg-white border rounded-lg p-3 shadow-lg">
              <div className="font-medium">{activeItem.title}</div>
              <div className="text-sm text-muted-foreground flex items-center gap-1">
                <Clock className="h-3 w-3" />
                {activeItem.start_time} - {activeItem.end_time}
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
};
