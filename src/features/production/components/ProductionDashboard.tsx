
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useEnhancedProduction } from '@/hooks/useEnhancedProduction';
import { SmartLoadingState } from '@/components/OptimizedLoadingStates';
import OverviewCards from './OverviewCards';
import BudgetTrackingCard from './BudgetTrackingCard';
import ScheduleStatusCard from './ScheduleStatusCard';
import ResourceStatusCard from './ResourceStatusCard';

const ProductionDashboard: React.FC = () => {
  const { 
    schedules, 
    budgets, 
    resources, 
    reports, 
    loading,
    getOptimizedLoadingState
  } = useEnhancedProduction();
  
  const [selectedBudgetId, setSelectedBudgetId] = useState<string>();

  useEffect(() => {
    if (budgets.length > 0 && !selectedBudgetId) {
      setSelectedBudgetId(budgets[0].id);
    }
  }, [budgets, selectedBudgetId]);

  // Get optimized loading states for different sections
  const overviewLoadingState = getOptimizedLoadingState('overview');
  const budgetLoadingState = getOptimizedLoadingState('budget');
  const scheduleLoadingState = getOptimizedLoadingState('schedule');
  const resourceLoadingState = getOptimizedLoadingState('resource');

  return (
    <div className="space-y-6">
      <SmartLoadingState
        isLoading={loading}
        loadingVariant="dashboard"
        isEmpty={!schedules.length && !budgets.length && !resources.length && !reports.length}
        emptyMessage="No production data available. Start by creating a schedule or budget."
      >
        <OverviewCards 
          schedules={schedules}
          budgets={budgets}
          resources={resources}
          reports={reports}
        />

        {selectedBudgetId && (
          <SmartLoadingState
            isLoading={budgetLoadingState.isLoading}
            error={budgetLoadingState.hasError ? 'Failed to load budget data' : null}
            loadingVariant="card"
            isEmpty={!budgets.length}
            emptyMessage="No budgets available"
          >
            <BudgetTrackingCard 
              selectedBudgetId={selectedBudgetId}
              budgets={budgets}
            />
          </SmartLoadingState>
        )}

        <SmartLoadingState
          isLoading={scheduleLoadingState.isLoading}
          error={scheduleLoadingState.hasError ? 'Failed to load schedule data' : null}
          loadingVariant="table"
          isEmpty={!schedules.length}
          emptyMessage="No schedules available"
        >
          <ScheduleStatusCard schedules={schedules} />
        </SmartLoadingState>

        <SmartLoadingState
          isLoading={resourceLoadingState.isLoading}
          error={resourceLoadingState.hasError ? 'Failed to load resource data' : null}
          loadingVariant="list"
          isEmpty={!resources.length}
          emptyMessage="No resources available"
        >
          <ResourceStatusCard resources={resources} />
        </SmartLoadingState>
      </SmartLoadingState>
    </div>
  );
};

export default ProductionDashboard;
