
import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Calendar, DollarSign, Users, Clock } from 'lucide-react';
import type { ProductionSchedule, ProductionBudget, ProductionResource, ProductionReport } from '@/lib/api/production';

interface OverviewCardsProps {
  schedules: ProductionSchedule[];
  budgets: ProductionBudget[];
  resources: ProductionResource[];
  reports: ProductionReport[];
}

const OverviewCards: React.FC<OverviewCardsProps> = ({
  schedules,
  budgets,
  resources,
  reports
}) => {
  const activeSchedules = schedules.filter(s => s.status === 'active');
  const totalBudget = budgets.reduce((sum, b) => sum + b.total_budget, 0);
  const availableResources = resources.filter(r => r.availability_status === 'available');
  
  const todayReports = reports.filter(r => {
    const today = new Date().toISOString().split('T')[0];
    return r.date === today;
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active Schedules</CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{activeSchedules.length}</div>
          <p className="text-xs text-muted-foreground">
            {schedules.length} total schedules
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            ${totalBudget.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            {budgets.length} active budgets
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Available Resources</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{availableResources.length}</div>
          <p className="text-xs text-muted-foreground">
            {resources.length} total resources
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Today's Reports</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{todayReports.length}</div>
          <p className="text-xs text-muted-foreground">
            {reports.length} total reports
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default OverviewCards;
