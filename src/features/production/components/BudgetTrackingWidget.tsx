
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, DollarSign, AlertTriangle } from 'lucide-react';
import { useBudgetTracking } from '@/hooks/useBudgetTracking';

interface BudgetTrackingWidgetProps {
  budgetId: string;
  showDetails?: boolean;
}

const BudgetTrackingWidget: React.FC<BudgetTrackingWidgetProps> = ({
  budgetId,
  showDetails = false
}) => {
  const { tracking, budget, loading } = useBudgetTracking(budgetId);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!budget) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          {budget.title}
        </CardTitle>
        <CardDescription>
          Budget tracking and variance analysis
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Alerts */}
        {tracking.alerts.length > 0 && (
          <div className="space-y-2">
            {tracking.alerts.slice(0, showDetails ? undefined : 2).map((alert, index) => (
              <Alert key={index} variant={alert.severity === 'error' ? 'destructive' : 'default'}>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{alert.message}</AlertDescription>
              </Alert>
            ))}
          </div>
        )}

        {/* Budget Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Budget Utilization</span>
            <span>{tracking.budgetUtilization.toFixed(1)}%</span>
          </div>
          <Progress 
            value={Math.min(tracking.budgetUtilization, 100)}
            className={tracking.isOverBudget ? 'bg-red-100' : ''}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Spent: ${tracking.totalActual.toLocaleString()}</span>
            <span>Remaining: ${tracking.remainingBudget.toLocaleString()}</span>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 border rounded-lg">
            <div className="text-lg font-bold text-green-600">
              ${tracking.totalEstimated.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">Estimated</div>
          </div>
          <div className="text-center p-3 border rounded-lg">
            <div className="text-lg font-bold text-blue-600">
              ${tracking.totalActual.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">Actual</div>
          </div>
        </div>

        {/* Variance */}
        <div className="flex items-center justify-between p-3 border rounded-lg">
          <div className="flex items-center gap-2">
            {tracking.budgetVariance > 0 ? (
              <TrendingUp className="h-4 w-4 text-red-500" />
            ) : (
              <TrendingDown className="h-4 w-4 text-green-500" />
            )}
            <span className="text-sm font-medium">Variance</span>
          </div>
          <div className="text-right">
            <div className={`font-bold ${tracking.budgetVariance > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {tracking.budgetVariance > 0 ? '+' : ''}${tracking.budgetVariance.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">
              {((tracking.budgetVariance / tracking.totalEstimated) * 100).toFixed(1)}%
            </div>
          </div>
        </div>

        {/* Category Breakdown (if details enabled) */}
        {showDetails && tracking.categoryBreakdown.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Category Breakdown</h4>
            {tracking.categoryBreakdown.map((category) => (
              <div key={category.category} className="border rounded-lg p-3">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">{category.category}</span>
                  <Badge variant={category.variance > 0 ? 'destructive' : 'secondary'}>
                    {category.variance > 0 ? '+' : ''}${category.variance.toLocaleString()}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>Est: ${category.estimated.toLocaleString()}</span>
                  <span>Act: ${category.actual.toLocaleString()}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BudgetTrackingWidget;
