
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock } from 'lucide-react';
import type { ProductionSchedule } from '@/lib/api/production';

interface ScheduleListProps {
  schedules: ProductionSchedule[];
}

const ScheduleList: React.FC<ScheduleListProps> = ({ schedules }) => {
  const getStatusColor = (status: ProductionSchedule['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {schedules.map((schedule) => (
        <div key={schedule.id} className="border rounded-lg p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-semibold">{schedule.title}</h3>
            <Badge className={getStatusColor(schedule.status)}>
              {schedule.status}
            </Badge>
          </div>
          {schedule.description && (
            <p className="text-muted-foreground text-sm mb-3">
              {schedule.description}
            </p>
          )}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {new Date(schedule.start_date).toLocaleDateString()} - {new Date(schedule.end_date).toLocaleDateString()}
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              Created {new Date(schedule.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ScheduleList;
