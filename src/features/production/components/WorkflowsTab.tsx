
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { FileText, Package, Users, MapPin, DollarSign, Calendar } from 'lucide-react';
import { CallSheetManager } from './CallSheetManager';
import { EquipmentManager } from './EquipmentManager';
import { CrewScheduler } from './CrewScheduler';

export const WorkflowsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState('call-sheets');

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Calendar className="h-6 w-6 mr-2" />
        <h1 className="text-3xl font-bold">Production Workflows</h1>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="call-sheets" className="flex items-center">
            <FileText className="h-4 w-4 mr-2" />
            Call Sheets
          </TabsTrigger>
          <TabsTrigger value="equipment" className="flex items-center">
            <Package className="h-4 w-4 mr-2" />
            Equipment
          </TabsTrigger>
          <TabsTrigger value="crew" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            Crew
          </TabsTrigger>
          <TabsTrigger value="locations" className="flex items-center">
            <MapPin className="h-4 w-4 mr-2" />
            Locations
          </TabsTrigger>
          <TabsTrigger value="approvals" className="flex items-center">
            <DollarSign className="h-4 w-4 mr-2" />
            Approvals
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="call-sheets">
          <CallSheetManager />
        </TabsContent>

        <TabsContent value="equipment">
          <EquipmentManager />
        </TabsContent>

        <TabsContent value="crew">
          <CrewScheduler />
        </TabsContent>

        <TabsContent value="locations">
          <Card className="p-6">
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <MapPin className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Location Scouting</h3>
                <p className="text-muted-foreground">Location management coming soon</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="approvals">
          <Card className="p-6">
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <DollarSign className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Budget Approvals</h3>
                <p className="text-muted-foreground">Approval workflows coming soon</p>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card className="p-6">
            <div className="flex items-center justify-center h-32">
              <div className="text-center">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">Workflow Analytics</h3>
                <p className="text-muted-foreground">Analytics dashboard coming soon</p>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
