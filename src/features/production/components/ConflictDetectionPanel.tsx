
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Clock, MapPin, RefreshCw } from 'lucide-react';
import { useScheduleConflictDetection } from '@/hooks/useScheduleConflictDetection';
import type { ProductionScheduleItem } from '@/lib/api/production';

interface ConflictDetectionPanelProps {
  scheduleItem: Partial<ProductionScheduleItem>;
  excludeItemId?: string;
  onConflictsDetected?: (hasConflicts: boolean) => void;
}

const ConflictDetectionPanel: React.FC<ConflictDetectionPanelProps> = ({
  scheduleItem,
  excludeItemId,
  onConflictsDetected
}) => {
  const { checkConflicts, loading } = useScheduleConflictDetection();
  const [conflicts, setConflicts] = useState<Awaited<ReturnType<typeof checkConflicts>> | null>(null);

  const runConflictCheck = async () => {
    if (!scheduleItem.schedule_id || !scheduleItem.scheduled_date) return;
    
    const result = await checkConflicts(scheduleItem, excludeItemId);
    setConflicts(result);
    onConflictsDetected?.(result.hasConflicts);
  };

  useEffect(() => {
    runConflictCheck();
  }, [scheduleItem, excludeItemId]);

  const getConflictIcon = (type: string) => {
    switch (type) {
      case 'time_overlap':
        return <Clock className="h-4 w-4" />;
      case 'location_conflict':
        return <MapPin className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getConflictColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      default:
        return 'secondary';
    }
  };

  if (!scheduleItem.schedule_id || !scheduleItem.scheduled_date) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Conflict Detection
            </CardTitle>
            <CardDescription>
              Checking for scheduling conflicts and resource overlaps
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={runConflictCheck}
            disabled={loading}
            className="gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Check
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        ) : conflicts ? (
          <div className="space-y-4">
            {conflicts.hasConflicts ? (
              <>
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {conflicts.conflicts.length} conflict{conflicts.conflicts.length !== 1 ? 's' : ''} detected
                  </AlertDescription>
                </Alert>
                
                <div className="space-y-3">
                  {conflicts.conflicts.map((conflict, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5">
                          {getConflictIcon(conflict.type)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge variant={getConflictColor(conflict.severity) as any}>
                              {conflict.severity}
                            </Badge>
                            <span className="text-sm font-medium capitalize">
                              {conflict.type.replace('_', ' ')}
                            </span>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {conflict.message}
                          </p>
                          {conflict.conflictingItems && conflict.conflictingItems.length > 0 && (
                            <div className="mt-2 text-xs text-muted-foreground">
                              Conflicts with: {conflict.conflictingItems.map(item => item.title).join(', ')}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  No conflicts detected. This schedule item can be safely added.
                </AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <p className="text-center text-muted-foreground py-4">
            Click "Check" to scan for conflicts
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default ConflictDetectionPanel;
