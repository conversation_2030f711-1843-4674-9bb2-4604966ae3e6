
import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';

interface AuditLogFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  filterEntity: string;
  onEntityFilterChange: (value: string) => void;
  filterAction: string;
  onActionFilterChange: (value: string) => void;
}

export const AuditLogFilters: React.FC<AuditLogFiltersProps> = ({
  searchTerm,
  onSearchChange,
  filterEntity,
  onEntityFilterChange,
  filterAction,
  onActionFilterChange
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search logs..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>
      <Select value={filterEntity} onValueChange={onEntityFilterChange}>
        <SelectTrigger className="w-full sm:w-40">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Entities</SelectItem>
          <SelectItem value="schedule">Schedules</SelectItem>
          <SelectItem value="budget">Budgets</SelectItem>
          <SelectItem value="resource">Resources</SelectItem>
          <SelectItem value="report">Reports</SelectItem>
        </SelectContent>
      </Select>
      <Select value={filterAction} onValueChange={onActionFilterChange}>
        <SelectTrigger className="w-full sm:w-32">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Actions</SelectItem>
          <SelectItem value="create">Created</SelectItem>
          <SelectItem value="update">Updated</SelectItem>
          <SelectItem value="delete">Deleted</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
