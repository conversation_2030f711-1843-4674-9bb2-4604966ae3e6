
import React from 'react';
import { Badge } from '@/components/ui/badge';
import type { ProductionReport } from '@/lib/api/production';

interface ReportListProps {
  reports: ProductionReport[];
}

const ReportList: React.FC<ReportListProps> = ({ reports }) => {
  const getStatusColor = (status: ProductionReport['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'submitted':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: ProductionReport['report_type']) => {
    switch (type) {
      case 'daily':
        return 'bg-blue-100 text-blue-800';
      case 'wrap':
        return 'bg-green-100 text-green-800';
      case 'incident':
        return 'bg-red-100 text-red-800';
      case 'progress':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-4">
      {reports.map((report) => (
        <div key={report.id} className="border rounded-lg p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-semibold">{report.title}</h3>
            <div className="flex gap-2">
              <Badge className={getTypeColor(report.report_type)}>
                {report.report_type}
              </Badge>
              <Badge className={getStatusColor(report.status)}>
                {report.status}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Date: {new Date(report.date).toLocaleDateString()}</span>
            <span>Created: {new Date(report.created_at).toLocaleDateString()}</span>
          </div>
          {report.content && typeof report.content === 'object' && (
            <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
              <pre className="whitespace-pre-wrap">
                {JSON.stringify(report.content, null, 2)}
              </pre>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default ReportList;
