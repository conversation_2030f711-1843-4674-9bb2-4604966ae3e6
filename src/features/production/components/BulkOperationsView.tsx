
import React from 'react';
import { BulkOperationsPanel } from './BulkOperationsPanel';
import type { ProductionSchedule } from '@/lib/api/production';

interface BulkOperationsViewProps {
  schedules: ProductionSchedule[];
  selectedItems: string[];
  onSelectionChange: (items: string[]) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkDelete: () => void;
}

export const BulkOperationsView: React.FC<BulkOperationsViewProps> = ({
  schedules,
  selectedItems,
  onSelectionChange,
  onBulkStatusUpdate,
  onBulkDelete
}) => {
  const scheduleStatusOptions = [
    { value: 'draft', label: 'Draft' },
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ];

  return (
    <BulkOperationsPanel
      items={schedules}
      selectedItems={selectedItems}
      onSelectionChange={onSelectionChange}
      onBulkStatusUpdate={onBulkStatusUpdate}
      onBulkDelete={onBulkDelete}
      statusOptions={scheduleStatusOptions}
      getDisplayName={(schedule) => schedule.title}
    />
  );
};
