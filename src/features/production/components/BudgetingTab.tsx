
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Plus, TrendingUp } from 'lucide-react';
import { useProduction } from '@/hooks/useProduction';
import type { ProductionBudget } from '@/lib/api/production';

const BudgetingTab: React.FC = () => {
  const { budgets, loading, createBudget } = useProduction();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    total_budget: '',
    currency: 'USD',
    status: 'draft' as const
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newBudget = await createBudget({
      ...formData,
      total_budget: parseFloat(formData.total_budget) || 0
    });
    
    if (newBudget) {
      setIsCreateDialogOpen(false);
      setFormData({
        title: '',
        description: '',
        total_budget: '',
        currency: 'USD',
        status: 'draft'
      });
    }
  };

  const getStatusColor = (status: ProductionBudget['status']) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalBudgetAmount = budgets.reduce((sum, budget) => sum + budget.total_budget, 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Budget Management
            </CardTitle>
            <CardDescription>
              Track expenses, manage department budgets, and monitor cost overruns
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                New Budget
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Production Budget</DialogTitle>
                <DialogDescription>
                  Set up a new budget for your production project.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="title" className="text-sm font-medium">
                    Budget Title
                  </label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="e.g., Feature Film - Production Budget"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="description" className="text-sm font-medium">
                    Description
                  </label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Brief description of this budget..."
                    rows={3}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="total_budget" className="text-sm font-medium">
                      Total Budget
                    </label>
                    <Input
                      id="total_budget"
                      type="number"
                      step="0.01"
                      value={formData.total_budget}
                      onChange={(e) => setFormData(prev => ({ ...prev, total_budget: e.target.value }))}
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <div>
                    <label htmlFor="currency" className="text-sm font-medium">
                      Currency
                    </label>
                    <Input
                      id="currency"
                      value={formData.currency}
                      onChange={(e) => setFormData(prev => ({ ...prev, currency: e.target.value }))}
                      placeholder="USD"
                      required
                    />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Create Budget
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {budgets.length > 0 && (
          <div className="mb-6 p-4 bg-muted rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span className="font-semibold">Total Budget Overview</span>
            </div>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(totalBudgetAmount, 'USD')}
            </p>
            <p className="text-sm text-muted-foreground">
              Across {budgets.length} budget{budgets.length !== 1 ? 's' : ''}
            </p>
          </div>
        )}

        {budgets.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <DollarSign className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No production budgets created yet</p>
            <p className="text-sm mt-2">
              Create your first budget to start tracking production costs
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {budgets.map((budget) => (
              <div key={budget.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold">{budget.title}</h3>
                  <Badge className={getStatusColor(budget.status)}>
                    {budget.status}
                  </Badge>
                </div>
                {budget.description && (
                  <p className="text-muted-foreground text-sm mb-3">
                    {budget.description}
                  </p>
                )}
                <div className="flex items-center justify-between">
                  <div className="text-lg font-bold text-green-600">
                    {formatCurrency(budget.total_budget, budget.currency)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Created {new Date(budget.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BudgetingTab;
