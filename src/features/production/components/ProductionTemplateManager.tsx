
import React from 'react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useTemplateOperations } from '../hooks/useTemplateOperations';
import { TemplateCreationDialog } from './TemplateCreationDialog';
import { TemplateList } from './TemplateList';

export const ProductionTemplateManager: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { templates, createTemplate, useTemplate } = useTemplateOperations();

  if (!currentOrganization) return null;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Production Templates</h3>
        <TemplateCreationDialog onCreateTemplate={createTemplate} />
      </div>

      <TemplateList templates={templates} onUseTemplate={useTemplate} />
    </div>
  );
};
