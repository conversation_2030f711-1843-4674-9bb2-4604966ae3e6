
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Package, CheckCircle, XCircle, Settings } from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';
import { equipmentApi, type Equipment } from '@/lib/api/production/equipmentApi';
import { toast } from '@/hooks/use-toast';

export const EquipmentManager: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const [equipment, setEquipment] = useState<Equipment[]>([]);
  const [loading, setLoading] = useState(false);
  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    serial_number: '',
    condition: 'good',
    daily_rate: '',
    location: '',
    specifications: {},
    maintenance_notes: ''
  });

  useEffect(() => {
    if (currentOrganization) {
      fetchEquipment();
    }
  }, [currentOrganization]);

  const fetchEquipment = async () => {
    if (!currentOrganization) return;
    
    setLoading(true);
    try {
      const result = await equipmentApi.getEquipment(currentOrganization.id);
      if (result.success && result.data) {
        setEquipment(result.data);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch equipment",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentOrganization) return;

    try {
      const result = await equipmentApi.createEquipment({
        ...formData,
        org_id: currentOrganization.id,
        status: 'available',
        daily_rate: formData.daily_rate ? parseFloat(formData.daily_rate) : undefined
      });

      if (result.success && result.data) {
        setEquipment(prev => [result.data!, ...prev]);
        setShowForm(false);
        setFormData({
          name: '',
          category: '',
          serial_number: '',
          condition: 'good',
          daily_rate: '',
          location: '',
          specifications: {},
          maintenance_notes: ''
        });
        toast({
          title: "Success",
          description: "Equipment added successfully"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add equipment",
        variant: "destructive"
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'checked_out': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-500" />;
      case 'retired': return <XCircle className="h-4 w-4 text-gray-500" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'checked_out': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'retired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const availableEquipment = equipment.filter(item => item.status === 'available');
  const checkedOutEquipment = equipment.filter(item => item.status === 'checked_out');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Equipment Management</h2>
        <Button onClick={() => setShowForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Equipment
        </Button>
      </div>

      {showForm && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Equipment</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Equipment Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="camera">Camera</SelectItem>
                      <SelectItem value="lighting">Lighting</SelectItem>
                      <SelectItem value="audio">Audio</SelectItem>
                      <SelectItem value="grip">Grip</SelectItem>
                      <SelectItem value="electrical">Electrical</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="serial_number">Serial Number</Label>
                  <Input
                    id="serial_number"
                    value={formData.serial_number}
                    onChange={(e) => setFormData(prev => ({ ...prev, serial_number: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="daily_rate">Daily Rate ($)</Label>
                  <Input
                    id="daily_rate"
                    type="number"
                    step="0.01"
                    value={formData.daily_rate}
                    onChange={(e) => setFormData(prev => ({ ...prev, daily_rate: e.target.value }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="maintenance_notes">Maintenance Notes</Label>
                <Textarea
                  id="maintenance_notes"
                  value={formData.maintenance_notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, maintenance_notes: e.target.value }))}
                  rows={3}
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Equipment</Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Equipment ({equipment.length})</TabsTrigger>
          <TabsTrigger value="available">Available ({availableEquipment.length})</TabsTrigger>
          <TabsTrigger value="checked-out">Checked Out ({checkedOutEquipment.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <EquipmentGrid equipment={equipment} />
        </TabsContent>

        <TabsContent value="available">
          <EquipmentGrid equipment={availableEquipment} />
        </TabsContent>

        <TabsContent value="checked-out">
          <EquipmentGrid equipment={checkedOutEquipment} />
        </TabsContent>
      </Tabs>

      {loading && (
        <div className="flex justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
};

const EquipmentGrid: React.FC<{ equipment: Equipment[] }> = ({ equipment }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'checked_out': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-500" />;
      case 'retired': return <XCircle className="h-4 w-4 text-gray-500" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'checked_out': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'retired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {equipment.map((item) => (
        <Card key={item.id}>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex justify-between items-start">
                <h3 className="font-semibold">{item.name}</h3>
                <Badge className={getStatusColor(item.status)}>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(item.status)}
                    <span>{item.status}</span>
                  </div>
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">{item.category}</p>
              {item.serial_number && (
                <p className="text-xs text-muted-foreground">SN: {item.serial_number}</p>
              )}
              {item.daily_rate && (
                <p className="text-sm font-medium">${item.daily_rate}/day</p>
              )}
              {item.location && (
                <p className="text-xs text-muted-foreground">📍 {item.location}</p>
              )}
              <div className="flex space-x-2 pt-2">
                <Button variant="outline" size="sm" className="flex-1">
                  Edit
                </Button>
                {item.status === 'available' && (
                  <Button size="sm" className="flex-1">
                    Check Out
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
