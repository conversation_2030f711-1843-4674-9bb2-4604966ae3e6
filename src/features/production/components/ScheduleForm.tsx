
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import type { CreateScheduleInput } from '@/features/production/validation/schemas';

type ScheduleFormData = {
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'active' | 'completed' | 'cancelled';
};

type ScheduleFormErrors = {
  title?: string;
  description?: string;
  start_date?: string;
  end_date?: string;
  status?: string;
};

interface ScheduleFormProps {
  formData: ScheduleFormData;
  setFormData: React.Dispatch<React.SetStateAction<ScheduleFormData>>;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  errors: ScheduleFormErrors;
  isCreating: boolean;
}

const ScheduleForm: React.FC<ScheduleFormProps> = ({
  formData,
  setFormData,
  onSubmit,
  onCancel,
  errors,
  isCreating
}) => {
  return (
    <form onSubmit={onSubmit} className="space-y-4">
      <div>
        <label htmlFor="title" className="text-sm font-medium">
          Title *
        </label>
        <Input
          id="title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="e.g., Main Unit - Week 1"
          className={errors.title ? 'border-destructive' : ''}
          required
        />
        {errors.title && (
          <p className="text-sm text-destructive mt-1">{String(errors.title)}</p>
        )}
      </div>
      <div>
        <label htmlFor="description" className="text-sm font-medium">
          Description
        </label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Brief description of this schedule..."
          rows={3}
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="start_date" className="text-sm font-medium">
            Start Date *
          </label>
          <Input
            id="start_date"
            type="date"
            value={formData.start_date}
            onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
            className={errors.start_date ? 'border-destructive' : ''}
            required
          />
          {errors.start_date && (
            <p className="text-sm text-destructive mt-1">{String(errors.start_date)}</p>
          )}
        </div>
        <div>
          <label htmlFor="end_date" className="text-sm font-medium">
            End Date *
          </label>
          <Input
            id="end_date"
            type="date"
            value={formData.end_date}
            onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
            className={errors.end_date ? 'border-destructive' : ''}
            required
          />
          {errors.end_date && (
            <p className="text-sm text-destructive mt-1">{String(errors.end_date)}</p>
          )}
        </div>
      </div>
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isCreating}>
          {isCreating ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Creating...
            </>
          ) : (
            'Create Schedule'
          )}
        </Button>
      </div>
    </form>
  );
};

export default ScheduleForm;
