
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Calendar, Users } from 'lucide-react';
import type { ProductionSchedule } from '@/lib/api/production';

interface ScheduleListViewProps {
  schedules: ProductionSchedule[];
}

export const ScheduleListView: React.FC<ScheduleListViewProps> = ({
  schedules
}) => {
  const getStatusColor = (status: ProductionSchedule['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (schedules.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
        <p>No production schedules created yet</p>
        <p className="text-sm mt-2">
          Create your first schedule to start planning your production
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {schedules.map((schedule) => (
        <div key={schedule.id} className="border rounded-lg p-4">
          <div className="flex items-start justify-between mb-2">
            <h3 className="font-semibold">{schedule.title}</h3>
            <div className="flex items-center gap-2">
              <Badge className={getStatusColor(schedule.status)}>
                {schedule.status}
              </Badge>
            </div>
          </div>
          {schedule.description && (
            <p className="text-muted-foreground text-sm mb-3">
              {schedule.description}
            </p>
          )}
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {new Date(schedule.start_date).toLocaleDateString()} - {new Date(schedule.end_date).toLocaleDateString()}
            </div>
            <div className="flex items-center gap-1">
              <Users className="h-4 w-4" />
              Created {new Date(schedule.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
