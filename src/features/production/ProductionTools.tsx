
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card } from '@/components/ui/card';
import { Clapperboard, Calendar, DollarSign, Users, FileText, Settings, Workflow } from 'lucide-react';
import { useProductionAccess } from '@/hooks/useProductionAccess';
import ProductionDashboard from './components/ProductionDashboard';
import EnhancedSchedulingTab from './components/EnhancedSchedulingTab';
import BudgetingTab from './components/BudgetingTab';
import ResourceManagementTab from './components/ResourceManagementTab';
import ReportsTab from './components/ReportsTab';
import { ProductionSettings } from './components/ProductionSettings';
import { WorkflowsTab } from './components/WorkflowsTab';
import AccessRestricted from './components/AccessRestricted';
import ProductionErrorBoundary from './components/ProductionErrorBoundary';

export const ProductionTools: React.FC = () => {
  const { canAccess, tier, loading, subscribed } = useProductionAccess();
  const [activeTab, setActiveTab] = useState('overview');

  if (loading) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading production tools...</span>
        </div>
      </Card>
    );
  }

  if (!canAccess) {
    return <AccessRestricted tier={tier} subscribed={subscribed} />;
  }

  return (
    <ProductionErrorBoundary>
      <div className="space-y-6">
        <div className="flex items-center">
          <Clapperboard className="h-6 w-6 mr-2" />
          <h1 className="text-3xl font-bold gold-gradient">Production Tools</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="overview" className="flex items-center">
              <Clapperboard className="h-4 w-4 mr-2" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="scheduling" className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              Scheduling
            </TabsTrigger>
            <TabsTrigger value="budgeting" className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Budgeting
            </TabsTrigger>
            <TabsTrigger value="resources" className="flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Resources
            </TabsTrigger>
            <TabsTrigger value="workflows" className="flex items-center">
              <Workflow className="h-4 w-4 mr-2" />
              Workflows
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <ProductionDashboard />
          </TabsContent>

          <TabsContent value="scheduling">
            <EnhancedSchedulingTab />
          </TabsContent>

          <TabsContent value="budgeting">
            <BudgetingTab />
          </TabsContent>

          <TabsContent value="resources">
            <ResourceManagementTab />
          </TabsContent>

          <TabsContent value="workflows">
            <WorkflowsTab />
          </TabsContent>

          <TabsContent value="reports">
            <ReportsTab />
          </TabsContent>

          <TabsContent value="settings">
            <ProductionSettings />
          </TabsContent>
        </Tabs>
      </div>
    </ProductionErrorBoundary>
  );
};

export default ProductionTools;
