
import React, { useState } from 'react';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { useScreenplays } from '@/hooks/useScreenplays';
import { useMarketplaceAccess } from '@/hooks/useMarketplaceAccess';
import { useNotifications } from '@/hooks/useNotifications';
import type { Screenplay } from '@/lib/api/screenplays';

// Import refactored components
import MarketplaceHeader from './components/MarketplaceHeader';
import AccessRestricted from './components/AccessRestricted';
import MarketplaceNavigation from './components/MarketplaceNavigation';
import BrowseTab from './components/BrowseTab';
import AnalyticsTab from './components/AnalyticsTab';
import ContractsTab from './components/ContractsTab';
import MyScreenplays from './components/MyScreenplays';
import MyPurchases from './components/MyPurchases';
import MyOffers from './components/MyOffers';
import ScreenplaySubmissionForm from './components/ScreenplaySubmissionForm';
import NotificationCenter from './components/NotificationCenter';
import EarningsDashboard from './components/EarningsDashboard';
import FavoritesPage from './components/FavoritesPage';
import CollectionsManager from './components/CollectionsManager';

const ScreenplayMarketplace: React.FC = () => {
  const [activeTab, setActiveTab] = useState('browse');
  const [selectedScreenplay, setSelectedScreenplay] = useState<Screenplay | null>(null);
  const { publishedScreenplays, loadingPublished } = useScreenplays();
  const { canView, canSubmit, canBuy, tier, loading, subscribed } = useMarketplaceAccess();
  const { unreadCount } = useNotifications();

  const handleViewDetails = (screenplay: Screenplay) => {
    setSelectedScreenplay(screenplay);
  };

  const handleBackToBrowse = () => {
    setSelectedScreenplay(null);
  };

  // Show loading while checking access
  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Show access denied for users without subscription access
  if (!canView) {
    return <AccessRestricted tier={tier} subscribed={subscribed} />;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <MarketplaceHeader canBuy={canBuy} />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <MarketplaceNavigation 
          canSubmit={canSubmit}
          canBuy={canBuy}
          unreadCount={unreadCount}
        />

        <TabsContent value="browse" className="space-y-6">
          <BrowseTab
            selectedScreenplay={selectedScreenplay}
            publishedScreenplays={publishedScreenplays}
            loadingPublished={loadingPublished}
            canBuy={canBuy}
            onViewDetails={handleViewDetails}
            onBackToBrowse={handleBackToBrowse}
          />
        </TabsContent>

        <TabsContent value="favorites">
          <FavoritesPage onViewDetails={handleViewDetails} />
        </TabsContent>

        <TabsContent value="collections">
          <CollectionsManager />
        </TabsContent>

        {canSubmit && (
          <>
            <TabsContent value="my-screenplays">
              <MyScreenplays />
            </TabsContent>
            <TabsContent value="submit">
              <ScreenplaySubmissionForm />
            </TabsContent>
            <TabsContent value="earnings">
              <EarningsDashboard />
            </TabsContent>
          </>
        )}

        {canBuy && (
          <>
            <TabsContent value="purchases">
              <MyPurchases />
            </TabsContent>
            <TabsContent value="offers">
              <MyOffers />
            </TabsContent>
          </>
        )}

        <TabsContent value="notifications">
          <NotificationCenter />
        </TabsContent>

        <TabsContent value="analytics">
          <AnalyticsTab />
        </TabsContent>

        <TabsContent value="contracts">
          <ContractsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ScreenplayMarketplace;
