
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, Download, Star, FileText } from 'lucide-react';
import { useScreenplayPurchases } from '@/hooks/useScreenplays';

const MyPurchases: React.FC = () => {
  const { purchases, loading } = useScreenplayPurchases();

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>My Purchases</CardTitle>
          <CardDescription>
            Access your purchased screenplays and leave reviews
          </CardDescription>
        </CardHeader>
        <CardContent>
          {purchases.length > 0 ? (
            <div className="space-y-4">
              {purchases.map((purchase) => (
                <div
                  key={purchase.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{purchase.screenplay.title}</h3>
                        <Badge variant="secondary">{purchase.screenplay.genre}</Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                        {purchase.screenplay.page_count && (
                          <span>{purchase.screenplay.page_count} pages</span>
                        )}
                        <span>Purchased for ${purchase.purchase_price}</span>
                      </div>
                      
                      {purchase.screenplay.logline && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {purchase.screenplay.logline}
                        </p>
                      )}
                      
                      <div className="text-xs text-muted-foreground">
                        Purchased: {new Date(purchase.purchased_at).toLocaleDateString()}
                      </div>
                    </div>

                    <div className="flex gap-2 ml-4">
                      <Button size="sm">
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                      <Button size="sm" variant="outline">
                        <FileText className="h-4 w-4 mr-1" />
                        Read
                      </Button>
                      <Button size="sm" variant="outline">
                        <Star className="h-4 w-4 mr-1" />
                        Review
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No purchases yet</h3>
              <p className="text-muted-foreground mb-4">
                You haven't purchased any screenplays yet. Browse the marketplace to find great scripts!
              </p>
              <Button>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Browse Marketplace
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MyPurchases;
