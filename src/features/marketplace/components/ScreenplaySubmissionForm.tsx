import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Upload, FileText } from 'lucide-react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { screenplaysApi, type Screenplay } from '@/lib/api/screenplays';
import { useToast } from '@/hooks/use-toast';

const genres = [
  'Action', 'Adventure', 'Comedy', 'Crime', 'Drama', 'Fantasy', 
  'Horror', 'Mystery', 'Romance', 'Sci-Fi', 'Thriller', 'Western'
];

const formSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  logline: z.string().max(500, 'Logline too long').optional(),
  synopsis: z.string().max(2000, 'Synopsis too long').optional(),
  genre: z.string().min(1, 'Genre is required'),
  pageCount: z.number().min(1, 'Page count must be at least 1').max(500, 'Page count too high'),
  price: z.number().min(0, 'Price must be non-negative').max(10000, 'Price too high'),
  scriptContent: z.string().min(100, 'Script content must be at least 100 characters'),
});

type FormData = z.infer<typeof formSchema>;

const ScreenplaySubmissionForm: React.FC = () => {
  const { toast } = useToast();
  const { execute: executeSubmit, loading } = useAsyncOperation<Screenplay>({
    errorMessage: 'Failed to submit screenplay'
  });

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: '',
      logline: '',
      synopsis: '',
      genre: '',
      pageCount: 0,
      price: 0,
      scriptContent: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    const { success } = await executeSubmit(async () => {
      const result = await screenplaysApi.createScreenplay({
        title: data.title,
        logline: data.logline || null,
        synopsis: data.synopsis || null,
        genre: data.genre,
        page_count: data.pageCount,
        price: data.price,
        script_content: data.scriptContent,
        writer_id: '', // Will be set by RLS
        status: 'pending'
      });

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to submit screenplay');
      }

      return result.data;
    });

    if (success) {
      toast({
        title: 'Screenplay Submitted',
        description: 'Your screenplay has been submitted for review. You\'ll be notified once it\'s been reviewed.',
      });
      form.reset();
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-700 rounded-lg flex items-center justify-center">
              <Upload className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle>Submit Your Screenplay</CardTitle>
              <CardDescription>
                Submit your screenplay for review and potential publication in the marketplace
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Title</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Enter screenplay title" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="genre"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Genre</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a genre" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {genres.map((genre) => (
                          <SelectItem key={genre} value={genre}>
                            {genre}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="pageCount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Page Count</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          placeholder="120"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price ($)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          placeholder="29.99"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="logline"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Logline</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="A compelling one-sentence summary of your screenplay"
                        className="resize-none"
                        rows={2}
                      />
                    </FormControl>
                    <FormDescription>
                      A brief, compelling summary that captures the essence of your story
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="synopsis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Synopsis</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="A detailed summary of your screenplay's plot, characters, and themes"
                        className="resize-none"
                        rows={4}
                      />
                    </FormControl>
                    <FormDescription>
                      A more detailed summary of your screenplay's story and themes
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="scriptContent"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Script Content</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Paste your complete screenplay content here..."
                        className="resize-none font-mono text-sm"
                        rows={8}
                      />
                    </FormControl>
                    <FormDescription>
                      Your complete screenplay in standard format. This will be reviewed before publication.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" disabled={loading} className="w-full">
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Submitting...
                  </>
                ) : (
                  <>
                    <FileText className="h-4 w-4 mr-2" />
                    Submit for Review
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ScreenplaySubmissionForm;
