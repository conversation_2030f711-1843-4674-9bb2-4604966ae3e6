
import React from 'react';
import { BookOpen } from 'lucide-react';

interface MarketplaceHeaderProps {
  canBuy: boolean;
}

const MarketplaceHeader: React.FC<MarketplaceHeaderProps> = ({ canBuy }) => {
  return (
    <div className="flex items-center space-x-3 mb-8">
      <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-700 rounded-lg flex items-center justify-center">
        <BookOpen className="h-6 w-6 text-white" />
      </div>
      <div>
        <h1 className="text-3xl font-playfair font-bold">Screenplay Marketplace</h1>
        <p className="text-muted-foreground">
          {canBuy 
            ? 'Discover and purchase quality screenplays' 
            : 'Submit your screenplays for approval'
          }
        </p>
      </div>
    </div>
  );
};

export default MarketplaceHeader;
