
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { CreditCard, Loader2 } from 'lucide-react';
import { paymentsApi } from '@/lib/api/payments';
import { useToast } from '@/hooks/use-toast';

interface PaymentButtonProps {
  screenplayId?: string;
  offerId?: string;
  amount: number;
  type: 'purchase' | 'offer_payment';
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const PaymentButton: React.FC<PaymentButtonProps> = ({
  screenplayId,
  offerId,
  amount,
  type,
  children,
  className,
  disabled
}) => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const handlePayment = async () => {
    setLoading(true);
    
    try {
      let result;
      
      if (type === 'purchase' && screenplayId) {
        result = await paymentsApi.purchaseScreenplay(screenplayId, amount);
      } else if (type === 'offer_payment' && offerId) {
        result = await paymentsApi.processOfferPayment(offerId, amount);
      } else {
        throw new Error('Invalid payment configuration');
      }

      if (result.success && result.data) {
        // Redirect to Stripe Checkout
        window.open(result.data.sessionUrl, '_blank');
      } else {
        throw new Error(result.error || 'Payment failed');
      }
    } catch (error) {
      toast({
        title: 'Payment Error',
        description: error instanceof Error ? error.message : 'Failed to process payment',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePayment}
      disabled={disabled || loading}
      className={className}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <CreditCard className="h-4 w-4 mr-2" />
      )}
      {children || `Pay $${amount}`}
    </Button>
  );
};

export default PaymentButton;
