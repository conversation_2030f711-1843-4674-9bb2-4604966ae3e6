
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  ExternalLink,
  RefreshCw,
  CreditCard,
  Building2
} from 'lucide-react';
import { useStripeConnect } from '@/hooks/useStripeConnect';

const StripeConnectStatus: React.FC = () => {
  const {
    sellerAccount,
    needsOnboarding,
    accountRequirements,
    isAccountReady,
    accountStatusText,
    loadingAccount,
    creatingAccount,
    createSellerAccount,
    continueOnboarding,
    refreshAccountData
  } = useStripeConnect();

  const getStatusIcon = () => {
    if (!sellerAccount) return <Clock className="w-5 h-5 text-yellow-500" />;
    
    switch (sellerAccount.account_status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'restricted':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'rejected':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />;
    }
  };

  const getStatusBadge = () => {
    if (!sellerAccount) return <Badge variant="secondary">Not Started</Badge>;
    
    switch (sellerAccount.account_status) {
      case 'active':
        return <Badge variant="default" className="bg-green-500">Active</Badge>;
      case 'restricted':
        return <Badge variant="destructive">Restricted</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  if (loadingAccount) {
    return (
      <Card className="p-6">
        <div className="flex items-center gap-3">
          <RefreshCw className="w-5 h-5 animate-spin" />
          <span>Loading account status...</span>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div>
              <h3 className="text-lg font-semibold">Stripe Connect Account</h3>
              <p className="text-sm text-muted-foreground">{accountStatusText}</p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {getStatusBadge()}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshAccountData}
              disabled={loadingAccount}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {!sellerAccount && (
          <div className="space-y-4">
            <Alert>
              <CreditCard className="h-4 w-4" />
              <AlertDescription>
                To sell screenplays on the marketplace, you need to set up a Stripe Connect account 
                to receive payments. This is a secure way to handle transactions and comply with financial regulations.
              </AlertDescription>
            </Alert>
            <Button 
              onClick={createSellerAccount} 
              disabled={creatingAccount}
              className="w-full"
            >
              {creatingAccount ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Creating Account...
                </>
              ) : (
                <>
                  <Building2 className="w-4 h-4 mr-2" />
                  Set Up Seller Account
                </>
              )}
            </Button>
          </div>
        )}

        {sellerAccount && needsOnboarding && (
          <div className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your seller account needs additional setup to start receiving payments. 
                Complete the onboarding process with Stripe to activate your account.
              </AlertDescription>
            </Alert>
            <Button 
              onClick={continueOnboarding} 
              className="w-full"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Continue Setup with Stripe
            </Button>
          </div>
        )}

        {isAccountReady && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Your seller account is fully set up and ready to receive payments! 
              You can now publish screenplays and receive earnings directly to your bank account.
            </AlertDescription>
          </Alert>
        )}
      </Card>

      {sellerAccount && (
        <Card className="p-6">
          <h4 className="text-md font-semibold mb-4">Account Details</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Onboarding:</span>
              <div className="flex items-center gap-2 mt-1">
                {sellerAccount.onboarding_completed ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <Clock className="w-4 h-4 text-yellow-500" />
                )}
                <span>{sellerAccount.onboarding_completed ? 'Complete' : 'Pending'}</span>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Payments:</span>
              <div className="flex items-center gap-2 mt-1">
                {sellerAccount.charges_enabled ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <Clock className="w-4 h-4 text-yellow-500" />
                )}
                <span>{sellerAccount.charges_enabled ? 'Enabled' : 'Disabled'}</span>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Payouts:</span>
              <div className="flex items-center gap-2 mt-1">
                {sellerAccount.payouts_enabled ? (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                ) : (
                  <Clock className="w-4 h-4 text-yellow-500" />
                )}
                <span>{sellerAccount.payouts_enabled ? 'Enabled' : 'Disabled'}</span>
              </div>
            </div>
            <div>
              <span className="text-muted-foreground">Country:</span>
              <div className="mt-1">
                <span>{sellerAccount.country || 'Not set'}</span>
              </div>
            </div>
          </div>

          {accountRequirements?.missingRequirements && accountRequirements.missingRequirements.length > 0 && (
            <div className="mt-4 p-4 bg-orange-50 rounded-lg border border-orange-200">
              <h5 className="text-sm font-medium text-orange-800 mb-2">Action Required</h5>
              <ul className="text-sm text-orange-700 space-y-1">
                {accountRequirements.missingRequirements.map((requirement, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <AlertTriangle className="w-3 h-3" />
                    {requirement.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </li>
                ))}
              </ul>
              <Button 
                onClick={continueOnboarding} 
                size="sm" 
                className="mt-3"
              >
                <ExternalLink className="w-3 h-3 mr-2" />
                Complete Requirements
              </Button>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default StripeConnectStatus;
