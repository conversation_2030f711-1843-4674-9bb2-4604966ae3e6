
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON>Heart, Check, X, Clock } from 'lucide-react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { offersApi, type ScreenplayOffer, type ScreenplayOfferWithScreenplay } from '@/lib/api/offers';
import { useToast } from '@/hooks/use-toast';
import PaymentButton from './PaymentButton';

const MyOffers: React.FC = () => {
  const [offers, setOffers] = useState<ScreenplayOfferWithScreenplay[]>([]);
  const { toast } = useToast();
  
  const { execute: executeGetOffers, loading } = useAsyncOperation<ScreenplayOfferWithScreenplay[]>({
    errorMessage: 'Failed to fetch offers'
  });

  const { execute: executeRespond } = useAsyncOperation<ScreenplayOffer>({
    errorMessage: 'Failed to respond to offer'
  });

  const fetchOffers = async () => {
    const { data, success } = await executeGetOffers(async () => {
      const result = await offersApi.getMyOffers();
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to fetch offers');
    });

    if (success && data) {
      setOffers(data);
    }
  };

  const handleRespond = async (offerId: string, status: 'accepted' | 'rejected') => {
    const { success } = await executeRespond(async () => {
      const result = await offersApi.respondToOffer(offerId, status);
      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to respond to offer');
      }
      return result.data;
    });

    if (success) {
      toast({
        title: `Offer ${status}`,
        description: `You have ${status} the offer.`,
      });
      fetchOffers(); // Refresh the list
    }
  };

  useEffect(() => {
    fetchOffers();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accepted':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <X className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'default' as const;
      case 'rejected':
        return 'destructive' as const;
      default:
        return 'secondary' as const;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-700 rounded-lg flex items-center justify-center">
            <HandHeart className="h-5 w-5 text-white" />
          </div>
          <div>
            <CardTitle>My Offers</CardTitle>
            <CardDescription>
              Offers you've made and received for screenplays
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {offers.length > 0 ? (
          <div className="space-y-4">
            {offers.map((offer) => (
              <div key={offer.id} className="border rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div>
                    <h3 className="font-semibold">{offer.screenplay.title}</h3>
                    <p className="text-sm text-muted-foreground">
                      Offer: ${offer.offer_amount}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(offer.status)}
                    <Badge variant={getStatusVariant(offer.status)}>
                      {offer.status}
                    </Badge>
                  </div>
                </div>

                {offer.message && (
                  <p className="text-sm mb-3 p-3 bg-muted rounded">
                    "{offer.message}"
                  </p>
                )}

                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>
                    Created: {new Date(offer.created_at).toLocaleDateString()}
                  </span>
                  {offer.responded_at && (
                    <span>
                      Responded: {new Date(offer.responded_at).toLocaleDateString()}
                    </span>
                  )}
                </div>

                {offer.status === 'pending' && (
                  <div className="flex gap-2 mt-3">
                    <Button
                      size="sm"
                      onClick={() => handleRespond(offer.id, 'accepted')}
                    >
                      Accept
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRespond(offer.id, 'rejected')}
                    >
                      Decline
                    </Button>
                  </div>
                )}

                {offer.status === 'accepted' && (
                  <div className="mt-3">
                    <PaymentButton
                      offerId={offer.id}
                      amount={Number(offer.offer_amount)}
                      type="offer_payment"
                      className="w-full sm:w-auto"
                    >
                      Pay ${offer.offer_amount}
                    </PaymentButton>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No offers yet. Start browsing screenplays to make offers.
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MyOffers;
