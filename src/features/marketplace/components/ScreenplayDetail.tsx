
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Star, ShoppingCart, FileText, User, Calendar, DollarSign } from 'lucide-react';
import { type Screenplay } from '@/lib/api/screenplays';
import { useScreenplayReviews } from '@/hooks/useScreenplays';

interface ScreenplayDetailProps {
  screenplay: Screenplay;
  onPurchase?: (screenplay: Screenplay) => void;
}

const ScreenplayDetail: React.FC<ScreenplayDetailProps> = ({ 
  screenplay, 
  onPurchase 
}) => {
  const { reviews, loading: loadingReviews } = useScreenplayReviews(screenplay.id);

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  const handlePurchase = () => {
    if (onPurchase) {
      onPurchase(screenplay);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Main screenplay info */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-2xl mb-2">{screenplay.title}</CardTitle>
              <div className="flex items-center gap-3 mb-4">
                <Badge variant="secondary">{screenplay.genre}</Badge>
                {screenplay.page_count && (
                  <span className="text-sm text-muted-foreground">
                    {screenplay.page_count} pages
                  </span>
                )}
                <div className="flex items-center gap-1">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star 
                      key={star} 
                      className={`h-4 w-4 ${
                        star <= averageRating 
                          ? 'fill-yellow-400 text-yellow-400' 
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="text-sm text-muted-foreground ml-1">
                    ({reviews.length} {reviews.length === 1 ? 'review' : 'reviews'})
                  </span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-primary mb-4">
                ${screenplay.price}
              </div>
              <Button size="lg" onClick={handlePurchase}>
                <ShoppingCart className="h-5 w-5 mr-2" />
                Purchase
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {screenplay.logline && (
            <div>
              <h3 className="font-semibold mb-2">Logline</h3>
              <p className="text-muted-foreground">{screenplay.logline}</p>
            </div>
          )}

          {screenplay.synopsis && (
            <div>
              <h3 className="font-semibold mb-2">Synopsis</h3>
              <p className="leading-relaxed">{screenplay.synopsis}</p>
            </div>
          )}

          <Separator />

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span>Writer ID: {screenplay.writer_id.slice(0, 8)}...</span>
            </div>
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>{new Date(screenplay.created_at).toLocaleDateString()}</span>
            </div>
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground" />
              <span>{screenplay.page_count || 'N/A'} pages</span>
            </div>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span>${screenplay.price}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews section */}
      <Card>
        <CardHeader>
          <CardTitle>Reviews</CardTitle>
          <CardDescription>
            See what other buyers think about this screenplay
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loadingReviews ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : reviews.length > 0 ? (
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="border-b pb-4 last:border-b-0">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="flex items-center gap-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          className={`h-4 w-4 ${
                            star <= review.rating 
                              ? 'fill-yellow-400 text-yellow-400' 
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {new Date(review.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  {review.review_text && (
                    <p className="text-sm">{review.review_text}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No reviews yet. Be the first to review this screenplay!
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ScreenplayDetail;
