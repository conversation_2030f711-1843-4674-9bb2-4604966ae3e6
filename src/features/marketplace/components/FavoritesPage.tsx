
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Heart, Trash2 } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import ScreenplayCard from './ScreenplayCard';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import type { Screenplay } from '@/lib/api/screenplays';

interface FavoritesPageProps {
  onViewDetails: (screenplay: any) => void;
}

const FavoritesPage: React.FC<FavoritesPageProps> = ({ onViewDetails }) => {
  const { favorites, loading, toggleFavorite } = useFavorites();

  const handleRemoveFavorite = async (screenplayId: string, title: string) => {
    if (window.confirm(`Remove "${title}" from favorites?`)) {
      const success = await toggleFavorite(screenplayId);
      if (success) {
        toast.success('Removed from favorites');
      }
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Heart className="h-6 w-6 text-red-500" />
          My Favorites
        </h2>
        <p className="text-muted-foreground">Screenplays you've marked as favorites</p>
      </div>

      {favorites.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Heart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No favorites yet</h3>
            <p className="text-muted-foreground">
              Browse screenplays and click the heart icon to add them to your favorites
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {favorites.map((favorite) => {
            if (!favorite.screenplay) return null;
            
            // Create a properly typed screenplay object for the ScreenplayCard
            const screenplay: Screenplay = {
              id: favorite.screenplay.id,
              title: favorite.screenplay.title,
              genre: favorite.screenplay.genre,
              price: favorite.screenplay.price,
              logline: favorite.screenplay.logline || '',
              writer_id: favorite.screenplay.writer_id,
              script_content: favorite.screenplay.script_content || '',
              synopsis: favorite.screenplay.synopsis || '',
              profiles: {
                full_name: favorite.screenplay.profiles?.full_name || '',
                username: favorite.screenplay.profiles?.username || ''
              },
              // Add required fields with default values
              cover_image_url: favorite.screenplay.cover_image_url || '',
              created_at: favorite.created_at,
              org_id: '',
              page_count: favorite.screenplay.page_count || 0,
              rejection_reason: '',
              reviewed_at: '',
              reviewed_by: '',
              status: (favorite.screenplay.status as 'draft' | 'submitted' | 'under_review' | 'published' | 'rejected') || 'published',
              updated_at: favorite.created_at,
              // Add optional fields for display
              views: 0,
              likes: 0,
              comments: 0
            };

            return (
              <div key={favorite.id} className="relative">
                <ScreenplayCard
                  screenplay={screenplay}
                  onViewDetails={onViewDetails}
                  showPurchaseOptions={true}
                />
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-2 right-2 text-red-500 hover:text-red-700"
                  onClick={() => handleRemoveFavorite(favorite.screenplay_id, favorite.screenplay!.title)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default FavoritesPage;
