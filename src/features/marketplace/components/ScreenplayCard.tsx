
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Eye, Heart, MessageSquare, DollarSign } from 'lucide-react';
import type { Screenplay } from '@/lib/api/screenplays';
import ScreenplayActions from './ScreenplayActions';

interface ScreenplayCardProps {
  screenplay: Screenplay;
  onViewDetails: (screenplay: Screenplay) => void;
  showPurchaseOptions?: boolean;
}

const ScreenplayCard: React.FC<ScreenplayCardProps> = ({ 
  screenplay, 
  onViewDetails,
  showPurchaseOptions = false 
}) => {
  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-lg font-playfair line-clamp-2">
              {screenplay.title}
            </CardTitle>
            <CardDescription className="mt-1">
              by {screenplay.profiles?.full_name || screenplay.profiles?.username || 'Anonymous'}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {screenplay.price && showPurchaseOptions && (
              <Badge variant="outline" className="ml-2">
                <DollarSign className="h-3 w-3 mr-1" />
                {screenplay.price}
              </Badge>
            )}
            <ScreenplayActions screenplayId={screenplay.id} />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Badge variant="secondary">{screenplay.genre}</Badge>
          <Badge variant="outline">{screenplay.status}</Badge>
        </div>
        
        <p className="text-sm text-muted-foreground line-clamp-3">
          {screenplay.logline}
        </p>
        
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span className="flex items-center gap-1">
              <Eye className="h-4 w-4" />
              {screenplay.views || 0}
            </span>
            <span className="flex items-center gap-1">
              <Heart className="h-4 w-4" />
              {screenplay.likes || 0}
            </span>
            <span className="flex items-center gap-1">
              <MessageSquare className="h-4 w-4" />
              {screenplay.comments || 0}
            </span>
          </div>
          <span>{screenplay.page_count} pages</span>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => onViewDetails(screenplay)}
            className="flex-1"
          >
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </Button>
          {showPurchaseOptions && screenplay.price && (
            <Button 
              size="sm" 
              onClick={() => onViewDetails(screenplay)}
              className="flex-1"
            >
              <DollarSign className="h-4 w-4 mr-2" />
              Purchase
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ScreenplayCard;
