
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Heart, FolderPlus, Plus } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import { useCollections, useCollectionItems } from '@/hooks/useCollections';
import { toast } from 'sonner';

interface ScreenplayActionsProps {
  screenplayId: string;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'ghost';
}

const ScreenplayActions: React.FC<ScreenplayActionsProps> = ({ 
  screenplayId, 
  size = 'sm', 
  variant = 'ghost' 
}) => {
  const { isFavorited, toggleFavorite } = useFavorites();
  const { collections } = useCollections();
  const [selectedCollectionId, setSelectedCollectionId] = useState<string>('');
  const { addToCollection } = useCollectionItems(selectedCollectionId || null);
  const [isCollectionDialogOpen, setIsCollectionDialogOpen] = useState(false);

  const handleToggleFavorite = async () => {
    const success = await toggleFavorite(screenplayId);
    if (success) {
      toast.success(
        isFavorited(screenplayId) ? 'Removed from favorites' : 'Added to favorites'
      );
    }
  };

  const handleAddToCollection = async () => {
    if (!selectedCollectionId) {
      toast.error('Please select a collection');
      return;
    }

    const success = await addToCollection(screenplayId);
    if (success) {
      toast.success('Added to collection');
      setIsCollectionDialogOpen(false);
      setSelectedCollectionId('');
    }
  };

  return (
    <div className="flex items-center gap-1">
      {/* Favorite Button */}
      <Button
        variant={variant}
        size={size}
        onClick={handleToggleFavorite}
        className={isFavorited(screenplayId) ? 'text-red-500 hover:text-red-700' : ''}
      >
        <Heart 
          className={`h-4 w-4 ${isFavorited(screenplayId) ? 'fill-current' : ''}`} 
        />
      </Button>

      {/* Add to Collection Button */}
      <Dialog open={isCollectionDialogOpen} onOpenChange={setIsCollectionDialogOpen}>
        <DialogTrigger asChild>
          <Button variant={variant} size={size}>
            <FolderPlus className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add to Collection</DialogTitle>
            <DialogDescription>
              Choose a collection to add this screenplay to
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            {collections.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-muted-foreground mb-4">
                  You don't have any collections yet.
                </p>
                <Button variant="outline" onClick={() => setIsCollectionDialogOpen(false)}>
                  Create a collection first
                </Button>
              </div>
            ) : (
              <>
                <Select onValueChange={setSelectedCollectionId} value={selectedCollectionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a collection" />
                  </SelectTrigger>
                  <SelectContent>
                    {collections.map((collection) => (
                      <SelectItem key={collection.id} value={collection.id}>
                        {collection.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <div className="flex justify-end gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setIsCollectionDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleAddToCollection} disabled={!selectedCollectionId}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add to Collection
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ScreenplayActions;
