
import React, { memo, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ScreenplayCard from './ScreenplayCard';
import ScreenplayDetail from './ScreenplayDetail';
import type { Screenplay } from '@/lib/api/screenplays';

interface BrowseTabProps {
  selectedScreenplay: Screenplay | null;
  publishedScreenplays: Screenplay[];
  loadingPublished: boolean;
  canBuy: boolean;
  onViewDetails: (screenplay: Screenplay) => void;
  onBackToBrowse: () => void;
}

const BrowseTab: React.FC<BrowseTabProps> = memo(({
  selectedScreenplay,
  publishedScreenplays,
  loadingPublished,
  canBuy,
  onViewDetails,
  onBackToBrowse
}) => {
  // Memoize expensive operations
  const sortedScreenplays = useMemo(() =>
    publishedScreenplays.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    ),
    [publishedScreenplays]
  );
  if (selectedScreenplay) {
    return (
      <div>
        <button 
          onClick={onBackToBrowse}
          className="mb-4 text-primary hover:underline"
        >
          ← Back to Browse
        </button>
        <ScreenplayDetail screenplay={selectedScreenplay} />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Featured Screenplays</CardTitle>
        <CardDescription>
          {canBuy 
            ? 'Discover professionally reviewed screenplays from talented writers'
            : 'View published screenplays (purchase access requires Studio/Enterprise tier)'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loadingPublished ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : sortedScreenplays.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedScreenplays.map((screenplay) => (
              <ScreenplayCard
                key={screenplay.id}
                screenplay={screenplay}
                onViewDetails={onViewDetails}
                showPurchaseOptions={canBuy}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No published screenplays available yet.
          </div>
        )}
      </CardContent>
    </Card>
  );
});

BrowseTab.displayName = 'BrowseTab';

export default BrowseTab;
