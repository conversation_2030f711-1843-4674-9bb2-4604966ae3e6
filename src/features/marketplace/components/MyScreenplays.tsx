
import React, { useEffect, memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Eye, Edit, Trash2 } from 'lucide-react';
import { useScreenplays } from '@/hooks/useScreenplays';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

// Memoize status colors to prevent recreation
const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  approved: 'bg-green-100 text-green-800 border-green-200',
  rejected: 'bg-red-100 text-red-800 border-red-200',
  published: 'bg-blue-100 text-blue-800 border-blue-200',
} as const;

// Memoized screenplay item component
const ScreenplayItem = memo<{
  screenplay: any;
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}>(({ screenplay, onEdit, onDelete }) => {
  const handleEdit = useCallback(() => onEdit(screenplay.id), [onEdit, screenplay.id]);
  const handleDelete = useCallback(() => onDelete(screenplay.id), [onDelete, screenplay.id]);

  return (
    <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <h3 className="font-semibold text-lg">{screenplay.title}</h3>
            <Badge className={STATUS_COLORS[screenplay.status as keyof typeof STATUS_COLORS]}>
              {screenplay.status}
            </Badge>
          </div>

          <p className="text-gray-600 mb-3 line-clamp-2">{screenplay.logline}</p>

          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span className="flex items-center">
              <FileText className="h-4 w-4 mr-1" />
              {screenplay.page_count} pages
            </span>
            <span>{screenplay.genre}</span>
            {screenplay.price && (
              <span className="font-medium text-green-600">${screenplay.price}</span>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Button variant="ghost" size="sm">
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleEdit}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleDelete} className="text-red-600">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
});

ScreenplayItem.displayName = 'ScreenplayItem';

const MyScreenplays: React.FC = memo(() => {
  const { myScreenplays, loadingMy, refreshMyScreenplays } = useScreenplays();
  const { useDeepCompareMemo } = usePerformanceOptimization();

  // Memoize screenplays to prevent unnecessary re-renders
  const memoizedScreenplays = useDeepCompareMemo(
    () => myScreenplays,
    [myScreenplays]
  );

  // Memoize event handlers
  const handleEdit = useCallback((id: string) => {
    console.log('Edit screenplay:', id);
    // TODO: Implement edit functionality
  }, []);

  const handleDelete = useCallback((id: string) => {
    console.log('Delete screenplay:', id);
    // TODO: Implement delete functionality
  }, []);

  useEffect(() => {
    refreshMyScreenplays();
  }, [refreshMyScreenplays]);

  if (loadingMy) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>My Screenplays</CardTitle>
          <CardDescription>
            Manage your submitted screenplays and track their review status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {memoizedScreenplays.length > 0 ? (
            <div className="space-y-4">
              {memoizedScreenplays.map((screenplay) => (
                <ScreenplayItem
                  key={screenplay.id}
                  screenplay={screenplay}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No screenplays yet</h3>
              <p className="text-muted-foreground mb-4">
                You haven't submitted any screenplays for review yet.
              </p>
              <Button>
                <FileText className="h-4 w-4 mr-2" />
                Submit Your First Screenplay
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
});

MyScreenplays.displayName = 'MyScreenplays';

export default MyScreenplays;
