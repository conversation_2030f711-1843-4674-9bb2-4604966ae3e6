
import React, { useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { FileText, Eye, Edit, Trash2 } from 'lucide-react';
import { useScreenplays } from '@/hooks/useScreenplays';

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  approved: 'bg-green-100 text-green-800 border-green-200',
  rejected: 'bg-red-100 text-red-800 border-red-200',
  published: 'bg-blue-100 text-blue-800 border-blue-200',
};

const MyScreenplays: React.FC = () => {
  const { myScreenplays, loadingMy, refreshMyScreenplays } = useScreenplays();

  useEffect(() => {
    refreshMyScreenplays();
  }, []);

  if (loadingMy) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>My Screenplays</CardTitle>
          <CardDescription>
            Manage your submitted screenplays and track their review status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {myScreenplays.length > 0 ? (
            <div className="space-y-4">
              {myScreenplays.map((screenplay) => (
                <div
                  key={screenplay.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{screenplay.title}</h3>
                        <Badge 
                          variant="outline" 
                          className={statusColors[screenplay.status as keyof typeof statusColors]}
                        >
                          {screenplay.status}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                        <span>{screenplay.genre}</span>
                        {screenplay.page_count && <span>{screenplay.page_count} pages</span>}
                        <span>${screenplay.price}</span>
                      </div>
                      
                      {screenplay.logline && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {screenplay.logline}
                        </p>
                      )}

                      {screenplay.status === 'rejected' && screenplay.rejection_reason && (
                        <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                          <p className="text-sm text-red-800">
                            <strong>Rejection Reason:</strong> {screenplay.rejection_reason}
                          </p>
                        </div>
                      )}
                      
                      <div className="text-xs text-muted-foreground mt-2">
                        Submitted: {new Date(screenplay.created_at).toLocaleDateString()}
                        {screenplay.updated_at !== screenplay.created_at && (
                          <span className="ml-4">
                            Updated: {new Date(screenplay.updated_at).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex gap-2 ml-4">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {screenplay.status === 'pending' && (
                        <>
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4 mr-1" />
                            Delete
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No screenplays yet</h3>
              <p className="text-muted-foreground mb-4">
                You haven't submitted any screenplays for review yet.
              </p>
              <Button>
                <FileText className="h-4 w-4 mr-2" />
                Submit Your First Screenplay
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MyScreenplays;
