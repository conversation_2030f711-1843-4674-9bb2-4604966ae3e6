
import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  FileText, 
  Upload, 
  ShoppingCart, 
  Handshake, 
  Bell, 
  BarChart3, 
  FileSignature,
  DollarSign,
  Heart,
  FolderOpen
} from 'lucide-react';

interface MarketplaceNavigationProps {
  canSubmit: boolean;
  canBuy: boolean;
  unreadCount?: number;
}

const MarketplaceNavigation: React.FC<MarketplaceNavigationProps> = ({
  canSubmit,
  canBuy,
  unreadCount = 0
}) => {
  return (
    <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6 xl:grid-cols-8">
      <TabsTrigger value="browse" className="flex items-center gap-2">
        <Search className="h-4 w-4" />
        <span className="hidden sm:inline">Browse</span>
      </TabsTrigger>

      <TabsTrigger value="favorites" className="flex items-center gap-2">
        <Heart className="h-4 w-4" />
        <span className="hidden sm:inline">Favorites</span>
      </TabsTrigger>

      <TabsTrigger value="collections" className="flex items-center gap-2">
        <FolderOpen className="h-4 w-4" />
        <span className="hidden sm:inline">Collections</span>
      </TabsTrigger>

      {canSubmit && (
        <>
          <TabsTrigger value="my-screenplays" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span className="hidden sm:inline">My Scripts</span>
          </TabsTrigger>
          
          <TabsTrigger value="submit" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            <span className="hidden sm:inline">Submit</span>
          </TabsTrigger>
          
          <TabsTrigger value="earnings" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            <span className="hidden sm:inline">Earnings</span>
          </TabsTrigger>
        </>
      )}

      {canBuy && (
        <>
          <TabsTrigger value="purchases" className="flex items-center gap-2">
            <ShoppingCart className="h-4 w-4" />
            <span className="hidden sm:inline">Purchases</span>
          </TabsTrigger>
          
          <TabsTrigger value="offers" className="flex items-center gap-2">
            <Handshake className="h-4 w-4" />
            <span className="hidden sm:inline">Offers</span>
          </TabsTrigger>
        </>
      )}

      <TabsTrigger value="notifications" className="flex items-center gap-2">
        <Bell className="h-4 w-4" />
        <span className="hidden sm:inline">Alerts</span>
        {unreadCount > 0 && (
          <Badge variant="destructive" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
            {unreadCount > 9 ? '9+' : unreadCount}
          </Badge>
        )}
      </TabsTrigger>

      <TabsTrigger value="analytics" className="flex items-center gap-2">
        <BarChart3 className="h-4 w-4" />
        <span className="hidden sm:inline">Analytics</span>
      </TabsTrigger>

      <TabsTrigger value="contracts" className="flex items-center gap-2">
        <FileSignature className="h-4 w-4" />
        <span className="hidden sm:inline">Contracts</span>
      </TabsTrigger>
    </TabsList>
  );
};

export default MarketplaceNavigation;
