
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FileSignature } from 'lucide-react';

const ContractsTab: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSignature className="h-5 w-5" />
          Contract Management
        </CardTitle>
        <CardDescription>
          Manage your screenplay contracts and agreements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <FileSignature className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Contract management system coming soon</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ContractsTab;
