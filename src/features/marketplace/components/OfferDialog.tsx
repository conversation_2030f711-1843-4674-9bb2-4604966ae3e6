
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { HandHeart } from 'lucide-react';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';
import { offersApi, type ScreenplayOffer } from '@/lib/api/offers';
import { useToast } from '@/hooks/use-toast';
import type { Screenplay } from '@/lib/api/screenplays';

const offerSchema = z.object({
  offer_amount: z.number().min(1, 'Offer must be at least $1'),
  message: z.string().max(500, 'Message too long').optional(),
});

type OfferFormData = z.infer<typeof offerSchema>;

interface OfferDialogProps {
  screenplay: Screenplay;
  onOfferSubmitted?: () => void;
}

const OfferDialog: React.FC<OfferDialogProps> = ({ screenplay, onOfferSubmitted }) => {
  const [open, setOpen] = React.useState(false);
  const { toast } = useToast();
  const { execute: executeOffer, loading } = useAsyncOperation<ScreenplayOffer>({
    errorMessage: 'Failed to submit offer'
  });

  const form = useForm<OfferFormData>({
    resolver: zodResolver(offerSchema),
    defaultValues: {
      offer_amount: screenplay.price,
      message: '',
    },
  });

  const onSubmit = async (data: OfferFormData) => {
    const { success } = await executeOffer(async () => {
      const result = await offersApi.createOffer({
        screenplay_id: screenplay.id,
        offer_amount: data.offer_amount,
        message: data.message || null,
      });

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Failed to submit offer');
      }

      return result.data;
    });

    if (success) {
      toast({
        title: 'Offer Submitted',
        description: 'Your offer has been sent to the writer. You\'ll be notified of their response.',
      });
      form.reset();
      setOpen(false);
      onOfferSubmitted?.();
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <HandHeart className="h-4 w-4 mr-2" />
          Make Offer
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Make an Offer</DialogTitle>
          <DialogDescription>
            Submit an offer for "{screenplay.title}". The writer will be notified and can accept or decline.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="offer_amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Offer Amount ($)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      placeholder="Enter your offer"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Message (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Add a personal message to your offer..."
                      className="resize-none"
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-2 justify-end">
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Submitting...' : 'Submit Offer'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default OfferDialog;
