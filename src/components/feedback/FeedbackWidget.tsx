
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, X, Send, Bug, Lightbulb } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

type FeedbackType = 'bug' | 'feature' | 'general';

const FeedbackWidget: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [feedbackType, setFeedbackType] = useState<FeedbackType>('general');
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) {
      toast({
        title: "Message required",
        description: "Please enter your feedback message.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const feedbackEmail = email || user?.email || '';
      const feedbackName = user?.user_metadata?.full_name || 'Anonymous User';
      
      const categoryMap = {
        bug: 'bug',
        feature: 'feature',
        general: 'general'
      };

      const subjectMap = {
        bug: 'Bug Report',
        feature: 'Feature Request', 
        general: 'General Feedback'
      };

      const { data, error } = await supabase.functions.invoke('send-support-email', {
        body: {
          name: feedbackName,
          email: feedbackEmail,
          subject: subjectMap[feedbackType],
          message: message,
          category: categoryMap[feedbackType],
          source: 'feedback_widget',
        },
      });

      if (error) {
        throw error;
      }
      
      toast({
        title: "Feedback sent!",
        description: `Thank you for your feedback. We'll review it soon. Ticket #${data.ticketId}`,
      });
      
      // Reset form
      setMessage('');
      setEmail('');
      setIsOpen(false);
    } catch (error: any) {
      console.error('Error submitting feedback:', error);
      toast({
        title: "Error",
        description: "Failed to send feedback. Please try again or email <NAME_EMAIL>",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-40">
        <Button
          onClick={() => setIsOpen(true)}
          className="rounded-full h-12 w-12 shadow-lg hover:shadow-xl transition-all duration-200 bg-primary hover:bg-primary/90"
          size="icon"
        >
          <MessageCircle className="h-5 w-5" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-40">
      <Card className="w-80 p-4 shadow-xl border-primary/20 animate-in slide-in-from-bottom-2 duration-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-semibold">Send Feedback</h3>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsOpen(false)}
            className="h-8 w-8"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-2">
            <Button
              type="button"
              variant={feedbackType === 'bug' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFeedbackType('bug')}
              className="flex items-center gap-1"
            >
              <Bug className="h-3 w-3" />
              Bug
            </Button>
            <Button
              type="button"
              variant={feedbackType === 'feature' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFeedbackType('feature')}
              className="flex items-center gap-1"
            >
              <Lightbulb className="h-3 w-3" />
              Feature
            </Button>
            <Button
              type="button"
              variant={feedbackType === 'general' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFeedbackType('general')}
            >
              General
            </Button>
          </div>

          <div>
            <Textarea
              placeholder={
                feedbackType === 'bug'
                  ? 'Describe the bug you encountered...'
                  : feedbackType === 'feature'
                  ? 'Describe the feature you\'d like to see...'
                  : 'Share your thoughts...'
              }
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="min-h-[100px] resize-none"
              required
            />
          </div>

          {!user && (
            <div>
              <Input
                type="email"
                placeholder="Your email (optional)"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full flex items-center gap-2"
          >
            {isSubmitting ? (
              'Sending...'
            ) : (
              <>
                <Send className="h-4 w-4" />
                Send Feedback
              </>
            )}
          </Button>
        </form>

        <p className="text-xs text-muted-foreground mt-3 text-center">
          Help us improve ScriptGenius
        </p>
      </Card>
    </div>
  );
};

export default FeedbackWidget;
