
export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  priority: number;
  isPopular?: boolean;
}

export const faqData: FAQItem[] = [
  // Getting Started
  {
    id: "what-is-scriptgenius",
    question: "What is ScriptGenius?",
    answer: "ScriptGenius is a comprehensive screenwriting platform that combines professional writing tools with AI assistance. We help writers create, collaborate, and bring their stories to life with features like intelligent script formatting, team collaboration, marketplace access, and production tools.",
    category: "getting-started",
    tags: ["overview", "features"],
    priority: 1,
    isPopular: true
  },
  {
    id: "who-is-scriptgenius-for",
    question: "Who is <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for?",
    answer: "ScriptGenius serves everyone in the screenwriting ecosystem: aspiring writers getting their first ideas down, professional screenwriters polishing their craft, collaborative writing teams, production companies managing multiple projects, and industry professionals looking to discover new talent.",
    category: "getting-started",
    tags: ["target-audience", "users"],
    priority: 2,
    isPopular: true
  },
  {
    id: "how-to-get-started",
    question: "How do I get started with <PERSON>riptGeni<PERSON>?",
    answer: "Getting started is simple! Sign up for a free account, choose your plan based on your needs, and start writing immediately. Our onboarding process guides you through the key features, and you can upgrade anytime as your needs grow.",
    category: "getting-started",
    tags: ["onboarding", "signup"],
    priority: 3
  },

  // Features & Tools
  {
    id: "ai-writing-features",
    question: "What AI writing features does ScriptGenius offer?",
    answer: "Our AI writing assistant helps with dialogue refinement, character development, plot structure suggestions, and overcoming writer's block. It maintains your unique voice while providing intelligent suggestions for improving your script's flow and readability.",
    category: "features",
    tags: ["ai", "writing", "assistance"],
    priority: 1,
    isPopular: true
  },
  {
    id: "collaboration-features",
    question: "How does team collaboration work?",
    answer: "ScriptGenius enables seamless collaboration with shared workspaces, real-time editing, inline comments and suggestions, version control, and role-based permissions. Team members can work together regardless of location, with all changes tracked and organized.",
    category: "features",
    tags: ["collaboration", "team", "sharing"],
    priority: 2,
    isPopular: true
  },
  {
    id: "production-tools",
    question: "What production tools are available?",
    answer: "Our production suite includes call sheet generation, budget tracking templates, scheduling utilities, resource management, and workflow automation. These tools bridge the gap between your finished script and actual production.",
    category: "features",
    tags: ["production", "tools", "workflow"],
    priority: 3
  },
  {
    id: "marketplace-access",
    question: "How does the Script Marketplace work?",
    answer: "The marketplace connects writers directly with industry professionals, producers, and buyers. Submit your polished scripts for review, get discovered by the right people, and earn revenue with our 90/10 split (you keep 90%). All submissions go through our quality review process.",
    category: "features",
    tags: ["marketplace", "selling", "discovery"],
    priority: 2,
    isPopular: true
  },

  // Pricing & Plans
  {
    id: "pricing-plans",
    question: "What are the different pricing plans?",
    answer: "We offer four plans: Starter ($29/month) for aspiring writers, Pro Solo ($49/month) for serious independents, Pro Team ($79/month) for collaborative groups, and Studio ($129/month) for production companies. All plans include our core writing tools, with advanced features scaling up.",
    category: "pricing",
    tags: ["plans", "pricing", "features"],
    priority: 1,
    isPopular: true
  },
  {
    id: "free-trial",
    question: "Do you offer a free trial?",
    answer: "Yes! All plans come with a 30-day money-back guarantee, and you can start with our Starter plan to explore the platform. We also offer beta access and promotional codes for eligible users.",
    category: "pricing",
    tags: ["trial", "guarantee", "beta"],
    priority: 2
  },
  {
    id: "beta-program",
    question: "What is the Beta Program?",
    answer: "Our Beta Program gives early access to new features, special pricing, and direct input on product development. Beta users help shape ScriptGenius while getting premium access at reduced rates. Apply through our beta invitation form.",
    category: "pricing",
    tags: ["beta", "early-access", "testing"],
    priority: 3,
    isPopular: true
  },

  // Technical & Support
  {
    id: "file-formats",
    question: "What file formats does ScriptGenius support?",
    answer: "We support all industry-standard formats including PDF, Final Draft (FDX), Fountain, and more. You can import existing scripts and export in any format required by producers, agents, or collaborators.",
    category: "technical",
    tags: ["formats", "import", "export"],
    priority: 2
  },
  {
    id: "mobile-access",
    question: "Can I use ScriptGenius on mobile devices?",
    answer: "Yes! ScriptGenius is fully optimized for mobile and tablet use. Write, edit, and collaborate on your scripts from anywhere. Our responsive design ensures a great experience across all devices.",
    category: "technical",
    tags: ["mobile", "responsive", "access"],
    priority: 3,
    isPopular: true
  },
  {
    id: "data-security",
    question: "How secure is my data?",
    answer: "Your scripts and data are protected with enterprise-grade security including end-to-end encryption, secure cloud storage, regular backups, and compliance with industry standards. We never share your creative work without permission.",
    category: "technical",
    tags: ["security", "privacy", "data"],
    priority: 1
  },

  // Business & Industry
  {
    id: "revenue-sharing",
    question: "How does the 90/10 revenue split work?",
    answer: "When your script sells through our marketplace, you keep 90% of the sale price while ScriptGenius takes 10%. This covers platform maintenance, payment processing, and continued development of tools that help you succeed.",
    category: "business",
    tags: ["revenue", "marketplace", "earnings"],
    priority: 2
  },
  {
    id: "industry-connections",
    question: "How does ScriptGenius connect me with industry professionals?",
    answer: "Our marketplace features vetted producers, agents, and industry buyers actively seeking new material. We also provide networking opportunities, industry insights, and direct submission pathways to established production companies.",
    category: "business",
    tags: ["networking", "industry", "connections"],
    priority: 3
  }
];

export const faqCategories = [
  { id: "getting-started", name: "Getting Started", icon: "play-circle" },
  { id: "features", name: "Features & Tools", icon: "tools" },
  { id: "pricing", name: "Pricing & Plans", icon: "credit-card" },
  { id: "technical", name: "Technical", icon: "settings" },
  { id: "business", name: "Business & Industry", icon: "briefcase" }
];

export const getPopularFAQs = () => faqData.filter(faq => faq.isPopular);
export const getFAQsByCategory = (category: string) => faqData.filter(faq => faq.category === category);
export const searchFAQs = (query: string) => 
  faqData.filter(faq => 
    faq.question.toLowerCase().includes(query.toLowerCase()) ||
    faq.answer.toLowerCase().includes(query.toLowerCase()) ||
    faq.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
  );
