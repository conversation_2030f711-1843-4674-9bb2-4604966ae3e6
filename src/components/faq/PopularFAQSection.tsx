
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronRight, HelpCircle } from 'lucide-react';
import { getPopularFAQs } from './FAQData';

interface PopularFAQSectionProps {
  showAll?: boolean;
  onViewAll?: () => void;
}

const PopularFAQSection: React.FC<PopularFAQSectionProps> = ({ 
  showAll = false, 
  onViewAll 
}) => {
  const popularFAQs = getPopularFAQs().slice(0, showAll ? undefined : 4);

  return (
    <section className="py-12">
      <div className="container mx-auto px-6">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <HelpCircle className="h-6 w-6 text-primary" />
            <h2 className="text-3xl font-playfair font-bold">
              Frequently Asked Questions
            </h2>
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Get quick answers to the most common questions about ScriptGenius
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          {popularFAQs.map((faq) => (
            <Card key={faq.id} className="p-6 hover:shadow-md transition-shadow">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <Badge variant="secondary" className="mt-1">
                    Popular
                  </Badge>
                  <h3 className="font-semibold text-lg leading-tight">
                    {faq.question}
                  </h3>
                </div>
                <p className="text-muted-foreground leading-relaxed">
                  {faq.answer.length > 120 
                    ? `${faq.answer.substring(0, 120)}...` 
                    : faq.answer
                  }
                </p>
                <div className="flex flex-wrap gap-2">
                  {faq.tags.slice(0, 2).map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </Card>
          ))}
        </div>

        {!showAll && onViewAll && (
          <div className="text-center mt-8">
            <Button 
              variant="outline" 
              onClick={onViewAll}
              className="group"
            >
              View All FAQs
              <ChevronRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        )}
      </div>
    </section>
  );
};

export default PopularFAQSection;
