
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { HelpCircle, ArrowRight } from 'lucide-react';
import { faqData, FAQItem } from './FAQData';

interface ContextualFAQSuggestionsProps {
  context: 'pricing' | 'features' | 'collaboration' | 'production' | 'marketplace';
  limit?: number;
}

const contextMapping = {
  pricing: ['pricing', 'plans', 'trial', 'beta'],
  features: ['ai', 'writing', 'tools'],
  collaboration: ['collaboration', 'team', 'sharing'],
  production: ['production', 'workflow', 'tools'],
  marketplace: ['marketplace', 'selling', 'revenue']
};

const ContextualFAQSuggestions: React.FC<ContextualFAQSuggestionsProps> = ({
  context,
  limit = 2
}) => {
  const relevantTags = contextMapping[context] || [];
  
  const relevantFAQs = faqData
    .filter(faq => 
      faq.tags.some(tag => relevantTags.includes(tag)) ||
      faq.category === context
    )
    .sort((a, b) => a.priority - b.priority)
    .slice(0, limit);

  if (relevantFAQs.length === 0) return null;

  return (
    <Card className="p-4 bg-muted/30 border-primary/20">
      <div className="flex items-center gap-2 mb-3">
        <HelpCircle className="h-4 w-4 text-primary" />
        <h4 className="font-medium text-sm">You might be wondering...</h4>
      </div>
      
      <div className="space-y-3">
        {relevantFAQs.map((faq) => (
          <div key={faq.id} className="space-y-2">
            <div className="flex items-start gap-2">
              <h5 className="font-medium text-sm leading-tight flex-1">
                {faq.question}
              </h5>
              {faq.isPopular && (
                <Badge variant="secondary" className="text-xs">
                  Popular
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground leading-relaxed">
              {faq.answer.length > 100 
                ? `${faq.answer.substring(0, 100)}...` 
                : faq.answer
              }
            </p>
          </div>
        ))}
      </div>

      <Button variant="ghost" size="sm" className="w-full mt-3 group" asChild>
        <a href="/faq">
          More Questions?
          <ArrowRight className="ml-2 h-3 w-3 group-hover:translate-x-1 transition-transform" />
        </a>
      </Button>
    </Card>
  );
};

export default ContextualFAQSuggestions;
