
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { HelpCircle, ChevronDown, ExternalLink } from 'lucide-react';
import { getFAQsByCategory, getPopularFAQs } from './FAQData';
import { cn } from '@/lib/utils';

interface InlineFAQWidgetProps {
  category?: string;
  title?: string;
  maxItems?: number;
  showViewAll?: boolean;
}

const InlineFAQWidget: React.FC<InlineFAQWidgetProps> = ({
  category,
  title = "Frequently Asked Questions",
  maxItems = 3,
  showViewAll = true
}) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const faqs = category 
    ? getFAQsByCategory(category).slice(0, maxItems)
    : getPopularFAQs().slice(0, maxItems);

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  if (faqs.length === 0) return null;

  return (
    <Card className="p-6">
      <div className="flex items-center gap-2 mb-4">
        <HelpCircle className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">{title}</h3>
      </div>

      <div className="space-y-3">
        {faqs.map((faq) => (
          <div key={faq.id} className="border rounded-lg overflow-hidden">
            <Collapsible 
              open={openItems.has(faq.id)}
              onOpenChange={() => toggleItem(faq.id)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full p-4 justify-between text-left hover:bg-muted/50"
                >
                  <div className="flex items-center gap-2 flex-1">
                    <span className="font-medium text-sm">
                      {faq.question}
                    </span>
                    {faq.isPopular && (
                      <Badge variant="secondary" className="text-xs">
                        Popular
                      </Badge>
                    )}
                  </div>
                  <ChevronDown 
                    className={cn(
                      "h-4 w-4 transition-transform",
                      openItems.has(faq.id) && "transform rotate-180"
                    )}
                  />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="px-4 pb-4">
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        ))}
      </div>

      {showViewAll && (
        <div className="mt-4 pt-4 border-t">
          <Button variant="outline" size="sm" className="w-full group" asChild>
            <a href="/faq">
              View All FAQs
              <ExternalLink className="ml-2 h-3 w-3 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
        </div>
      )}
    </Card>
  );
};

export default InlineFAQWidget;
