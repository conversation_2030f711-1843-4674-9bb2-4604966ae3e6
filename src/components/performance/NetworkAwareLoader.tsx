
import React, { useState, useEffect, ReactNode } from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

interface NetworkAwareLoaderProps {
  children: ReactNode;
  fallback?: ReactNode;
  enableLowDataMode?: boolean;
}

interface ConnectionInfo {
  effectiveType: '4g' | '3g' | '2g' | 'slow-2g';
  saveData: boolean;
  downlink: number;
  rtt: number;
}

const NetworkAwareLoader: React.FC<NetworkAwareLoaderProps> = ({
  children,
  fallback,
  enableLowDataMode = true
}) => {
  const [networkInfo, setNetworkInfo] = useState<ConnectionInfo | null>(null);
  const [shouldOptimize, setShouldOptimize] = useState(false);

  useEffect(() => {
    // Check for Network Information API support
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    if (connection) {
      const updateNetworkInfo = () => {
        const info: ConnectionInfo = {
          effectiveType: connection.effectiveType || '4g',
          saveData: connection.saveData || false,
          downlink: connection.downlink || 10,
          rtt: connection.rtt || 50
        };
        
        setNetworkInfo(info);
        
        // Determine if we should optimize for slow connections
        const isSlowConnection = info.effectiveType === '2g' || 
                                info.effectiveType === 'slow-2g' || 
                                info.saveData ||
                                info.downlink < 1.5;
        
        setShouldOptimize(enableLowDataMode && isSlowConnection);
      };

      updateNetworkInfo();
      connection.addEventListener('change', updateNetworkInfo);
      
      return () => {
        connection.removeEventListener('change', updateNetworkInfo);
      };
    }
  }, [enableLowDataMode]);

  if (shouldOptimize && fallback) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default NetworkAwareLoader;
