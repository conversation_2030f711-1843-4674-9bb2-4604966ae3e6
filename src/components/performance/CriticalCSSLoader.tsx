
import React, { useEffect } from 'react';

interface CriticalCSSLoaderProps {
  href: string;
  media?: string;
  onLoad?: () => void;
}

const CriticalCSSLoader: React.FC<CriticalCSSLoaderProps> = ({
  href,
  media = 'all',
  onLoad
}) => {
  useEffect(() => {
    // Create link element for non-critical CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.media = 'print'; // Load as print first to avoid render blocking
    link.onload = () => {
      link.media = media; // Switch to intended media once loaded
      onLoad?.();
    };
    
    document.head.appendChild(link);
    
    return () => {
      // Clean up when component unmounts
      if (document.head.contains(link)) {
        document.head.removeChild(link);
      }
    };
  }, [href, media, onLoad]);

  return null; // This component doesn't render anything
};

export default CriticalCSSLoader;
