
import React, { memo, useState, useEffect, Suspense } from 'react';
import { usePerformanceOptimizations } from '@/hooks/usePerformanceOptimizations';
import LoadingSpinner from '@/components/LoadingSpinner';
import NetworkAwareLoader from './NetworkAwareLoader';

interface PerformanceAwareComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  preloadAssets?: string[];
  enableNetworkOptimization?: boolean;
  priority?: 'high' | 'medium' | 'low';
}

const PerformanceAwareComponent: React.FC<PerformanceAwareComponentProps> = memo(({
  children,
  fallback = <LoadingSpinner />,
  preloadAssets = [],
  enableNetworkOptimization = true,
  priority = 'medium'
}) => {
  const [isOptimized, setIsOptimized] = useState(false);
  const { preloadResource, measurePerformance } = usePerformanceOptimizations();

  useEffect(() => {
    const initializeOptimizations = async () => {
      try {
        // Preload specified assets
        await Promise.all(
          preloadAssets.map(asset => {
            const extension = asset.split('.').pop()?.toLowerCase();
            const asType = extension === 'js' ? 'script' : 
                          extension === 'css' ? 'style' : 'fetch';
            return preloadResource(asset, asType);
          })
        );

        setIsOptimized(true);
      } catch (error) {
        console.warn('Failed to initialize performance optimizations:', error);
        setIsOptimized(true); // Continue rendering even if optimizations fail
      }
    };

    // Delay initialization based on priority
    const delay = priority === 'high' ? 0 : priority === 'medium' ? 100 : 300;
    const timeoutId = setTimeout(initializeOptimizations, delay);

    return () => clearTimeout(timeoutId);
  }, [preloadAssets, preloadResource, priority]);

  const content = (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  );

  if (!isOptimized) {
    return fallback;
  }

  if (enableNetworkOptimization) {
    return (
      <NetworkAwareLoader fallback={fallback}>
        {content}
      </NetworkAwareLoader>
    );
  }

  return content;
});

PerformanceAwareComponent.displayName = 'PerformanceAwareComponent';

export default PerformanceAwareComponent;
