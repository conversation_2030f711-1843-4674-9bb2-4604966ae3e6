
import React from 'react';
import { cn } from '@/lib/utils';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

/**
 * Reusable loading spinner component with customizable size and text
 * 
 * @component
 * @param {Object} props - Component props
 * @param {'sm' | 'md' | 'lg'} [props.size='md'] - Size of the spinner
 * @param {string} [props.className] - Additional CSS classes
 * @param {string} [props.text] - Optional loading text to display
 * 
 * @example
 * ```tsx
 * <LoadingSpinner size="lg" text="Loading data..." />
 * ```
 */
export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className,
  text 
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      <div 
        className={cn(
          'animate-spin border-2 border-primary border-t-transparent rounded-full',
          sizeClasses[size],
          className
        )}
      />
      {text && (
        <p className="text-sm text-muted-foreground">{text}</p>
      )}
    </div>
  );
};

export default LoadingSpinner;
