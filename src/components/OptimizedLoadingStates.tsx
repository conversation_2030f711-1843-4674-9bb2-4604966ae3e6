
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface OptimizedLoadingStateProps {
  variant?: 'card' | 'list' | 'dashboard' | 'table' | 'minimal';
  count?: number;
  className?: string;
}

/**
 * Optimized loading states with different variants for various UI contexts
 */
export const OptimizedLoadingState: React.FC<OptimizedLoadingStateProps> = ({
  variant = 'card',
  count = 1,
  className
}) => {
  const renderCardSkeleton = () => (
    <Card className={cn("p-6 space-y-4", className)}>
      <div className="flex items-center space-x-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-5/6" />
        <Skeleton className="h-4 w-4/6" />
      </div>
    </Card>
  );

  const renderListSkeleton = () => (
    <div className={cn("space-y-3", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3 border rounded-lg">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-2/3" />
            <Skeleton className="h-3 w-1/3" />
          </div>
          <Skeleton className="h-8 w-20" />
        </div>
      ))}
    </div>
  );

  const renderDashboardSkeleton = () => (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
              </div>
              <Skeleton className="h-12 w-12 rounded-lg" />
            </div>
          </Card>
        ))}
      </div>
      
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </Card>
        <Card className="p-6">
          <Skeleton className="h-6 w-40 mb-4" />
          <Skeleton className="h-64 w-full" />
        </Card>
      </div>
    </div>
  );

  const renderTableSkeleton = () => (
    <div className={cn("space-y-4", className)}>
      {/* Table Header */}
      <div className="grid grid-cols-4 gap-4 p-4 border-b">
        {Array.from({ length: 4 }).map((_, i) => (
          <Skeleton key={i} className="h-4 w-full" />
        ))}
      </div>
      
      {/* Table Rows */}
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="grid grid-cols-4 gap-4 p-4 border-b">
          {Array.from({ length: 4 }).map((_, j) => (
            <Skeleton key={j} className="h-4 w-full" />
          ))}
        </div>
      ))}
    </div>
  );

  const renderMinimalSkeleton = () => (
    <div className={cn("space-y-2", className)}>
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton key={i} className="h-4 w-full" />
      ))}
    </div>
  );

  const skeletonVariants = {
    card: renderCardSkeleton,
    list: renderListSkeleton,
    dashboard: renderDashboardSkeleton,
    table: renderTableSkeleton,
    minimal: renderMinimalSkeleton
  };

  if (variant === 'dashboard') {
    return skeletonVariants[variant]();
  }

  return (
    <div>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className={i > 0 ? 'mt-4' : ''}>
          {skeletonVariants[variant]()}
        </div>
      ))}
    </div>
  );
};

/**
 * Smart loading state that adapts based on content type
 */
interface SmartLoadingStateProps {
  isLoading: boolean;
  error?: string | null;
  isEmpty?: boolean;
  loadingVariant?: OptimizedLoadingStateProps['variant'];
  emptyMessage?: string;
  children: React.ReactNode;
}

export const SmartLoadingState: React.FC<SmartLoadingStateProps> = ({
  isLoading,
  error,
  isEmpty,
  loadingVariant = 'card',
  emptyMessage = 'No data available',
  children
}) => {
  if (isLoading) {
    return <OptimizedLoadingState variant={loadingVariant} count={3} />;
  }

  if (error) {
    return (
      <Card className="p-6 text-center">
        <div className="text-red-500 font-medium mb-2">Error loading data</div>
        <div className="text-sm text-muted-foreground">{error}</div>
      </Card>
    );
  }

  if (isEmpty) {
    return (
      <Card className="p-6 text-center">
        <div className="text-muted-foreground">{emptyMessage}</div>
      </Card>
    );
  }

  return <>{children}</>;
};

export default OptimizedLoadingState;
