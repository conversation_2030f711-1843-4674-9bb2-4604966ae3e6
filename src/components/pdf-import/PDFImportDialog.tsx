
import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileText, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { PDFProcessor, ScreenplayElement } from '@/lib/pdf/pdfProcessor';
import { toast } from 'sonner';

interface PDFImportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (content: string, elements: ScreenplayElement[]) => void;
}

const PDFImportDialog: React.FC<PDFImportDialogProps> = ({
  isOpen,
  onClose,
  onImport
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [processing, setProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [previewText, setPreviewText] = useState('');
  const [elements, setElements] = useState<ScreenplayElement[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile && selectedFile.type === 'application/pdf') {
      setFile(selectedFile);
      setError(null);
      setPreviewText('');
      setElements([]);
    } else {
      setError('Please select a valid PDF file');
    }
  }, []);

  const processPDF = useCallback(async () => {
    if (!file) return;

    setProcessing(true);
    setProgress(0);
    setError(null);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const result = await PDFProcessor.extractText(file);
      clearInterval(progressInterval);
      setProgress(100);

      if (!result.success || !result.text) {
        throw new Error(result.error || 'Failed to extract text from PDF');
      }

      const parsedElements = PDFProcessor.parseScreenplayFormat(result.text);
      const convertedText = PDFProcessor.convertToScriptFormat(parsedElements);

      setElements(parsedElements);
      setPreviewText(convertedText.substring(0, 1000) + (convertedText.length > 1000 ? '...' : ''));
      
      toast.success(`Successfully processed PDF with ${parsedElements.length} screenplay elements`);
    } catch (err) {
      console.error('PDF processing error:', err);
      setError(err instanceof Error ? err.message : 'Failed to process PDF');
      toast.error('Failed to process PDF file');
    } finally {
      setProcessing(false);
    }
  }, [file]);

  const handleImport = useCallback(() => {
    if (elements.length > 0) {
      const convertedText = PDFProcessor.convertToScriptFormat(elements);
      onImport(convertedText, elements);
      onClose();
      toast.success('PDF imported successfully into script editor');
    }
  }, [elements, onImport, onClose]);

  const handleClose = useCallback(() => {
    setFile(null);
    setPreviewText('');
    setElements([]);
    setError(null);
    setProgress(0);
    setProcessing(false);
    onClose();
  }, [onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Import PDF Screenplay</span>
          </DialogTitle>
          <DialogDescription>
            Upload a PDF screenplay to automatically convert it to editable script format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* File Upload */}
          <div className="space-y-2">
            <Label htmlFor="pdf-file">Select PDF File</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="pdf-file"
                type="file"
                accept=".pdf"
                onChange={handleFileSelect}
                disabled={processing}
                className="flex-1"
              />
              <Button
                onClick={processPDF}
                disabled={!file || processing}
                variant="outline"
                className="shrink-0"
              >
                <Upload className="h-4 w-4 mr-2" />
                Process
              </Button>
            </div>
          </div>

          {/* Progress */}
          {processing && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Processing PDF...</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Preview */}
          {previewText && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Preview (First 1000 characters)</Label>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span>{elements.length} elements detected</span>
                </div>
              </div>
              <div className="border rounded-md p-3 bg-muted/50 max-h-48 overflow-y-auto">
                <pre className="text-sm whitespace-pre-wrap font-mono">
                  {previewText}
                </pre>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              onClick={handleImport} 
              disabled={elements.length === 0}
              className="bg-primary hover:bg-primary/90"
            >
              Import to Editor
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PDFImportDialog;
