
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import PDFImportDialog from './PDFImportDialog';
import { ScreenplayElement } from '@/lib/pdf/pdfProcessor';

interface PDFImportButtonProps {
  onImport: (content: string, elements: ScreenplayElement[]) => void;
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const PDFImportButton: React.FC<PDFImportButtonProps> = ({
  onImport,
  className,
  variant = 'outline',
  size = 'sm'
}) => {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setShowDialog(true)}
        className={className}
      >
        <FileText className="h-4 w-4 mr-2" />
        Import PDF
      </Button>

      <PDFImportDialog
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        onImport={onImport}
      />
    </>
  );
};

export default PDFImportButton;
