
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { CheckCircle } from 'lucide-react';

export const BetaInvitationForm: React.FC = () => {
  const [invitationCode, setInvitationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!invitationCode.trim()) return;

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc('accept_beta_invitation', {
        invitation_code_param: invitationCode.trim(),
      });

      if (error) throw error;

      const result = data[0];
      if (result.success) {
        setSuccess(true);
        toast({
          title: 'Welcome to Beta!',
          description: result.message,
        });
      } else {
        toast({
          title: 'Invalid Code',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error accepting beta invitation:', error);
      toast({
        title: 'Error',
        description: 'Failed to accept invitation. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto" />
            <div>
              <h3 className="text-lg font-semibold">Welcome to ScriptGenius Beta!</h3>
              <p className="text-muted-foreground">
                You're now part of our exclusive beta testing program.
              </p>
            </div>
            <Button onClick={() => window.location.reload()}>
              Continue to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Join Beta Testing</CardTitle>
        <CardDescription>
          Enter your beta invitation code to get early access to ScriptGenius
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="invitation-code">Invitation Code</Label>
            <Input
              id="invitation-code"
              type="text"
              placeholder="BETA-XXXXXXXX"
              value={invitationCode}
              onChange={(e) => setInvitationCode(e.target.value)}
              className="uppercase"
              required
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? 'Validating...' : 'Join Beta'}
          </Button>
        </form>

        <div className="mt-4 text-sm text-muted-foreground">
          <p>Don't have an invitation code?</p>
          <p>Beta invitations are currently limited. Stay tuned for our public launch!</p>
        </div>
      </CardContent>
    </Card>
  );
};
