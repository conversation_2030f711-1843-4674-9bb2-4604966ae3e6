
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { usePlanSelection } from '@/hooks/usePlanSelection';
import { useEnhancedSubscription } from '@/hooks/useEnhancedSubscription';
import { fallbackPlans, convertDbPlanToLegacy } from './pricing/planData';
import PlanCard from './pricing/PlanCard';
import PlanComparisonModal from './pricing/PlanComparisonModal';
import { EnhancedPromoCodeInput } from './pricing/EnhancedPromoCodeInput';
import PricingHeader from './pricing/PricingHeader';
import { Badge } from '@/components/ui/badge';

const Pricing = () => {
  const { plans, subscription, loading } = useEnhancedSubscription();
  const { handlePlanSelection, loadingPlan } = usePlanSelection();
  const [isYearly, setIsYearly] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [appliedPromo, setAppliedPromo] = useState<{ code: string; discount: { type: string; value: number } } | undefined>();

  // Use database plans or fallback to static plans
  const activePlans = plans.length > 0 ? plans : fallbackPlans.map((plan, index) => ({
    id: `fallback-${index}`,
    plan_id: plan.name.toLowerCase().replace(/\s+/g, '-'),
    name: plan.name,
    display_name: plan.name,
    description: plan.description,
    price_monthly: plan.priceAmount / 100,
    price_yearly: plan.priceAmount / 100 * 10, // 20% discount for yearly
    features: plan.features.reduce((acc, feature) => ({ ...acc, [feature]: true }), {}),
    limits: {},
    is_popular: plan.popular,
    is_active: true,
    sort_order: index
  }));

  const handlePromoApplied = (code: string, discount: { type: string; value: number }) => {
    setAppliedPromo({ code, discount });
  };

  const handlePromoRemoved = () => {
    setAppliedPromo(undefined);
  };

  const handlePlanSelect = (planId: string) => {
    handlePlanSelection(planId, isYearly ? 'yearly' : 'monthly', appliedPromo?.code);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p>Loading pricing plans...</p>
        </div>
      </div>
    );
  }

  return (
    <section id="pricing" className="py-16 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-6">
        {/* Header */}
        <PricingHeader
          isYearly={isYearly}
          onToggleBilling={setIsYearly}
          onShowComparison={() => setShowComparison(true)}
        />

        {/* Current Plan Notice */}
        {subscription && subscription.subscribed && (
          <div className="text-center mb-8">
            <Badge className="bg-primary/10 text-primary border-primary">
              Current Plan: {subscription.plan_name}
            </Badge>
          </div>
        )}

        {/* Enhanced Promo Code Input with Beta Support */}
        <div className="max-w-md mx-auto mb-8">
          <EnhancedPromoCodeInput
            onPromoApplied={handlePromoApplied}
            onPromoRemoved={handlePromoRemoved}
            appliedPromo={appliedPromo}
          />
        </div>

        {/* Plans Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {activePlans.map((plan, index) => (
            <PlanCard
              key={plan.id || plan.plan_id}
              plan={plan}
              index={index}
              onSelectPlan={handlePlanSelect}
              isLoading={loadingPlan === plan.plan_id}
              isYearly={isYearly}
              isCurrentPlan={subscription?.plan_id === plan.plan_id}
            />
          ))}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12 space-y-4">
          <p className="text-sm text-muted-foreground">
            All plans include free updates and 30-day money-back guarantee
          </p>
          <p className="text-xs text-muted-foreground">
            Need enterprise features? <a href="/contact" className="text-primary hover:underline">Contact us</a>
          </p>
        </div>

        {/* Plan Comparison Modal */}
        <PlanComparisonModal
          isOpen={showComparison}
          onClose={() => setShowComparison(false)}
          plans={activePlans}
          onSelectPlan={handlePlanSelect}
          isYearly={isYearly}
          currentPlanId={subscription?.plan_id}
        />
      </div>
    </section>
  );
};

export default Pricing;
