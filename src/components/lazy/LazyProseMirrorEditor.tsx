
import React, { Suspense, lazy } from 'react';
import LoadingSpinner from '@/components/LoadingSpinner';

// Lazy load the heavy ProseMirror editor with its dependencies
const ProseMirrorScriptEditor = lazy(() => 
  import('@/components/ProseMirrorScriptEditor').then(module => ({
    default: module.default
  }))
);

interface LazyProseMirrorEditorProps {
  initialContent?: string;
  onContentChange?: (content: string) => void;
  className?: string;
  sceneId?: string;
}

/**
 * Loading fallback specifically for ProseMirror editor
 */
const ProseMirrorLoadingFallback = () => (
  <div className="min-h-[60vh] flex items-center justify-center bg-slate-950 rounded-lg border border-slate-700">
    <div className="text-center space-y-4">
      <LoadingSpinner size="md" />
      <div className="space-y-2">
        <p className="text-lg font-medium text-slate-200">Loading Script Editor...</p>
        <p className="text-sm text-slate-400">Initializing ProseMirror components</p>
      </div>
    </div>
  </div>
);

const LazyProseMirrorEditor: React.FC<LazyProseMirrorEditorProps> = (props) => {
  return (
    <Suspense fallback={<ProseMirrorLoadingFallback />}>
      <ProseMirrorScriptEditor {...props} />
    </Suspense>
  );
};

export default LazyProseMirrorEditor;
