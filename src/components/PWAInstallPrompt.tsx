
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Download, X, Smartphone } from 'lucide-react';
import { onInstallAvailable, promptAppInstall, isAppInstalled } from '@/utils/pwa';

interface PWAInstallPromptProps {
  className?: string;
}

const PWAInstallPrompt: React.FC<PWAInstallPromptProps> = ({ className }) => {
  const [canInstall, setCanInstall] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    // Don't show if already installed
    if (isAppInstalled()) {
      return;
    }

    const unsubscribe = onInstallAvailable((installable) => {
      setCanInstall(installable);
      setIsVisible(installable);
    });

    return unsubscribe;
  }, []);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const installed = await promptAppInstall();
      if (installed) {
        setIsVisible(false);
      }
    } catch (error) {
      console.error('Install failed:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  if (!isVisible || !canInstall || isAppInstalled()) {
    return null;
  }

  return (
    <Card className={`fixed bottom-4 left-4 right-4 sm:left-auto sm:right-4 sm:w-80 p-4 bg-background border shadow-lg z-50 ${className}`}>
      <div className="flex items-start gap-3">
        <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
          <Smartphone className="h-5 w-5 text-primary-foreground" />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm mb-1">Install ScriptGenius</h3>
          <p className="text-xs text-muted-foreground mb-3">
            Get quick access to your scripts and work offline with our app.
          </p>
          
          <div className="flex gap-2">
            <Button 
              size="sm" 
              onClick={handleInstall}
              disabled={isInstalling}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-1" />
              {isInstalling ? 'Installing...' : 'Install'}
            </Button>
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={handleDismiss}
              className="px-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PWAInstallPrompt;
