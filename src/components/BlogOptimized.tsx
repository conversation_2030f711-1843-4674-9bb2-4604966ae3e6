
import React, { useState, Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import MobileOptimizations from '@/components/mobile/MobileOptimizations';
import BlogHero from '@/components/blog/BlogHero';
import CategoryFilter from '@/components/blog/CategoryFilter';
import FeaturedArticle from '@/components/blog/FeaturedArticle';
import BlogPostsGrid from '@/components/blog/BlogPostsGrid';
import BlogSEOManager from '@/components/blog/BlogSEOManager';
import { 
  getPostsByCategory,
  getFeaturedPosts
} from '@/lib/blog/optimizedBlogData';

// Lazy load the newsletter component
const NewsletterSection = React.lazy(() => import('@/components/blog/NewsletterSection'));

const BlogOptimized = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = ['All', 'Writing Craft', 'AI Collaboration', 'Industry Insights', 'Production', 'Technology', 'Interviews'];

  const filteredPosts = getPostsByCategory(selectedCategory).filter(post =>
    searchQuery === '' || 
    post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
    post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const featuredPost = selectedCategory === 'All' ? getFeaturedPosts()[0] : null;
  const regularPosts = filteredPosts.filter(post => !post.featured || selectedCategory !== 'All');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  return (
    <MobileOptimizations enableTouchOptimizations enableViewportOptimizations reducedAnimations>
      <div className="min-h-screen bg-background">
        <BlogSEOManager />
        <Header />
        
        <BlogHero 
          searchQuery={searchQuery}
          onSearchChange={handleSearch}
        />

        <CategoryFilter 
          categories={categories}
          selectedCategory={selectedCategory}
          onCategoryChange={handleCategoryChange}
        />

        {featuredPost && <FeaturedArticle post={featuredPost} />}

        <BlogPostsGrid posts={regularPosts} />

        <Suspense fallback={<div className="h-64 bg-muted/10 animate-pulse" />}>
          <NewsletterSection />
        </Suspense>

        <Footer />
      </div>
    </MobileOptimizations>
  );
};

export default BlogOptimized;
