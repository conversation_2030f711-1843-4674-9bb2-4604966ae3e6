
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Home, Film, Edit, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SidebarItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  active?: boolean;
  onClick?: () => void;
}

interface AnimatedSidebarProps {
  items?: SidebarItem[];
  collapsed?: boolean;
  onToggle?: (collapsed: boolean) => void;
  className?: string;
}

const defaultItems: SidebarItem[] = [
  { icon: Home, label: 'Dashboard', active: true },
  { icon: Film, label: 'Projects' },
  { icon: Edit, label: 'Script Editor' },
];

const AnimatedSidebar: React.FC<AnimatedSidebarProps> = ({
  items = defaultItems,
  collapsed: controlledCollapsed,
  onToggle,
  className
}) => {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  
  const collapsed = controlledCollapsed !== undefined ? controlledCollapsed : internalCollapsed;
  
  const handleToggle = () => {
    const newCollapsed = !collapsed;
    if (onToggle) {
      onToggle(newCollapsed);
    } else {
      setInternalCollapsed(newCollapsed);
    }
  };

  return (
    <div className={cn(
      "cinema-sidebar h-full transition-all duration-300 ease-in-out relative flex flex-col",
      collapsed ? "w-16" : "w-64",
      className
    )}>
      {/* Header */}
      <div className="p-4 border-b border-border/50">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div className="flex items-center space-x-2 animate-fade-in-up">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-lg font-bold text-primary-foreground">S</span>
              </div>
              <span className="text-lg font-playfair font-bold gold-gradient">
                ScriptGenius
              </span>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleToggle}
            className="ml-auto hover:bg-white/10 transition-colors"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 space-y-2">
        {items.map((item, index) => {
          const Icon = item.icon;
          return (
            <button
              key={index}
              onClick={item.onClick}
              className={cn(
                "sidebar-item w-full text-left group",
                item.active && "active",
                collapsed && "justify-center px-0"
              )}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <Icon className="h-5 w-5 flex-shrink-0" />
              {!collapsed && (
                <span className="animate-fade-in-up truncate">
                  {item.label}
                </span>
              )}
              {collapsed && (
                <div className="absolute left-16 ml-2 px-2 py-1 bg-popover text-popover-foreground text-sm rounded opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap">
                  {item.label}
                </div>
              )}
            </button>
          );
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border/50">
        {!collapsed && (
          <div className="text-xs text-muted-foreground animate-fade-in-up">
            <p>ScriptGenius v1.0</p>
            <p>Professional Screenwriting</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AnimatedSidebar;
