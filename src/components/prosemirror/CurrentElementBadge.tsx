
import React from 'react';
import { Badge } from '@/components/ui/badge';

interface ElementInfo {
  label: string;
  description: string;
}

interface CurrentElementBadgeProps {
  elementInfo: ElementInfo;
}

const CurrentElementBadge: React.FC<CurrentElementBadgeProps> = ({ elementInfo }) => {
  return (
    <div className="flex items-center space-x-3">
      <div className="flex items-center space-x-2">
        <Badge variant="outline" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
          {elementInfo.label}
        </Badge>
        <span className="text-xs text-slate-400 hidden sm:block">
          {elementInfo.description}
        </span>
      </div>
    </div>
  );
};

export default CurrentElementBadge;
