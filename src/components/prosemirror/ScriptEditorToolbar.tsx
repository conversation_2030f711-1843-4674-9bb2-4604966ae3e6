
import React from 'react';
import { Button } from '@/components/ui/button';
import { Undo, Redo, Play, Download, Save } from 'lucide-react';

interface ScriptEditorToolbarProps {
  onExport: () => void;
  onSave: () => void;
  isSaving: boolean;
}

const ScriptEditorToolbar: React.FC<ScriptEditorToolbarProps> = ({
  onExport,
  onSave,
  isSaving
}) => {
  return (
    <div className="border-b border-border p-3 bg-secondary/30">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Undo className="h-4 w-4 mr-1" />
            Undo
          </Button>
          <Button variant="outline" size="sm">
            <Redo className="h-4 w-4 mr-1" />
            Redo
          </Button>
          <div className="w-px h-6 bg-border mx-2" />
          <Button variant="outline" size="sm">
            <Play className="h-4 w-4 mr-1" />
            Preview
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onExport}
          >
            <Download className="h-4 w-4 mr-1" />
            Export
          </Button>
          <Button 
            size="sm" 
            onClick={onSave}
            disabled={isSaving}
            className="bg-primary hover:bg-primary/90"
          >
            <Save className="h-4 w-4 mr-1" />
            {isSaving ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditorToolbar;
