
import { useState, useEffect } from 'react';
import { EditorView } from 'prosemirror-view';
import { ElementSuggestion } from '../SmartSuggestions';
import { 
  Heading1, 
  FileText, 
  User, 
  MessageSquare, 
  Type, 
  ArrowRight
} from 'lucide-react';
import {
  insertSceneHeading,
  insertAction,
  insertCharacter,
  insertDialogue,
  insertParenthetical,
  insertTransition
} from '@/lib/prosemirror/commands';

export const useSmartSuggestions = (editorView: EditorView | null) => {
  const [currentElement, setCurrentElement] = useState<string>('');
  const [suggestions, setSuggestions] = useState<ElementSuggestion[]>([]);

  const allElements: ElementSuggestion[] = [
    { 
      icon: Heading1, 
      command: insertSceneHeading, 
      label: 'Scene Heading', 
      shortcut: 'Cmd+1',
      description: 'Start a new scene with location and time',
      priority: 1
    },
    { 
      icon: FileText, 
      command: insertAction, 
      label: 'Action', 
      shortcut: 'Cmd+2',
      description: 'Describe what happens on screen',
      priority: 2
    },
    { 
      icon: User, 
      command: insert<PERSON><PERSON><PERSON>, 
      label: 'Character', 
      shortcut: 'Cmd+3',
      description: 'Character name before dialogue',
      priority: 3
    },
    { 
      icon: MessageSquare, 
      command: insertDialogue, 
      label: 'Dialogue', 
      shortcut: 'Cmd+4',
      description: 'What the character says',
      priority: 4
    },
    { 
      icon: Type, 
      command: insertParenthetical, 
      label: 'Parenthetical', 
      shortcut: 'Cmd+5',
      description: 'Direction within dialogue',
      priority: 5
    },
    { 
      icon: ArrowRight, 
      command: insertTransition, 
      label: 'Transition', 
      shortcut: 'Cmd+6',
      description: 'Scene transition instruction',
      priority: 6
    }
  ];

  useEffect(() => {
    if (!editorView) return;

    const updateContext = () => {
      const { state } = editorView;
      const { $from } = state.selection;
      const currentNode = $from.parent;
      const currentType = currentNode.type.name;
      const currentText = currentNode.textContent.trim();
      
      setCurrentElement(currentType);
      
      // Smart suggestions based on current context
      let contextSuggestions: ElementSuggestion[] = [];
      
      if (currentType === 'scene_heading' || !currentText) {
        contextSuggestions = [
          allElements.find(e => e.label === 'Action')!,
          allElements.find(e => e.label === 'Character')!,
          allElements.find(e => e.label === 'Scene Heading')!
        ];
      } else if (currentType === 'action') {
        if (currentText.match(/\b(enters|exits|walks|runs|sits|stands)\b/i)) {
          contextSuggestions = [
            allElements.find(e => e.label === 'Character')!,
            allElements.find(e => e.label === 'Action')!
          ];
        } else {
          contextSuggestions = [
            allElements.find(e => e.label === 'Character')!,
            allElements.find(e => e.label === 'Scene Heading')!,
            allElements.find(e => e.label === 'Action')!
          ];
        }
      } else if (currentType === 'character') {
        contextSuggestions = [
          allElements.find(e => e.label === 'Dialogue')!,
          allElements.find(e => e.label === 'Action')!
        ];
      } else if (currentType === 'dialogue') {
        if (currentText.match(/[!?]$/)) {
          contextSuggestions = [
            allElements.find(e => e.label === 'Character')!,
            allElements.find(e => e.label === 'Action')!
          ];
        } else {
          contextSuggestions = [
            allElements.find(e => e.label === 'Parenthetical')!,
            allElements.find(e => e.label === 'Action')!,
            allElements.find(e => e.label === 'Character')!
          ];
        }
      } else if (currentType === 'parenthetical') {
        contextSuggestions = [
          allElements.find(e => e.label === 'Dialogue')!,
          allElements.find(e => e.label === 'Action')!
        ];
      }
      
      setSuggestions(contextSuggestions.filter(Boolean));
    };

    // Update context when selection changes
    const view = editorView;
    const originalDispatchTransaction = view.dispatch;
    view.dispatch = (tr) => {
      originalDispatchTransaction(tr);
      setTimeout(updateContext, 0);
    };

    updateContext();

    return () => {
      view.dispatch = originalDispatchTransaction;
    };
  }, [editorView]);

  const getCurrentElementInfo = () => {
    const element = allElements.find(e => e.label.toLowerCase().replace(' ', '_') === currentElement);
    return element || { label: 'Text', description: 'Regular text content' };
  };

  return {
    currentElement,
    suggestions,
    allElements,
    getCurrentElementInfo
  };
};
