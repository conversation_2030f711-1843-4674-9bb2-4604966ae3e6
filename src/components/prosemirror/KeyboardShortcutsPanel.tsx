
import React from 'react';
import { Badge } from '@/components/ui/badge';

interface KeyboardShortcut {
  key: string;
  description: string;
  category?: string;
}

interface ShortcutCategory {
  category: string;
  shortcuts: KeyboardShortcut[];
  color: string;
}

interface KeyboardShortcutsPanelProps {
  isVisible: boolean;
}

const KeyboardShortcutsPanel: React.FC<KeyboardShortcutsPanelProps> = ({
  isVisible
}) => {
  const keyboardShortcuts: ShortcutCategory[] = [
    { 
      category: 'Smart Navigation', 
      color: 'bg-blue-500/20 text-blue-300 border-blue-500/30',
      shortcuts: [
        { key: 'Enter', description: 'Smart element transition' },
        { key: 'Shift + Enter', description: 'Same element type' },
        { key: 'Tab', description: 'Next element in flow' },
        { key: 'Cmd/Ctrl + S', description: 'Save script' }
      ]
    },
    { 
      category: 'Screenplay Elements', 
      color: 'bg-green-500/20 text-green-300 border-green-500/30',
      shortcuts: [
        { key: 'Cmd/Ctrl + 1 or H', description: 'Scene Heading' },
        { key: 'Cmd/Ctrl + 2 or A', description: 'Action' },
        { key: 'Cmd/Ctrl + 3 or C', description: 'Character' },
        { key: 'Cmd/Ctrl + 4 or D', description: 'Dialogue' },
        { key: 'Cmd/Ctrl + 5 or P', description: 'Parenthetical' },
        { key: 'Cmd/Ctrl + 6 or T', description: 'Transition' }
      ]
    },
    { 
      category: 'Text Formatting', 
      color: 'bg-purple-500/20 text-purple-300 border-purple-500/30',
      shortcuts: [
        { key: 'Cmd/Ctrl + B', description: 'Bold text' },
        { key: 'Cmd/Ctrl + I', description: 'Italic text' },
        { key: 'Cmd/Ctrl + U', description: 'Underline text' },
        { key: 'Cmd/Ctrl + Z', description: 'Undo' },
        { key: 'Cmd/Ctrl + Y', description: 'Redo' }
      ]
    }
  ];

  if (!isVisible) return null;

  return (
    <div className="border-b border-border p-4 glass">
      <div className="max-w-6xl mx-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Professional Shortcuts</h3>
          <Badge variant="outline" className="bg-secondary text-secondary-foreground border-border">
            Industry Standard
          </Badge>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {keyboardShortcuts.map((category) => (
            <div key={category.category} className="space-y-3">
              <div className="flex items-center space-x-2">
                <Badge className={category.color}>
                  {category.category}
                </Badge>
              </div>
              
              <div className="space-y-2">
                {category.shortcuts.map((shortcut, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-secondary/50 border border-border/50">
                    <code className="bg-muted px-3 py-1 rounded text-sm font-mono">
                      {shortcut.key}
                    </code>
                    <span className="text-sm text-muted-foreground ml-3 flex-1">
                      {shortcut.description}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-6 p-4 bg-secondary/30 rounded-lg border border-border/50">
          <h4 className="text-sm font-medium mb-2">💡 Pro Tips</h4>
          <ul className="text-xs text-muted-foreground space-y-1">
            <li>• Use Tab to flow through elements naturally: Scene → Action → Character → Dialogue</li>
            <li>• Press Enter on empty elements to cycle through common types</li>
            <li>• Character names are automatically capitalized</li>
            <li>• The editor suggests next elements based on your content</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default KeyboardShortcutsPanel;
