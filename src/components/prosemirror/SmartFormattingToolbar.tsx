
import React from 'react';
import { EditorView } from 'prosemirror-view';
import CurrentElementBadge from './CurrentElementBadge';
import SmartSuggestions from './SmartSuggestions';
import ElementButtons from './ElementButtons';
import { useSmartSuggestions } from './hooks/useSmartSuggestions';

interface SmartFormattingToolbarProps {
  editorView: EditorView | null;
  className?: string;
}

const SmartFormattingToolbar: React.FC<SmartFormattingToolbarProps> = ({
  editorView,
  className = ''
}) => {
  const {
    currentElement,
    suggestions,
    allElements,
    getCurrentElementInfo
  } = useSmartSuggestions(editorView);

  const executeCommand = (command: any) => {
    if (editorView) {
      command(editorView.state, editorView.dispatch);
      editorView.focus();
    }
  };

  return (
    <div className={`flex items-center justify-between bg-slate-900/90 backdrop-blur-sm border-b border-slate-700 p-3 ${className}`}>
      {/* Current Context */}
      <CurrentElementBadge elementInfo={getCurrentElementInfo()} />

      {/* Smart Suggestions */}
      <SmartSuggestions 
        suggestions={suggestions}
        onExecuteCommand={executeCommand}
      />

      {/* All Elements (Quick Access) */}
      <ElementButtons
        elements={allElements}
        currentElement={currentElement}
        onExecuteCommand={executeCommand}
      />
    </div>
  );
};

export default SmartFormattingToolbar;
