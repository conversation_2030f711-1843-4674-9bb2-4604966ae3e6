
import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ElementSuggestion } from './SmartSuggestions';

interface ElementButtonsProps {
  elements: ElementSuggestion[];
  currentElement: string;
  onExecuteCommand: (command: any) => void;
}

const ElementButtons: React.FC<ElementButtonsProps> = ({
  elements,
  currentElement,
  onExecuteCommand
}) => {
  return (
    <div className="flex items-center space-x-1">
      <Separator orientation="vertical" className="h-6 bg-slate-600" />
      <div className="flex items-center space-x-1">
        {elements.map((element) => {
          const Icon = element.icon;
          const isActive = element.label.toLowerCase().replace(' ', '_') === currentElement;
          
          return (
            <Button
              key={element.label}
              variant="ghost"
              size="sm"
              onClick={() => onExecuteCommand(element.command)}
              className={`h-8 w-8 p-0 transition-all ${
                isActive 
                  ? 'bg-blue-500/20 text-blue-300 border border-blue-500/50' 
                  : 'hover:bg-slate-700 text-slate-400'
              }`}
              title={`${element.label} (${element.shortcut})`}
            >
              <Icon className="h-4 w-4" />
            </Button>
          );
        })}
      </div>
    </div>
  );
};

export default ElementButtons;
