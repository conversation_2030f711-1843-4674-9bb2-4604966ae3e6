
import React from 'react';

interface ScriptEditorStatsProps {
  wordCount: number;
  lastSaved: Date | null;
  sceneId?: string;
}

const ScriptEditorStats: React.FC<ScriptEditorStatsProps> = ({
  wordCount,
  lastSaved,
  sceneId
}) => {
  return (
    <div className="border-t border-cinema-700 p-4 bg-cinema-800/20">
      <div className="flex items-center justify-between text-sm text-cinema-400">
        <div className="flex items-center space-x-4">
          <span>Words: {wordCount.toLocaleString()}</span>
          <span>Pages: ~{Math.ceil(wordCount / 250)}</span>
          {lastSaved && (
            <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
          )}
          {sceneId && <span>Scene ID: {sceneId}</span>}
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Ready</span>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditorStats;
