
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Lightbulb, Zap } from 'lucide-react';

export interface ElementSuggestion {
  icon: React.ElementType;
  command: any;
  label: string;
  shortcut: string;
  description: string;
  priority: number;
}

interface SmartSuggestionsProps {
  suggestions: ElementSuggestion[];
  onExecuteCommand: (command: any) => void;
}

const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  suggestions,
  onExecuteCommand
}) => {
  if (suggestions.length === 0) return null;

  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center space-x-1">
        <Lightbulb className="h-4 w-4 text-yellow-500" />
        <span className="text-xs text-slate-400 hidden sm:block">Next:</span>
      </div>
      {suggestions.slice(0, 3).map((suggestion, index) => {
        const Icon = suggestion.icon;
        return (
          <Button
            key={suggestion.label}
            variant="ghost"
            size="sm"
            onClick={() => onExecuteCommand(suggestion.command)}
            className={`h-8 px-3 text-xs hover:bg-slate-700 border transition-all ${
              index === 0 
                ? 'border-blue-500/50 bg-blue-500/10 text-blue-300' 
                : 'border-slate-600 text-slate-300'
            }`}
            title={`${suggestion.label} (${suggestion.shortcut})`}
          >
            <Icon className="h-3 w-3 mr-1" />
            {suggestion.label}
            {index === 0 && <Zap className="h-3 w-3 ml-1 text-yellow-500" />}
          </Button>
        );
      })}
    </div>
  );
};

export default SmartSuggestions;
