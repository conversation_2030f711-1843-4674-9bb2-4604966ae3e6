
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Bold, 
  Italic, 
  Underline, 
  Type, 
  MessageSquare,
  User,
  FileText,
  ArrowRight,
  Heading1
} from 'lucide-react';
import { EditorView } from 'prosemirror-view';
import {
  insertSceneHeading,
  insertAction,
  insertCharacter,
  insertDialogue,
  insertParenthetical,
  insertTransition,
  toggleBold,
  toggleItalic,
  toggleUnderline
} from '@/lib/prosemirror/commands';

interface FormattingToolbarProps {
  editorView: EditorView | null;
  className?: string;
}

const FormattingToolbar: React.FC<FormattingToolbarProps> = ({
  editorView,
  className = ''
}) => {
  const executeCommand = (command: any) => {
    if (editorView) {
      command(editorView.state, editorView.dispatch);
      editorView.focus();
    }
  };

  const formatButtons = [
    { icon: Bold, command: toggleBold, label: 'Bold', shortcut: 'Cmd+B' },
    { icon: Italic, command: toggleItalic, label: 'Italic', shortcut: 'Cmd+I' },
    { icon: Underline, command: toggleUnderline, label: 'Underline', shortcut: 'Cmd+U' }
  ];

  const elementButtons = [
    { icon: Heading1, command: insertSceneHeading, label: 'Scene Heading', shortcut: 'Cmd+1' },
    { icon: FileText, command: insertAction, label: 'Action', shortcut: 'Cmd+2' },
    { icon: User, command: insertCharacter, label: 'Character', shortcut: 'Cmd+3' },
    { icon: MessageSquare, command: insertDialogue, label: 'Dialogue', shortcut: 'Cmd+4' },
    { icon: Type, command: insertParenthetical, label: 'Parenthetical', shortcut: 'Cmd+5' },
    { icon: ArrowRight, command: insertTransition, label: 'Transition', shortcut: 'Cmd+6' }
  ];

  return (
    <div className={`flex items-center space-x-1 bg-cinema-800/30 border-b border-cinema-700 p-2 ${className}`}>
      {/* Text Formatting */}
      <div className="flex items-center space-x-1">
        <Badge variant="secondary" className="text-xs bg-cinema-700 text-cinema-300">
          Format
        </Badge>
        {formatButtons.map(({ icon: Icon, command, label, shortcut }) => (
          <Button
            key={label}
            variant="ghost"
            size="sm"
            onClick={() => executeCommand(command)}
            className="h-8 w-8 p-0 hover:bg-cinema-700"
            title={`${label} (${shortcut})`}
          >
            <Icon className="h-4 w-4" />
          </Button>
        ))}
      </div>

      <Separator orientation="vertical" className="h-6 bg-cinema-600" />

      {/* Screenplay Elements */}
      <div className="flex items-center space-x-1">
        <Badge variant="secondary" className="text-xs bg-cinema-700 text-cinema-300">
          Elements
        </Badge>
        {elementButtons.map(({ icon: Icon, command, label, shortcut }) => (
          <Button
            key={label}
            variant="ghost"
            size="sm"
            onClick={() => executeCommand(command)}
            className="h-8 px-2 text-xs hover:bg-cinema-700"
            title={`${label} (${shortcut})`}
          >
            <Icon className="h-3 w-3 mr-1" />
            {label}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default FormattingToolbar;

