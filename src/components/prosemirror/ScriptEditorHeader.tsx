
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Settings, Keyboard } from 'lucide-react';

interface ScriptEditorHeaderProps {
  showShortcuts: boolean;
  onToggleShortcuts: () => void;
}

const ScriptEditorHeader: React.FC<ScriptEditorHeaderProps> = ({
  showShortcuts,
  onToggleShortcuts
}) => {
  return (
    <div className="border-b border-border p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <FileText className="h-4 w-4 text-primary-foreground" />
          </div>
          <div>
            <h2 className="text-lg font-playfair font-semibold gold-gradient">
              Professional Script Editor
            </h2>
            <p className="text-sm text-muted-foreground">Industry-standard screenplay formatting</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="bg-secondary text-secondary-foreground">
            Pro Editor
          </Badge>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onToggleShortcuts}
          >
            <Keyboard className="h-4 w-4 mr-1" />
            Shortcuts
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-1" />
            Settings
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditorHeader;
