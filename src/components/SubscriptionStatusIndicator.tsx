
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { useEnhancedSubscription } from '@/hooks/useEnhancedSubscription';
import { Clock, CheckCircle, AlertCircle, Loader2, Users } from 'lucide-react';

interface SubscriptionStatusIndicatorProps {
  showDebugInfo?: boolean;
  showSeatsInfo?: boolean;
}

const SubscriptionStatusIndicator: React.FC<SubscriptionStatusIndicatorProps> = ({ 
  showDebugInfo = false,
  showSeatsInfo = false
}) => {
  const { subscription, loading, error } = useEnhancedSubscription();

  const getStatusColor = () => {
    if (loading) return 'bg-blue-500';
    if (error) return 'bg-red-500';
    if (subscription?.subscribed) return 'bg-green-500';
    return 'bg-gray-500';
  };

  const getStatusIcon = () => {
    if (loading) return <Loader2 className="w-3 h-3 animate-spin" />;
    if (error) return <AlertCircle className="w-3 h-3" />;
    if (subscription?.subscribed) return <CheckCircle className="w-3 h-3" />;
    return <Clock className="w-3 h-3" />;
  };

  const getStatusText = () => {
    if (loading) return 'Checking...';
    if (error) return 'Error';
    if (subscription?.subscribed && subscription.plan_name) return subscription.plan_name.toUpperCase();
    return 'FREE';
  };

  return (
    <div className="flex items-center gap-2">
      <Badge 
        variant="secondary" 
        className={`${getStatusColor()} text-white flex items-center gap-1`}
      >
        {getStatusIcon()}
        {getStatusText()}
      </Badge>
      
      {/* Show seats info for organization plans */}
      {showSeatsInfo && subscription?.seats_info?.type === 'organization' && (
        <Badge variant="outline" className="flex items-center gap-1">
          <Users className="w-3 h-3" />
          {subscription.seats_info.used}/{subscription.seats_info.total}
        </Badge>
      )}
      
      {showDebugInfo && (
        <div className="text-xs text-muted-foreground">
          Status: {subscription?.status || 'Unknown'}
          {subscription?.seats_info && (
            <div className="mt-1">
              Seats: {subscription.seats_info.used}/{subscription.seats_info.total} ({subscription.seats_info.type})
            </div>
          )}
          {error && (
            <div className="text-red-500 mt-1">
              Error: {error}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SubscriptionStatusIndicator;
