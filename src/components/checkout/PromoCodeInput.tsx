import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Sparkles, 
  Check, 
  X, 
  Loader2,
  Tag,
  AlertTriangle,
  Gift
} from 'lucide-react';
import { usePromoCode } from '@/hooks/usePromoCode';
import { TIER_PRICING } from '@/lib/promo/promoCodeService';

type TierType = keyof typeof TIER_PRICING;

interface PromoCodeInputProps {
  tier: TierType;
  onPromoApplied?: (application: any) => void;
  onPromoRemoved?: () => void;
  className?: string;
}

export function PromoCodeInput({ 
  tier, 
  onPromoApplied, 
  onPromoRemoved, 
  className 
}: PromoCodeInputProps) {
  const [inputValue, setInputValue] = useState('');
  const [showInput, setShowInput] = useState(false);
  
  const {
    promoCode,
    isValidating,
    isApplying,
    validation,
    application,
    setPromoCode,
    validateCode,
    applyCode,
    clearCode,
    formatPrice,
    getTierPrice,
    calculateSavings,
  } = usePromoCode();

  // Auto-validate when input changes
  useEffect(() => {
    if (promoCode && promoCode.length >= 4) {
      const timeoutId = setTimeout(() => {
        validateCode(tier);
      }, 500);
      
      return () => clearTimeout(timeoutId);
    }
  }, [promoCode, tier, validateCode]);

  // Notify parent when promo is applied
  useEffect(() => {
    if (application) {
      onPromoApplied?.(application);
    }
  }, [application, onPromoApplied]);

  const handleInputChange = (value: string) => {
    const upperValue = value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    setInputValue(upperValue);
    setPromoCode(upperValue);
  };

  const handleApplyCode = async () => {
    if (!validation?.valid) {
      await validateCode(tier);
      return;
    }
    
    const result = await applyCode(tier);
    if (result) {
      setShowInput(false);
    }
  };

  const handleRemoveCode = () => {
    clearCode();
    setInputValue('');
    setShowInput(false);
    onPromoRemoved?.();
  };

  const originalPrice = getTierPrice(tier);
  const savings = application ? calculateSavings(originalPrice, application.finalPrice) : null;

  // If promo is applied, show the applied state
  if (application) {
    return (
      <Card className={`border-green-200 bg-green-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 p-2 rounded-full">
                <Gift className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-green-800">Promo Code Applied</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    {application.promoCode}
                  </Badge>
                </div>
                <div className="text-sm text-green-600">
                  You saved {savings?.formattedSavings} ({savings?.savingsPercentage}% off)
                </div>
              </div>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRemoveCode}
              className="text-green-700 hover:text-green-800"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="mt-3 pt-3 border-t border-green-200">
            <div className="flex justify-between items-center">
              <span className="text-sm text-green-700">Original Price:</span>
              <span className="text-sm line-through text-green-600">
                {formatPrice(application.originalPrice)}
              </span>
            </div>
            <div className="flex justify-between items-center font-medium">
              <span className="text-green-800">Final Price:</span>
              <span className="text-lg text-green-800">
                {formatPrice(application.finalPrice)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If input is shown, show the input form
  if (showInput) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Tag className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Enter Promo Code</span>
            </div>
            
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  value={inputValue}
                  onChange={(e) => handleInputChange(e.target.value)}
                  placeholder="Enter code (e.g., BETA123456)"
                  className={`uppercase ${
                    validation?.valid === false ? 'border-red-500' : 
                    validation?.valid === true ? 'border-green-500' : ''
                  }`}
                  disabled={isValidating || isApplying}
                />
                
                {/* Validation feedback */}
                {validation && (
                  <div className="mt-1 flex items-center space-x-1">
                    {validation.valid ? (
                      <>
                        <Check className="h-3 w-3 text-green-500" />
                        <span className="text-xs text-green-600">
                          {validation.discountPercentage}% off • Valid for {tier}
                        </span>
                      </>
                    ) : (
                      <>
                        <X className="h-3 w-3 text-red-500" />
                        <span className="text-xs text-red-600">
                          {validation.error}
                        </span>
                      </>
                    )}
                  </div>
                )}
              </div>
              
              <Button
                onClick={handleApplyCode}
                disabled={!promoCode || isValidating || isApplying || !validation?.valid}
                size="sm"
              >
                {isValidating || isApplying ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Apply'
                )}
              </Button>
            </div>
            
            <div className="flex justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInput(false)}
                className="text-gray-500"
              >
                Cancel
              </Button>
              
              {validation?.valid && (
                <div className="text-sm text-green-600">
                  Save {validation.discountPercentage}% on your {tier} plan
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Default state - show the "Have a promo code?" button
  return (
    <Card className={`border-dashed border-2 border-gray-200 hover:border-gray-300 transition-colors ${className}`}>
      <CardContent className="p-4">
        <Button
          variant="ghost"
          onClick={() => setShowInput(true)}
          className="w-full flex items-center justify-center space-x-2 text-gray-600 hover:text-gray-800"
        >
          <Sparkles className="h-4 w-4" />
          <span>Have a promo code?</span>
        </Button>
        
        <div className="mt-2 text-center">
          <p className="text-xs text-gray-500">
            Enter your beta access code for exclusive discounts
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

export default PromoCodeInput;
