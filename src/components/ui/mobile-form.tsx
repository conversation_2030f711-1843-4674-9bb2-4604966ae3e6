
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';

interface MobileFormProps {
  children: React.ReactNode;
  className?: string;
  onSubmit?: (e: React.FormEvent) => void;
  autoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (formData: FormData) => void;
}

export const MobileForm: React.FC<MobileFormProps> = ({
  children,
  className,
  onSubmit,
  autoSave = false,
  autoSaveDelay = 2000,
  onAutoSave
}) => {
  const isMobile = useIsMobile();
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!autoSave || !isDirty || !onAutoSave) return;

    // Clear existing timer
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
    }

    // Set new timer
    const timer = setTimeout(() => {
      const form = document.querySelector('form') as HTMLFormElement;
      if (form) {
        const formData = new FormData(form);
        onAutoSave(formData);
        setLastSaved(new Date());
        setIsDirty(false);
      }
    }, autoSaveDelay);

    setAutoSaveTimer(timer);

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isDirty, autoSave, autoSaveDelay, onAutoSave]);

  const handleFormChange = () => {
    setIsDirty(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsDirty(false);
    onSubmit?.(e);
  };

  return (
    <div className={cn("relative", className)}>
      {autoSave && (
        <div className="flex items-center justify-between mb-4 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <div className={cn(
              "w-2 h-2 rounded-full transition-colors",
              isDirty ? "bg-yellow-500 animate-pulse" : "bg-green-500"
            )} />
            <span>
              {isDirty ? 'Unsaved changes' : lastSaved ? `Saved ${lastSaved.toLocaleTimeString()}` : 'Auto-save enabled'}
            </span>
          </div>
        </div>
      )}
      
      <form
        onSubmit={handleSubmit}
        onChange={handleFormChange}
        className={cn(
          "space-y-4",
          isMobile && [
            "space-y-6", // Increased spacing on mobile
            "[&_input]:min-h-[44px]", // Touch-friendly input height
            "[&_textarea]:min-h-[88px]", // Larger textarea on mobile
            "[&_button]:min-h-[44px]", // Touch-friendly button height
            "[&_select]:min-h-[44px]", // Touch-friendly select height
          ]
        )}
      >
        {children}
      </form>
    </div>
  );
};

export default MobileForm;
