
import React from 'react';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Clock, CheckCircle, XCircle, Loader } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProgressIndicatorProps {
  progress: number;
  status: string;
  jobId: string;
  estimatedTimeRemaining?: number;
  className?: string;
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  status,
  jobId,
  estimatedTimeRemaining,
  className
}) => {
  const getStatusIcon = () => {
    if (progress === 100) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (progress === 0 && status.includes('Failed')) return <XCircle className="h-5 w-5 text-red-500" />;
    return <Loader className="h-5 w-5 text-blue-500 animate-spin" />;
  };

  const getStatusColor = () => {
    if (progress === 100) return 'bg-green-500/10 text-green-400 border-green-400/30';
    if (progress === 0 && status.includes('Failed')) return 'bg-red-500/10 text-red-400 border-red-400/30';
    return 'bg-blue-500/10 text-blue-400 border-blue-400/30';
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${Math.round(remainingSeconds)}s`;
  };

  return (
    <Card className={cn("p-4 border-cinema-700 bg-cinema-800/50", className)}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">Coverage Analysis</span>
          </div>
          <Badge variant="secondary" className={getStatusColor()}>
            {progress}%
          </Badge>
        </div>

        <Progress value={progress} className="h-2" />

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">{status}</p>
          
          {estimatedTimeRemaining && estimatedTimeRemaining > 0 && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>Est. {formatTime(estimatedTimeRemaining)} remaining</span>
            </div>
          )}

          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Job ID: {jobId.slice(-8)}</span>
            {progress > 0 && progress < 100 && (
              <span className="animate-pulse">Processing...</span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};
