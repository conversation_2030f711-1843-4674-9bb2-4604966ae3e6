
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface EnhancedSkeletonProps {
  variant?: 'card' | 'list' | 'dashboard' | 'form' | 'text' | 'avatar';
  count?: number;
  className?: string;
  animate?: boolean;
}

export const EnhancedSkeleton: React.FC<EnhancedSkeletonProps> = ({
  variant = 'text',
  count = 1,
  className,
  animate = true
}) => {
  const baseClass = cn(
    "rounded-md bg-muted",
    animate && "animate-pulse",
    className
  );

  const renderVariant = () => {
    switch (variant) {
      case 'card':
        return (
          <div className={cn("p-4 space-y-3 border rounded-lg", baseClass)}>
            <div className="flex items-center space-x-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
            <Skeleton className="h-20 w-full" />
            <div className="flex justify-between">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-16" />
            </div>
          </div>
        );

      case 'list':
        return (
          <div className={cn("flex items-center space-x-3 p-3", baseClass)}>
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-3 w-1/3" />
            </div>
            <Skeleton className="h-6 w-16" />
          </div>
        );

      case 'dashboard':
        return (
          <div className={cn("space-y-4", className)}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className={cn("p-4 border rounded-lg", baseClass)}>
                  <div className="flex items-center justify-between mb-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-8 w-8 rounded" />
                  </div>
                  <Skeleton className="h-8 w-16" />
                </div>
              ))}
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div className={cn("p-4 border rounded-lg", baseClass)}>
                <Skeleton className="h-6 w-32 mb-4" />
                <div className="space-y-3">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              </div>
              <div className={cn("p-4 border rounded-lg", baseClass)}>
                <Skeleton className="h-6 w-40 mb-4" />
                <Skeleton className="h-48 w-full" />
              </div>
            </div>
          </div>
        );

      case 'form':
        return (
          <div className={cn("space-y-4", className)}>
            <div className="space-y-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-10 w-full" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-20 w-full" />
            </div>
            <Skeleton className="h-10 w-24" />
          </div>
        );

      case 'avatar':
        return <Skeleton className={cn("h-10 w-10 rounded-full", baseClass)} />;

      default:
        return <Skeleton className={cn("h-4 w-full", baseClass)} />;
    }
  };

  return (
    <div>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className={i > 0 ? 'mt-4' : ''}>
          {renderVariant()}
        </div>
      ))}
    </div>
  );
};

export default EnhancedSkeleton;
