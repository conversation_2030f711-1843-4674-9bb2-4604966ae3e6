import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useCharacters } from '@/hooks/useCharacters';
import { useScenes } from '@/hooks/useScenes';
import { scenesApi, revisionsApi } from '@/lib/api';
import PDFImportButton from '@/components/pdf-import/PDFImportButton';
import { usePDFImport } from '@/hooks/usePDFImport';
import { ScreenplayElement } from '@/lib/pdf/pdfProcessor';

interface ScriptLine {
  id: string;
  type: 'action' | 'character' | 'dialogue' | 'transition' | 'scene';
  content: string;
  characterName?: string;
}

interface VirtualizedScriptEditorProps {
  className?: string;
  sceneId?: string;
  onSceneSelect?: (sceneId: string) => void;
}

const VirtualizedScriptEditor: React.FC<VirtualizedScriptEditorProps> = ({ 
  className,
  sceneId,
  onSceneSelect 
}) => {
  const { characters } = useCharacters();
  const { scenes } = useScenes();
  const [lines, setLines] = useState<ScriptLine[]>([]);
  const [currentLine, setCurrentLine] = useState('');
  const [currentType, setCurrentType] = useState<ScriptLine['type']>('action');
  const [characterName, setCharacterName] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Virtual scrolling implementation
  const ITEM_HEIGHT = 60;
  const VISIBLE_ITEMS = 10;
  const [scrollTop, setScrollTop] = useState(0);

  const visibleLines = useMemo(() => {
    const startIndex = Math.floor(scrollTop / ITEM_HEIGHT);
    const endIndex = Math.min(startIndex + VISIBLE_ITEMS, lines.length);
    
    return lines.slice(startIndex, endIndex).map((line, index) => ({
      ...line,
      virtualIndex: startIndex + index
    }));
  }, [lines, scrollTop]);

  // SmartType autocomplete
  const handleInputChange = useCallback((value: string) => {
    setCurrentLine(value);
    
    if (currentType === 'character' || (currentType === 'dialogue' && !characterName)) {
      const lastWord = value.split(' ').pop()?.toLowerCase() || '';
      if (lastWord.length > 1) {
        const matchingCharacters = characters
          .filter(char => char.name.toLowerCase().includes(lastWord))
          .map(char => char.name)
          .slice(0, 5);
        
        if (matchingCharacters.length > 0) {
          setSuggestions(matchingCharacters);
          setShowSuggestions(true);
        } else {
          setShowSuggestions(false);
        }
      } else {
        setShowSuggestions(false);
      }
    }
  }, [currentType, characterName, characters]);

  const selectSuggestion = useCallback((suggestion: string) => {
    if (currentType === 'character') {
      setCurrentLine(suggestion);
    } else if (currentType === 'dialogue') {
      setCharacterName(suggestion);
    }
    setShowSuggestions(false);
    inputRef.current?.focus();
  }, [currentType]);

  const addLine = useCallback(async () => {
    if (!currentLine.trim()) return;

    const newLine: ScriptLine = {
      id: `line-${Date.now()}`,
      type: currentType,
      content: currentLine,
      characterName: currentType === 'dialogue' ? characterName : undefined
    };

    const updatedLines = [...lines, newLine];
    setLines(updatedLines);
    
    // Save to database if we have a scene
    if (sceneId) {
      const content = updatedLines
        .map(line => {
          if (line.type === 'dialogue' && line.characterName) {
            return `${line.characterName}\n${line.content}`;
          }
          return line.content;
        })
        .join('\n\n');
      
      try {
        await scenesApi.updateScene(sceneId, { content });
        // Create revision
        await revisionsApi.createRevision({
          scene_id: sceneId,
          content,
          change_summary: `Added ${currentType} line`
        });
      } catch (error) {
        console.error('Error saving scene content:', error);
      }
    }

    setCurrentLine('');
    setCharacterName('');
  }, [currentLine, currentType, characterName, lines, sceneId]);

  const getLineStyle = (type: ScriptLine['type']) => {
    switch (type) {
      case 'character':
        return 'text-center uppercase font-bold tracking-wide';
      case 'dialogue':
        return 'ml-8 mr-16';
      case 'action':
        return 'mx-4';
      case 'transition':
        return 'text-right uppercase font-bold mr-4';
      case 'scene':
        return 'uppercase font-bold tracking-wide border-b border-muted pb-2';
      default:
        return '';
    }
  };

  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  };

  const { handlePDFImport } = usePDFImport({
    onContentChange: (content: string) => {
      // Parse content into script lines
      const contentLines = content.split('\n').filter(line => line.trim());
      const newLines: ScriptLine[] = contentLines.map((line, index) => ({
        id: `imported_${Date.now()}_${index}`,
        type: 'action', // Default type
        content: line.trim()
      }));
      setLines(newLines);
    },
    onElementsChange: (elements: ScreenplayElement[]) => {
      // Convert screenplay elements to script lines
      const newLines: ScriptLine[] = elements.map((element, index) => ({
        id: `element_${Date.now()}_${index}`,
        type: element.type === 'scene_heading' ? 'scene' :
              element.type === 'character' ? 'character' :
              element.type === 'dialogue' ? 'dialogue' :
              element.type === 'parenthetical' ? 'action' :
              element.type === 'transition' ? 'transition' : 'action',
        content: element.content,
        characterName: element.character
      }));
      setLines(newLines);
    }
  });

  return (
    <Card className={cn("cinema-card p-6", className)}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-playfair font-semibold">Script Editor</h2>
          <div className="flex items-center space-x-2">
            <PDFImportButton
              onImport={handlePDFImport}
              variant="outline"
              size="sm"
            />
            {scenes.length > 0 && (
              <div className="flex items-center space-x-2">
                <Label htmlFor="scene-select">Scene:</Label>
                <select
                  id="scene-select"
                  value={sceneId || ''}
                  onChange={(e) => onSceneSelect?.(e.target.value)}
                  className="px-3 py-1 bg-cinema-800 border border-cinema-700 rounded-md text-sm"
                >
                  <option value="">Select a scene</option>
                  {scenes.map(scene => (
                    <option key={scene.id} value={scene.id}>
                      {scene.act && `Act ${scene.act} - `}{scene.title}
                    </option>
                  ))}
                </select>
              </div>
            )}
          </div>
        </div>
        
        {/* Input Section */}
        <div className="space-y-3 border-b border-border pb-4">
          <div className="flex space-x-2">
            <div className="flex-1">
              <Label htmlFor="line-type">Type</Label>
              <select
                id="line-type"
                value={currentType}
                onChange={(e) => setCurrentType(e.target.value as ScriptLine['type'])}
                className="w-full px-3 py-2 bg-cinema-800 border border-cinema-700 rounded-md"
              >
                <option value="scene">Scene Heading</option>
                <option value="action">Action</option>
                <option value="character">Character</option>
                <option value="dialogue">Dialogue</option>
                <option value="transition">Transition</option>
              </select>
            </div>
            {currentType === 'dialogue' && (
              <div className="flex-1 relative">
                <Label htmlFor="character-name">Character</Label>
                <Input
                  ref={inputRef}
                  id="character-name"
                  value={characterName}
                  onChange={(e) => {
                    setCharacterName(e.target.value);
                    handleInputChange(e.target.value);
                  }}
                  placeholder="Character name"
                  className="bg-cinema-800 border-cinema-700"
                />
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute top-full left-0 right-0 z-10 bg-cinema-800 border border-cinema-700 rounded-md mt-1 max-h-32 overflow-y-auto">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => selectSuggestion(suggestion)}
                        className="w-full px-3 py-2 text-left hover:bg-cinema-700 text-sm"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <Input
                value={currentLine}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Type your line here..."
                className="bg-cinema-800 border-cinema-700"
                onKeyPress={(e) => e.key === 'Enter' && addLine()}
              />
              {currentType === 'character' && showSuggestions && suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 z-10 bg-cinema-800 border border-cinema-700 rounded-md mt-1 max-h-32 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => selectSuggestion(suggestion)}
                      className="w-full px-3 py-2 text-left hover:bg-cinema-700 text-sm"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
            <Button onClick={addLine} className="bg-primary hover:bg-primary/90">
              Add Line
            </Button>
          </div>
        </div>

        {/* Virtualized Script Display */}
        <ScrollArea 
          className="h-96 border border-cinema-700 rounded-md p-4"
          onScrollCapture={handleScroll}
        >
          <div 
            style={{ 
              height: lines.length * ITEM_HEIGHT,
              position: 'relative'
            }}
          >
            {visibleLines.map((line) => (
              <div
                key={line.id}
                className={cn(
                  "absolute w-full py-2 text-sm",
                  getLineStyle(line.type)
                )}
                style={{
                  top: line.virtualIndex * ITEM_HEIGHT,
                  height: ITEM_HEIGHT
                }}
              >
                {line.type === 'dialogue' && line.characterName && (
                  <div className="text-center uppercase font-bold mb-1">
                    {line.characterName}
                  </div>
                )}
                <div className="whitespace-pre-wrap">{line.content}</div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="text-xs text-muted-foreground flex justify-between">
          <span>{lines.length} lines • Virtual scrolling enabled • SmartType autocomplete active</span>
          {sceneId && <span>Auto-saving to scene</span>}
        </div>
      </div>
    </Card>
  );
};

export default VirtualizedScriptEditor;
