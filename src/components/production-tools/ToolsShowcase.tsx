
import React from 'react';
import { Card } from '@/components/ui/card';
import { Calendar, Users, FileText, DollarSign, MapPin, Camera } from 'lucide-react';

const ToolsShowcase: React.FC = () => {
  const tools = [
    {
      icon: <Calendar className="h-6 w-6 text-blue-500" />,
      title: "Advanced Scheduling",
      description: "Intelligent scheduling with conflict detection, weather considerations, and automatic optimization.",
      features: ["Drag-and-drop interface", "Conflict resolution", "Weather integration", "Multi-location support"]
    },
    {
      icon: <Users className="h-6 w-6 text-green-500" />,
      title: "Crew Management",
      description: "Comprehensive crew tracking with availability, skills matching, and performance analytics.",
      features: ["Skills database", "Availability tracking", "Performance metrics", "Contact management"]
    },
    {
      icon: <FileText className="h-6 w-6 text-purple-500" />,
      title: "Call Sheets & Reports",
      description: "Professional call sheets, wrap reports, and production documentation with smart templates.",
      features: ["Auto-generated call sheets", "Daily reports", "Custom templates", "Digital signatures"]
    },
    {
      icon: <DollarSign className="h-6 w-6 text-yellow-500" />,
      title: "Budget Tracking",
      description: "Real-time budget monitoring with cost forecasting and department-wise expense tracking.",
      features: ["Real-time tracking", "Cost forecasting", "Department budgets", "Expense approvals"]
    },
    {
      icon: <MapPin className="h-6 w-6 text-red-500" />,
      title: "Location Management",
      description: "Centralized location database with permits, contracts, and accessibility information.",
      features: ["Location database", "Permit tracking", "Contact details", "Accessibility info"]
    },
    {
      icon: <Camera className="h-6 w-6 text-teal-500" />,
      title: "Equipment Tracking",
      description: "Complete equipment inventory with maintenance schedules and usage analytics.",
      features: ["Inventory management", "Maintenance tracking", "Usage analytics", "Rental coordination"]
    }
  ];

  return (
    <section className="py-24">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
            Professional <span className="gold-gradient">Production Tools</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Everything you need to manage a professional production, from indie films to major studio projects.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {tools.map((tool, index) => (
            <Card 
              key={index} 
              className="cinema-card p-8 hover:scale-105 transition-all duration-300 group animate-fade-scale"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="mb-4 group-hover:scale-110 transition-transform">
                {tool.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 font-playfair">{tool.title}</h3>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                {tool.description}
              </p>
              <ul className="space-y-2">
                {tool.features.map((feature, i) => (
                  <li key={i} className="text-sm flex items-center">
                    <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ToolsShowcase;
