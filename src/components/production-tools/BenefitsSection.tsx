
import React from 'react';

const BenefitsSection: React.FC = () => {
  const benefits = [
    "Reduce scheduling conflicts by 85%",
    "Save 3-5 hours daily on administrative tasks",
    "Improve crew satisfaction with better communication",
    "Track budget variances in real-time",
    "Ensure compliance with industry standards",
    "Streamline location and equipment management"
  ];

  return (
    <section className="py-24 bg-muted/10">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
              Proven <span className="gold-gradient">Results</span>
            </h2>
            <p className="text-xl text-muted-foreground">
              Productions using our tools report significant improvements in efficiency and cost management.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center space-x-3 p-4 bg-background rounded-lg border">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white text-sm">✓</span>
                </div>
                <span className="text-foreground font-medium">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
