
import React from 'react';
import { Card } from '@/components/ui/card';

const WorkflowsSection: React.FC = () => {
  const workflows = [
    {
      title: "Pre-Production",
      description: "Streamline your prep with integrated planning tools",
      color: "bg-blue-500"
    },
    {
      title: "Production",
      description: "Real-time coordination and communication during shoot",
      color: "bg-green-500"
    },
    {
      title: "Post-Production",
      description: "Wrap reports and asset management for post",
      color: "bg-purple-500"
    }
  ];

  return (
    <section className="py-16 bg-muted/20">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
            Complete <span className="gold-gradient">Production Workflow</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Integrated tools that work together seamlessly across all phases of production.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {workflows.map((workflow, index) => (
            <Card key={index} className="cinema-card p-8 text-center hover:scale-105 transition-all duration-300">
              <div className={`w-16 h-16 ${workflow.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                <span className="text-2xl font-bold text-white">{index + 1}</span>
              </div>
              <h3 className="text-xl font-semibold mb-3 font-playfair">{workflow.title}</h3>
              <p className="text-muted-foreground">{workflow.description}</p>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WorkflowsSection;
