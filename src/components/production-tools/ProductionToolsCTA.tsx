
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';

interface ProductionToolsCTAProps {
  user: any;
  onGetStarted: () => void;
}

const ProductionToolsCTA: React.FC<ProductionToolsCTAProps> = ({ user, onGetStarted }) => {
  return (
    <section className="py-24 relative overflow-hidden">
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-accent rounded-full blur-3xl animate-float delay-200"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-playfair font-bold mb-6 animate-slide-up">
            Ready to <span className="gold-gradient">Streamline</span> Your Production?
          </h2>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-slide-up delay-100">
            Join productions worldwide that trust our tools to deliver on time and on budget.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xl px-12 py-6 group animate-glow"
              onClick={onGetStarted}
            >
              {user ? 'Access Production Dashboard' : 'Start Free Trial'}
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-xl px-12 py-6">
              Schedule Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductionToolsCTA;
