
import React from 'react';
import { useHealthCheck } from '@/hooks/useHealthCheck';
import { productionConfig } from '@/utils/productionConfig';

interface HealthIndicatorProps {
  showDetails?: boolean;
  className?: string;
}

export function HealthIndicator({ showDetails = false, className = '' }: HealthIndicatorProps) {
  const { healthStatus, isLoading, error } = useHealthCheck();

  if (!productionConfig.isFeatureEnabled('performanceMonitoring')) {
    return null;
  }

  if (isLoading && !healthStatus) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
        <span className="text-sm text-gray-600">Checking...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
        <span className="text-sm text-red-600">Health check failed</span>
      </div>
    );
  }

  if (!healthStatus) return null;

  const statusColors = {
    healthy: 'bg-green-500',
    degraded: 'bg-yellow-500',
    unhealthy: 'bg-red-500'
  };

  const statusLabels = {
    healthy: 'All systems operational',
    degraded: 'Some issues detected',
    unhealthy: 'System issues'
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${statusColors[healthStatus.status]}`}></div>
      <span className="text-sm text-gray-600">
        {statusLabels[healthStatus.status]}
      </span>
      
      {showDetails && (
        <div className="text-xs text-gray-500">
          <span>DB: {healthStatus.services.database}</span>
          <span className="mx-1">•</span>
          <span>Auth: {healthStatus.services.auth}</span>
          <span className="mx-1">•</span>
          <span>Storage: {healthStatus.services.storage}</span>
        </div>
      )}
    </div>
  );
}
