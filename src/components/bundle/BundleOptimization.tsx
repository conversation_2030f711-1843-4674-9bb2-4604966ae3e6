
import React, { Suspense, lazy } from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';

// Lazy load heavy components for better bundle splitting
const LazyStoryboardStudio = lazy(() => import('@/components/StoryboardStudio'));
const LazyCoverageGenerator = lazy(() => import('@/components/CoverageGenerator'));
const LazyProductionTools = lazy(() => import('@/features/production/ProductionTools'));
const LazyScreenplayMarketplace = lazy(() => import('@/features/marketplace/ScreenplayMarketplace'));

interface BundleOptimizedComponentProps {
  component: 'storyboard' | 'coverage' | 'production' | 'marketplace';
  [key: string]: any;
}

const BundleOptimizedComponent: React.FC<BundleOptimizedComponentProps> = ({ component, ...props }) => {
  const componentMap = {
    storyboard: LazyStoryboardStudio,
    coverage: LazyCoverageGenerator,
    production: LazyProductionTools,
    marketplace: LazyScreenplayMarketplace,
  };

  const Component = componentMap[component];

  if (!Component) {
    return <div>Component not found</div>;
  }

  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Component {...props} />
    </Suspense>
  );
};

export default BundleOptimizedComponent;
