
import React from 'react';
import StoryboardStudioMain from './storyboard/StoryboardStudioMain';
import { useSubscriptionAccess } from '@/hooks/useSubscriptionAccess';
import { Card } from '@/components/ui/card';

const ALLOWED_TIERS = ['pro-team', 'studio', 'enterprise'];

const StoryboardStudio = ({ className }: { className?: string }) => {
  const { loading, tier, subscribed } = useSubscriptionAccess();

  if (loading) {
    return (
      <Card className="p-8 text-center">
        <div className="animate-pulse">Checking your access...</div>
      </Card>
    );
  }

  // If not subscribed, or on a disallowed tier, hide the studio
  if (
    !subscribed ||
    !tier ||
    !ALLOWED_TIERS.includes(tier)
  ) {
    return (
      <Card className="p-8 text-center max-w-xl mx-auto">
        <h2 className="text-xl font-bold mb-2">Storyboard Studio Access Restricted</h2>
        <p className="mb-2 text-muted-foreground">
          The Storyboard Studio is available on Pro Team, Studio, and Enterprise plans.
        </p>
        <div className="mb-4 text-sm">
          Upgrade your plan to unlock advanced visual storyboarding and shot design tools.
        </div>
        <a href="/pricing">
          <button className="bg-primary text-primary-foreground px-4 py-2 rounded hover:bg-primary/80 transition">
            View Plans
          </button>
        </a>
      </Card>
    );
  }

  return <StoryboardStudioMain className={className} />;
};

export default StoryboardStudio;
