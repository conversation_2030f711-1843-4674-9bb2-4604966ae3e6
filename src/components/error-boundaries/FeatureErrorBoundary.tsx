import React, { ReactNode } from 'react';
import ErrorBoundary from '../ErrorBoundary';

interface FeatureErrorBoundaryProps {
  children: ReactNode;
  featureName: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error boundary specifically for feature components
 * Provides graceful degradation for non-critical features
 */
export function FeatureErrorBoundary({ 
  children, 
  featureName, 
  onError 
}: FeatureErrorBoundaryProps) {
  return (
    <ErrorBoundary
      level="feature"
      name={featureName}
      onError={onError}
    >
      {children}
    </ErrorBoundary>
  );
}

export default FeatureErrorBoundary;
