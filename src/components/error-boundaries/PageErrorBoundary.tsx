import React, { ReactNode } from 'react';
import ErrorBoundary from '../ErrorBoundary';

interface PageErrorBoundaryProps {
  children: ReactNode;
  pageName: string;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error boundary for entire pages
 * Provides full-page error handling with navigation options
 */
export function PageErrorBoundary({ 
  children, 
  pageName, 
  onError 
}: PageErrorBoundaryProps) {
  return (
    <ErrorBoundary
      level="page"
      name={pageName}
      onError={onError}
    >
      {children}
    </ErrorBoundary>
  );
}

export default PageErrorBoundary;
