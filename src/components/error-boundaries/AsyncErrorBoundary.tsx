import React, { ReactNode, useState, useEffect } from 'react';
import ErrorBoundary from '../ErrorBoundary';

interface AsyncErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

/**
 * Error boundary that can catch async errors
 * Useful for components that perform async operations
 */
export function AsyncErrorBoundary({ 
  children, 
  fallback,
  onError 
}: AsyncErrorBoundaryProps) {
  const [asyncError, setAsyncError] = useState<Error | null>(null);

  // Reset async error when children change
  useEffect(() => {
    setAsyncError(null);
  }, [children]);

  // Throw async error to be caught by ErrorBoundary
  if (asyncError) {
    throw asyncError;
  }

  // Provide error handler for async operations
  const handleAsyncError = (error: Error) => {
    setAsyncError(error);
  };

  return (
    <ErrorBoundary
      level="component"
      name="AsyncComponent"
      onError={onError}
      fallback={fallback}
    >
      <AsyncErrorProvider onError={handleAsyncError}>
        {children}
      </AsyncErrorProvider>
    </ErrorBoundary>
  );
}

/**
 * Context provider for async error handling
 */
const AsyncErrorContext = React.createContext<{
  reportError: (error: Error) => void;
}>({
  reportError: () => {},
});

interface AsyncErrorProviderProps {
  children: ReactNode;
  onError: (error: Error) => void;
}

function AsyncErrorProvider({ children, onError }: AsyncErrorProviderProps) {
  const reportError = (error: Error) => {
    onError(error);
  };

  return (
    <AsyncErrorContext.Provider value={{ reportError }}>
      {children}
    </AsyncErrorContext.Provider>
  );
}

/**
 * Hook to report async errors
 */
export function useAsyncError() {
  const context = React.useContext(AsyncErrorContext);
  
  if (!context) {
    throw new Error('useAsyncError must be used within AsyncErrorBoundary');
  }
  
  return context.reportError;
}

export default AsyncErrorBoundary;
