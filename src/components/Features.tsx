
import { Card } from "@/components/ui/card";
import InlineFAQWidget from '@/components/faq/InlineFAQWidget';

const Features = () => {
  const features = [
    {
      icon: "🤖",
      title: "AI-Powered Writing",
      description: "Let our AI writing assistant help you overcome writer's block and refine your script. Get intelligent suggestions for dialogue and structure, all while maintaining your unique voice.",
      highlights: ["Smart autocomplete", "Context-aware suggestions", "Dialogue refinement"]
    },
    {
      icon: "🎭",
      title: "Story Development Suite",
      description: "Visually map out your narrative. Use our flexible storyboard to structure your scenes, track plot points, and build a solid foundation for your script.",
      highlights: ["Drag-and-drop beat board", "Visual scene planning", "Story structure templates"]
    },
    {
      icon: "👥",
      title: "Team Collaboration",
      description: "Work seamlessly with your writing partners. Share your script, gather feedback, and manage your team, all in one place.",
      highlights: ["Shared workspaces", "Inline comments & suggestions", "Role-based permissions"]
    },
    {
      icon: "🎬",
      title: "Production Tools",
      description: "Bridge the gap between script and screen with integrated tools designed to streamline pre-production planning.",
      highlights: ["Call sheet generation", "Budget templates", "Scheduling utilities"]
    },
    {
      icon: "🏪",
      title: "Script Marketplace",
      description: "Connect directly with industry professionals. Submit your polished scripts to our curated marketplace for a chance to be discovered.",
      highlights: ["Vetted submissions", "Direct producer access", "90/10 revenue split"]
    },
    {
      icon: "📊",
      title: "AI Coverage & Analysis",
      description: "Go beyond surface-level feedback. Get industry-grade script coverage powered by AI that understands narrative structure and dramatic principles.",
      highlights: ["In-depth script coverage", "Structural feedback", "Actionable improvement suggestions"]
    }
  ];

  return (
    <section id="features" className="py-16 relative">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
            Everything You Need to <span className="gold-gradient">Create</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Professional screenwriting tools enhanced with cutting-edge AI, designed for the modern storyteller.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {features.map((feature, index) => (
            <Card 
              key={index} 
              className="cinema-card p-8 hover:scale-105 transition-all duration-300 group animate-fade-scale"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 font-playfair">{feature.title}</h3>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                {feature.description}
              </p>
              <ul className="space-y-2">
                {feature.highlights.map((highlight, i) => (
                  <li key={i} className="text-sm flex items-center">
                    <span className="w-2 h-2 bg-primary rounded-full mr-3"></span>
                    {highlight}
                  </li>
                ))}
              </ul>
            </Card>
          ))}
        </div>

        {/* Inline FAQ Widget for Features */}
        <div className="max-w-2xl mx-auto">
          <InlineFAQWidget 
            category="features" 
            title="Common Questions About Our Features"
            maxItems={4}
          />
        </div>
      </div>
    </section>
  );
};

export default Features;
