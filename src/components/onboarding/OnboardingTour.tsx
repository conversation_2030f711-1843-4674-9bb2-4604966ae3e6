
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { X, ArrowRight, ArrowLeft, Sparkles } from 'lucide-react';
import { useLocalStorage } from '@/hooks/useLocalStorage';

interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  target?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to ScriptGenius! 🎬',
    description: 'Your AI-powered screenwriting companion. Let\'s take a quick tour to get you started.',
  },
  {
    id: 'dashboard',
    title: 'Your Creative Dashboard',
    description: 'This is your home base. Here you can see your recent projects, quick actions, and tool usage.',
  },
  {
    id: 'script-editor',
    title: 'AI-Powered Script Editor',
    description: 'Our advanced editor helps you write professional screenplays with intelligent formatting and AI suggestions.',
  },
  {
    id: 'storyboard-studio',
    title: 'Storyboard Studio',
    description: 'Visualize your scenes with AI-generated storyboard panels. Perfect for planning your shots.',
  },
  {
    id: 'coverage-generator',
    title: 'Coverage Generator',
    description: 'Get professional script coverage and feedback to improve your screenplays.',
  },
  {
    id: 'production-tools',
    title: 'Production Management',
    description: 'Manage schedules, budgets, and resources for your productions.',
  },
];

interface OnboardingTourProps {
  onComplete: () => void;
  onSkip: () => void;
}

const OnboardingTour: React.FC<OnboardingTourProps> = ({ onComplete, onSkip }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setIsVisible(false);
    onComplete();
  };

  const handleSkip = () => {
    setIsVisible(false);
    onSkip();
  };

  const currentStepData = onboardingSteps[currentStep];

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="max-w-md w-full p-6 relative animate-in fade-in-0 scale-in-95 duration-200">
        <button
          onClick={handleSkip}
          className="absolute top-4 right-4 p-1 hover:bg-muted rounded-full transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
        
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="h-5 w-5 text-primary" />
          <Badge variant="outline" className="text-xs">
            Step {currentStep + 1} of {onboardingSteps.length}
          </Badge>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">{currentStepData.title}</h3>
          <p className="text-muted-foreground leading-relaxed">
            {currentStepData.description}
          </p>
        </div>
        
        <div className="flex items-center justify-between mt-6">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Previous
          </Button>
          
          <div className="flex gap-1">
            {onboardingSteps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentStep ? 'bg-primary' : 'bg-muted'
                }`}
              />
            ))}
          </div>
          
          <Button onClick={handleNext} className="flex items-center gap-2">
            {currentStep === onboardingSteps.length - 1 ? 'Get Started' : 'Next'}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="mt-4 text-center">
          <button
            onClick={handleSkip}
            className="text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            Skip tour
          </button>
        </div>
      </Card>
    </div>
  );
};

export default OnboardingTour;
