
import React, { useState } from 'react';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { SecureForm } from '@/components/security/SecureForm';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useOrganization } from '@/contexts/OrganizationContext';
import { SecurityMiddleware } from '@/lib/security/securityMiddleware';

interface PostCreationFormProps {
  onPostCreated: () => void;
}

const PostCreationForm: React.FC<PostCreationFormProps> = ({ onPostCreated }) => {
  const { user } = useAuth();
  const { currentOrganization } = useOrganization();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const handleSecureSubmit = async (formData: FormData, csrfToken: string) => {
    if (!user || !currentOrganization) {
      throw new Error('User authentication required');
    }

    setLoading(true);
    
    try {
      // Validate CSRF token
      const isValidCSRF = await SecurityMiddleware.validateCSRFToken(csrfToken);
      if (!isValidCSRF) {
        throw new Error('Security validation failed');
      }

      const title = formData.get('title') as string;
      const content = formData.get('content') as string;

      if (!title?.trim()) {
        throw new Error('Title is required');
      }

      // Additional validation and sanitization
      const validation = SecurityMiddleware.sanitizeAndValidate({
        title: title.trim(),
        content: content?.trim() || ''
      }, z.object({
        title: z.string().min(1).max(200),
        content: z.string().max(5000)
      }));

      if (!validation.success) {
        throw new Error('Invalid input data');
      }

      const { error } = await supabase
        .from('posts')
        .insert({
          title: validation.data.title,
          content: validation.data.content,
          user_id: user.id,
          org_id: currentOrganization.id
        });

      if (error) throw error;

      onPostCreated();
      
      // Reset form by reloading the component
      window.location.reload();
      
    } catch (error) {
      console.error('Error creating post:', error);
      throw error; // Re-throw to let SecureForm handle it
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-6 cinema-card">
      <h2 className="text-xl font-semibold mb-4">Create New Post</h2>
      {currentOrganization ? (
        <SecureForm 
          onSubmit={handleSecureSubmit}
          rateLimitType="CREATE_OPERATIONS"
          className="space-y-4"
          validateInput={true}
        >
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              name="title"
              placeholder="Enter post title"
              className="bg-cinema-800 border-cinema-700"
              required
              maxLength={200}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <textarea
              id="content"
              name="content"
              placeholder="What's on your mind?"
              className="w-full h-32 px-3 py-2 bg-cinema-800 border border-cinema-700 rounded-md text-foreground resize-none"
              maxLength={5000}
            />
          </div>
          <Button
            type="submit"
            className="w-full bg-primary hover:bg-primary/90"
            disabled={loading}
          >
            {loading ? 'Creating...' : 'Create Post'}
          </Button>
        </SecureForm>
      ) : (
        <p className="text-muted-foreground">Please select an organization to create posts.</p>
      )}
    </Card>
  );
};

export default PostCreationForm;
