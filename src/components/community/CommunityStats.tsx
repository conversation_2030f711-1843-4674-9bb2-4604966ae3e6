
import React from 'react';

const CommunityStats: React.FC = () => {
  const communityStats = [
    { number: "25,000+", label: "Active Writers" },
    { number: "50,000+", label: "Scripts Shared" },
    { number: "200+", label: "Industry Professionals" },
    { number: "98%", label: "Member Satisfaction" }
  ];

  return (
    <section className="py-16 bg-muted/20">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
          {communityStats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl font-bold gold-gradient mb-2">{stat.number}</div>
              <div className="text-muted-foreground">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CommunityStats;
