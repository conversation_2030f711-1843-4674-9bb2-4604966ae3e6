
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Users } from 'lucide-react';

const UpcomingEvents: React.FC = () => {
  const upcomingEvents = [
    {
      title: "AI in Screenwriting: Panel Discussion",
      date: "June 20, 2024",
      time: "7:00 PM EST",
      type: "Webinar",
      speakers: ["<PERSON>", "<PERSON>", "Dr. <PERSON>"],
      attendees: 1247
    },
    {
      title: "Character Development Workshop",
      date: "June 25, 2024",
      time: "2:00 PM EST",
      type: "Workshop",
      speakers: ["<PERSON>"],
      attendees: 456
    },
    {
      title: "Networking Night: TV Writers",
      date: "June 30, 2024",
      time: "8:00 PM EST",
      type: "Networking",
      speakers: ["Industry Professionals"],
      attendees: 234
    }
  ];

  return (
    <section className="py-24 bg-muted/10">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
            Upcoming <span className="gold-gradient">Events</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Don't miss these exciting community events and learning opportunities.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {upcomingEvents.map((event, index) => (
            <Card key={index} className="cinema-card p-6 hover:scale-105 transition-all duration-300 group">
              <Badge className="mb-4">{event.type}</Badge>
              <h3 className="text-xl font-semibold mb-3 font-playfair">{event.title}</h3>
              
              <div className="space-y-2 mb-4 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  {event.date}
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2" />
                  {event.time}
                </div>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  {event.attendees} registered
                </div>
              </div>
              
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">Speakers:</p>
                <div className="flex flex-wrap gap-1">
                  {event.speakers.map((speaker, i) => (
                    <Badge key={i} variant="outline" className="text-xs">
                      {speaker}
                    </Badge>
                  ))}
                </div>
              </div>
              
              <Button className="w-full group-hover:bg-primary group-hover:text-primary-foreground">
                Register Now
              </Button>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default UpcomingEvents;
