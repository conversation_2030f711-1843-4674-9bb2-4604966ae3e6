
import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight } from 'lucide-react';

interface CommunityHeroProps {
  user: any;
  onJoinCommunity: () => void;
}

const CommunityHero: React.FC<CommunityHeroProps> = ({ user, onJoinCommunity }) => {
  return (
    <section className="pt-32 pb-16 relative overflow-hidden">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
            🤝 Writers Community
          </Badge>
          
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
            Where <span className="gold-gradient">Stories</span> Connect
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
            Join thousands of screenwriters, from aspiring newcomers to industry veterans, 
            sharing knowledge, collaboration, and creative inspiration.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group"
              onClick={onJoinCommunity}
            >
              {user ? 'Visit Community' : 'Join Community'}
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4">
              Explore Features
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CommunityHero;
