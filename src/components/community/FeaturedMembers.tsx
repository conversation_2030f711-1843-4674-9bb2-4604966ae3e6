
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Heart, MessageCircle } from 'lucide-react';

const FeaturedMembers: React.FC = () => {
  const featuredMembers = [
    {
      name: "<PERSON>",
      title: "TV Writer, Netflix",
      achievement: "Emmy Nominated",
      avatar: "👨‍💻",
      specialty: "Drama Series"
    },
    {
      name: "<PERSON> Kim",
      title: "Feature Screenwriter",
      achievement: "Sundance Winner",
      avatar: "👩‍🎨",
      specialty: "Independent Film"
    },
    {
      name: "<PERSON>",
      title: "Animation Writer",
      achievement: "Annie Award Winner",
      avatar: "👩‍💼",
      specialty: "Family Entertainment"
    }
  ];

  return (
    <section className="py-24">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
            Featured <span className="gold-gradient">Members</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Learn from industry professionals who are active in our community.
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {featuredMembers.map((member, index) => (
            <Card key={index} className="cinema-card p-6 text-center hover:scale-105 transition-all duration-300 group">
              <div className="w-20 h-20 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">{member.avatar}</span>
              </div>
              <h3 className="text-xl font-semibold mb-2 font-playfair">{member.name}</h3>
              <p className="text-muted-foreground mb-2">{member.title}</p>
              <Badge className="mb-3">{member.achievement}</Badge>
              <p className="text-sm text-muted-foreground mb-4">{member.specialty}</p>
              
              <div className="flex justify-center space-x-2">
                <Button variant="ghost" size="sm">
                  <Heart className="h-4 w-4 mr-1" />
                  Follow
                </Button>
                <Button variant="ghost" size="sm">
                  <MessageCircle className="h-4 w-4 mr-1" />
                  Message
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturedMembers;
