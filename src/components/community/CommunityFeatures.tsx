
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageCircle, Calendar, Trophy, Users, ArrowRight } from 'lucide-react';

const CommunityFeatures: React.FC = () => {
  const communityFeatures = [
    {
      icon: <MessageCircle className="h-6 w-6 text-blue-500" />,
      title: "Discussion Forums",
      description: "Connect with fellow writers, share ideas, and get feedback on your work",
      members: "12.5k"
    },
    {
      icon: <Calendar className="h-6 w-6 text-green-500" />,
      title: "Virtual Events",
      description: "Join webinars, workshops, and live Q&A sessions with industry professionals",
      events: "24"
    },
    {
      icon: <Trophy className="h-6 w-6 text-yellow-500" />,
      title: "Writing Challenges",
      description: "Participate in monthly writing contests and showcase your talent",
      challenges: "8"
    },
    {
      icon: <Users className="h-6 w-6 text-purple-500" />,
      title: "Writing Groups",
      description: "Form or join writing groups for collaborative projects and peer review",
      groups: "156"
    }
  ];

  return (
    <section className="py-24">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-playfair font-bold mb-6">
            Community <span className="gold-gradient">Features</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Everything you need to connect, learn, and grow as a screenwriter.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {communityFeatures.map((feature, index) => (
            <Card 
              key={index} 
              className="cinema-card p-8 hover:scale-105 transition-all duration-300 group animate-fade-scale"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="mb-6 group-hover:scale-110 transition-transform">
                {feature.icon}
              </div>
              <h3 className="text-2xl font-semibold mb-3 font-playfair">{feature.title}</h3>
              <p className="text-muted-foreground mb-4 leading-relaxed">
                {feature.description}
              </p>
              <div className="flex items-center justify-between">
                <Badge variant="outline">
                  {feature.members && `${feature.members} members`}
                  {feature.events && `${feature.events} monthly events`}
                  {feature.challenges && `${feature.challenges} active challenges`}
                  {feature.groups && `${feature.groups} writing groups`}
                </Badge>
                <Button variant="ghost" size="sm" className="group-hover:bg-primary group-hover:text-primary-foreground">
                  Join Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CommunityFeatures;
