
import React from 'react';

interface ContextualTipsPanelProps {
  contextualTips: string[];
}

const ContextualTipsPanel: React.FC<ContextualTipsPanelProps> = ({ contextualTips }) => {
  if (contextualTips.length === 0) return null;

  return (
    <div className="border-b border-border/50 p-3 bg-muted/10">
      <h4 className="text-sm font-semibold mb-2 text-muted-foreground">Writing Tips</h4>
      <div className="space-y-1">
        {contextualTips.map((tip, index) => (
          <p key={index} className="text-xs text-muted-foreground flex items-start space-x-1">
            <span className="text-accent">•</span>
            <span>{tip}</span>
          </p>
        ))}
      </div>
    </div>
  );
};

export default ContextualTipsPanel;
