
import React from 'react';
import { ScreenplayContext } from '@/lib/ai/contextAnalyzer';

interface AIHeaderProps {
  screenplayContext: ScreenplayContext | null;
  isAnalyzing?: boolean;
}

const AIHeader: React.FC<AIHeaderProps> = ({ screenplayContext, isAnalyzing = false }) => {
  return (
    <div className="border-b border-border/50 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center">
            <span className="text-sm">🤖</span>
          </div>
          <h2 className="text-lg font-playfair font-semibold">Enhanced AI Writing Assistant</h2>
        </div>
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full transition-colors ${
            isAnalyzing ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'
          }`} />
          <span className="text-xs text-muted-foreground">
            {isAnalyzing ? 'Analyzing...' : 'Ready'}
          </span>
        </div>
      </div>
      
      {screenplayContext && (
        <div className="mt-2 text-xs text-muted-foreground">
          Current: {screenplayContext.currentElement} • Tone: {screenplayContext.tonalContext}
          {screenplayContext.location && ` • Location: ${screenplayContext.location}`}
        </div>
      )}
    </div>
  );
};

export default AIHeader;
