
import React from 'react';
import { Button } from '@/components/ui/button';

interface ChatInputProps {
  inputValue: string;
  setInputValue: (value: string) => void;
  onSendMessage: () => void;
  isTyping: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  setInputValue,
  onSendMessage,
  isTyping
}) => {
  return (
    <div className="border-t border-border/50 p-4">
      <div className="flex space-x-2">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Ask about your screenplay, request improvements, or get writing advice..."
          className="flex-1 px-3 py-2 bg-input border border-border rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              e.preventDefault();
              onSendMessage();
            }
          }}
          disabled={isTyping}
        />
        <Button 
          onClick={onSendMessage}
          disabled={!inputValue.trim() || isTyping}
          className="px-4"
        >
          {isTyping ? 'Analyzing...' : 'Send'}
        </Button>
      </div>
    </div>
  );
};

export default ChatInput;
