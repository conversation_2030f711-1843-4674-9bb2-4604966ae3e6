
import React from 'react';
import { ContentAnalysis } from '@/lib/ai/contextAnalyzer';

interface ContentAnalysisCardProps {
  contentAnalysis: ContentAnalysis;
}

const ContentAnalysisCard: React.FC<ContentAnalysisCardProps> = ({ contentAnalysis }) => {
  return (
    <div className="border-b border-border/50 p-3 bg-muted/20">
      <h4 className="text-sm font-semibold mb-2">Content Analysis</h4>
      <div className="grid grid-cols-2 gap-2 text-xs">
        <div>
          <span className="text-muted-foreground">Visual Language:</span>
          <div className="flex items-center space-x-1">
            <div className="w-16 h-1 bg-border rounded">
              <div 
                className="h-1 bg-primary rounded transition-all"
                style={{ width: `${contentAnalysis.visualLanguage}%` }}
              />
            </div>
            <span>{contentAnalysis.visualLanguage}%</span>
          </div>
        </div>
        <div>
          <span className="text-muted-foreground">Subtext:</span>
          <div className="flex items-center space-x-1">
            <div className="w-16 h-1 bg-border rounded">
              <div 
                className="h-1 bg-accent rounded transition-all"
                style={{ width: `${contentAnalysis.subtext}%` }}
              />
            </div>
            <span>{contentAnalysis.subtext}%</span>
          </div>
        </div>
      </div>
      <div className="mt-2 text-xs">
        <span className="text-muted-foreground">Pacing:</span>
        <span className={`ml-1 px-1 rounded ${
          contentAnalysis.pacing === 'good' ? 'bg-green-500/20 text-green-300' :
          contentAnalysis.pacing === 'too-fast' ? 'bg-red-500/20 text-red-300' :
          'bg-yellow-500/20 text-yellow-300'
        }`}>
          {contentAnalysis.pacing}
        </span>
      </div>
    </div>
  );
};

export default ContentAnalysisCard;
