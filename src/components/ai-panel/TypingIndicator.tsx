
import React from 'react';

const TypingIndicator: React.FC = () => {
  return (
    <div className="flex justify-start">
      <div className="bg-muted text-muted-foreground p-3 rounded-lg mr-4 max-w-[80%]">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-current rounded-full animate-bounce delay-100"></div>
          <div className="w-2 h-2 bg-current rounded-full animate-bounce delay-200"></div>
        </div>
      </div>
    </div>
  );
};

export default TypingIndicator;
