
import React from 'react';
import { Button } from '@/components/ui/button';
import { SmartSuggestion } from '@/lib/ai/smartSuggestionEngine';

interface SmartSuggestionsPanelProps {
  smartSuggestions: SmartSuggestion[];
  onApplySuggestion: (suggestion: SmartSuggestion) => void;
}

const SmartSuggestionsPanel: React.FC<SmartSuggestionsPanelProps> = ({
  smartSuggestions,
  onApplySuggestion
}) => {
  const getSuggestionIcon = (type: SmartSuggestion['type']) => {
    switch (type) {
      case 'next_element':
        return '➡️';
      case 'dialogue_enhancement':
        return '💬';
      case 'action_improvement':
        return '🎬';
      case 'character_development':
        return '👤';
      case 'structure':
        return '📝';
      default:
        return '✨';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-green-500';
    if (confidence >= 60) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getPriorityColor = (priority: SmartSuggestion['priority']) => {
    switch (priority) {
      case 'high': return 'border-red-500/50 bg-red-500/10';
      case 'medium': return 'border-yellow-500/50 bg-yellow-500/10';
      case 'low': return 'border-blue-500/50 bg-blue-500/10';
    }
  };

  return (
    <div className="border-b border-border/50 p-4 max-h-80 overflow-y-auto cinema-scrollbar">
      <h3 className="text-sm font-semibold mb-3 text-muted-foreground">Smart Suggestions</h3>
      <div className="space-y-3">
        {smartSuggestions.map((suggestion, index) => (
          <div 
            key={suggestion.id}
            className={`p-3 rounded-lg border transition-colors animate-fade-in-up ${getPriorityColor(suggestion.priority)}`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getSuggestionIcon(suggestion.type)}</span>
                <div>
                  <span className="font-medium text-sm">{suggestion.title}</span>
                  <span className={`ml-2 text-xs px-1 py-0.5 rounded ${
                    suggestion.priority === 'high' ? 'bg-red-500/20 text-red-300' :
                    suggestion.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-300' :
                    'bg-blue-500/20 text-blue-300'
                  }`}>
                    {suggestion.priority}
                  </span>
                </div>
              </div>
              <span className={`text-xs font-mono ${getConfidenceColor(suggestion.confidence)}`}>
                {suggestion.confidence}%
              </span>
            </div>
            <p className="text-xs text-muted-foreground mb-2 leading-relaxed">
              {suggestion.description}
            </p>
            {suggestion.example && (
              <p className="text-xs italic text-muted-foreground/80 mb-3 bg-muted/30 p-2 rounded">
                Example: {suggestion.example}
              </p>
            )}
            <div className="flex justify-end space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                className="text-xs h-6"
              >
                Dismiss
              </Button>
              <Button 
                variant="default" 
                size="sm"
                className="text-xs h-6"
                onClick={() => onApplySuggestion(suggestion)}
              >
                Apply
              </Button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SmartSuggestionsPanel;
