
import React from 'react';
import { cn } from '@/lib/utils';

interface EnhancedTypingIndicatorProps {
  variant?: 'default' | 'thinking' | 'processing';
  message?: string;
}

const EnhancedTypingIndicator: React.FC<EnhancedTypingIndicatorProps> = ({ 
  variant = 'default',
  message 
}) => {
  const getIndicatorContent = () => {
    switch (variant) {
      case 'thinking':
        return {
          dots: ['🤔', '💭', '✨'],
          text: message || 'Thinking...'
        };
      case 'processing':
        return {
          dots: ['⚡', '🔄', '⚙️'],
          text: message || 'Processing your request...'
        };
      default:
        return {
          dots: [null, null, null],
          text: message || 'AI is typing...'
        };
    }
  };

  const { dots, text } = getIndicatorContent();

  return (
    <div className="flex justify-start">
      <div className="bg-muted text-muted-foreground p-4 rounded-lg mr-4 max-w-[80%] border">
        <div className="flex items-center space-x-3">
          <div className="flex space-x-1">
            {dots.map((emoji, index) => (
              <div
                key={index}
                className={cn(
                  "transition-all duration-300",
                  emoji ? "text-lg" : "w-2 h-2 bg-current rounded-full animate-bounce"
                )}
                style={{ animationDelay: `${index * 200}ms` }}
              >
                {emoji}
              </div>
            ))}
          </div>
          <span className="text-sm font-medium">{text}</span>
        </div>
        
        <div className="mt-2 w-full bg-border rounded-full h-1 overflow-hidden">
          <div className="h-full bg-primary rounded-full animate-shimmer bg-gradient-to-r from-transparent via-primary/50 to-transparent" />
        </div>
      </div>
    </div>
  );
};

export default EnhancedTypingIndicator;
