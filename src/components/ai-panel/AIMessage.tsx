
import React from 'react';

interface AIMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface AIMessageProps {
  message: AIMessage;
  index: number;
}

const AIMessage: React.FC<AIMessageProps> = ({ message, index }) => {
  return (
    <div 
      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-fade-in-up`}
      style={{ animationDelay: `${index * 50}ms` }}
    >
      <div className={`max-w-[80%] p-3 rounded-lg text-sm ${
        message.type === 'user' 
          ? 'bg-primary text-primary-foreground ml-4' 
          : 'bg-muted text-muted-foreground mr-4'
      }`}>
        <p className="leading-relaxed whitespace-pre-wrap">{message.content}</p>
        <span className="text-xs opacity-70 mt-1 block">
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </span>
      </div>
    </div>
  );
};

export default AIMessage;
