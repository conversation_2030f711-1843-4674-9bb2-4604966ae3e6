
import React, { useState, useEffect } from 'react';
import { AlertCircle, Wifi, WifiOff } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { checkSupabaseConnection } from '@/lib/supabase';

interface NetworkAwareWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const NetworkAwareWrapper: React.FC<NetworkAwareWrapperProps> = ({ 
  children, 
  fallback 
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [isSupabaseConnected, setIsSupabaseConnected] = useState(true);
  const [isChecking, setIsChecking] = useState(false);

  const checkConnectivity = async () => {
    setIsChecking(true);
    try {
      const supabaseConnected = await checkSupabaseConnection();
      setIsSupabaseConnected(supabaseConnected);
    } catch (error) {
      console.error('Connectivity check failed:', error);
      setIsSupabaseConnected(false);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      checkConnectivity();
    };
    
    const handleOffline = () => {
      setIsOnline(false);
      setIsSupabaseConnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Initial connectivity check
    checkConnectivity();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Alert className="max-w-md">
          <WifiOff className="h-4 w-4" />
          <AlertDescription>
            No internet connection. Please check your network and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!isSupabaseConnected) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="space-y-3">
            <p>Unable to connect to our servers. This might be a temporary issue.</p>
            <Button 
              onClick={checkConnectivity} 
              disabled={isChecking}
              className="w-full"
            >
              <Wifi className="h-4 w-4 mr-2" />
              {isChecking ? 'Checking...' : 'Retry Connection'}
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return <>{children}</>;
};
