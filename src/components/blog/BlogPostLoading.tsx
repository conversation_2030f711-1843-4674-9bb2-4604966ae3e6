
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

const BlogPostLoading = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <div className="pt-32 pb-16">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto space-y-8">
            <div className="animate-pulse">
              <div className="h-8 bg-muted rounded w-1/4 mb-4"></div>
              <div className="h-12 bg-muted rounded w-3/4 mb-6"></div>
              <div className="h-6 bg-muted rounded w-full mb-4"></div>
              <div className="h-6 bg-muted rounded w-2/3"></div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default BlogPostLoading;
