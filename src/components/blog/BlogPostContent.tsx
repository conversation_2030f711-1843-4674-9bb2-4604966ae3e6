
import React from 'react';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostContentProps {
  post: BlogPost;
}

const BlogPostContent = ({ post }: BlogPostContentProps) => {
  return (
    <div className="mb-16">
      <div 
        className="prose prose-lg max-w-none prose-headings:font-playfair prose-headings:text-foreground prose-p:text-foreground prose-p:leading-relaxed"
        dangerouslySetInnerHTML={{ __html: post.content }}
      />
    </div>
  );
};

export default BlogPostContent;
