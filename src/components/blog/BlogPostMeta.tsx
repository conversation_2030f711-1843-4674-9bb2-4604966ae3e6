
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, Clock, Share2 } from 'lucide-react';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostMetaProps {
  post: BlogPost;
}

const BlogPostMeta = ({ post }: BlogPostMetaProps) => {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback to copying URL to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 py-6 border-y border-border mb-12">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
          <span className="text-2xl">{post.author.avatar}</span>
        </div>
        <div>
          <div className="font-semibold">{post.author.name}</div>
          <div className="text-sm text-muted-foreground">{post.author.bio}</div>
        </div>
      </div>
      
      <div className="flex items-center space-x-6 text-sm text-muted-foreground">
        <span className="flex items-center">
          <Calendar className="h-4 w-4 mr-1" />
          {new Date(post.publishedAt).toLocaleDateString('en-US', { 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
          })}
        </span>
        <span className="flex items-center">
          <Clock className="h-4 w-4 mr-1" />
          {post.readTime}
        </span>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleShare}
          className="flex items-center"
        >
          <Share2 className="h-4 w-4 mr-1" />
          Share
        </Button>
      </div>
    </div>
  );
};

export default BlogPostMeta;
