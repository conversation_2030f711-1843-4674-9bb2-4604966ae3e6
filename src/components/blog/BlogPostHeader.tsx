
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostHeaderProps {
  post: BlogPost;
}

const BlogPostHeader = ({ post }: BlogPostHeaderProps) => {
  const navigate = useNavigate();

  return (
    <header className="mb-12">
      {/* Back Button */}
      <Button 
        variant="ghost" 
        className="mb-8 -ml-4"
        onClick={() => navigate('/blog')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Blog
      </Button>

      <Badge variant="outline" className="mb-4 text-lg px-4 py-2">
        {post.category}
      </Badge>
      
      <h1 className="text-4xl md:text-6xl font-playfair font-bold mb-6 leading-tight">
        {post.title}
      </h1>
      
      <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
        {post.excerpt}
      </p>
    </header>
  );
};

export default BlogPostHeader;
