
import React from 'react';
import { Link } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowRight, Calendar, User, Clock } from 'lucide-react';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface FeaturedArticleProps {
  post: Omit<BlogPost, 'content'>;
}

const FeaturedArticle = React.memo(({ post }: FeaturedArticleProps) => (
  <section className="py-16">
    <div className="container mx-auto px-6">
      <div className="max-w-4xl mx-auto">
        <Badge className="mb-4">Featured Article</Badge>
        <Card className="cinema-card overflow-hidden group hover:scale-[1.02] transition-all duration-300">
          <Link to={`/blog/${post.slug}`}>
            <div className="md:flex">
              <div className="md:w-1/3 aspect-video md:aspect-square bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                <span className="text-8xl">{post.image}</span>
              </div>
              <div className="md:w-2/3 p-8">
                <Badge variant="outline" className="mb-3">{post.category}</Badge>
                <h2 className="text-3xl font-playfair font-bold mb-4 group-hover:text-primary transition-colors">
                  {post.title}
                </h2>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  {post.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {post.author.name}
                    </span>
                    <span className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {new Date(post.publishedAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {post.readTime}
                    </span>
                  </div>
                  <Button variant="ghost" className="group-hover:bg-primary group-hover:text-primary-foreground">
                    Read More
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </Link>
        </Card>
      </div>
    </div>
  </section>
));

FeaturedArticle.displayName = 'FeaturedArticle';

export default FeaturedArticle;
