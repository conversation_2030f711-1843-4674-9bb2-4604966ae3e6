
import React from 'react';
import { Link } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { User, Calendar, Clock } from 'lucide-react';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostCardProps {
  post: Omit<BlogPost, 'content'>;
  index: number;
}

const BlogPostCard = React.memo(({ post, index }: BlogPostCardProps) => (
  <Card 
    className="cinema-card overflow-hidden hover:scale-105 transition-all duration-300 group animate-fade-scale"
    style={{ animationDelay: `${index * 100}ms` }}
  >
    <Link to={`/blog/${post.slug}`}>
      <div className="aspect-video bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center relative overflow-hidden">
        <span className="text-6xl">{post.image}</span>
        <div className="absolute inset-0 bg-black/20 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
          <Button size="sm">Read Article</Button>
        </div>
      </div>
      
      <div className="p-6">
        <Badge variant="outline" className="mb-3">{post.category}</Badge>
        <h3 className="text-xl font-semibold mb-3 font-playfair group-hover:text-primary transition-colors line-clamp-2">
          {post.title}
        </h3>
        <p className="text-muted-foreground mb-4 text-sm leading-relaxed line-clamp-3">
          {post.excerpt}
        </p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="space-y-1">
            <div className="flex items-center">
              <User className="h-3 w-3 mr-1" />
              {post.author.name}
            </div>
            <div className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              {new Date(post.publishedAt).toLocaleDateString()}
            </div>
          </div>
          <div className="flex items-center">
            <Clock className="h-3 w-3 mr-1" />
            {post.readTime}
          </div>
        </div>
      </div>
    </Link>
  </Card>
));

BlogPostCard.displayName = 'BlogPostCard';

export default BlogPostCard;
