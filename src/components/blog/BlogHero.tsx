
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Search } from 'lucide-react';

interface BlogHeroProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

const BlogHero = ({ searchQuery, onSearchChange }: BlogHeroProps) => {
  return (
    <section className="pt-32 pb-16 relative overflow-hidden">
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <Badge variant="outline" className="mb-6 text-lg px-4 py-2">
            ✍️ ScriptGenius Blog
          </Badge>
          
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
            Stories About <span className="gold-gradient">Storytelling</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-3xl mx-auto animate-slide-up delay-200">
            Insights, techniques, and industry perspectives on the craft and business of screenwriting 
            in the age of artificial intelligence.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 animate-slide-up delay-300">
            <div className="relative max-w-md mx-auto">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <input 
                type="text" 
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-border rounded-lg bg-background"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BlogHero;
