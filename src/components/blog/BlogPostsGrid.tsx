
import React from 'react';
import { Button } from '@/components/ui/button';
import BlogPostCard from './BlogPostCard';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostsGridProps {
  posts: Omit<BlogPost, 'content'>[];
}

const BlogPostsGrid = React.memo(({ posts }: BlogPostsGridProps) => (
  <section className="py-16">
    <div className="container mx-auto px-6">
      {posts.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">No articles found matching your criteria.</p>
          <Button 
            variant="outline" 
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.map((post, index) => (
            <BlogPostCard key={post.id} post={post} index={index} />
          ))}
        </div>
      )}
    </div>
  </section>
));

BlogPostsGrid.displayName = 'BlogPostsGrid';

export default BlogPostsGrid;
