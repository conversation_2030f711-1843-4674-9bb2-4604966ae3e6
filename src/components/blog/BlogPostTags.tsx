
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface BlogPostTagsProps {
  post: BlogPost;
}

const BlogPostTags = ({ post }: BlogPostTagsProps) => {
  return (
    <div className="mb-12">
      <h3 className="text-lg font-semibold mb-4">Tags</h3>
      <div className="flex flex-wrap gap-2">
        {post.tags.map((tag) => (
          <Badge key={tag} variant="secondary" className="hover:bg-primary hover:text-primary-foreground cursor-pointer">
            {tag}
          </Badge>
        ))}
      </div>
    </div>
  );
};

export default BlogPostTags;
