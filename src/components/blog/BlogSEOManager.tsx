
import { useEffect } from 'react';
import { blogCache } from '@/utils/blogCache';

// Safe SEO import with fallback
let seo: any = {
  setMetadata: () => {},
  generateWebsiteData: () => ({}),
  addStructuredData: () => {},
  clearStructuredData: () => {}
};

(async () => {
  try {
    const seoModule = await import('@/utils/seo');
    seo = seoModule.seo;
  } catch (error) {
    console.warn('SEO module failed to load, using fallback:', error);
  }
})();

const BlogSEOManager = () => {
  useEffect(() => {
    try {
      const cacheKey = 'blog-seo';
      if (!blogCache.has(cacheKey)) {
        seo.setMetadata({
          title: 'Blog - ScriptGenius Insights and Industry News',
          description: 'Discover the latest insights on screenwriting, AI collaboration, industry trends, and creative techniques from industry experts.',
          keywords: ['screenwriting blog', 'AI writing', 'film industry', 'television writing', 'creative writing'],
          canonical: '/blog',
          robots: 'index, follow'
        });

        const websiteData = seo.generateWebsiteData();
        seo.addStructuredData(websiteData);
        
        blogCache.set(cacheKey, true);
      }
    } catch (error) {
      console.warn('SEO setup failed:', error);
    }

    return () => {
      try {
        seo.clearStructuredData();
      } catch (error) {
        console.warn('SEO cleanup failed:', error);
      }
    };
  }, []);

  return null;
};

export default BlogSEOManager;
