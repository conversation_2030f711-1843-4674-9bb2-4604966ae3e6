
import React from 'react';
import { Link } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookOpen, User, Clock } from 'lucide-react';
import { type BlogPost } from '@/lib/blog/optimizedBlogData';

interface RelatedPostsSectionProps {
  relatedPosts: Omit<BlogPost, 'content'>[];
}

const RelatedPostsSection = React.memo(({ relatedPosts }: RelatedPostsSectionProps) => (
  <section className="py-12 bg-muted/10">
    <div className="container mx-auto px-6">
      <div className="max-w-4xl mx-auto">
        <h2 className="text-3xl font-playfair font-bold mb-8 flex items-center">
          <BookOpen className="mr-3 h-8 w-8" />
          Related Articles
        </h2>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {relatedPosts.map((relatedPost) => (
            <Card 
              key={relatedPost.id} 
              className="cinema-card overflow-hidden hover:scale-105 transition-all duration-300 group"
            >
              <Link to={`/blog/${relatedPost.slug}`}>
                <div className="aspect-video bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center">
                  <span className="text-4xl">{relatedPost.image}</span>
                </div>
                
                <div className="p-6">
                  <Badge variant="outline" className="mb-3">{relatedPost.category}</Badge>
                  <h3 className="text-lg font-semibold mb-3 font-playfair group-hover:text-primary transition-colors line-clamp-2">
                    {relatedPost.title}
                  </h3>
                  <p className="text-muted-foreground mb-4 text-sm leading-relaxed line-clamp-3">
                    {relatedPost.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span className="flex items-center">
                      <User className="h-3 w-3 mr-1" />
                      {relatedPost.author.name}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {relatedPost.readTime}
                    </span>
                  </div>
                </div>
              </Link>
            </Card>
          ))}
        </div>
      </div>
    </div>
  </section>
));

RelatedPostsSection.displayName = 'RelatedPostsSection';

export default RelatedPostsSection;
