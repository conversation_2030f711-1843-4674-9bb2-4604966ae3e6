
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Film, Plus, Move, Trash2, Eye, EyeOff } from 'lucide-react';

interface StoryNode {
  id: string;
  type: 'scene' | 'character' | 'plot' | 'location';
  title: string;
  description: string;
  x: number;
  y: number;
  connections: string[];
  color: string;
}

interface StoryCanvasProps {
  initialNodes?: StoryNode[];
  onNodesChange?: (nodes: StoryNode[]) => void;
  className?: string;
}

const defaultNodes: StoryNode[] = [
  {
    id: '1',
    type: 'scene',
    title: 'Opening Scene',
    description: 'Coffee shop encounter between <PERSON> and <PERSON>',
    x: 100,
    y: 100,
    connections: ['2'],
    color: 'bg-story-500'
  },
  {
    id: '2',
    type: 'character',
    title: '<PERSON>',
    description: '25-year-old determined journalist',
    x: 300,
    y: 150,
    connections: ['3'],
    color: 'bg-gold-500'
  },
  {
    id: '3',
    type: 'plot',
    title: 'The Mystery',
    description: '<PERSON> investigates a conspiracy',
    x: 500,
    y: 100,
    connections: ['4'],
    color: 'bg-cinema-600'
  },
  {
    id: '4',
    type: 'location',
    title: 'Downtown Office',
    description: 'Corporate headquarters where secrets hide',
    x: 700,
    y: 200,
    connections: [],
    color: 'bg-accent'
  }
];

const StoryCanvas: React.FC<StoryCanvasProps> = ({
  initialNodes = defaultNodes,
  onNodesChange,
  className
}) => {
  const [nodes, setNodes] = useState<StoryNode[]>(initialNodes);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showConnections, setShowConnections] = useState(true);
  const [canvasMode, setCanvasMode] = useState<'select' | 'connect'>('select');

  const getNodeIcon = (type: StoryNode['type']) => {
    switch (type) {
      case 'scene':
        return '🎬';
      case 'character':
        return '👤';
      case 'plot':
        return '📝';
      case 'location':
        return '📍';
      default:
        return '✨';
    }
  };

  const handleMouseDown = (e: React.MouseEvent, nodeId: string) => {
    if (canvasMode === 'select') {
      const node = nodes.find(n => n.id === nodeId);
      if (node) {
        setSelectedNode(nodeId);
        setIsDragging(true);
        const rect = e.currentTarget.getBoundingClientRect();
        setDragOffset({
          x: e.clientX - rect.left,
          y: e.clientY - rect.top
        });
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && selectedNode) {
      const canvas = e.currentTarget;
      const rect = canvas.getBoundingClientRect();
      const newX = e.clientX - rect.left - dragOffset.x;
      const newY = e.clientY - rect.top - dragOffset.y;

      setNodes(prev => prev.map(node =>
        node.id === selectedNode
          ? { ...node, x: Math.max(0, newX), y: Math.max(0, newY) }
          : node
      ));
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  };

  const addNewNode = (type: StoryNode['type']) => {
    const newNode: StoryNode = {
      id: `node_${Date.now()}`,
      type,
      title: `New ${type}`,
      description: `Description for new ${type}`,
      x: Math.random() * 400 + 100,
      y: Math.random() * 300 + 100,
      connections: [],
      color: type === 'scene' ? 'bg-story-500' : 
             type === 'character' ? 'bg-gold-500' :
             type === 'plot' ? 'bg-cinema-600' : 'bg-accent'
    };

    const updatedNodes = [...nodes, newNode];
    setNodes(updatedNodes);
    onNodesChange?.(updatedNodes);
  };

  const deleteNode = (nodeId: string) => {
    const updatedNodes = nodes.filter(node => node.id !== nodeId)
      .map(node => ({
        ...node,
        connections: node.connections.filter(id => id !== nodeId)
      }));
    setNodes(updatedNodes);
    onNodesChange?.(updatedNodes);
    setSelectedNode(null);
  };

  const renderConnection = (fromNode: StoryNode, toNodeId: string) => {
    const toNode = nodes.find(n => n.id === toNodeId);
    if (!toNode || !showConnections) return null;

    const fromX = fromNode.x + 80;
    const fromY = fromNode.y + 40;
    const toX = toNode.x + 80;
    const toY = toNode.y + 40;

    return (
      <line
        key={`${fromNode.id}-${toNodeId}`}
        x1={fromX}
        y1={fromY}
        x2={toX}
        y2={toY}
        stroke="hsl(var(--muted-foreground))"
        strokeWidth="2"
        strokeDasharray="5,5"
        className="opacity-60"
      />
    );
  };

  return (
    <Card className={`cinema-editor h-full flex flex-col ${className}`}>
      {/* Canvas Header */}
      <div className="border-b border-border/50 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-story-500 to-story-700 rounded-lg flex items-center justify-center">
              <Film className="h-4 w-4 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-playfair font-semibold">Story Canvas</h2>
              <p className="text-sm text-muted-foreground">Visual story mapping and structure</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant={showConnections ? "default" : "outline"}
              size="sm"
              onClick={() => setShowConnections(!showConnections)}
            >
              {showConnections ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
            <Button
              variant={canvasMode === 'select' ? "default" : "outline"}
              size="sm"
              onClick={() => setCanvasMode('select')}
            >
              <Move className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Canvas Toolbar */}
      <div className="border-b border-border/50 p-3 bg-muted/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-muted-foreground mr-3">Add Node:</span>
            {['scene', 'character', 'plot', 'location'].map((type) => (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={() => addNewNode(type as StoryNode['type'])}
                className="text-xs capitalize"
              >
                <Plus className="h-3 w-3 mr-1" />
                {type}
              </Button>
            ))}
          </div>
          {selectedNode && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => deleteNode(selectedNode)}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
      </div>

      {/* Canvas Area */}
      <div 
        className="flex-1 relative overflow-hidden bg-gradient-to-br from-background to-muted/20"
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* SVG for connections */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none">
          {nodes.map(node =>
            node.connections.map(connectionId => renderConnection(node, connectionId))
          )}
        </svg>

        {/* Story Nodes */}
        {nodes.map((node, index) => (
          <div
            key={node.id}
            className={`absolute cursor-pointer animate-fade-in-scale ${node.color} text-white rounded-lg p-4 w-40 h-20 shadow-lg hover:shadow-xl transition-all duration-200 ${
              selectedNode === node.id ? 'ring-2 ring-primary scale-105' : ''
            }`}
            style={{ 
              left: node.x, 
              top: node.y,
              animationDelay: `${index * 100}ms`
            }}
            onMouseDown={(e) => handleMouseDown(e, node.id)}
          >
            <div className="flex items-center space-x-2 mb-1">
              <span className="text-lg">{getNodeIcon(node.type)}</span>
              <span className="text-xs font-semibold uppercase opacity-80">
                {node.type}
              </span>
            </div>
            <h3 className="text-sm font-bold truncate">{node.title}</h3>
            <p className="text-xs opacity-90 truncate">{node.description}</p>
          </div>
        ))}

        {/* Canvas Instructions */}
        {nodes.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <Film className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">Your Story Canvas Awaits</h3>
              <p className="text-sm mb-4">Start building your story by adding scenes, characters, and plot points</p>
              <Button onClick={() => addNewNode('scene')} className="animate-pulse-glow">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Scene
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Canvas Footer */}
      <div className="border-t border-border/50 p-4 bg-muted/20">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span>Nodes: {nodes.length}</span>
            <span>Scenes: {nodes.filter(n => n.type === 'scene').length}</span>
            <span>Characters: {nodes.filter(n => n.type === 'character').length}</span>
          </div>
          <div className="text-xs">
            {canvasMode === 'select' ? 'Click and drag to move nodes' : 'Click nodes to connect them'}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StoryCanvas;
