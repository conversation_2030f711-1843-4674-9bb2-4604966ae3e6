
import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Plus, Edit2, Trash2, Move, Clock, Users, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Beat {
  id: string;
  title: string;
  description: string;
  act: 1 | 2 | 3;
  duration: number; // in minutes
  characters: string[];
  purpose: 'setup' | 'conflict' | 'resolution' | 'development' | 'climax';
  notes: string;
  order: number;
}

interface BeatBoardProps {
  className?: string;
}

const BeatBoard: React.FC<BeatBoardProps> = ({ className }) => {
  const [beats, setBeats] = useState<Beat[]>([
    {
      id: '1',
      title: 'Opening Image',
      description: 'A visual that represents the theme of the movie',
      act: 1,
      duration: 2,
      characters: ['Protagonist'],
      purpose: 'setup',
      notes: 'Sets the tone and mood',
      order: 1
    },
    {
      id: '2',
      title: 'Inciting Incident',
      description: 'The event that sets the story in motion',
      act: 1,
      duration: 5,
      characters: ['Protagonist', 'Catalyst Character'],
      purpose: 'conflict',
      notes: 'Should happen around page 12-15',
      order: 2
    }
  ]);

  const [isCreating, setIsCreating] = useState(false);
  const [editingBeat, setEditingBeat] = useState<string | null>(null);
  const [newBeat, setNewBeat] = useState<Partial<Beat>>({
    title: '',
    description: '',
    act: 1,
    duration: 3,
    characters: [],
    purpose: 'setup',
    notes: ''
  });

  const createBeat = useCallback(() => {
    if (!newBeat.title || !newBeat.description) return;

    const beat: Beat = {
      id: Date.now().toString(),
      title: newBeat.title,
      description: newBeat.description,
      act: newBeat.act || 1,
      duration: newBeat.duration || 3,
      characters: newBeat.characters || [],
      purpose: newBeat.purpose || 'setup',
      notes: newBeat.notes || '',
      order: beats.length + 1
    };

    setBeats([...beats, beat]);
    setNewBeat({
      title: '',
      description: '',
      act: 1,
      duration: 3,
      characters: [],
      purpose: 'setup',
      notes: ''
    });
    setIsCreating(false);
  }, [newBeat, beats]);

  const deleteBeat = useCallback((id: string) => {
    setBeats(beats.filter(beat => beat.id !== id));
  }, [beats]);

  const getPurposeColor = (purpose: Beat['purpose']) => {
    switch (purpose) {
      case 'setup': return 'bg-blue-500/20 text-blue-300';
      case 'conflict': return 'bg-red-500/20 text-red-300';
      case 'development': return 'bg-yellow-500/20 text-yellow-300';
      case 'climax': return 'bg-purple-500/20 text-purple-300';
      case 'resolution': return 'bg-green-500/20 text-green-300';
      default: return 'bg-muted/20 text-muted-foreground';
    }
  };

  const getActColor = (act: number) => {
    switch (act) {
      case 1: return 'border-l-4 border-l-story-500';
      case 2: return 'border-l-4 border-l-cinema-500';
      case 3: return 'border-l-4 border-l-gold-500';
      default: return 'border-l-4 border-l-muted';
    }
  };

  const totalDuration = beats.reduce((total, beat) => total + beat.duration, 0);

  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-playfair font-bold gold-gradient">Beat Board</h2>
          <p className="text-muted-foreground">
            Visual story structure planning • {beats.length} beats • {totalDuration} minutes
          </p>
        </div>
        <Button
          onClick={() => setIsCreating(true)}
          className="bg-primary hover:bg-primary/90"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Beat
        </Button>
      </div>

      {/* Story Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cinema-card p-4">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-story-500" />
            <span className="text-sm font-medium">Total Runtime</span>
          </div>
          <p className="text-2xl font-bold">{totalDuration} min</p>
        </Card>
        
        <Card className="cinema-card p-4">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4 text-cinema-500" />
            <span className="text-sm font-medium">Characters</span>
          </div>
          <p className="text-2xl font-bold">
            {new Set(beats.flatMap(beat => beat.characters)).size}
          </p>
        </Card>
        
        <Card className="cinema-card p-4">
          <div className="flex items-center space-x-2">
            <Target className="w-4 h-4 text-gold-500" />
            <span className="text-sm font-medium">Story Beats</span>
          </div>
          <p className="text-2xl font-bold">{beats.length}</p>
        </Card>
      </div>

      {/* Create Beat Form */}
      {isCreating && (
        <Card className="cinema-card p-6">
          <h3 className="text-lg font-semibold mb-4">Create New Beat</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="beat-title">Title</Label>
                <Input
                  id="beat-title"
                  value={newBeat.title || ''}
                  onChange={(e) => setNewBeat({ ...newBeat, title: e.target.value })}
                  placeholder="Beat title"
                  className="bg-cinema-800 border-cinema-700"
                />
              </div>
              <div>
                <Label htmlFor="beat-act">Act</Label>
                <select
                  id="beat-act"
                  value={newBeat.act || 1}
                  onChange={(e) => setNewBeat({ ...newBeat, act: Number(e.target.value) as 1 | 2 | 3 })}
                  className="w-full px-3 py-2 bg-cinema-800 border border-cinema-700 rounded-md"
                >
                  <option value={1}>Act I</option>
                  <option value={2}>Act II</option>
                  <option value={3}>Act III</option>
                </select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="beat-description">Description</Label>
              <Textarea
                id="beat-description"
                value={newBeat.description || ''}
                onChange={(e) => setNewBeat({ ...newBeat, description: e.target.value })}
                placeholder="What happens in this beat?"
                className="bg-cinema-800 border-cinema-700"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="beat-duration">Duration (minutes)</Label>
                <Input
                  id="beat-duration"
                  type="number"
                  value={newBeat.duration || 3}
                  onChange={(e) => setNewBeat({ ...newBeat, duration: Number(e.target.value) })}
                  className="bg-cinema-800 border-cinema-700"
                />
              </div>
              <div>
                <Label htmlFor="beat-purpose">Purpose</Label>
                <select
                  id="beat-purpose"
                  value={newBeat.purpose || 'setup'}
                  onChange={(e) => setNewBeat({ ...newBeat, purpose: e.target.value as Beat['purpose'] })}
                  className="w-full px-3 py-2 bg-cinema-800 border border-cinema-700 rounded-md"
                >
                  <option value="setup">Setup</option>
                  <option value="conflict">Conflict</option>
                  <option value="development">Development</option>
                  <option value="climax">Climax</option>
                  <option value="resolution">Resolution</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button onClick={createBeat} className="bg-primary hover:bg-primary/90">
                Create Beat
              </Button>
              <Button onClick={() => setIsCreating(false)} variant="outline">
                Cancel
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Beats List */}
      <div className="space-y-4">
        {beats.sort((a, b) => a.order - b.order).map((beat) => (
          <Card key={beat.id} className={cn("cinema-card p-6", getActColor(beat.act))}>
            <div className="flex justify-between items-start mb-3">
              <div>
                <h3 className="text-lg font-semibold">{beat.title}</h3>
                <p className="text-sm text-muted-foreground">Act {beat.act} • {beat.duration} minutes</p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge className={getPurposeColor(beat.purpose)}>
                  {beat.purpose}
                </Badge>
                <Button variant="ghost" size="sm">
                  <Edit2 className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" onClick={() => deleteBeat(beat.id)}>
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
            
            <p className="text-foreground mb-3">{beat.description}</p>
            
            {beat.characters.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-3">
                {beat.characters.map((character, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {character}
                  </Badge>
                ))}
              </div>
            )}
            
            {beat.notes && (
              <p className="text-sm text-muted-foreground italic">{beat.notes}</p>
            )}
          </Card>
        ))}
      </div>
    </div>
  );
};

export default BeatBoard;
