
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  MessageSquare, 
  Plus, 
  Pin, 
  Clock,
  Users
} from 'lucide-react';
import { useTeam } from '@/hooks/useTeam';
import { formatDistanceToNow } from 'date-fns';
import CreateDiscussionDialog from './CreateDiscussionDialog';

interface TeamDiscussionsProps {
  isOverview?: boolean;
}

const TeamDiscussions = ({ isOverview = false }: TeamDiscussionsProps) => {
  const { discussions, isDiscussionsLoading } = useTeam();
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const displayDiscussions = isOverview ? discussions.slice(0, 5) : discussions;

  if (isDiscussionsLoading) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Team Discussions</h3>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <>
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Team Discussions</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
              <MessageSquare className="h-3 w-3 mr-1" />
              {discussions.length} Discussions
            </Badge>
            <Button size="sm" onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-1" />
              New Discussion
            </Button>
          </div>
        </div>

        {displayDiscussions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No discussions yet.</p>
            <p className="text-sm">Start a discussion to communicate with your team.</p>
          </div>
        ) : (
          <ScrollArea className={isOverview ? "h-64" : "h-96"}>
            <div className="space-y-4">
              {displayDiscussions.map((discussion) => (
                <div key={discussion.id} className="p-4 rounded-lg border hover:bg-muted/50 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {discussion.is_pinned && (
                        <Pin className="h-4 w-4 text-orange-500" />
                      )}
                      <h4 className="font-medium">{discussion.title}</h4>
                    </div>
                    <div className="flex items-center gap-2">
                      {discussion.is_announcement && (
                        <Badge variant="default" className="text-xs">
                          Announcement
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs capitalize">
                        {discussion.discussion_type}
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {discussion.content}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={discussion.profiles?.avatar_url} />
                        <AvatarFallback>
                          {(discussion.profiles?.full_name || discussion.profiles?.username || 'U')
                            .charAt(0)
                            .toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-xs text-muted-foreground">
                        {discussion.profiles?.full_name || discussion.profiles?.username || 'Unknown User'}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {discussion.reply_count || 0} replies
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDistanceToNow(new Date(discussion.created_at), { addSuffix: true })}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        {isOverview && discussions.length > 5 && (
          <div className="text-center mt-4">
            <Button variant="outline" size="sm">
              View All Discussions
            </Button>
          </div>
        )}
      </Card>

      <CreateDiscussionDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog} 
      />
    </>
  );
};

export default TeamDiscussions;
