
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  UserPlus, 
  Users,
  Crown,
  User,
  Shield,
  Mail
} from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';

const TeamMembers = () => {
  const { members, isAdmin } = useOrganization();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Team Members</h2>
          <p className="text-muted-foreground">
            Manage your organization members and their roles
          </p>
        </div>
        {isAdmin && (
          <Button className="gap-2">
            <UserPlus className="h-4 w-4" />
            Invite Member
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {members.map((member) => (
          <Card key={member.id} className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={member.profiles?.avatar_url} />
                <AvatarFallback>
                  {(member.profiles?.full_name || member.profiles?.username || 'U')
                    .charAt(0)
                    .toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="font-semibold">
                  {member.profiles?.full_name || member.profiles?.username || 'Unknown User'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Joined {new Date(member.joined_at).toLocaleDateString()}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <Badge variant={member.role === 'admin' ? 'default' : 'secondary'}>
                {member.role === 'admin' ? (
                  <Crown className="w-3 h-3 mr-1" />
                ) : (
                  <User className="w-3 h-3 mr-1" />
                )}
                {member.role}
              </Badge>
              
              {isAdmin && (
                <div className="flex gap-1">
                  <Button variant="ghost" size="sm">
                    <Mail className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Shield className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {members.length === 0 && (
        <Card className="p-8 text-center">
          <Users className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-semibold mb-2">No team members</h3>
          <p className="text-muted-foreground mb-6">
            Invite team members to start collaborating on projects.
          </p>
          <Button className="gap-2">
            <UserPlus className="h-4 w-4" />
            Invite First Member
          </Button>
        </Card>
      )}
    </div>
  );
};

export default TeamMembers;
