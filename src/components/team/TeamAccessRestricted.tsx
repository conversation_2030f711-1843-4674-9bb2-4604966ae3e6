
import React from 'react';
import { Card } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  MessageSquare, 
  Activity, 
  Shield,
  Zap
} from 'lucide-react';

interface TeamAccessRestrictedProps {
  teamAccess: any;
}

const TeamAccessRestricted: React.FC<TeamAccessRestrictedProps> = ({ teamAccess }) => {
  return (
    <Card className="p-8 text-center">
      <div className="mb-6">
        <Users className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
        <h2 className="text-2xl font-bold mb-2">Team Collaboration</h2>
        <p className="text-muted-foreground mb-6">
          Unlock powerful team collaboration features with a Pro Team plan or higher.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="p-4 border rounded-lg">
          <MessageSquare className="h-8 w-8 mx-auto mb-2 text-primary" />
          <h3 className="font-semibold mb-1">Team Discussions</h3>
          <p className="text-sm text-muted-foreground">
            Communicate with your team through organized discussions
          </p>
        </div>
        <div className="p-4 border rounded-lg">
          <Activity className="h-8 w-8 mx-auto mb-2 text-primary" />
          <h3 className="font-semibold mb-1">Activity Tracking</h3>
          <p className="text-sm text-muted-foreground">
            Keep track of all team activities and project updates
          </p>
        </div>
        <div className="p-4 border rounded-lg">
          <Shield className="h-8 w-8 mx-auto mb-2 text-primary" />
          <h3 className="font-semibold mb-1">Role Management</h3>
          <p className="text-sm text-muted-foreground">
            Assign roles and permissions to team members
          </p>
        </div>
      </div>

      <Alert>
        <Zap className="h-4 w-4" />
        <AlertDescription>
          Upgrade to Pro Team to unlock team collaboration features for up to {teamAccess?.max_members_per_team || 5} members per team.
        </AlertDescription>
      </Alert>
    </Card>
  );
};

export default TeamAccessRestricted;
