
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useTeam } from '@/hooks/useTeam';
import TeamHeader from './TeamHeader';
import TeamOverview from './TeamOverview';
import TeamAccessRestricted from './TeamAccessRestricted';
import TeamActivity from './TeamActivity';
import TeamDiscussions from './TeamDiscussions';
import TeamProjects from './TeamProjects';
import TeamMembers from './TeamMembers';
import TeamSettings from './TeamSettings';

const TeamManagement = () => {
  const { currentOrganization } = useOrganization();
  const { teamAccess, isLoading } = useTeam();
  const [activeTab, setActiveTab] = useState('overview');

  if (!currentOrganization) {
    return (
      <Alert>
        <AlertDescription>
          Please select an organization to access team features.
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  // Check if user has team access
  if (!teamAccess?.can_manage_teams) {
    return <TeamAccessRestricted teamAccess={teamAccess} />;
  }

  return (
    <div className="space-y-6">
      <TeamHeader teamAccess={teamAccess} />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="discussions">Discussions</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <TeamOverview teamAccess={teamAccess} />
        </TabsContent>

        <TabsContent value="discussions">
          <TeamDiscussions />
        </TabsContent>

        <TabsContent value="projects">
          <TeamProjects />
        </TabsContent>

        <TabsContent value="members">
          <TeamMembers />
        </TabsContent>

        <TabsContent value="settings">
          <TeamSettings />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TeamManagement;
