
import React, { useState, memo, useCallback, useMemo, lazy, Suspense } from 'react';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useOrganization } from '@/contexts/OrganizationContext';
import { useTeam } from '@/hooks/useTeam';
import TeamHeader from './TeamHeader';
import TeamOverview from './TeamOverview';
import TeamAccessRestricted from './TeamAccessRestricted';
import { Card } from '@/components/ui/card';

// Lazy load heavy tab components to improve initial load time
const TeamActivity = lazy(() => import('./TeamActivity'));
const TeamDiscussions = lazy(() => import('./TeamDiscussions'));
const TeamProjects = lazy(() => import('./TeamProjects'));
const TeamMembers = lazy(() => import('./TeamMembers'));
const TeamSettings = lazy(() => import('./TeamSettings'));

// Loading component for lazy-loaded tabs
const TabLoadingState = () => (
  <Card className="p-6">
    <div className="animate-pulse space-y-4">
      <div className="h-4 bg-muted rounded w-1/4"></div>
      <div className="h-32 bg-muted rounded"></div>
      <div className="h-4 bg-muted rounded w-1/2"></div>
    </div>
  </Card>
);

// Memoized tab content components to prevent unnecessary re-renders
const MemoizedTeamOverview = memo(TeamOverview);

const TeamManagement = memo(() => {
  const { currentOrganization } = useOrganization();
  const { teamAccess, isLoading } = useTeam();
  const [activeTab, setActiveTab] = useState('overview');

  // Memoize tab change handler
  const handleTabChange = useCallback((value: string) => {
    setActiveTab(value);
  }, []);

  // Memoize team access check
  const hasTeamAccess = useMemo(() => {
    return teamAccess?.can_manage_teams;
  }, [teamAccess?.can_manage_teams]);

  // Memoize organization check
  const hasOrganization = useMemo(() => {
    return Boolean(currentOrganization);
  }, [currentOrganization]);

  if (!hasOrganization) {
    return (
      <Alert>
        <AlertDescription>
          Please select an organization to access team features.
        </AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="h-32 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  // Check if user has team access
  if (!hasTeamAccess) {
    return <TeamAccessRestricted teamAccess={teamAccess} />;
  }

  return (
    <div className="space-y-6">
      <TeamHeader teamAccess={teamAccess} />

      {/* Main Content with optimized tab loading */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="discussions">Discussions</TabsTrigger>
          <TabsTrigger value="projects">Projects</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <MemoizedTeamOverview teamAccess={teamAccess} />
        </TabsContent>

        <TabsContent value="discussions">
          <Suspense fallback={<TabLoadingState />}>
            <TeamDiscussions />
          </Suspense>
        </TabsContent>

        <TabsContent value="projects">
          <Suspense fallback={<TabLoadingState />}>
            <TeamProjects />
          </Suspense>
        </TabsContent>

        <TabsContent value="members">
          <Suspense fallback={<TabLoadingState />}>
            <TeamMembers />
          </Suspense>
        </TabsContent>

        <TabsContent value="settings">
          <Suspense fallback={<TabLoadingState />}>
            <TeamSettings />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
});

TeamManagement.displayName = 'TeamManagement';

export default TeamManagement;
