
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  UserPlus, 
  UserMinus, 
  MessageSquare, 
  FileText, 
  Settings,
  Clock
} from 'lucide-react';
import { useTeam } from '@/hooks/useTeam';
import { formatDistanceToNow } from 'date-fns';

interface TeamActivityProps {
  limit?: number;
}

const TeamActivity = ({ limit = 10 }: TeamActivityProps) => {
  const { activities, isActivitiesLoading } = useTeam();

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'member_added':
        return <UserPlus className="h-4 w-4 text-green-500" />;
      case 'member_removed':
        return <UserMinus className="h-4 w-4 text-red-500" />;
      case 'role_changed':
        return <Settings className="h-4 w-4 text-blue-500" />;
      case 'discussion_created':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      case 'project_created':
        return <FileText className="h-4 w-4 text-orange-500" />;
      default:
        return <Activity className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getActivityColor = (activityType: string) => {
    switch (activityType) {
      case 'member_added':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'member_removed':
        return 'bg-red-500/10 text-red-500 border-red-500/20';
      case 'role_changed':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'discussion_created':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20';
      case 'project_created':
        return 'bg-orange-500/10 text-orange-500 border-orange-500/20';
      default:
        return 'bg-muted text-muted-foreground';
    }
  };

  if (isActivitiesLoading) {
    return (
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="animate-pulse flex items-center gap-3">
              <div className="w-10 h-10 bg-muted rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-muted rounded mb-1"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  const displayActivities = limit ? activities.slice(0, limit) : activities;

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Recent Activity</h3>
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          <Activity className="h-3 w-3 mr-1" />
          {activities.length} Activities
        </Badge>
      </div>

      {displayActivities.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No team activity yet.</p>
          <p className="text-sm">Activity will appear here as your team collaborates.</p>
        </div>
      ) : (
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {displayActivities.map((activity) => (
              <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border">
                <div className="flex-shrink-0">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={activity.profiles?.avatar_url} />
                    <AvatarFallback>
                      {(activity.profiles?.full_name || activity.profiles?.username || 'U')
                        .charAt(0)
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    {getActivityIcon(activity.activity_type)}
                    <Badge variant="outline" className={getActivityColor(activity.activity_type)}>
                      {activity.activity_type.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <p className="text-sm font-medium mb-1">
                    {activity.profiles?.full_name || activity.profiles?.username || 'Unknown User'}
                  </p>
                  
                  <p className="text-sm text-muted-foreground mb-2">
                    {activity.description}
                  </p>
                  
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}
    </Card>
  );
};

export default TeamActivity;
