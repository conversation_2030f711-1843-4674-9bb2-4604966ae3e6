
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useTeam } from '@/hooks/useTeam';

interface CreateDiscussionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateDiscussionDialog = ({ open, onOpenChange }: CreateDiscussionDialogProps) => {
  const { createDiscussion, isCreatingDiscussion } = useTeam();
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    discussionType: 'general',
    isAnnouncement: false
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.content.trim()) return;

    createDiscussion({
      title: formData.title,
      content: formData.content,
      discussionType: formData.discussionType,
      isAnnouncement: formData.isAnnouncement
    });

    setFormData({ title: '', content: '', discussionType: 'general', isAnnouncement: false });
    onOpenChange(false);
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Start a Team Discussion</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">Discussion Title</Label>
            <Input
              id="title"
              placeholder="What would you like to discuss?"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="discussionType">Discussion Type</Label>
            <Select 
              value={formData.discussionType} 
              onValueChange={(value) => handleInputChange('discussionType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="general">General Discussion</SelectItem>
                <SelectItem value="feedback">Feedback & Review</SelectItem>
                <SelectItem value="brainstorming">Brainstorming</SelectItem>
                <SelectItem value="question">Question & Answer</SelectItem>
                <SelectItem value="update">Project Update</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="content">Discussion Content</Label>
            <Textarea
              id="content"
              placeholder="Describe your discussion topic in detail..."
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              rows={6}
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="announcement"
              checked={formData.isAnnouncement}
              onCheckedChange={(checked) => handleInputChange('isAnnouncement', checked as boolean)}
            />
            <Label htmlFor="announcement" className="text-sm">
              Mark as announcement (will be highlighted for all team members)
            </Label>
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              disabled={isCreatingDiscussion}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!formData.title.trim() || !formData.content.trim() || isCreatingDiscussion}
            >
              {isCreatingDiscussion ? 'Creating...' : 'Start Discussion'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDiscussionDialog;
