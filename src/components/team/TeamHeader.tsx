
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Crown 
} from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';

interface TeamHeaderProps {
  teamAccess: any;
}

const TeamHeader: React.FC<TeamHeaderProps> = ({ teamAccess }) => {
  const { currentOrganization } = useOrganization();

  return (
    <div className="flex justify-between items-start">
      <div>
        <h1 className="text-3xl font-bold">Team Collaboration</h1>
        <p className="text-muted-foreground mt-1">
          Manage your team and collaborate on projects for {currentOrganization?.name}
        </p>
      </div>
      
      <div className="flex gap-2">
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          <Users className="h-3 w-3 mr-1" />
          {teamAccess?.max_members_per_team || 5} Max Members
        </Badge>
        <Badge variant="outline" className="bg-secondary/10 text-secondary-foreground">
          <Crown className="h-3 w-3 mr-1" />
          {currentOrganization?.plan || 'Free'}
        </Badge>
      </div>
    </div>
  );
};

export default TeamHeader;
