
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Settings, 
  Shield, 
  Bell, 
  Users,
  Lock,
  Globe
} from 'lucide-react';
import { useTeam } from '@/hooks/useTeam';
import { useOrganization } from '@/contexts/OrganizationContext';

const TeamSettings = () => {
  const { teamAccess } = useTeam();
  const { currentOrganization, isAdmin } = useOrganization();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Team Settings</h2>
        <p className="text-muted-foreground">
          Configure team collaboration settings and permissions
        </p>
      </div>

      {/* Team Limits */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Users className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Team Limits</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border rounded-lg">
            <div className="text-2xl font-bold text-primary">{teamAccess?.max_teams || 0}</div>
            <div className="text-sm text-muted-foreground">Maximum Teams</div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="text-2xl font-bold text-primary">{teamAccess?.max_members_per_team || 0}</div>
            <div className="text-sm text-muted-foreground">Members per Team</div>
          </div>
          <div className="p-4 border rounded-lg">
            <div className="text-2xl font-bold text-primary">
              {teamAccess?.can_create_custom_roles ? 'Unlimited' : 'Standard'}
            </div>
            <div className="text-sm text-muted-foreground">Role Types</div>
          </div>
        </div>
      </Card>

      {/* Permissions */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Team Permissions</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Globe className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="font-medium">Public Discussions</div>
                <div className="text-sm text-muted-foreground">
                  Allow team members to create public discussions
                </div>
              </div>
            </div>
            <Switch disabled={!isAdmin} />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Lock className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="font-medium">Project Access Control</div>
                <div className="text-sm text-muted-foreground">
                  Restrict project access to invited members only
                </div>
              </div>
            </div>
            <Switch disabled={!isAdmin} />
          </div>

          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              <Bell className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="font-medium">Activity Notifications</div>
                <div className="text-sm text-muted-foreground">
                  Send notifications for team activities
                </div>
              </div>
            </div>
            <Switch disabled={!isAdmin} />
          </div>
        </div>
      </Card>

      {/* Plan Information */}
      <Card className="p-6">
        <div className="flex items-center gap-3 mb-4">
          <Settings className="h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Current Plan</h3>
        </div>

        <div className="flex items-center justify-between">
          <div>
            <div className="font-medium text-lg">{currentOrganization?.plan} Plan</div>
            <div className="text-sm text-muted-foreground">
              {teamAccess?.can_manage_teams 
                ? 'Team collaboration features enabled'
                : 'Upgrade to access team features'
              }
            </div>
          </div>
          <div className="flex gap-2">
            <Badge variant="outline" className="capitalize">
              {currentOrganization?.plan}
            </Badge>
            {teamAccess?.can_access_production && (
              <Badge variant="outline" className="bg-purple-500/10 text-purple-500">
                Production Access
              </Badge>
            )}
          </div>
        </div>

        {!teamAccess?.can_manage_teams && (
          <div className="mt-4">
            <Button className="w-full">
              Upgrade to Pro Team
            </Button>
          </div>
        )}
      </Card>
    </div>
  );
};

export default TeamSettings;
