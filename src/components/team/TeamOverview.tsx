
import React from 'react';
import { Card } from '@/components/ui/card';
import { 
  Users, 
  MessageSquare, 
  Activity, 
  Crown 
} from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';
import TeamActivity from './TeamActivity';
import TeamDiscussions from './TeamDiscussions';

interface TeamOverviewProps {
  teamAccess: any;
}

const TeamOverview: React.FC<TeamOverviewProps> = ({ teamAccess }) => {
  const { currentOrganization } = useOrganization();

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-500/10">
              <Users className="h-5 w-5 text-blue-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Team Members</p>
              <p className="text-2xl font-bold">Active</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-green-500/10">
              <MessageSquare className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Discussions</p>
              <p className="text-2xl font-bold">Open</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-orange-500/10">
              <Activity className="h-5 w-5 text-orange-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Activities</p>
              <p className="text-2xl font-bold">Today</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-purple-500/10">
              <Crown className="h-5 w-5 text-purple-500" />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Access Level</p>
              <p className="text-sm font-medium">
                {teamAccess?.can_create_custom_roles ? 'Custom Roles' : 'Standard Roles'}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Activity and Discussions Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TeamActivity />
        <TeamDiscussions isOverview />
      </div>
    </div>
  );
};

export default TeamOverview;
