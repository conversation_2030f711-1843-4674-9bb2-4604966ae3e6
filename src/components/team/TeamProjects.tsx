
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  FolderOpen, 
  Plus, 
  Users,
  Calendar,
  MoreHorizontal
} from 'lucide-react';
import { useDashboard } from '@/hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';

const TeamProjects = () => {
  const { projects, isLoading } = useDashboard();

  const collaborativeProjects = projects.filter(p => p.collaborator_count > 0);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Team Projects</h2>
          <p className="text-muted-foreground">
            Collaborative projects with team members
          </p>
        </div>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          New Team Project
        </Button>
      </div>

      {collaborativeProjects.length === 0 ? (
        <Card className="p-8 text-center">
          <FolderOpen className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-semibold mb-2">No team projects yet</h3>
          <p className="text-muted-foreground mb-6">
            Create a project and invite team members to start collaborating.
          </p>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Create Team Project
          </Button>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collaborativeProjects.map((project) => (
            <Card key={project.id} className="p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-lg mb-1">{project.title}</h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {project.description || 'No description'}
                  </p>
                </div>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="text-xs">
                    {project.status}
                  </Badge>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    {project.collaborator_count}
                  </div>
                </div>

                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  Updated {formatDistanceToNow(new Date(project.updated_at), { addSuffix: true })}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default TeamProjects;
