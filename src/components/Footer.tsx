
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>, Eye, FileText, Mail, Phone, MapPin, Github, Twitter, Linkedin } from 'lucide-react';
const Footer = () => {
  return <footer className="bg-background border-t mt-auto">
      <div className="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="space-y-4 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-lg font-bold text-primary-foreground">S</span>
              </div>
              <span className="text-xl font-playfair font-bold">ScriptGenius</span>
            </div>
            <p className="text-sm text-muted-foreground">
              Professional screenwriting tools powered by AI. Create, collaborate, and bring your stories to life.
            </p>
            <div className="flex space-x-4">
              <Link to="#" className="text-muted-foreground hover:text-foreground transition-colors" aria-label="Twitter">
                <Twitter className="h-5 w-5" />
              </Link>
              <Link to="#" className="text-muted-foreground hover:text-foreground transition-colors" aria-label="LinkedIn">
                <Linkedin className="h-5 w-5" />
              </Link>
              <Link to="#" className="text-muted-foreground hover:text-foreground transition-colors" aria-label="GitHub">
                <Github className="h-5 w-5" />
              </Link>
            </div>
          </div>
          
          {/* Product Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-foreground">Product</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/#features" className="text-muted-foreground hover:text-foreground transition-colors">Features</Link></li>
              <li><Link to="/#pricing" className="text-muted-foreground hover:text-foreground transition-colors">Pricing</Link></li>
              <li><Link to="/marketplace" className="text-muted-foreground hover:text-foreground transition-colors">Marketplace</Link></li>
              <li><Link to="/production-tools" className="text-muted-foreground hover:text-foreground transition-colors">Production Tools</Link></li>
              <li><Link to="/showcase" className="text-muted-foreground hover:text-foreground transition-colors">Demo</Link></li>
              <li><Link to="/community" className="text-muted-foreground hover:text-foreground transition-colors">Community</Link></li>
              <li><Link to="/blog" className="text-muted-foreground hover:text-foreground transition-colors">Blog</Link></li>
            </ul>
          </div>
          
          {/* Resources Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-foreground">Resources</h4>
            <ul className="space-y-2 text-sm">
              <li><Link to="/documentation" className="text-muted-foreground hover:text-foreground transition-colors">Documentation</Link></li>
              <li><Link to="/support" className="text-muted-foreground hover:text-foreground transition-colors">Support Center</Link></li>
              <li><Link to="/about" className="text-muted-foreground hover:text-foreground transition-colors">About Us</Link></li>
              <li><Link to="/contact" className="text-muted-foreground hover:text-foreground transition-colors">Contact</Link></li>
              <li><Link to="/status" className="text-muted-foreground hover:text-foreground transition-colors">System Status</Link></li>
            </ul>
          </div>
          
          {/* Legal Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-foreground">Legal & Privacy</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/privacy" className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
                  <Eye className="h-3 w-3" />
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link to="/terms" className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
                  <FileText className="h-3 w-3" />
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link to="/security" className="text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  Security
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Section */}
          <div className="space-y-4">
            <h4 className="font-medium text-foreground">Contact</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-3 w-3" />
                <span><EMAIL></span>
              </li>
              <li className="flex items-center gap-2 text-muted-foreground">
                <Phone className="h-3 w-3" />
                <span>+27 76 452 3909</span>
              </li>
              <li className="flex items-start gap-2 text-muted-foreground">
                <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>Middle Town, Delaware<br />United States</span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-sm text-muted-foreground text-center sm:text-left">
            © 2024 ScriptGenius. All rights reserved.
          </p>
          <div className="flex items-center gap-4">
            <Link to="/security" className="text-xs text-muted-foreground hover:text-foreground transition-colors flex items-center gap-1">
              <Shield className="h-3 w-3" />
              Security
            </Link>
            <span className="text-xs text-muted-foreground">•</span>
            <p className="text-xs text-muted-foreground">
              Made with ❤️ for storytellers
            </p>
          </div>
        </div>
      </div>
    </footer>;
};
export default Footer;
