
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Al<PERSON><PERSON>riangle, Refresh<PERSON>w, <PERSON>, Co<PERSON>, Eye } from 'lucide-react';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
  showDetails: boolean;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { 
      hasError: false,
      showDetails: false
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return { 
      hasError: true, 
      error,
      errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      showDetails: false
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Enhanced Error Boundary caught an error:', error, errorInfo);

    // Log detailed error information
    console.group('🚨 Error Details');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();

    // Check for common error patterns
    this.analyzeError(error);

    this.setState({ errorInfo });
    this.props.onError?.(error, errorInfo);
  }

  private analyzeError = (error: Error) => {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Common error patterns
    if (message.includes('network') || message.includes('fetch')) {
      console.warn('🌐 Network-related error detected');
    }
    if (message.includes('supabase') || stack.includes('supabase')) {
      console.warn('🗃️ Supabase-related error detected');
    }
    if (message.includes('circular') || message.includes('module')) {
      console.warn('🔄 Module/import-related error detected');
    }
    if (message.includes('react') || stack.includes('react')) {
      console.warn('⚛️ React-related error detected');
    }
  };

  private handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: undefined,
      showDetails: false
    });
  };

  private handleCopyError = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
    toast.success('Error details copied to clipboard');
  };

  private toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }));
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
          <Card className="max-w-2xl w-full">
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground text-center">
                An unexpected error occurred. Our team has been automatically notified.
              </p>
              
              {this.state.errorId && (
                <div className="bg-muted/50 p-3 rounded-lg">
                  <p className="text-sm font-mono text-center">
                    Error ID: {this.state.errorId}
                  </p>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={this.handleRetry}
                  className="flex-1"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/'}
                  className="flex-1"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  onClick={this.handleCopyError}
                  className="flex-1"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Error Details
                </Button>

                <Button
                  variant="ghost"
                  onClick={this.toggleDetails}
                  className="flex-1"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  {this.state.showDetails ? 'Hide' : 'Show'} Details
                </Button>
              </div>

              {this.state.showDetails && this.state.error && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-muted-foreground mb-2">
                    Technical Details
                  </summary>
                  <div className="space-y-2">
                    <div className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                      <strong>Error:</strong> {this.state.error.message}
                    </div>
                    {this.state.error.stack && (
                      <div className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                        <strong>Stack Trace:</strong>
                        <pre className="whitespace-pre-wrap mt-1">
                          {this.state.error.stack}
                        </pre>
                      </div>
                    )}
                    {this.state.errorInfo?.componentStack && (
                      <div className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                        <strong>Component Stack:</strong>
                        <pre className="whitespace-pre-wrap mt-1">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}
