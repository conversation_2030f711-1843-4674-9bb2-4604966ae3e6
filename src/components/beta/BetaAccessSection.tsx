import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Spark<PERSON>, 
  Users, 
  Zap, 
  Shield, 
  Clock,
  ArrowRight,
  Star
} from 'lucide-react';
import BetaRequestForm from './BetaRequestForm';

interface BetaAccessSectionProps {
  className?: string;
}

export function BetaAccessSection({ className }: BetaAccessSectionProps) {
  const [showForm, setShowForm] = useState(false);

  const handleGetStarted = () => {
    setShowForm(true);
  };

  const handleFormSuccess = () => {
    // Form will handle its own success state
    // Could add additional tracking here
  };

  if (showForm) {
    return (
      <section className={`py-16 px-4 ${className}`}>
        <div className="max-w-2xl mx-auto">
          <BetaRequestForm 
            onSuccess={handleFormSuccess}
            className="shadow-xl"
          />
        </div>
      </section>
    );
  }

  return (
    <section className={`py-16 px-4 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <Badge variant="secondary" className="mb-4 text-sm px-3 py-1">
            <Sparkles className="h-3 w-3 mr-1" />
            Limited Beta Access
          </Badge>
          
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            Join the ScriptGenius Beta
          </h2>
          
          <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
            Get early access to the future of screenwriting with AI-powered tools, 
            real-time collaboration, and industry-leading performance.
          </p>

          <div className="flex justify-center mb-8">
            <Badge variant="default" className="text-lg px-6 py-3">
              <Sparkles className="h-5 w-5 mr-2" />
              90% Off Lifetime Deal - Beta Only
            </Badge>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-8 items-center">
          {/* Left side - Benefits */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-2xl font-semibold">Why Join the Beta?</h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 p-2 rounded-lg">
                    <Zap className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Early Access Features</h4>
                    <p className="text-gray-600 text-sm">
                      Be the first to try new AI tools, collaboration features, and performance optimizations.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-green-100 p-2 rounded-lg">
                    <Shield className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Lifetime Pricing Lock</h4>
                    <p className="text-gray-600 text-sm">
                      Secure 90% off our Starter or Pro plans for life - never pay full price.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-purple-100 p-2 rounded-lg">
                    <Users className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Direct Influence</h4>
                    <p className="text-gray-600 text-sm">
                      Your feedback directly shapes the product. Help us build the perfect screenwriting tool.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-100 p-2 rounded-lg">
                    <Clock className="h-5 w-5 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Priority Support</h4>
                    <p className="text-gray-600 text-sm">
                      Get priority customer support and direct access to our development team.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Social Proof */}
            <Card className="bg-white/50 backdrop-blur">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <span className="text-sm font-medium">500+ Beta Users</span>
                </div>
                <p className="text-sm text-gray-600">
                  "ScriptGenius has transformed our writing process. The AI features and collaboration tools are game-changers."
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  - Sarah M., Independent Producer
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Right side - CTA */}
          <div className="space-y-6">
            <Card className="shadow-xl bg-white">
              <CardContent className="p-8 text-center">
                <div className="mb-6">
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Sparkles className="h-8 w-8" />
                  </div>
                  <h3 className="text-2xl font-bold mb-2">Ready to Get Started?</h3>
                  <p className="text-gray-600">
                    Join hundreds of writers, producers, and directors already using ScriptGenius.
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-3xl font-bold text-green-600 mb-1">90% OFF</div>
                    <div className="text-sm text-gray-600">Lifetime Access</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Starter: $9.90 (was $99) • Pro: $19.90 (was $199)
                    </div>
                  </div>

                  <Button 
                    onClick={handleGetStarted}
                    size="lg" 
                    className="w-full text-lg py-6"
                  >
                    Request Beta Access
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </Button>

                  <div className="text-xs text-gray-500 space-y-1">
                    <p>✓ No credit card required</p>
                    <p>✓ 24-48 hour approval process</p>
                    <p>✓ Instant access after payment</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Urgency/Scarcity */}
            <Card className="bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <span className="text-sm font-semibold text-orange-800">Limited Time Offer</span>
                </div>
                <p className="text-xs text-orange-700">
                  Beta pricing ends when we launch publicly. Secure your lifetime discount today.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16 max-w-3xl mx-auto">
          <h3 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h3>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-2">How long does approval take?</h4>
              <p className="text-sm text-gray-600">
                Most requests are approved within 24-48 hours. You'll receive an email with your promo code.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">What's included in the beta?</h4>
              <p className="text-sm text-gray-600">
                Full access to all Starter or Pro features, plus early access to new features as they're developed.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Is the 90% discount really lifetime?</h4>
              <p className="text-sm text-gray-600">
                Yes! Once you purchase with the beta discount, you'll never pay more, even as we add new features.
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold mb-2">Can I upgrade tiers later?</h4>
              <p className="text-sm text-gray-600">
                Yes, you can upgrade from Starter to Pro at any time, maintaining your beta discount.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default BetaAccessSection;
