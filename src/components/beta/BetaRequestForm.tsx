import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  CheckCircle, 
  Loader2, 
  Sparkles, 
  Users, 
  Zap,
  AlertTriangle 
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Form validation schema
const betaRequestSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  company: z.string().optional(),
  useCase: z.string().min(20, 'Please describe your use case in at least 20 characters'),
  referralSource: z.string().min(1, 'Please select how you heard about us'),
});

type BetaRequestForm = z.infer<typeof betaRequestSchema>;

interface BetaRequestFormProps {
  onSuccess?: () => void;
  className?: string;
}

export function BetaRequestForm({ onSuccess, className }: BetaRequestFormProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<BetaRequestForm>({
    resolver: zodResolver(betaRequestSchema),
    mode: 'onChange',
  });

  const watchedReferralSource = watch('referralSource');

  const onSubmit = async (data: BetaRequestForm) => {
    setIsLoading(true);
    
    try {
      const { error } = await supabase
        .from('beta_requests')
        .insert({
          email: data.email,
          name: data.name,
          company: data.company || null,
          use_case: data.useCase,
          referral_source: data.referralSource,
        });

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          toast({
            title: "Already Requested",
            description: "You've already submitted a beta request with this email address.",
            variant: "destructive",
          });
          return;
        }
        throw error;
      }

      // Log the beta request submission
      await supabase
        .from('beta_request_logs')
        .insert({
          beta_request_id: null, // Will be updated by trigger
          action: 'submitted',
          details: {
            referral_source: data.referralSource,
            has_company: !!data.company,
            use_case_length: data.useCase.length,
          },
        });

      setIsSubmitted(true);
      onSuccess?.();

      toast({
        title: "Request Submitted!",
        description: "We'll review your request and send you a promo code within 24-48 hours.",
      });

    } catch (error) {
      console.error('Failed to submit beta request:', error);
      toast({
        title: "Submission Failed",
        description: "Please try again or contact support if the problem persists.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <Card className={className}>
        <CardContent className="text-center p-8">
          <div className="mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-2xl font-bold mb-2">Request Submitted!</h3>
            <p className="text-gray-600 mb-4">
              Thank you for your interest in ScriptGenius. We'll review your request and send you a promo code within 24-48 hours.
            </p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-2">
              <Badge variant="secondary" className="text-lg px-4 py-2">
                <Sparkles className="h-4 w-4 mr-2" />
                90% Off Lifetime Deal
              </Badge>
            </div>

            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>What's next?</strong> Check your email for updates and watch for your exclusive promo code!
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Join 500+ beta users</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>Early access features</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl">Request Beta Access</CardTitle>
        <CardDescription>
          Get early access to ScriptGenius with an exclusive 90% off lifetime deal
        </CardDescription>
        <div className="flex justify-center">
          <Badge variant="default" className="text-lg px-4 py-2">
            <Sparkles className="h-4 w-4 mr-2" />
            Limited Time: 90% Off Lifetime
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <div className="space-y-2">
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              {...register('email')}
              className={errors.email ? 'border-red-500' : ''}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* Name Field */}
          <div className="space-y-2">
            <Label htmlFor="name">Full Name *</Label>
            <Input
              id="name"
              type="text"
              placeholder="John Doe"
              {...register('name')}
              className={errors.name ? 'border-red-500' : ''}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          {/* Company Field */}
          <div className="space-y-2">
            <Label htmlFor="company">Company (Optional)</Label>
            <Input
              id="company"
              type="text"
              placeholder="Your Production Company"
              {...register('company')}
            />
            <p className="text-xs text-gray-500">
              Help us understand your professional context
            </p>
          </div>

          {/* Use Case Field */}
          <div className="space-y-2">
            <Label htmlFor="useCase">How do you plan to use ScriptGenius? *</Label>
            <Textarea
              id="useCase"
              placeholder="Describe your screenwriting projects, team collaboration needs, or specific features you're interested in..."
              rows={4}
              {...register('useCase')}
              className={errors.useCase ? 'border-red-500' : ''}
            />
            {errors.useCase && (
              <p className="text-sm text-red-500">{errors.useCase.message}</p>
            )}
            <p className="text-xs text-gray-500">
              The more detail you provide, the faster we can approve your request
            </p>
          </div>

          {/* Referral Source Field */}
          <div className="space-y-2">
            <Label htmlFor="referralSource">How did you hear about us? *</Label>
            <Select onValueChange={(value) => setValue('referralSource', value)}>
              <SelectTrigger className={errors.referralSource ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="social">Social Media</SelectItem>
                <SelectItem value="search">Search Engine</SelectItem>
                <SelectItem value="referral">Friend/Colleague Referral</SelectItem>
                <SelectItem value="blog">Blog/Article</SelectItem>
                <SelectItem value="newsletter">Newsletter/Email</SelectItem>
                <SelectItem value="event">Event/Conference</SelectItem>
                <SelectItem value="advertisement">Advertisement</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {errors.referralSource && (
              <p className="text-sm text-red-500">{errors.referralSource.message}</p>
            )}
          </div>

          {/* Benefits Section */}
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <h4 className="font-semibold text-sm">What you'll get:</h4>
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>90% off Starter or Pro lifetime plans</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Early access to new features</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Direct feedback channel to our team</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span>Priority customer support</span>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <Button 
            type="submit" 
            className="w-full" 
            disabled={isLoading || !isValid}
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Submitting Request...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Request Beta Access
              </>
            )}
          </Button>

          <p className="text-xs text-gray-500 text-center">
            By submitting this form, you agree to receive email updates about your beta access request.
          </p>
        </form>
      </CardContent>
    </Card>
  );
}

export default BetaRequestForm;
