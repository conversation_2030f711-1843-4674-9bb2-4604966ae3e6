
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';
import { toast } from 'sonner';

type CallbackType = 'signup' | 'recovery' | 'invite' | 'magiclink' | 'email_change' | 'unknown';
type CallbackStatus = 'processing' | 'success' | 'error';

interface CallbackConfig {
  successRedirect?: string;
  errorRedirect?: string;
  showUI?: boolean;
}

const AuthCallback: React.FC<CallbackConfig> = ({
  successRedirect = '/dashboard',
  errorRedirect = '/auth',
  showUI = true
}) => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { refreshProfile } = useAuth();
  
  const [status, setStatus] = useState<CallbackStatus>('processing');
  const [callbackType, setCallbackType] = useState<CallbackType>('unknown');
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('Processing authentication...');

  useEffect(() => {
    handleAuthCallback();
  }, []);

  const detectCallbackType = (): CallbackType => {
    const type = searchParams.get('type');
    const accessToken = searchParams.get('access_token');
    const refreshToken = searchParams.get('refresh_token');
    
    // Check for explicit type parameter
    if (type) {
      switch (type) {
        case 'signup':
        case 'email_change':
        case 'recovery':
        case 'invite':
        case 'magiclink':
          return type as CallbackType;
        default:
          return 'unknown';
      }
    }
    
    // Fallback detection based on URL parameters
    if (accessToken && refreshToken) {
      return 'signup'; // Most common case for email verification
    }
    
    return 'unknown';
  };

  const handleAuthCallback = async () => {
    try {
      const detectedType = detectCallbackType();
      setCallbackType(detectedType);

      // Extract session from URL
      const { data, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        throw new Error(`Session error: ${sessionError.message}`);
      }

      // Handle different callback types
      switch (detectedType) {
        case 'signup':
          await handleSignupCallback();
          break;
        case 'recovery':
          await handleRecoveryCallback();
          break;
        case 'invite':
          await handleInviteCallback();
          break;
        case 'magiclink':
          await handleMagicLinkCallback();
          break;
        case 'email_change':
          await handleEmailChangeCallback();
          break;
        default:
          await handleGenericCallback();
      }

    } catch (err) {
      console.error('Auth callback error:', err);
      setStatus('error');
      setError(err instanceof Error ? err.message : 'Authentication failed');
      setMessage('Authentication failed. Please try again.');
      
      // Show error toast
      toast.error('Authentication failed', {
        description: err instanceof Error ? err.message : 'Unknown error occurred'
      });
    }
  };

  const handleSignupCallback = async () => {
    setMessage('Verifying your email address...');
    
    // Check if user is now authenticated
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user && user.email_confirmed_at) {
      setStatus('success');
      setMessage('Email verified successfully! Redirecting...');
      
      // Refresh user profile
      await refreshProfile();
      
      toast.success('Welcome!', {
        description: 'Your email has been verified successfully.'
      });
      
      setTimeout(() => navigate(successRedirect, { replace: true }), 2000);
    } else {
      throw new Error('Email verification failed or user not found');
    }
  };

  const handleRecoveryCallback = async () => {
    setMessage('Processing password reset...');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      setStatus('success');
      setMessage('Password reset verified! You can now set a new password.');
      
      toast.success('Password reset verified', {
        description: 'You can now set a new password.'
      });
      
      // Redirect to password reset form
      setTimeout(() => navigate('/auth/reset-password', { replace: true }), 2000);
    } else {
      throw new Error('Password reset verification failed');
    }
  };

  const handleInviteCallback = async () => {
    setMessage('Processing team invitation...');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      setStatus('success');
      setMessage('Invitation accepted successfully! Welcome to the team.');
      
      await refreshProfile();
      
      toast.success('Invitation accepted!', {
        description: 'Welcome to the team.'
      });
      
      setTimeout(() => navigate(successRedirect, { replace: true }), 2000);
    } else {
      throw new Error('Invitation processing failed');
    }
  };

  const handleMagicLinkCallback = async () => {
    setMessage('Processing magic link...');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      setStatus('success');
      setMessage('Signed in successfully!');
      
      await refreshProfile();
      
      toast.success('Signed in!', {
        description: 'Welcome back.'
      });
      
      setTimeout(() => navigate(successRedirect, { replace: true }), 2000);
    } else {
      throw new Error('Magic link authentication failed');
    }
  };

  const handleEmailChangeCallback = async () => {
    setMessage('Processing email change...');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      setStatus('success');
      setMessage('Email address updated successfully!');
      
      await refreshProfile();
      
      toast.success('Email updated!', {
        description: 'Your email address has been changed successfully.'
      });
      
      setTimeout(() => navigate(successRedirect, { replace: true }), 2000);
    } else {
      throw new Error('Email change verification failed');
    }
  };

  const handleGenericCallback = async () => {
    setMessage('Processing authentication...');
    
    const { data: { user } } = await supabase.auth.getUser();
    
    if (user) {
      setStatus('success');
      setMessage('Authentication successful!');
      
      await refreshProfile();
      
      toast.success('Authentication successful!');
      
      setTimeout(() => navigate(successRedirect, { replace: true }), 2000);
    } else {
      throw new Error('Authentication verification failed');
    }
  };

  const handleRetry = () => {
    setStatus('processing');
    setError(null);
    setMessage('Retrying authentication...');
    handleAuthCallback();
  };

  const handleGoBack = () => {
    navigate(errorRedirect, { replace: true });
  };

  if (!showUI) {
    // Headless mode - just process in background
    return null;
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
        return <XCircle className="h-8 w-8 text-red-500" />;
    }
  };

  const getCallbackTypeDisplay = () => {
    switch (callbackType) {
      case 'signup': return 'Email Verification';
      case 'recovery': return 'Password Reset';
      case 'invite': return 'Team Invitation';
      case 'magiclink': return 'Magic Link';
      case 'email_change': return 'Email Change';
      default: return 'Authentication';
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <CardTitle>
            {getCallbackTypeDisplay()}
          </CardTitle>
          <CardDescription>
            {message}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {status === 'error' && error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}
          
          {status === 'error' && (
            <div className="flex gap-2">
              <Button onClick={handleRetry} variant="outline" className="flex-1">
                Try Again
              </Button>
              <Button onClick={handleGoBack} className="flex-1">
                Go Back
              </Button>
            </div>
          )}
          
          {status === 'success' && (
            <div className="text-center text-sm text-gray-600">
              Redirecting automatically...
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthCallback;
