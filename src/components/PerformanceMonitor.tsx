import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Activity, Zap, Clock, Eye } from 'lucide-react';

interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage: number;
  bundleSize: number;
  cacheHitRate: number;
}

interface ComponentMetric {
  name: string;
  renderCount: number;
  avgRenderTime: number;
  lastRenderTime: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    bundleSize: 0,
    cacheHitRate: 0
  });

  const [componentMetrics, setComponentMetrics] = useState<ComponentMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const updateMetrics = () => {
      // Performance API metrics
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const renderTime = navigation.loadEventEnd - navigation.loadEventStart;

      // Memory usage (if available)
      const memory = (performance as any).memory;
      const memoryUsage = memory ? memory.usedJSHeapSize / 1024 / 1024 : 0;

      // Component count (approximate)
      const componentCount = document.querySelectorAll('[data-component]').length;

      setMetrics({
        renderTime: Math.round(renderTime),
        componentCount,
        memoryUsage: Math.round(memoryUsage * 100) / 100,
        bundleSize: 0, // Would need webpack bundle analyzer
        cacheHitRate: 85 // Mock data
      });
    };

    // Update metrics every 5 seconds
    const interval = setInterval(updateMetrics, 5000);
    updateMetrics(); // Initial update

    return () => clearInterval(interval);
  }, []);

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'bg-green-500';
    if (value <= thresholds.warning) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getMemoryColor = (mb: number) => getPerformanceColor(mb, { good: 50, warning: 100 });
  const getRenderTimeColor = (ms: number) => getPerformanceColor(ms, { good: 100, warning: 300 });

  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50 bg-blue-600 hover:bg-blue-700"
        size="sm"
      >
        <Activity className="h-4 w-4 mr-2" />
        Performance
      </Button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="bg-white shadow-lg border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-semibold flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Performance Monitor
            </CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Core Metrics */}
          <div className="grid grid-cols-2 gap-2">
            <div className="bg-gray-50 p-2 rounded">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Render Time</span>
                <Badge className={`text-xs ${getRenderTimeColor(metrics.renderTime)}`}>
                  {metrics.renderTime}ms
                </Badge>
              </div>
            </div>
            
            <div className="bg-gray-50 p-2 rounded">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Memory</span>
                <Badge className={`text-xs ${getMemoryColor(metrics.memoryUsage)}`}>
                  {metrics.memoryUsage}MB
                </Badge>
              </div>
            </div>
            
            <div className="bg-gray-50 p-2 rounded">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Components</span>
                <Badge variant="outline" className="text-xs">
                  {metrics.componentCount}
                </Badge>
              </div>
            </div>
            
            <div className="bg-gray-50 p-2 rounded">
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-600">Cache Hit</span>
                <Badge variant="outline" className="text-xs">
                  {metrics.cacheHitRate}%
                </Badge>
              </div>
            </div>
          </div>

          {/* Performance Tips */}
          <div className="bg-blue-50 p-2 rounded">
            <h4 className="text-xs font-semibold text-blue-800 mb-1">Quick Tips</h4>
            <ul className="text-xs text-blue-700 space-y-1">
              {metrics.renderTime > 300 && (
                <li>• Consider lazy loading heavy components</li>
              )}
              {metrics.memoryUsage > 100 && (
                <li>• High memory usage detected</li>
              )}
              {metrics.componentCount > 50 && (
                <li>• Many components rendered</li>
              )}
              {metrics.cacheHitRate < 80 && (
                <li>• Improve caching strategy</li>
              )}
            </ul>
          </div>

          {/* Component Metrics */}
          {componentMetrics.length > 0 && (
            <div>
              <h4 className="text-xs font-semibold mb-2">Component Performance</h4>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {componentMetrics.slice(0, 5).map((component, index) => (
                  <div key={index} className="flex items-center justify-between text-xs">
                    <span className="truncate">{component.name}</span>
                    <div className="flex items-center gap-1">
                      <Badge variant="outline" className="text-xs">
                        {component.renderCount}
                      </Badge>
                      <Badge 
                        className={`text-xs ${getRenderTimeColor(component.avgRenderTime)}`}
                      >
                        {component.avgRenderTime}ms
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              onClick={() => {
                // Clear performance marks
                performance.clearMarks();
                performance.clearMeasures();
                console.log('Performance data cleared');
              }}
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
            >
              <Zap className="h-3 w-3 mr-1" />
              Clear
            </Button>
            <Button
              onClick={() => {
                // Export performance data
                const data = {
                  metrics,
                  componentMetrics,
                  timestamp: new Date().toISOString()
                };
                console.log('Performance Report:', data);
                
                // Download as JSON
                const blob = new Blob([JSON.stringify(data, null, 2)], {
                  type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `performance-report-${Date.now()}.json`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              variant="outline"
              size="sm"
              className="flex-1 text-xs"
            >
              <Clock className="h-3 w-3 mr-1" />
              Export
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// HOC for monitoring component performance
export const withPerformanceMonitoring = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  const MonitoredComponent: React.FC<P> = (props) => {
    useEffect(() => {
      if (process.env.NODE_ENV === 'development') {
        const startTime = performance.now();
        
        return () => {
          const endTime = performance.now();
          const renderTime = endTime - startTime;
          
          console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
          
          // Store in performance buffer for monitoring
          if (typeof window !== 'undefined') {
            window.performanceBuffer = window.performanceBuffer || [];
            window.performanceBuffer.push({
              component: componentName,
              renderTime,
              timestamp: Date.now()
            });
            
            // Keep only last 100 entries
            if (window.performanceBuffer.length > 100) {
              window.performanceBuffer = window.performanceBuffer.slice(-100);
            }
          }
        };
      }
    });

    return <WrappedComponent {...props} />;
  };

  MonitoredComponent.displayName = `withPerformanceMonitoring(${componentName})`;
  return MonitoredComponent;
};

// Global performance buffer type
declare global {
  interface Window {
    performanceBuffer?: Array<{
      component: string;
      renderTime: number;
      timestamp: number;
    }>;
  }
}

export default PerformanceMonitor;
