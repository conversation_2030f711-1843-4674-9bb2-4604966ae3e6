
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';

const backupFormSchema = z.object({
  backupType: z.enum(['full', 'incremental', 'point_in_time'], {
    required_error: "Please select a backup type"
  }),
  retentionDays: z.number()
    .min(1, "Retention period must be at least 1 day")
    .max(365, "Retention period cannot exceed 365 days")
    .default(30),
  description: z.string()
    .min(1, "Description is required")
    .max(500, "Description cannot exceed 500 characters")
});

type BackupFormData = z.infer<typeof backupFormSchema>;

interface DatabaseMonitoringFormProps {
  onSubmit: (data: BackupFormData) => Promise<void>;
  isLoading?: boolean;
}

export const DatabaseMonitoringForm: React.FC<DatabaseMonitoringFormProps> = ({
  onSubmit,
  isLoading = false
}) => {
  const { toast } = useToast();
  
  const form = useForm<BackupFormData>({
    resolver: zodResolver(backupFormSchema),
    defaultValues: {
      backupType: 'full',
      retentionDays: 30,
      description: ''
    }
  });

  const handleSubmit = async (data: BackupFormData) => {
    try {
      await onSubmit(data);
      form.reset();
      toast({
        title: "Backup Scheduled",
        description: "Your backup has been successfully scheduled",
      });
    } catch (error) {
      toast({
        title: "Scheduling Failed",
        description: "Could not schedule the backup. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule Database Backup</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="backupType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Backup Type</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                    disabled={isLoading}
                  >
                    <FormControl>
                      <SelectTrigger 
                        aria-label="Select backup type"
                        className="w-full"
                      >
                        <SelectValue placeholder="Select backup type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="full">Full Backup</SelectItem>
                      <SelectItem value="incremental">Incremental Backup</SelectItem>
                      <SelectItem value="point_in_time">Point-in-Time Backup</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose the type of backup to perform
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="retentionDays"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Retention Period (Days)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="1"
                      max="365"
                      placeholder="30"
                      disabled={isLoading}
                      aria-describedby="retention-description"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription id="retention-description">
                    How long to keep this backup (1-365 days)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter backup description..."
                      disabled={isLoading}
                      aria-describedby="description-description"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription id="description-description">
                    Brief description of this backup
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button 
              type="submit" 
              disabled={isLoading || !form.formState.isValid}
              className="w-full"
              aria-label="Schedule backup"
            >
              {isLoading ? 'Scheduling...' : 'Schedule Backup'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};
