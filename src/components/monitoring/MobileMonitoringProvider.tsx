import React, { createContext, useContext, useEffect, ReactNode } from 'react';

interface MobileMonitoringContextType {
  isMonitoringActive: boolean;
  captureError: (error: any, context?: any) => void;
  measurePerformance: <T>(operation: string, fn: () => T | Promise<T>) => T | Promise<T>;
}

const MobileMonitoringContext = createContext<MobileMonitoringContextType>({
  isMonitoringActive: false,
  captureError: () => {},
  measurePerformance: (_, fn) => fn()
});

export const useMobileMonitoring = () => useContext(MobileMonitoringContext);

interface MobileMonitoringProviderProps {
  children: ReactNode;
  enableHealthMonitoring?: boolean;
}

export const MobileMonitoringProvider: React.FC<MobileMonitoringProviderProps> = ({
  children,
  enableHealthMonitoring = false // Disabled by default on mobile
}) => {
  const captureError = (error: any, context?: any) => {
    // Simplified error logging for mobile
    console.error('Mobile Error:', error, context);
    
    // Only log critical errors to reduce overhead
    if (error?.message?.includes('Network') || error?.status >= 500) {
      try {
        // Simple error storage in localStorage for offline capability
        const errorLog = {
          timestamp: new Date().toISOString(),
          message: error?.message || String(error),
          context: context || {}
        };
        
        const existingErrors = JSON.parse(localStorage.getItem('mobile-errors') || '[]');
        existingErrors.push(errorLog);
        
        // Keep only last 10 errors to prevent storage bloat
        if (existingErrors.length > 10) {
          existingErrors.splice(0, existingErrors.length - 10);
        }
        
        localStorage.setItem('mobile-errors', JSON.stringify(existingErrors));
      } catch (storageError) {
        console.warn('Failed to store error log:', storageError);
      }
    }
  };

  const measurePerformance = <T,>(operation: string, fn: () => T | Promise<T>): T | Promise<T> => {
    // Simplified performance tracking
    const startTime = performance.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - startTime;
          if (duration > 1000) { // Only log slow operations
            console.warn(`Slow operation: ${operation} took ${duration.toFixed(2)}ms`);
          }
        });
      } else {
        const duration = performance.now() - startTime;
        if (duration > 100) { // Only log slow sync operations
          console.warn(`Slow sync operation: ${operation} took ${duration.toFixed(2)}ms`);
        }
        return result;
      }
    } catch (error) {
      captureError(error, { operation });
      throw error;
    }
  };

  return (
    <MobileMonitoringContext.Provider value={{
      isMonitoringActive: true,
      captureError,
      measurePerformance
    }}>
      {children}
    </MobileMonitoringContext.Provider>
  );
};
