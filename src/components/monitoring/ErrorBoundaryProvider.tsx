
import React from 'react';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { ErrorMonitor } from '@/utils/errorMonitoring';

interface ErrorBoundaryProviderProps {
  children: React.ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export const ErrorBoundaryProvider: React.FC<ErrorBoundaryProviderProps> = ({ 
  children, 
  onError 
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Report to monitoring service
    ErrorMonitor.getInstance().captureError(error, {
      level: 'error',
      additionalData: {
        componentStack: errorInfo.componentStack,
        source: 'database-monitoring'
      }
    });

    // Call custom error handler if provided
    onError?.(error, errorInfo);
  };

  return (
    <ErrorBoundary onError={handleError}>
      {children}
    </ErrorBoundary>
  );
};
