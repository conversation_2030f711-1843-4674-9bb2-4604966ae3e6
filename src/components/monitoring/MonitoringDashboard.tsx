
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RefreshCw, Activity, AlertTriangle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { healthCheckManager, type SystemHealth } from '@/lib/monitoring/healthChecks';
import { alertingSystem, type Alert as AlertType } from '@/lib/monitoring/alertingSystem';
import { systemMonitor, type MonitoringMetrics } from '@/lib/monitoring/systemMonitor';
import { disasterRecovery } from '@/lib/monitoring/disasterRecovery';

export function MonitoringDashboard() {
  const [healthStatus, setHealthStatus] = useState<SystemHealth | null>(null);
  const [alerts, setAlerts] = useState<AlertType[]>([]);
  const [metrics, setMetrics] = useState<MonitoringMetrics[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('health');

  useEffect(() => {
    // Start system monitoring
    systemMonitor.startMonitoring(30000); // 30 seconds
    
    // Initial data load
    refreshData();
    
    // Set up periodic refresh
    const interval = setInterval(refreshData, 30000);
    
    return () => {
      clearInterval(interval);
      systemMonitor.stopMonitoring();
    };
  }, []);

  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      const [health, alertData, metricsData] = await Promise.all([
        healthCheckManager.runAllChecks(),
        Promise.resolve(alertingSystem.getAlerts()),
        Promise.resolve(systemMonitor.getMetrics(10))
      ]);
      
      setHealthStatus(health);
      setAlerts(alertData);
      setMetrics(metricsData);
    } catch (error) {
      console.error('Failed to refresh monitoring data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'destructive';
      case 'error':
        return 'destructive';
      case 'warning':
        return 'default';
      case 'info':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const testRecoveryPlan = async (planId: string) => {
    try {
      const result = await disasterRecovery.testRecoveryPlan(planId, true);
      alertingSystem.triggerAlert({
        level: 'info',
        title: 'Recovery Plan Test',
        message: `Recovery plan test ${result.success ? 'passed' : 'failed'} in ${result.duration}ms`,
        source: 'disaster-recovery-test'
      });
      refreshData();
    } catch (error) {
      console.error('Recovery plan test failed:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-gray-600">Monitor system health, alerts, and performance metrics</p>
        </div>
        <Button onClick={refreshData} disabled={isRefreshing} variant="outline">
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* System Status Overview */}
      {healthStatus && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                {getStatusIcon(healthStatus.status)}
                <div>
                  <p className="text-sm font-medium text-gray-600">System Status</p>
                  <p className={`text-lg font-bold capitalize ${healthStatus.status === 'healthy' ? 'text-green-600' : healthStatus.status === 'degraded' ? 'text-yellow-600' : 'text-red-600'}`}>
                    {healthStatus.status}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <Activity className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Uptime</p>
                  <p className="text-lg font-bold text-gray-900">
                    {Math.round(healthStatus.uptime / (1000 * 60))}m
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Healthy Checks</p>
                  <p className="text-lg font-bold text-gray-900">
                    {healthStatus.summary.healthy}/{healthStatus.summary.total}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                  <p className="text-lg font-bold text-gray-900">
                    {alerts.filter(a => !a.resolvedAt).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="health">Health Checks</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="recovery">Disaster Recovery</TabsTrigger>
        </TabsList>

        <TabsContent value="health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Health Check Details</CardTitle>
              <CardDescription>
                Status of individual system components
              </CardDescription>
            </CardHeader>
            <CardContent>
              {healthStatus ? (
                <div className="space-y-3">
                  {healthStatus.checks.map((check) => (
                    <div key={check.name} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(check.status)}
                        <div>
                          <p className="font-medium capitalize">{check.name}</p>
                          {check.error && (
                            <p className="text-sm text-red-600">{check.error}</p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(check.status)}>
                          {check.status}
                        </Badge>
                        <p className="text-xs text-gray-500 mt-1">
                          {Math.round(check.responseTime)}ms
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">Loading health check data...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>
                Recent alerts and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              {alerts.length > 0 ? (
                <div className="space-y-3">
                  {alerts.slice(0, 10).map((alert) => (
                    <Alert key={alert.id} className="relative">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <div className="flex items-start justify-between">
                          <div>
                            <p className="font-medium">{alert.title}</p>
                            <p className="text-sm text-gray-600">{alert.message}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(alert.timestamp).toLocaleString()} • {alert.source}
                            </p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={getAlertColor(alert.level) as any}>
                              {alert.level}
                            </Badge>
                            {alert.acknowledged && (
                              <Badge variant="outline">Acked</Badge>
                            )}
                            {alert.resolvedAt && (
                              <Badge variant="secondary">Resolved</Badge>
                            )}
                          </div>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No alerts found</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Metrics</CardTitle>
              <CardDescription>
                System performance over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {metrics.length > 0 ? (
                <div className="space-y-4">
                  {metrics.slice(0, 5).map((metric, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm text-gray-500">
                          {new Date(metric.timestamp).toLocaleString()}
                        </p>
                        <Badge className={getStatusColor(metric.health.status)}>
                          {metric.health.status}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-600">Response Time</p>
                          <p className="font-medium">{Math.round(metric.performance.responseTime)}ms</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Memory Usage</p>
                          <p className="font-medium">{Math.round(metric.performance.memoryUsage)}MB</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Error Rate</p>
                          <p className="font-medium">{(metric.performance.errorRate * 100).toFixed(2)}%</p>
                        </div>
                        <div>
                          <p className="text-gray-600">Throughput</p>
                          <p className="font-medium">{metric.performance.throughput.toFixed(2)}/s</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No metrics data available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recovery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Disaster Recovery Plans</CardTitle>
              <CardDescription>
                Test and manage disaster recovery procedures
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {disasterRecovery.getRecoveryPlans().map((plan) => (
                  <div key={plan.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{plan.name}</h3>
                      <div className="flex items-center space-x-2">
                        <Badge variant={plan.priority === 'critical' ? 'destructive' : 'default'}>
                          {plan.priority}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => testRecoveryPlan(plan.id)}
                        >
                          Test Plan
                        </Button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">RTO (Recovery Time Objective)</p>
                        <p className="font-medium">{plan.rto} minutes</p>
                      </div>
                      <div>
                        <p className="text-gray-600">RPO (Recovery Point Objective)</p>
                        <p className="font-medium">{plan.rpo} minutes</p>
                      </div>
                    </div>
                    <div className="mt-2">
                      <p className="text-xs text-gray-500">
                        {plan.steps.length} steps • Est. {plan.steps.reduce((sum, step) => sum + step.estimatedTimeMinutes, 0)} minutes
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
