
import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { errorTracker } from '@/lib/monitoring/core/errorTracker';
import { healthMonitor } from '@/lib/monitoring/core/healthMonitor';

interface MonitoringContextType {
  isMonitoringActive: boolean;
  captureError: (error: any, context?: any) => void;
  measurePerformance: <T>(operation: string, fn: () => T | Promise<T>) => T | Promise<T>;
}

const MonitoringContext = createContext<MonitoringContextType>({
  isMonitoringActive: false,
  captureError: () => {},
  measurePerformance: (_, fn) => fn()
});

export const useMonitoring = () => useContext(MonitoringContext);

interface MonitoringProviderProps {
  children: ReactNode;
  enableHealthMonitoring?: boolean;
  healthCheckInterval?: number;
}

export const MonitoringProvider: React.FC<MonitoringProviderProps> = ({
  children,
  enableHealthMonitoring = true,
  healthCheckInterval = 60000
}) => {
  useEffect(() => {
    // Start health monitoring if enabled
    if (enableHealthMonitoring) {
      healthMonitor.startMonitoring(healthCheckInterval);
    }

    // Cleanup on unmount
    return () => {
      if (enableHealthMonitoring) {
        healthMonitor.stopMonitoring();
      }
      errorTracker.destroy();
    };
  }, [enableHealthMonitoring, healthCheckInterval]);

  const captureError = (error: any, context?: any) => {
    errorTracker.captureError({
      level: 'error',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      context
    });
  };

  const measurePerformance = <T,>(operation: string, fn: () => T | Promise<T>): T | Promise<T> => {
    return errorTracker.measurePerformance(operation, fn);
  };

  return (
    <MonitoringContext.Provider value={{
      isMonitoringActive: true,
      captureError,
      measurePerformance
    }}>
      {children}
    </MonitoringContext.Provider>
  );
};
