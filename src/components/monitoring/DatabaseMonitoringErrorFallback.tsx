
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Database } from 'lucide-react';
import { useErrorRecovery } from '@/hooks/useErrorRecovery';

interface DatabaseMonitoringErrorFallbackProps {
  error?: Error;
  onRetry?: () => Promise<void>;
  onReset?: () => void;
}

export const DatabaseMonitoringErrorFallback: React.FC<DatabaseMonitoringErrorFallbackProps> = ({
  error,
  onRetry,
  onReset
}) => {
  const { retry, isRetrying, canRetry, retryCount } = useErrorRecovery();

  const handleRetry = async () => {
    if (onRetry) {
      await retry(onRetry);
    }
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Database Monitoring Error</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Database className="h-4 w-4" />
            <AlertDescription>
              There was an issue loading the database monitoring dashboard. 
              This could be due to connectivity issues or insufficient permissions.
            </AlertDescription>
          </Alert>

          {error && import.meta.env.DEV && (
            <details className="text-sm">
              <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                Error Details (Development)
              </summary>
              <pre className="mt-2 whitespace-pre-wrap bg-gray-100 p-2 rounded text-xs overflow-auto">
                {error.message}
                {error.stack && `\n\n${error.stack}`}
              </pre>
            </details>
          )}

          <div className="flex flex-col gap-2">
            <Button 
              onClick={handleRetry} 
              disabled={isRetrying || !canRetry}
              className="w-full"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </Button>
            
            {retryCount > 0 && (
              <p className="text-sm text-gray-600 text-center">
                Retry attempt {retryCount}/3
              </p>
            )}

            <Button 
              variant="outline" 
              onClick={onReset}
              className="w-full"
            >
              Reset Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
