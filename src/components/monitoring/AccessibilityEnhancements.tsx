
import React, { useEffect, useRef } from 'react';

interface AccessibilityEnhancementsProps {
  children: React.ReactNode;
}

export const AccessibilityEnhancements: React.FC<AccessibilityEnhancementsProps> = ({ children }) => {
  const skipLinkRef = useRef<HTMLAnchorElement>(null);

  useEffect(() => {
    // Enhanced keyboard navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      // Skip to main content (Ctrl/Cmd + /)
      if ((event.ctrlKey || event.metaKey) && event.key === '/') {
        event.preventDefault();
        const mainContent = document.getElementById('main-content');
        if (mainContent) {
          mainContent.focus();
          mainContent.scrollIntoView({ behavior: 'smooth' });
        }
      }

      // Quick navigation with Alt + number keys
      if (event.altKey && /[1-4]/.test(event.key)) {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        const tabs = document.querySelectorAll('[role="tab"]');
        if (tabs[tabIndex]) {
          (tabs[tabIndex] as HTMLElement).click();
          (tabs[tabIndex] as HTMLElement).focus();
        }
      }

      // Escape key to close modals/dialogs
      if (event.key === 'Escape') {
        const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]');
        if (activeModal) {
          const closeButton = activeModal.querySelector('[aria-label*="close"], [aria-label*="Close"]');
          if (closeButton) {
            (closeButton as HTMLElement).click();
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div className="relative">
      {/* Skip to main content link */}
      <a
        ref={skipLinkRef}
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 bg-blue-600 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        onFocus={(e) => e.target.scrollIntoView({ behavior: 'smooth' })}
      >
        Skip to main content
      </a>
      
      {/* Keyboard shortcuts help */}
      <div className="sr-only" aria-live="polite" id="keyboard-shortcuts-help">
        Keyboard shortcuts: Ctrl+/ to skip to main content, Alt+1-4 for quick tab navigation, Escape to close dialogs
      </div>
      
      <main id="main-content" tabIndex={-1} className="focus:outline-none">
        {children}
      </main>
    </div>
  );
};
