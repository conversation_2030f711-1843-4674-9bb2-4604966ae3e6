import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Database, 
  HardDrive, 
  Activity, 
  RefreshCw, 
  Download, 
  GitBranch,
  Gauge,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { databaseMonitoring, type DatabaseBackup, type MigrationRecord, type DatabasePerformanceMetrics } from '@/lib/monitoring/databaseMonitoring';
import { ErrorBoundaryProvider } from './ErrorBoundaryProvider';
import { DatabaseMonitoringErrorFallback } from './DatabaseMonitoringErrorFallback';
import { useErrorRecovery } from '@/hooks/useErrorRecovery';
import { useToast } from '@/hooks/use-toast';

export function DatabaseMonitoringDashboard() {
  const [backups, setBackups] = useState<DatabaseBackup[]>([]);
  const [migrations, setMigrations] = useState<MigrationRecord[]>([]);
  const [metrics, setMetrics] = useState<DatabasePerformanceMetrics[]>([]);
  const [healthReport, setHealthReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  
  const { retry, isRetrying } = useErrorRecovery();
  const { toast } = useToast();

  // Memoized computed values for performance
  const latestMetrics = useMemo(() => metrics[0], [metrics]);
  
  const healthStats = useMemo(() => {
    if (!healthReport) return null;
    
    return {
      overallHealth: healthReport.backupStatus === 'healthy' && 
                    healthReport.performanceStatus === 'healthy' && 
                    healthReport.connectionStatus === 'healthy',
      criticalIssues: healthReport.recommendations?.length || 0,
      statusCounts: {
        healthy: [healthReport.backupStatus, healthReport.performanceStatus, healthReport.connectionStatus]
                 .filter(status => status === 'healthy').length,
        warning: [healthReport.backupStatus, healthReport.performanceStatus, healthReport.connectionStatus]
                 .filter(status => status === 'warning').length,
        critical: [healthReport.backupStatus, healthReport.performanceStatus, healthReport.connectionStatus]
                  .filter(status => status === 'critical').length
      }
    };
  }, [healthReport]);

  const loadDashboardData = async () => {
    try {
      const [backupsData, migrationsData, metricsData, healthData] = await Promise.all([
        databaseMonitoring.getBackups(10),
        databaseMonitoring.getMigrationHistory(10),
        databaseMonitoring.getPerformanceMetrics(20),
        databaseMonitoring.generateHealthReport()
      ]);

      setBackups(backupsData);
      setMigrations(migrationsData);
      setMetrics(metricsData);
      setHealthReport(healthData);
      setLastRefresh(new Date());
      
      toast({
        title: "Dashboard Updated",
        description: "Database monitoring data refreshed successfully",
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast({
        title: "Update Failed",
        description: "Could not refresh monitoring data. Retrying...",
        variant: "destructive"
      });
      throw error;
    }
  };

  useEffect(() => {
    const initializeDashboard = async () => {
      setIsLoading(true);
      await retry(loadDashboardData);
      setIsLoading(false);
    };

    initializeDashboard();
    
    // Start monitoring if not already running
    if (!databaseMonitoring.isCurrentlyMonitoring()) {
      databaseMonitoring.startMonitoring(30000);
    }

    // Set up periodic refresh with error handling
    const interval = setInterval(async () => {
      try {
        await loadDashboardData();
      } catch (error) {
        console.error('Periodic refresh failed:', error);
        // Don't show toast for background failures to avoid spam
      }
    }, 60000);
    
    return () => {
      clearInterval(interval);
    };
  }, [retry]);

  const handleManualRefresh = async () => {
    await retry(loadDashboardData);
  };

  const initiateBackup = async (backupType: 'full' | 'incremental' | 'point_in_time') => {
    try {
      await databaseMonitoring.initiateBackup(backupType);
      await handleManualRefresh();
      
      toast({
        title: "Backup Initiated",
        description: `${backupType} backup has been started successfully`,
      });
    } catch (error) {
      console.error('Failed to initiate backup:', error);
      toast({
        title: "Backup Failed",
        description: "Could not start the backup process",
        variant: "destructive"
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'warning':
      case 'running':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'critical':
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
      case 'running':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical':
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatBytes = (bytes?: number) => {
    if (!bytes) return 'N/A';
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-blue-500" />
          <p className="text-gray-600">Loading database monitoring dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundaryProvider 
      onError={(error) => console.error('Dashboard error:', error)}
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Database Production Configuration</h1>
            <p className="text-gray-600">
              Monitor backups, performance, and database health
              {lastRefresh && (
                <span className="ml-2 text-sm text-gray-500">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </span>
              )}
            </p>
          </div>
          <Button 
            onClick={handleManualRefresh} 
            disabled={isRetrying} 
            variant="outline"
            className="min-w-[120px]"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>

        {/* Health Overview with enhanced stats */}
        {healthReport && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(healthReport.backupStatus)}
                  <div>
                    <p className="text-sm font-medium text-gray-600">Backup Status</p>
                    <Badge className={getStatusColor(healthReport.backupStatus)}>
                      {healthReport.backupStatus}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(healthReport.performanceStatus)}
                  <div>
                    <p className="text-sm font-medium text-gray-600">Performance Status</p>
                    <Badge className={getStatusColor(healthReport.performanceStatus)}>
                      {healthReport.performanceStatus}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(healthReport.connectionStatus)}
                  <div>
                    <p className="text-sm font-medium text-gray-600">Connection Status</p>
                    <Badge className={getStatusColor(healthReport.connectionStatus)}>
                      {healthReport.connectionStatus}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced recommendations with better UX */}
        {healthReport?.recommendations && healthReport.recommendations.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <p className="font-medium">
                  {healthReport.recommendations.length} Recommendation{healthReport.recommendations.length > 1 ? 's' : ''}:
                </p>
                <ul className="list-disc list-inside space-y-1">
                  {healthReport.recommendations.map((rec: string, index: number) => (
                    <li key={index} className="text-sm">{rec}</li>
                  ))}
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="backups">
              Backups
              {backups.filter(b => b.backupStatus === 'failed').length > 0 && (
                <Badge variant="destructive" className="ml-2 text-xs">
                  {backups.filter(b => b.backupStatus === 'failed').length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="migrations">Migrations</TabsTrigger>
          </TabsList>

          {/* Keep existing tab content but add error boundaries */}
          <TabsContent value="overview" className="space-y-4">
            <ErrorBoundaryProvider>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2">
                      <Database className="h-4 w-4 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-600">Active Connections</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {latestMetrics?.activeConnections || 0}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-green-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Connections</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {latestMetrics?.connectionCount || 0}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2">
                      <Gauge className="h-4 w-4 text-purple-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-600">Cache Hit Ratio</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {latestMetrics?.cacheHitRatio ? `${(latestMetrics.cacheHitRatio * 100).toFixed(1)}%` : 'N/A'}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-2">
                      <HardDrive className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-600">Database Size</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {formatBytes(latestMetrics?.databaseSize)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </ErrorBoundaryProvider>
          </TabsContent>

          {/* Keep existing backup tab content with enhanced error handling */}
          <TabsContent value="backups" className="space-y-4">
            <ErrorBoundaryProvider>
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Database Backups</CardTitle>
                      <CardDescription>
                        Manage and monitor database backup operations
                      </CardDescription>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" onClick={() => initiateBackup('full')}>
                        <Download className="h-4 w-4 mr-2" />
                        Full Backup
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => initiateBackup('incremental')}>
                        Incremental
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {backups.map((backup) => (
                      <div key={backup.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          {getStatusIcon(backup.backupStatus)}
                          <div>
                            <p className="font-medium capitalize">{backup.backupType} Backup</p>
                            <p className="text-sm text-gray-600">
                              Started: {new Date(backup.startedAt).toLocaleString()}
                            </p>
                            {backup.errorMessage && (
                              <p className="text-sm text-red-600">{backup.errorMessage}</p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge className={getStatusColor(backup.backupStatus)}>
                            {backup.backupStatus}
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            {formatBytes(backup.backupSize)}
                          </p>
                        </div>
                      </div>
                    ))}
                    {backups.length === 0 && (
                      <div className="text-center py-8">
                        <Database className="h-12 w-12 mx-auto text-gray-400 mb-2" />
                        <p className="text-gray-500">No backups found</p>
                        <p className="text-sm text-gray-400">Create your first backup to get started</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </ErrorBoundaryProvider>
          </TabsContent>

          {/* Keep existing performance and migrations tabs with error boundaries */}
          <TabsContent value="performance" className="space-y-4">
            <ErrorBoundaryProvider>
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>
                    Real-time database performance monitoring
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {metrics.slice(0, 10).map((metric, index) => (
                      <div key={metric.id} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm text-gray-500">
                            {new Date(metric.timestamp).toLocaleString()}
                          </p>
                          <Badge variant="outline">Sample {index + 1}</Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Connections</p>
                            <p className="font-medium">{metric.activeConnections}/{metric.maxConnections}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Cache Hit Ratio</p>
                            <p className="font-medium">
                              {metric.cacheHitRatio ? `${(metric.cacheHitRatio * 100).toFixed(1)}%` : 'N/A'}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Lock Waits</p>
                            <p className="font-medium">{metric.lockWaits || 0}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">Deadlocks</p>
                            <p className="font-medium">{metric.deadlocks || 0}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </ErrorBoundaryProvider>
          </TabsContent>

          <TabsContent value="migrations" className="space-y-4">
            <ErrorBoundaryProvider>
              <Card>
                <CardHeader>
                  <CardTitle>Migration History</CardTitle>
                  <CardDescription>
                    Database migration tracking and rollback management
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {migrations.map((migration) => (
                      <div key={migration.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <GitBranch className="h-4 w-4 text-blue-500" />
                          <div>
                            <p className="font-medium">{migration.migrationName}</p>
                            <p className="text-sm text-gray-600">
                              Version: {migration.migrationVersion} • 
                              Applied: {new Date(migration.appliedAt).toLocaleString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{migration.environment}</Badge>
                          {migration.rollbackAvailable && (
                            <Badge variant="secondary">Rollback Available</Badge>
                          )}
                        </div>
                      </div>
                    ))}
                    {migrations.length === 0 && (
                      <p className="text-gray-500 text-center py-8">No migration history found</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </ErrorBoundaryProvider>
          </TabsContent>
        </Tabs>
      </div>
    </ErrorBoundaryProvider>
  );
}
