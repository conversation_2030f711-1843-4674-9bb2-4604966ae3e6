import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import LoadingState from './LoadingState';
import type { Profile } from '@/types/database';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
  requiredRole?: Profile['role'];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
  requiredRole,
}) => {
  const { user, profile, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <LoadingState fullScreen />;
  }

  if (!user) {
    // Redirect to login page but save the attempted url
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Backward compatibility: check requireAdmin prop first
  if (requireAdmin && profile?.role !== 'admin' && profile?.role !== 'super_admin') {
    // Redirect to dashboard if user is not an admin
    return <Navigate to="/dashboard" replace />;
  }

  // New granular role checking
  if (requiredRole && profile?.role !== requiredRole) {
    // For super_admin, allow access to any role requirement
    if (profile?.role === 'super_admin') {
      return <>{children}</>;
    }
    
    // For admin, allow access to non-admin roles
    if (profile?.role === 'admin' && requiredRole !== 'super_admin') {
      return <>{children}</>;
    }
    
    // Otherwise, redirect to dashboard if role doesn't match
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
