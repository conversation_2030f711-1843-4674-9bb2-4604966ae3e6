
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Settings, Loader2 } from 'lucide-react';
import { useCustomerPortal } from '@/hooks/useCustomerPortal';

interface ManageSubscriptionButtonProps {
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

const ManageSubscriptionButton: React.FC<ManageSubscriptionButtonProps> = ({
  variant = 'outline',
  size = 'default',
  className,
  children
}) => {
  const { openCustomerPortal, loading } = useCustomerPortal();

  return (
    <Button
      onClick={openCustomerPortal}
      disabled={loading}
      variant={variant}
      size={size}
      className={className}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <Settings className="h-4 w-4 mr-2" />
      )}
      {children || "Manage Subscription"}
    </Button>
  );
};

export default ManageSubscriptionButton;
