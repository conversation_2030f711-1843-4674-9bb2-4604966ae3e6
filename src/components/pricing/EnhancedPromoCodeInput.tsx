
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { promoCampaignsApi } from '@/lib/api/promo-campaigns';
import { useBetaPromoValidation } from '@/hooks/useBetaPromoValidation';
import { BetaPromoCard } from './BetaPromoCard';
import { toast } from '@/hooks/use-toast';
import { Tag, X, Sparkles } from 'lucide-react';

interface EnhancedPromoCodeInputProps {
  onPromoApplied?: (code: string, discount: { type: string; value: number }) => void;
  onPromoRemoved?: () => void;
  appliedPromo?: { code: string; discount: { type: string; value: number } };
}

export const EnhancedPromoCodeInput: React.FC<EnhancedPromoCodeInputProps> = ({
  onPromoApplied,
  onPromoRemoved,
  appliedPromo
}) => {
  const [promoCode, setPromoCode] = useState('');
  const [loading, setLoading] = useState(false);
  const { validateBetaEligibility } = useBetaPromoValidation();

  const handleApplyPromo = async () => {
    if (!promoCode.trim()) return;

    setLoading(true);
    try {
      const result = await promoCampaignsApi.validatePromoCode(promoCode.trim());
      
      if (result.success && result.data?.valid) {
        const discount = {
          type: result.data.discount_type || 'percentage',
          value: result.data.discount_value || 0
        };
        
        if (onPromoApplied) {
          onPromoApplied(promoCode.trim(), discount);
        }
        
        setPromoCode('');
        toast({
          title: 'Promo Code Applied!',
          description: `${discount.value}${discount.type === 'percentage' ? '%' : '$'} discount applied`,
        });
      } else {
        toast({
          title: 'Invalid Code',
          description: result.data?.error_message || 'Please check your promo code and try again',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error applying promo code:', error);
      toast({
        title: 'Error',
        description: 'Failed to apply promo code',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleBetaPromoApplied = async (discount: { type: string; value: number }) => {
    if (onPromoApplied) {
      onPromoApplied('BETA-LIFETIME-90', discount);
    }
  };

  const handleRemovePromo = () => {
    if (onPromoRemoved) {
      onPromoRemoved();
    }
  };

  return (
    <div className="space-y-4">
      {/* Beta Promo Card */}
      <BetaPromoCard onPromoApplied={handleBetaPromoApplied} />

      {/* Applied Promo Display */}
      {appliedPromo && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                {appliedPromo.code === 'BETA-LIFETIME-90' ? (
                  <Sparkles className="h-4 w-4 text-primary" />
                ) : (
                  <Tag className="h-4 w-4 text-green-600" />
                )}
                <span className="font-medium text-green-800">
                  {appliedPromo.code}
                </span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  {appliedPromo.discount.value}
                  {appliedPromo.discount.type === 'percentage' ? '% OFF' : '$ OFF'}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemovePromo}
                className="text-green-600 hover:text-green-800"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Regular Promo Code Input */}
      {!appliedPromo && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Have a Promo Code?</CardTitle>
            <CardDescription>
              Enter your promo code to get a discount on your subscription
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Input
                placeholder="Enter promo code"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
                onKeyPress={(e) => e.key === 'Enter' && handleApplyPromo()}
                className="flex-1"
              />
              <Button 
                onClick={handleApplyPromo}
                disabled={loading || !promoCode.trim()}
              >
                {loading ? 'Applying...' : 'Apply'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
