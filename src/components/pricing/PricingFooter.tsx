
import React from 'react';
import ContextualFAQSuggestions from '@/components/faq/ContextualFAQSuggestions';

const PricingFooter: React.FC = () => {
  return (
    <div className="text-center mt-12 space-y-8">
      {/* Contextual FAQ for Pricing */}
      <div className="max-w-2xl mx-auto">
        <ContextualFAQSuggestions context="pricing" limit={3} />
      </div>

      <p className="text-sm text-muted-foreground">
        All plans include free updates and 30-day money-back guarantee
      </p>
      <p className="text-xs text-muted-foreground">
        Need enterprise features? <a href="/contact" className="text-primary hover:underline">Contact us</a>
      </p>
      
      <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto mt-8 pt-8 border-t border-muted-foreground/20">
        <div className="text-center">
          <div className="text-2xl font-bold gold-gradient mb-2">Professional</div>
          <div className="text-muted-foreground text-sm">Grade Tools</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold gold-gradient mb-2">No Lock-in</div>
          <div className="text-muted-foreground text-sm">Cancel Anytime</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold gold-gradient mb-2">24/7</div>
          <div className="text-muted-foreground text-sm">Support</div>
        </div>
      </div>
    </div>
  );
};

export default PricingFooter;
