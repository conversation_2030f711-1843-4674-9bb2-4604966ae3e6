
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Zap } from "lucide-react";
import { featureDisplayNames, limitDisplayNames } from "./planData";

interface PlanCardProps {
  plan: any; // Database plan object
  index: number;
  onSelectPlan: (planId: string) => void;
  isLoading: boolean;
  isYearly?: boolean;
  isCurrentPlan?: boolean;
}

const PlanCard = ({ 
  plan, 
  index, 
  onSelectPlan, 
  isLoading, 
  isYearly = false,
  isCurrentPlan = false 
}: PlanCardProps) => {
  const currentPrice = isYearly && plan.price_yearly ? plan.price_yearly : plan.price_monthly;
  const originalPrice = plan.price_monthly * (isYearly ? 12 : 1);
  const savings = isYearly && plan.price_yearly 
    ? Math.round(((originalPrice - plan.price_yearly) / originalPrice) * 100)
    : 0;

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  const formatLimitValue = (value: any): string => {
    if (value === -1) return 'Unlimited';
    if (typeof value === 'number') return value.toString();
    if (typeof value === 'string') return value;
    return '0';
  };

  return (
    <Card 
      className={`cinema-card p-8 relative transition-all duration-300 hover:scale-105 animate-fade-scale ${
        plan.is_popular ? 'ring-2 ring-primary' : ''
      } ${isCurrentPlan ? 'bg-primary/5' : ''}`}
      style={{ animationDelay: `${index * 100}ms` }}
    >
      {plan.is_popular && (
        <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
          Most Popular
        </Badge>
      )}
      
      <div className="text-center">
        <h3 className="text-2xl font-playfair font-bold mb-2">{plan.display_name}</h3>
        <div className="mb-4">
          <span className="text-4xl font-bold gold-gradient">
            {formatPrice(currentPrice)}
          </span>
          <span className="text-muted-foreground">
            /{isYearly ? 'year' : 'month'}
          </span>
          {isYearly && savings > 0 && (
            <div className="mt-1">
              <Badge variant="secondary" className="text-xs">
                Save {savings}%
              </Badge>
            </div>
          )}
        </div>
        <p className="text-muted-foreground mb-6">{plan.description}</p>
        
        <div className="space-y-3 mb-6 text-left">
          {/* Features */}
          {Object.entries(plan.features || {}).map(([key, value]) => {
            if (value !== true) return null;
            return (
              <div key={key} className="flex items-center text-sm">
                <Check className="w-4 h-4 text-primary mr-3 flex-shrink-0" />
                <span>{featureDisplayNames[key] || key}</span>
              </div>
            );
          })}
          
          {/* Limits */}
          {Object.entries(plan.limits || {}).map(([key, value]) => (
            <div key={key} className="flex items-center text-sm">
              <Zap className="w-4 h-4 text-orange-500 mr-3 flex-shrink-0" />
              <span>
                {limitDisplayNames[key] || key}: {formatLimitValue(value)}
              </span>
            </div>
          ))}
        </div>
        
        <Button 
          className={`w-full transition-all duration-200 ${
            plan.is_popular 
              ? 'bg-primary hover:bg-primary/90 text-primary-foreground' 
              : 'bg-cinema-700 hover:bg-cinema-600 border border-gold-400/30 text-gold-400 font-semibold'
          } ${isLoading ? 'opacity-75 scale-95' : ''}`}
          onClick={() => onSelectPlan(plan.plan_id)}
          disabled={isLoading || isCurrentPlan}
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              Processing...
            </div>
          ) : isCurrentPlan ? (
            'Current Plan'
          ) : (
            `Get ${plan.display_name}`
          )}
        </Button>
      </div>
    </Card>
  );
};

export default PlanCard;
