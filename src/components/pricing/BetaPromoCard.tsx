
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useBetaPromoValidation } from '@/hooks/useBetaPromoValidation';
import { Sparkles, Clock, Users } from 'lucide-react';

interface BetaPromoCardProps {
  onPromoApplied?: (discount: { type: string; value: number }) => void;
}

export const BetaPromoCard: React.FC<BetaPromoCardProps> = ({ onPromoApplied }) => {
  const { validateBetaEligibility, loading } = useBetaPromoValidation();
  const [isEligible, setIsEligible] = useState(false);
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    const checkEligibility = async () => {
      setChecking(true);
      const validation = await validateBetaEligibility();
      setIsEligible(validation.isEligible);
      setChecking(false);
    };

    checkEligibility();
  }, [validateBetaEligibility]);

  const handleApplyPromo = () => {
    if (onPromoApplied) {
      onPromoApplied({ type: 'percentage', value: 90 });
    }
  };

  if (checking) {
    return (
      <Card className="border-dashed border-muted-foreground/30">
        <CardContent className="pt-6">
          <div className="text-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Checking beta eligibility...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!isEligible) {
    return null;
  }

  return (
    <Card className="border-2 border-primary bg-gradient-to-br from-primary/5 to-purple-500/5 relative overflow-hidden">
      <div className="absolute top-2 right-2">
        <Sparkles className="h-6 w-6 text-primary animate-pulse" />
      </div>
      
      <CardHeader>
        <div className="flex items-center gap-2 mb-2">
          <Badge className="bg-primary text-primary-foreground">
            EXCLUSIVE BETA
          </Badge>
          <Badge variant="outline" className="text-xs">
            LIMITED TIME
          </Badge>
        </div>
        <CardTitle className="text-xl font-bold bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
          90% Lifetime Discount
        </CardTitle>
        <CardDescription>
          Exclusive offer for beta testers - Save 90% on any plan, forever!
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-primary" />
            <span>Lifetime savings</span>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-primary" />
            <span>Beta exclusive</span>
          </div>
        </div>

        <div className="bg-background/50 rounded-lg p-3 border">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">90% OFF</p>
            <p className="text-xs text-muted-foreground">
              Pay only 10% of the regular price
            </p>
          </div>
        </div>

        <Button 
          onClick={handleApplyPromo}
          disabled={loading}
          className="w-full bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90"
        >
          {loading ? 'Applying...' : 'Apply Beta Discount'}
        </Button>

        <p className="text-xs text-center text-muted-foreground">
          This exclusive offer is only available to active beta testers and can only be used once.
        </p>
      </CardContent>
    </Card>
  );
};
