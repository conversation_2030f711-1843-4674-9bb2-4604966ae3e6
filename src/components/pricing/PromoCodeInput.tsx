
import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X, Loader2, Tag } from 'lucide-react';
import { toast } from 'sonner';

interface PromoCodeInputProps {
  onPromoApplied: (code: string, discount: { type: string; value: number }) => void;
  onPromoRemoved: () => void;
  appliedPromo?: { code: string; discount: { type: string; value: number } };
  disabled?: boolean;
}

const PromoCodeInput: React.FC<PromoCodeInputProps> = ({
  onPromoApplied,
  onPromoRemoved,
  appliedPromo,
  disabled = false
}) => {
  const [promoCode, setPromoCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);

  const validatePromoCode = async () => {
    if (!promoCode.trim()) return;

    setIsValidating(true);
    try {
      // Simulate API call - replace with actual implementation
      const response = await fetch('/api/validate-promo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: promoCode })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.valid) {
          onPromoApplied(promoCode, {
            type: result.discount_type,
            value: result.discount_value
          });
          toast.success('Promo code applied!', {
            description: `${result.discount_value}${result.discount_type === 'percentage' ? '%' : '$'} discount applied`
          });
        } else {
          toast.error('Invalid promo code', {
            description: result.error_message || 'This promo code is not valid'
          });
        }
      } else {
        throw new Error('Failed to validate promo code');
      }
    } catch (error) {
      console.error('Promo validation error:', error);
      toast.error('Validation failed', {
        description: 'Unable to validate promo code. Please try again.'
      });
    } finally {
      setIsValidating(false);
    }
  };

  const removePromo = () => {
    setPromoCode('');
    onPromoRemoved();
    toast.success('Promo code removed');
  };

  if (appliedPromo) {
    return (
      <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-center space-x-2">
          <Check className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-green-800">
            Promo "{appliedPromo.code}" applied
          </span>
          <Badge variant="secondary" className="text-green-700">
            {appliedPromo.discount.value}
            {appliedPromo.discount.type === 'percentage' ? '%' : '$'} off
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={removePromo}
          disabled={disabled}
          className="text-green-600 hover:text-green-700"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center space-x-2">
        <Tag className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">Have a promo code?</span>
      </div>
      <div className="flex space-x-2">
        <Input
          placeholder="Enter promo code"
          value={promoCode}
          onChange={(e) => setPromoCode(e.target.value.toUpperCase())}
          disabled={disabled || isValidating}
          onKeyPress={(e) => e.key === 'Enter' && validatePromoCode()}
        />
        <Button
          onClick={validatePromoCode}
          disabled={!promoCode.trim() || disabled || isValidating}
          variant="outline"
        >
          {isValidating ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            'Apply'
          )}
        </Button>
      </div>
    </div>
  );
};

export default PromoCodeInput;
