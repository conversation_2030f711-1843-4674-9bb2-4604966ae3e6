
// Static plan data for fallback when database plans are not available
export const fallbackPlans = [
  {
    name: "Starter",
    description: "Perfect for aspiring screenwriters getting their ideas down",
    priceAmount: 2900, // $29.00 in cents
    features: [
      "3 active projects",
      "Screenplay editor", 
      "Standard AI suggestions",
      "PDF export",
      "Email support"
    ],
    popular: false
  },
  {
    name: "Pro Solo", 
    description: "For serious independent writers ready to polish and sell their work",
    priceAmount: 4900, // $49.00 in cents
    features: [
      "10 active projects",
      "Expanded AI suggestions",
      "Marketplace submission access", 
      "All export formats (FDX, Fountain)",
      "Priority support"
    ],
    popular: true
  },
  {
    name: "Pro Team",
    description: "For collaborative writing teams and small production groups", 
    priceAmount: 7900, // $79.00 in cents
    features: [
      "10 active projects",
      "Team collaboration tools",
      "Visual storyboarding",
      "Admin controls",
      "Centralized billing"
    ],
    popular: false
  },
  {
    name: "Studio",
    description: "For production companies and studios managing a slate of projects",
    priceAmount: 12900, // $129.00 in cents  
    features: [
      "Unlimited projects",
      "Full production tools suite",
      "Full marketplace access",
      "Advanced team & security controls", 
      "Advanced analytics dashboard"
    ],
    popular: false
  }
];

// Feature display names for database plans
export const featureDisplayNames: Record<string, string> = {
  screenplay_editor: 'Screenplay Editor',
  basic_ai_tools: 'Standard AI Suggestions', 
  advanced_ai_tools: 'Expanded AI Suggestions',
  pdf_export: 'PDF Export',
  all_export_formats: 'All Export Formats (FDX, Fountain)',
  marketplace_submission: 'Marketplace Submission Access',
  team_collaboration: 'Team Collaboration Tools',
  scene_planning: 'Visual Scene Planning',
  storyboarding: 'Visual Storyboarding', 
  production_tools: 'Production Tools Suite',
  real_time_editing: 'Real-time Editing',
  version_history: 'Version History',
  admin_controls: 'Admin Controls',
  priority_support: 'Priority Support',
  email_support: 'Email Support',
  custom_integrations: 'Custom Integrations',
  advanced_analytics: 'Advanced Analytics Dashboard',
  script_discovery: 'Script Discovery'
};

// Limit display names for database plans
export const limitDisplayNames: Record<string, string> = {
  active_projects: 'Active Projects',
  ai_generations_per_day: 'AI Generations/Day',
  team_members: 'Team Members',
  marketplace_submissions: 'Marketplace Submissions'
};

// Convert database plan format to legacy format for compatibility
export const convertDbPlanToLegacy = (dbPlan: any) => {
  return {
    name: dbPlan.display_name,
    description: dbPlan.description,
    priceAmount: Math.round(dbPlan.price_monthly * 100), // Convert to cents
    features: Object.entries(dbPlan.features || {})
      .filter(([_, value]) => value === true)
      .map(([key, _]) => featureDisplayNames[key] || key)
      .concat(
        Object.entries(dbPlan.limits || {})
          .map(([key, value]) => {
            const displayName = limitDisplayNames[key] || key;
            const displayValue = value === -1 ? 'Unlimited' : value;
            return `${displayValue} ${displayName.toLowerCase()}`;
          })
      ),
    popular: dbPlan.is_popular || false
  };
};
