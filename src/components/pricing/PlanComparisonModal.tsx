
import React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check, X, Zap } from 'lucide-react';
import { featureDisplayNames, limitDisplayNames } from './planData';

interface PlanComparisonModalProps {
  isOpen: boolean;
  onClose: () => void;
  plans: any[];
  onSelectPlan: (planId: string) => void;
  isYearly: boolean;
  currentPlanId?: string;
}

const PlanComparisonModal: React.FC<PlanComparisonModalProps> = ({
  isOpen,
  onClose,
  plans,
  onSelectPlan,
  isYearly,
  currentPlanId
}) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  // Get all unique features and limits across all plans
  const allFeatures = new Set<string>();
  const allLimits = new Set<string>();
  
  plans.forEach(plan => {
    Object.keys(plan.features || {}).forEach(feature => allFeatures.add(feature));
    Object.keys(plan.limits || {}).forEach(limit => allLimits.add(limit));
  });

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Compare Plans</DialogTitle>
        </DialogHeader>
        
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="text-left p-4 border-b">Features</th>
                {plans.map(plan => (
                  <th key={plan.id} className="text-center p-4 border-b min-w-[200px]">
                    <div className="space-y-2">
                      <div className="font-semibold">{plan.display_name}</div>
                      {plan.is_popular && (
                        <Badge className="bg-primary text-primary-foreground">
                          Most Popular
                        </Badge>
                      )}
                      <div className="text-2xl font-bold">
                        {formatPrice(isYearly && plan.price_yearly ? plan.price_yearly : plan.price_monthly)}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        /{isYearly ? 'year' : 'month'}
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {/* Features Section */}
              <tr className="bg-muted/50">
                <td colSpan={plans.length + 1} className="p-3 font-semibold">
                  Features
                </td>
              </tr>
              {Array.from(allFeatures).map(featureKey => (
                <tr key={featureKey} className="border-b">
                  <td className="p-3">{featureDisplayNames[featureKey] || featureKey}</td>
                  {plans.map(plan => (
                    <td key={plan.id} className="p-3 text-center">
                      {plan.features?.[featureKey] ? (
                        <Check className="h-5 w-5 text-green-500 mx-auto" />
                      ) : (
                        <X className="h-5 w-5 text-gray-300 mx-auto" />
                      )}
                    </td>
                  ))}
                </tr>
              ))}
              
              {/* Limits Section */}
              <tr className="bg-muted/50">
                <td colSpan={plans.length + 1} className="p-3 font-semibold">
                  Limits
                </td>
              </tr>
              {Array.from(allLimits).map(limitKey => (
                <tr key={limitKey} className="border-b">
                  <td className="p-3 flex items-center">
                    <Zap className="h-4 w-4 text-orange-500 mr-2" />
                    {limitDisplayNames[limitKey] || limitKey}
                  </td>
                  {plans.map(plan => (
                    <td key={plan.id} className="p-3 text-center font-medium">
                      {plan.limits?.[limitKey] === -1 ? 'Unlimited' : (plan.limits?.[limitKey] || '0')}
                    </td>
                  ))}
                </tr>
              ))}
              
              {/* Action Buttons */}
              <tr>
                <td className="p-3"></td>
                {plans.map(plan => (
                  <td key={plan.id} className="p-3 text-center">
                    <Button
                      onClick={() => {
                        onSelectPlan(plan.plan_id);
                        onClose();
                      }}
                      disabled={currentPlanId === plan.plan_id}
                      variant={plan.is_popular ? 'default' : 'outline'}
                      className="w-full"
                    >
                      {currentPlanId === plan.plan_id ? 'Current Plan' : `Choose ${plan.display_name}`}
                    </Button>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PlanComparisonModal;
