
import React from 'react';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3 } from 'lucide-react';

interface PricingHeaderProps {
  isYearly: boolean;
  onToggleBilling: (yearly: boolean) => void;
  onShowComparison: () => void;
}

const PricingHeader: React.FC<PricingHeaderProps> = ({
  isYearly,
  onToggleBilling,
  onShowComparison
}) => {
  return (
    <div className="text-center mb-12">
      <h2 className="text-4xl font-playfair font-bold mb-4 gold-gradient">
        Choose Your Creative Journey
      </h2>
      <p className="text-xl text-muted-foreground mb-8">
        Unlock your storytelling potential with the right tools for your vision
      </p>
      
      {/* Billing Toggle */}
      <div className="flex items-center justify-center gap-4 mb-6">
        <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
          Monthly
        </span>
        <Switch
          checked={isYearly}
          onCheckedChange={onToggleBilling}
          className="data-[state=checked]:bg-primary"
        />
        <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
          Yearly
        </span>
        {isYearly && (
          <Badge variant="secondary" className="ml-2">
            Save up to 20%
          </Badge>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center gap-4 mb-8">
        <Button
          variant="outline"
          onClick={onShowComparison}
          className="flex items-center gap-2"
        >
          <BarChart3 className="h-4 w-4" />
          Compare Plans
        </Button>
      </div>
    </div>
  );
};

export default PricingHeader;
