import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import ScriptEditorHeader from './script-editor/ScriptEditorHeader';
import ScriptEditorToolbar from './script-editor/ScriptEditorToolbar';
import ScriptEditorContent from './script-editor/ScriptEditorContent';
import ScriptEditorFooter from './script-editor/ScriptEditorFooter';
import { ScriptLine, ScriptEditorProps } from './script-editor/ScriptEditorTypes';
import { usePDFImport } from '@/hooks/usePDFImport';
import { ScreenplayElement } from '@/lib/pdf/pdfProcessor';

const defaultScript: ScriptLine[] = [
  {
    id: '1',
    type: 'action',
    content: 'FADE IN:'
  },
  {
    id: '2',
    type: 'action',
    content: 'EXT. COFFEE SHOP - MORNING'
  },
  {
    id: '3',
    type: 'action',
    content: 'A bustling coffee shop on a busy street corner. Steam rises from the windows as early morning commuters rush past.'
  },
  {
    id: '4',
    type: 'character',
    content: '<PERSON><PERSON><PERSON> (25, determined journalist) sits at a corner table, laptop open, typing furiously.'
  },
  {
    id: '5',
    type: 'dialogue',
    content: "I need to find the truth about this story.",
    character: 'SARAH'
  },
  {
    id: '6',
    type: 'action',
    content: 'The door chimes as MICHAEL (30, mysterious stranger) enters. Their eyes meet across the crowded room.'
  },
  {
    id: '7',
    type: 'dialogue',
    content: "Sarah? I never thought I'd see you again.",
    character: 'MICHAEL'
  },
  {
    id: '8',
    type: 'parenthetical',
    content: '(surprised, closing laptop)'
  },
  {
    id: '9',
    type: 'dialogue',
    content: "Michael... what are you doing here?",
    character: 'SARAH'
  }
];

const ScriptEditor: React.FC<ScriptEditorProps> = ({
  initialScript = defaultScript,
  onScriptChange,
  className
}) => {
  const [script, setScript] = useState<ScriptLine[]>(initialScript);
  const [selectedLineId, setSelectedLineId] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const { handlePDFImport } = usePDFImport({
    onContentChange: (content: string) => {
      // Convert content to script lines for display
      const lines = content.split('\n').filter(line => line.trim());
      const newScript: ScriptLine[] = lines.map((line, index) => ({
        id: `imported_${index}`,
        type: 'action', // Default type, could be enhanced with element type detection
        content: line.trim()
      }));
      setScript(newScript);
      onScriptChange?.(newScript);
    },
    onElementsChange: (elements: ScreenplayElement[]) => {
      // Convert screenplay elements to script lines with proper types
      const newScript: ScriptLine[] = elements.map((element, index) => ({
        id: `element_${index}`,
        type: element.type === 'scene_heading' ? 'action' : 
              element.type === 'character' ? 'character' :
              element.type === 'dialogue' ? 'dialogue' :
              element.type === 'parenthetical' ? 'parenthetical' : 'action',
        content: element.content,
        character: element.character
      }));
      setScript(newScript);
      onScriptChange?.(newScript);
    }
  });

  const handleLineEdit = (id: string, newContent: string) => {
    const updatedScript = script.map(line =>
      line.id === id ? { ...line, content: newContent } : line
    );
    setScript(updatedScript);
    onScriptChange?.(updatedScript);
  };

  const addNewLine = (type: ScriptLine['type']) => {
    const newLine: ScriptLine = {
      id: `line_${Date.now()}`,
      type,
      content: type === 'character' ? 'CHARACTER NAME' : 'New line...'
    };
    const updatedScript = [...script, newLine];
    setScript(updatedScript);
    onScriptChange?.(updatedScript);
  };

  const togglePlayback = () => {
    setIsPlaying(!isPlaying);
    if (!isPlaying) {
      console.log('Starting script playback...');
    } else {
      console.log('Stopping script playback...');
    }
  };

  return (
    <Card className={`cinema-editor h-full flex flex-col ${className}`}>
      <ScriptEditorHeader 
        isPlaying={isPlaying}
        onTogglePlayback={togglePlayback}
        onPDFImport={handlePDFImport}
      />
      
      <ScriptEditorToolbar 
        onAddLine={addNewLine}
      />
      
      <ScriptEditorContent
        script={script}
        selectedLineId={selectedLineId}
        onLineSelect={setSelectedLineId}
        onLineEdit={handleLineEdit}
        onAddLine={addNewLine}
      />
      
      <ScriptEditorFooter 
        script={script}
      />
    </Card>
  );
};

export default ScriptEditor;
