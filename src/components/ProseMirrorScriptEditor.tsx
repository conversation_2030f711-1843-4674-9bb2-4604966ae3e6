import React, { useState, useCallback } from 'react';
import { Card } from '@/components/ui/card';
import ProseMirrorEditor from '@/lib/prosemirror/ProseMirrorEditor';
import ScriptEditorHeader from '@/components/prosemirror/ScriptEditorHeader';
import ScriptEditorToolbar from '@/components/prosemirror/ScriptEditorToolbar';
import KeyboardShortcutsPanel from '@/components/prosemirror/KeyboardShortcutsPanel';
import ScriptEditorStats from '@/components/prosemirror/ScriptEditorStats';
import PDFImportButton from '@/components/pdf-import/PDFImportButton';
import { usePDFImport } from '@/hooks/usePDFImport';
import { toast } from 'sonner';

interface ProseMirrorScriptEditorProps {
  initialContent?: string;
  onContentChange?: (content: string) => void;
  className?: string;
  sceneId?: string;
}

const ProseMirrorScriptEditor: React.FC<ProseMirrorScriptEditorProps> = ({
  initialContent = '',
  onContentChange,
  className,
  sceneId
}) => {
  const [content, setContent] = useState(initialContent);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [showShortcuts, setShowShortcuts] = useState(false);

  // Calculate word count and page estimate
  const updateStats = useCallback((newContent: string) => {
    const text = newContent.replace(/<[^>]*>/g, '').trim();
    const words = text ? text.split(/\s+/).length : 0;
    setWordCount(words);
  }, []);

  const handleContentChange = useCallback((newContent: string) => {
    setContent(newContent);
    updateStats(newContent);
    onContentChange?.(newContent);
  }, [onContentChange, updateStats]);

  const handleSave = useCallback(async () => {
    if (isSaving) return;
    
    setIsSaving(true);
    try {
      // Simulate save operation
      await new Promise(resolve => setTimeout(resolve, 500));
      setLastSaved(new Date());
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving script:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [isSaving]);

  const handleExport = useCallback(() => {
    // Create a simple text export
    const text = content.replace(/<[^>]*>/g, '\n').replace(/\n\s*\n/g, '\n\n');
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `screenplay-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Script exported successfully');
  }, [content]);

  const handleToggleShortcuts = useCallback(() => {
    setShowShortcuts(!showShortcuts);
  }, [showShortcuts]);

  const { handlePDFImport } = usePDFImport({
    onContentChange: (content: string) => {
      setContent(content);
      updateStats(content);
      onContentChange?.(content);
    }
  });

  return (
    <Card className={`cinema-card h-full flex flex-col ${className}`}>
      <ScriptEditorHeader 
        showShortcuts={showShortcuts}
        onToggleShortcuts={handleToggleShortcuts}
      />

      <KeyboardShortcutsPanel isVisible={showShortcuts} />

      <div className="flex items-center justify-between px-4 py-2 border-b">
        <ScriptEditorToolbar 
          onExport={handleExport}
          onSave={handleSave}
          isSaving={isSaving}
        />
        
        <PDFImportButton
          onImport={handlePDFImport}
          variant="outline"
          size="sm"
        />
      </div>

      <div className="flex-1 overflow-hidden">
        <ProseMirrorEditor
          content={content}
          onChange={handleContentChange}
          onSave={handleSave}
          placeholder="FADE IN:

EXT. LOCATION - TIME

Start writing your screenplay here..."
          className="h-full"
          showToolbar={true}
          autoSave={true}
          autoSaveDelay={2000}
        />
      </div>

      <ScriptEditorStats 
        wordCount={wordCount}
        lastSaved={lastSaved}
        sceneId={sceneId}
      />
    </Card>
  );
};

export default ProseMirrorScriptEditor;
