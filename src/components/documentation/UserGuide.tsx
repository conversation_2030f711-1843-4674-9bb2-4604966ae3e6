
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  BookOpen, 
  Search, 
  Video, 
  FileText, 
  HelpCircle, 
  ChevronRight,
  ExternalLink,
  Star,
  Clock
} from 'lucide-react';

interface GuideSection {
  id: string;
  title: string;
  description: string;
  content: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string;
  videoUrl?: string;
}

const guideSections: GuideSection[] = [
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Learn the basics of ScriptGenius and create your first screenplay',
    content: `
# Getting Started with ScriptGenius

Welcome to ScriptGenius! This guide will help you get up and running quickly.

## Creating Your First Scene

1. **Navigate to the Script Editor**
   - Click on "Script Editor" in the navigation menu
   - You'll see a clean, formatted editing interface

2. **Start Writing**
   - Type naturally - ScriptGenius will automatically format your screenplay
   - Use standard screenplay elements:
     - **Scene Headings**: Start with INT. or EXT.
     - **Character Names**: Type in ALL CAPS
     - **Dialogue**: Type normally after character names
     - **Action**: Describe what happens

3. **Smart Formatting**
   - Press Tab to cycle through screenplay elements
   - The editor recognizes context and suggests appropriate formatting
   - Use keyboard shortcuts for quick formatting (Ctrl+1 for Scene Heading, etc.)

## Best Practices

- **Keep scenes focused**: Each scene should advance the story
- **Write visually**: Describe what the camera sees
- **Character voices**: Make each character's dialogue unique
- **Show, don't tell**: Use action and dialogue to reveal character
    `,
    difficulty: 'beginner',
    estimatedTime: '10 minutes',
    videoUrl: 'https://example.com/getting-started-video'
  },
  {
    id: 'coverage-generator',
    title: 'AI Coverage Generator',
    description: 'Generate professional script coverage reports using AI',
    content: `
# Using the AI Coverage Generator

The Coverage Generator provides industry-standard script analysis using advanced AI.

## Prerequisites

- **Pro Subscription Required**: Coverage generation is available for Pro subscribers and above
- **Complete Scene**: Ensure your scene is well-written and complete

## Generating Coverage

1. **Select Your Scene**
   - Navigate to Coverage Generator
   - Choose from your existing scenes
   - Preview the scene content before generating

2. **Choose Fidelity Level**
   - **Standard**: Basic coverage with synopsis and key points
   - **Premium**: Detailed analysis with character and dialogue notes
   - **Professional**: Comprehensive coverage ready for industry submission

3. **Review Your Report**
   - **Synopsis**: Summary of your story
   - **Strengths**: What works well in your script
   - **Weaknesses**: Areas for improvement
   - **Verdict**: Overall assessment and recommendations

## Writing for Better Coverage

- **Clear Structure**: Follow three-act structure
- **Strong Characters**: Develop compelling, distinct characters
- **Engaging Dialogue**: Make conversations natural and purposeful
- **Visual Storytelling**: Write cinematically

## Subscription Limits

- **Pro Solo**: 5 reports per day
- **Pro Team**: 15 reports per day
- **Studio**: 50 reports per day
- **Enterprise**: 200 reports per day
    `,
    difficulty: 'intermediate',
    estimatedTime: '15 minutes'
  },
  {
    id: 'collaboration',
    title: 'Team Collaboration',
    description: 'Work effectively with writing partners and team members',
    content: `
# Team Collaboration Features

ScriptGenius makes it easy to collaborate with writing partners, directors, and producers.

## Setting Up Your Team

1. **Invite Team Members**
   - Go to Team Management in your organization settings
   - Send invitations via email
   - Assign appropriate roles (Writer, Director, Producer, etc.)

2. **Role Permissions**
   - **Admin**: Full access to all features and settings
   - **Writer**: Can create and edit scripts, generate coverage
   - **Director**: Can comment and review scripts
   - **Viewer**: Read-only access to scripts

## Real-Time Collaboration

- **Live Editing**: Multiple writers can edit simultaneously
- **Comments**: Add notes and suggestions directly to script lines
- **Version Control**: Track changes and revert to previous versions
- **Notifications**: Get alerts when team members make changes

## Best Practices for Teams

- **Establish Workflows**: Define who does what and when
- **Use Comments Effectively**: Be specific and constructive
- **Regular Check-ins**: Schedule review sessions
- **Version Naming**: Use clear, descriptive version names
    `,
    difficulty: 'intermediate',
    estimatedTime: '20 minutes'
  },
  {
    id: 'advanced-features',
    title: 'Advanced Features',
    description: 'Master the advanced tools for professional screenwriting',
    content: `
# Advanced ScriptGenius Features

Take your screenwriting to the next level with these professional tools.

## Production Tools

### Call Sheets
- Generate professional call sheets
- Schedule cast and crew
- Include location and timing details

### Budget Tracking
- Track production costs
- Monitor budget allocations
- Generate budget reports

### Resource Management
- Manage equipment and locations
- Schedule resource availability
- Track resource costs

## Storyboard Studio

- **Visual Planning**: Create storyboards for key scenes
- **AI-Generated Images**: Use AI to generate storyboard panels
- **Style Consistency**: Maintain visual consistency across boards
- **Export Options**: Export for production use

## Marketplace Integration

- **Sell Your Scripts**: List completed screenplays for sale
- **Professional Review**: Get industry professional assessments
- **Direct Producer Contact**: Connect with buyers directly
- **Rights Management**: Track licensing and rights

## API Integration

- **Custom Workflows**: Integrate with your existing tools
- **Automated Reporting**: Set up automated coverage generation
- **Data Export**: Export scripts in various formats
- **Webhook Support**: Real-time notifications to external systems
    `,
    difficulty: 'advanced',
    estimatedTime: '30 minutes'
  }
];

const troubleshootingItems = [
  {
    question: 'Why is my coverage generation failing?',
    answer: 'Check your subscription plan, ensure your scene has sufficient content (minimum 20 pages), and verify your internet connection.'
  },
  {
    question: 'How do I format dialogue correctly?',
    answer: 'Type the character name in ALL CAPS, press Enter, then type the dialogue. The editor will automatically format it.'
  },
  {
    question: 'Can I export my screenplay?',
    answer: 'Yes, go to File > Export and choose from PDF, Final Draft, or plain text formats.'
  },
  {
    question: 'How do I invite team members?',
    answer: 'Go to Team Management in your organization settings and send email invitations with appropriate role assignments.'
  }
];

export const UserGuide: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSection, setSelectedSection] = useState<string>('getting-started');

  const filteredSections = guideSections.filter(section =>
    section.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    section.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    section.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const currentSection = guideSections.find(section => section.id === selectedSection);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">ScriptGenius User Guide</h1>
        <p className="text-muted-foreground">
          Comprehensive documentation to help you master ScriptGenius
        </p>
      </div>

      <Tabs defaultValue="guides" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="guides">
            <BookOpen className="h-4 w-4 mr-2" />
            Guides
          </TabsTrigger>
          <TabsTrigger value="tutorials">
            <Video className="h-4 w-4 mr-2" />
            Video Tutorials
          </TabsTrigger>
          <TabsTrigger value="troubleshooting">
            <HelpCircle className="h-4 w-4 mr-2" />
            Troubleshooting
          </TabsTrigger>
        </TabsList>

        <TabsContent value="guides" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Search Guides</CardTitle>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search documentation..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-[400px]">
                    <div className="space-y-2">
                      {filteredSections.map((section) => (
                        <Button
                          key={section.id}
                          variant={selectedSection === section.id ? "default" : "ghost"}
                          className="w-full justify-start h-auto p-3"
                          onClick={() => setSelectedSection(section.id)}
                        >
                          <div className="text-left">
                            <div className="font-medium">{section.title}</div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {section.description}
                            </div>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge className={getDifficultyColor(section.difficulty)}>
                                {section.difficulty}
                              </Badge>
                              <div className="flex items-center text-xs text-muted-foreground">
                                <Clock className="h-3 w-3 mr-1" />
                                {section.estimatedTime}
                              </div>
                            </div>
                          </div>
                          <ChevronRight className="h-4 w-4 ml-auto" />
                        </Button>
                      ))}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              {currentSection && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-2xl">{currentSection.title}</CardTitle>
                        <CardDescription className="mt-2">
                          {currentSection.description}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getDifficultyColor(currentSection.difficulty)}>
                          {currentSection.difficulty}
                        </Badge>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="h-4 w-4 mr-1" />
                          {currentSection.estimatedTime}
                        </div>
                      </div>
                    </div>
                    {currentSection.videoUrl && (
                      <Button variant="outline" className="w-fit mt-4">
                        <Video className="h-4 w-4 mr-2" />
                        Watch Video Tutorial
                        <ExternalLink className="h-4 w-4 ml-2" />
                      </Button>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="prose max-w-none">
                      <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                        {currentSection.content}
                      </pre>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="tutorials" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {guideSections
              .filter(section => section.videoUrl)
              .map((section) => (
                <Card key={section.id}>
                  <CardHeader>
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                      <Video className="h-12 w-12 text-muted-foreground" />
                    </div>
                    <CardTitle className="text-lg">{section.title}</CardTitle>
                    <CardDescription>{section.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge className={getDifficultyColor(section.difficulty)}>
                          {section.difficulty}
                        </Badge>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <Clock className="h-4 w-4 mr-1" />
                          {section.estimatedTime}
                        </div>
                      </div>
                      <Button size="sm">
                        <Video className="h-4 w-4 mr-2" />
                        Watch
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="troubleshooting" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>
                Common issues and their solutions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {troubleshootingItems.map((item, index) => (
                  <Card key={index}>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center">
                        <HelpCircle className="h-5 w-5 mr-2 text-blue-500" />
                        {item.question}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-muted-foreground">{item.answer}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Still Need Help?</CardTitle>
              <CardDescription>
                Contact our support team for additional assistance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button variant="outline">
                  <FileText className="h-4 w-4 mr-2" />
                  Submit Support Ticket
                </Button>
                <Button variant="outline">
                  <Star className="h-4 w-4 mr-2" />
                  Community Forum
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
