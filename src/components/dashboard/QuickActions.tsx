
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  FileText, 
  BarChart3, 
  Image, 
  Users, 
  FolderPlus,
  Settings,
  HelpCircle,
  Zap
} from 'lucide-react';
import { useDashboard } from '@/hooks/useDashboard';
import { useNavigate } from 'react-router-dom';

const QuickActions = () => {
  const { trackToolUsage } = useDashboard();
  const navigate = useNavigate();

  const handleQuickAction = (action: string, path?: string) => {
    trackToolUsage({ 
      toolName: 'quick_actions', 
      eventData: { action } 
    });
    
    if (path) {
      navigate(path);
    }
  };

  const actions = [
    {
      title: 'New Script',
      description: 'Start writing a new screenplay',
      icon: <FileText className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-blue-500/10 hover:bg-blue-500/20 border-blue-500/20',
      textColor: 'text-blue-600',
      action: () => handleQuickAction('new_script', '/dashboard?tool=script_editor')
    },
    {
      title: 'Generate Coverage',
      description: 'AI-powered script analysis',
      icon: <BarChart3 className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-green-500/10 hover:bg-green-500/20 border-green-500/20',
      textColor: 'text-green-600',
      action: () => handleQuickAction('generate_coverage', '/dashboard?tool=coverage_generator')
    },
    {
      title: 'Create Storyboard',
      description: 'Visual story planning',
      icon: <Image className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-purple-500/10 hover:bg-purple-500/20 border-purple-500/20',
      textColor: 'text-purple-600',
      action: () => handleQuickAction('create_storyboard', '/dashboard?tool=storyboard_studio')
    },
    {
      title: 'Team Collaboration',
      description: 'Invite team members',
      icon: <Users className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-orange-500/10 hover:bg-orange-500/20 border-orange-500/20',
      textColor: 'text-orange-600',
      action: () => handleQuickAction('team_collaboration', '/dashboard?tab=team')
    },
    {
      title: 'Production Tools',
      description: 'Schedule & manage production',
      icon: <FolderPlus className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-red-500/10 hover:bg-red-500/20 border-red-500/20',
      textColor: 'text-red-600',
      action: () => handleQuickAction('production_tools', '/production-tools')
    },
    {
      title: 'Marketplace',
      description: 'Buy or sell screenplays',
      icon: <Zap className="h-4 w-4 sm:h-5 sm:w-5" />,
      color: 'bg-yellow-500/10 hover:bg-yellow-500/20 border-yellow-500/20',
      textColor: 'text-yellow-600',
      action: () => handleQuickAction('marketplace', '/marketplace')
    }
  ];

  return (
    <Card className="p-4 sm:p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-base sm:text-lg font-semibold truncate">Quick Actions</h3>
        <Button 
          variant="ghost" 
          size="sm"
          onClick={() => handleQuickAction('view_settings', '/dashboard?tab=settings')}
          className="flex-shrink-0"
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4">
        {actions.map((action) => (
          <Button
            key={action.title}
            variant="outline"
            className={`h-auto p-3 sm:p-4 flex flex-col items-center text-center ${action.color} hover:scale-105 transition-all duration-200 min-h-[80px] sm:min-h-[100px]`}
            onClick={action.action}
          >
            <div className={`mb-2 sm:mb-3 ${action.textColor} flex-shrink-0`}>
              {action.icon}
            </div>
            <div className="min-w-0 w-full">
              <h4 className="font-medium text-xs sm:text-sm mb-1 truncate">{action.title}</h4>
              <p className="text-xs text-muted-foreground line-clamp-2">{action.description}</p>
            </div>
          </Button>
        ))}
      </div>

      <div className="mt-6 p-3 sm:p-4 bg-muted/50 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <HelpCircle className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <span className="text-sm font-medium truncate">Need Help?</span>
        </div>
        <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
          New to ScriptGenius? Check out our getting started guide.
        </p>
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={() => handleQuickAction('view_help', '/docs')}
        >
          View Documentation
        </Button>
      </div>
    </Card>
  );
};

export default QuickActions;
