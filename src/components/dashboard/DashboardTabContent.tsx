
import React, { Suspense } from 'react';
import { TabsContent } from '@/components/ui/tabs';
import DashboardOverview from './DashboardOverview';
import ActiveProjects from './ActiveProjects';
import ToolUsageWidget from './ToolUsageWidget';
import RecentActivity from './RecentActivity';
import QuickActions from './QuickActions';
import VirtualizedScriptEditor from '../VirtualizedScriptEditor';
import TeamManagement from '../TeamManagement';

// Lazy load feature components with different import paths to avoid conflicts
const ScreenplayMarketplace = React.lazy(() => 
  import('@/features/marketplace/ScreenplayMarketplace').then(module => ({ 
    default: module.default 
  }))
);

const CoverageGenerator = React.lazy(() => 
  import('@/features/coverage/CoverageGenerator').then(module => ({ 
    default: module.default 
  }))
);

const ProductionToolsFeature = React.lazy(() => 
  import('@/features/production/ProductionTools').then(module => ({ 
    default: module.default 
  }))
);

const StoryboardStudio = React.lazy(() => 
  import('../StoryboardStudio').then(module => ({ 
    default: module.default 
  }))
);

// Import loading states
import {
  EditorLoadingState,
  CoverageLoadingState,
  StoryboardLoadingState,
  MarketplaceLoadingState,
  ProductionLoadingState,
  TeamLoadingState
} from './loading/FeatureLoadingStates';

interface DashboardTabContentProps {
  activeTab: string;
}

const DashboardTabContent: React.FC<DashboardTabContentProps> = ({ activeTab }) => {
  return (
    <>
      <TabsContent value="overview" className="space-y-6">
        <DashboardOverview />
        
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Main content area */}
          <div className="xl:col-span-2 space-y-6">
            <ActiveProjects />
          </div>
          
          {/* Sidebar widgets */}
          <div className="space-y-6">
            <QuickActions />
            <ToolUsageWidget />
            <RecentActivity />
          </div>
        </div>
      </TabsContent>

      <TabsContent value="projects">
        <ActiveProjects />
      </TabsContent>

      <TabsContent value="editor">
        <div className="bg-white rounded-lg shadow-sm border">
          <Suspense fallback={<EditorLoadingState />}>
            <VirtualizedScriptEditor />
          </Suspense>
        </div>
      </TabsContent>

      <TabsContent value="coverage">
        <Suspense fallback={<CoverageLoadingState />}>
          <CoverageGenerator />
        </Suspense>
      </TabsContent>

      <TabsContent value="storyboard">
        <Suspense fallback={<StoryboardLoadingState />}>
          <StoryboardStudio />
        </Suspense>
      </TabsContent>

      <TabsContent value="marketplace">
        <Suspense fallback={<MarketplaceLoadingState />}>
          <ScreenplayMarketplace />
        </Suspense>
      </TabsContent>

      <TabsContent value="production">
        <Suspense fallback={<ProductionLoadingState />}>
          <ProductionToolsFeature />
        </Suspense>
      </TabsContent>

      <TabsContent value="team">
        <Suspense fallback={<TeamLoadingState />}>
          <TeamManagement />
        </Suspense>
      </TabsContent>
    </>
  );
};

export default DashboardTabContent;
