
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Activity, 
  FileText, 
  Users, 
  Image, 
  BarChart3,
  Plus,
  Edit,
  Archive,
  Play,
  Pause
} from 'lucide-react';
import { useDashboard } from '@/hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';

const RecentActivity = () => {
  const { recentActivity, isActivityLoading } = useDashboard();

  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'tool_usage':
        return <Activity className="h-4 w-4" />;
      case 'project_created':
        return <Plus className="h-4 w-4" />;
      case 'project_updated':
        return <Edit className="h-4 w-4" />;
      case 'project_archived':
        return <Archive className="h-4 w-4" />;
      case 'project_activated':
        return <Play className="h-4 w-4" />;
      case 'project_paused':
        return <Pause className="h-4 w-4" />;
      case 'collaboration':
        return <Users className="h-4 w-4" />;
      case 'coverage_generated':
        return <BarChart3 className="h-4 w-4" />;
      case 'storyboard_created':
        return <Image className="h-4 w-4" />;
      case 'script_edited':
        return <FileText className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'project_created':
        return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'project_updated':
        return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      case 'project_archived':
        return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
      case 'tool_usage':
        return 'bg-purple-500/10 text-purple-500 border-purple-500/20';
      case 'collaboration':
        return 'bg-orange-500/10 text-orange-500 border-orange-500/20';
      default:
        return 'bg-primary/10 text-primary border-primary/20';
    }
  };

  if (isActivityLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <div className="w-10 h-10 bg-muted rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Recent Activity</h3>
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          <Activity className="h-3 w-3 mr-1" />
          Live
        </Badge>
      </div>

      {recentActivity.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No recent activity.</p>
          <p className="text-sm">Your activity will appear here as you use the platform.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {recentActivity.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 pb-4 last:pb-0 border-b last:border-b-0">
              <Avatar className="h-10 w-10">
                <AvatarFallback className={getActivityColor(activity.type)}>
                  {getActivityIcon(activity.type)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">
                    {activity.description}
                  </p>
                  <time className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
                  </time>
                </div>
                
                {activity.project_title && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Project: {activity.project_title}
                  </p>
                )}
                
                <Badge variant="outline" className={`text-xs mt-2 ${getActivityColor(activity.type)}`}>
                  {activity.type.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      )}
    </Card>
  );
};

export default RecentActivity;
