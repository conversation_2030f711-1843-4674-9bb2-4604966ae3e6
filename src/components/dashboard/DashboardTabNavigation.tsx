
import React from 'react';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';

interface DashboardTabNavigationProps {
  activeTab: string;
}

const DashboardTabNavigation: React.FC<DashboardTabNavigationProps> = ({ activeTab }) => {
  return (
    <div className="w-full overflow-hidden">
      <TabsList className="w-full grid-cols-none flex lg:grid lg:grid-cols-8 gap-1">
        <TabsTrigger value="overview" className="text-xs sm:text-sm">
          Overview
        </TabsTrigger>
        <TabsTrigger value="projects" className="text-xs sm:text-sm">
          Projects
        </TabsTrigger>
        <TabsTrigger value="editor" className="text-xs sm:text-sm">
          Editor
        </TabsTrigger>
        <TabsTrigger value="coverage" className="text-xs sm:text-sm">
          Coverage
        </TabsTrigger>
        <TabsTrigger value="storyboard" className="text-xs sm:text-sm">
          Storyboard
        </TabsTrigger>
        <TabsTrigger value="marketplace" className="text-xs sm:text-sm">
          Marketplace
        </TabsTrigger>
        <TabsTrigger value="production" className="text-xs sm:text-sm">
          Production
        </TabsTrigger>
        <TabsTrigger value="team" className="text-xs sm:text-sm">
          Team
        </TabsTrigger>
      </TabsList>
    </div>
  );
};

export default DashboardTabNavigation;
