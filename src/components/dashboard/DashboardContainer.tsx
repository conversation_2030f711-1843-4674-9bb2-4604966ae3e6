
import React from 'react';
import { Tabs } from '@/components/ui/tabs';
import { useDashboardState } from '@/hooks/useDashboardState';
import OrganizationSelector from '../OrganizationSelector';
import ScriptGeniusLayout from '../ScriptGeniusLayout';
import DashboardHeader from '../DashboardHeader';
import SubscriptionStatusIndicator from '../SubscriptionStatusIndicator';
import DashboardTabNavigation from './DashboardTabNavigation';
import DashboardTabContent from './DashboardTabContent';

const DashboardContainer: React.FC = () => {
  const { user, currentOrganization, activeTab, handleTabChange } = useDashboardState();

  if (!user || !currentOrganization) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="text-center max-w-sm w-full">
          <p className="mb-4 text-lg">Loading your workspace...</p>
          <OrganizationSelector />
        </div>
      </div>
    );
  }

  return (
    <ScriptGeniusLayout>
      <div className="min-h-screen bg-background">
        <div className="space-y-6">
          <DashboardHeader />
          <SubscriptionStatusIndicator />
          
          <div className="container mx-auto px-4 sm:px-6 py-4 sm:py-8">
            <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
              <DashboardTabNavigation activeTab={activeTab} />
              <DashboardTabContent activeTab={activeTab} />
            </Tabs>
          </div>
        </div>
      </div>
    </ScriptGeniusLayout>
  );
};

export default DashboardContainer;
