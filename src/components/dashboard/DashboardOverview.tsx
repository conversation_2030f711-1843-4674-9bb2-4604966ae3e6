
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FolderOpen, 
  Users, 
  Activity, 
  Crown,
  TrendingUp,
  Target,
  Clock,
  CheckCircle
} from 'lucide-react';
import { useDashboard } from '@/hooks/useDashboard';

const DashboardOverview = () => {
  const { usageStats, tierLimits, isLoading } = useDashboard();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="p-6 animate-pulse">
          <div className="h-4 bg-muted rounded mb-2"></div>
          <div className="h-8 bg-muted rounded"></div>
        </Card>
        <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="p-6 animate-pulse">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-8 bg-muted rounded"></div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  const activeProjectsProgress = usageStats ? 
    (usageStats.active_projects / usageStats.max_active_projects) * 100 : 0;

  const collaboratorsProgress = usageStats && usageStats.max_collaborators_per_project > 0 ? 
    (usageStats.total_collaborators / usageStats.max_collaborators_per_project) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Tier Info */}
      <Card className="p-4 sm:p-6 bg-gradient-to-r from-primary/10 to-accent/10 border-primary/20">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center gap-3 min-w-0">
            <Crown className="w-5 h-5 sm:w-6 sm:h-6 text-primary flex-shrink-0" />
            <div className="min-w-0">
              <h3 className="font-semibold text-base sm:text-lg truncate">
                {usageStats?.tier_name?.replace('-', ' ').toUpperCase() || 'Starter'} Plan
              </h3>
              <p className="text-xs sm:text-sm text-muted-foreground truncate">
                Your current subscription tier
              </p>
            </div>
          </div>
          <Badge variant="outline" className="bg-primary/10 border-primary text-primary flex-shrink-0">
            Active
          </Badge>
        </div>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 sm:gap-6">
        {/* Active Projects */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 min-w-0">
              <FolderOpen className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0" />
              <span className="font-medium text-sm sm:text-base truncate">Active Projects</span>
            </div>
            <Badge variant="outline" className="text-xs flex-shrink-0">
              {usageStats?.active_projects || 0}/{usageStats?.max_active_projects || 0}
            </Badge>
          </div>
          <div className="space-y-2">
            <div className="text-xl sm:text-2xl font-bold">
              {usageStats?.active_projects || 0}
            </div>
            <Progress 
              value={activeProjectsProgress} 
              className="h-2"
            />
            <p className="text-xs text-muted-foreground truncate">
              {usageStats?.max_active_projects ? 
                `${usageStats.max_active_projects - (usageStats.active_projects || 0)} remaining` :
                'Unlimited'
              }
            </p>
          </div>
        </Card>

        {/* Total Projects */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 min-w-0">
              <Target className="w-4 h-4 sm:w-5 sm:h-5 text-green-500 flex-shrink-0" />
              <span className="font-medium text-sm sm:text-base truncate">Total Projects</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-xl sm:text-2xl font-bold">
              {usageStats?.total_projects || 0}
            </div>
            <p className="text-xs text-muted-foreground truncate">
              All-time projects created
            </p>
          </div>
        </Card>

        {/* Collaborators */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 min-w-0">
              <Users className="w-4 h-4 sm:w-5 sm:h-5 text-purple-500 flex-shrink-0" />
              <span className="font-medium text-sm sm:text-base truncate">Collaborators</span>
            </div>
            {usageStats?.max_collaborators_per_project > 0 && (
              <Badge variant="outline" className="text-xs flex-shrink-0">
                {usageStats?.total_collaborators || 0}/{usageStats?.max_collaborators_per_project}
              </Badge>
            )}
          </div>
          <div className="space-y-2">
            <div className="text-xl sm:text-2xl font-bold">
              {usageStats?.total_collaborators || 0}
            </div>
            {usageStats?.max_collaborators_per_project > 0 && (
              <Progress 
                value={collaboratorsProgress} 
                className="h-2"
              />
            )}
            <p className="text-xs text-muted-foreground truncate">
              {usageStats?.max_collaborators_per_project > 0 ? 
                `${usageStats.max_collaborators_per_project - (usageStats.total_collaborators || 0)} remaining` :
                'Solo plan'
              }
            </p>
          </div>
        </Card>

        {/* Teams */}
        <Card className="p-4 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2 min-w-0">
              <Activity className="w-4 h-4 sm:w-5 sm:h-5 text-orange-500 flex-shrink-0" />
              <span className="font-medium text-sm sm:text-base truncate">Teams</span>
            </div>
            {usageStats?.max_teams > 0 && (
              <Badge variant="outline" className="text-xs flex-shrink-0">
                {usageStats?.team_count || 0}/{usageStats?.max_teams}
              </Badge>
            )}
          </div>
          <div className="space-y-2">
            <div className="text-xl sm:text-2xl font-bold">
              {usageStats?.team_count || 0}
            </div>
            <p className="text-xs text-muted-foreground truncate">
              {usageStats?.max_teams > 0 ? 
                'Active team projects' :
                'Teams not available'
              }
            </p>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default DashboardOverview;
