
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Zap, 
  FileText, 
  Users, 
  Image, 
  BarChart3,
  Clock,
  TrendingUp
} from 'lucide-react';
import { useDashboard } from '@/hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';

const ToolUsageWidget = () => {
  const { toolUsage, isToolUsageLoading } = useDashboard();

  const getToolIcon = (toolName: string) => {
    switch (toolName.toLowerCase()) {
      case 'script_editor':
      case 'screenplay_editor':
        return <FileText className="h-4 w-4" />;
      case 'coverage_generator':
        return <BarChart3 className="h-4 w-4" />;
      case 'storyboard_studio':
        return <Image className="h-4 w-4" />;
      case 'team_collaboration':
      case 'project_management':
        return <Users className="h-4 w-4" />;
      default:
        return <Zap className="h-4 w-4" />;
    }
  };

  const getToolDisplayName = (toolName: string) => {
    return toolName
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const maxUsage = Math.max(...toolUsage.map(tool => tool.usage_count), 1);

  if (isToolUsageLoading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded"></div>
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-16 bg-muted rounded"></div>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold">Tool Usage</h3>
        <Badge variant="outline" className="bg-primary/10 text-primary border-primary/20">
          <TrendingUp className="h-3 w-3 mr-1" />
          Last 30 days
        </Badge>
      </div>

      {toolUsage.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Zap className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No tool usage recorded yet.</p>
          <p className="text-sm">Start using ScriptGenius tools to see analytics here!</p>
        </div>
      ) : (
        <div className="space-y-4">
          {toolUsage.slice(0, 6).map((tool) => {
            const progressValue = (tool.usage_count / maxUsage) * 100;
            
            return (
              <div key={tool.tool_name} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-muted">
                      {getToolIcon(tool.tool_name)}
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">
                        {getToolDisplayName(tool.tool_name)}
                      </h4>
                      <p className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        Last used {formatDistanceToNow(new Date(tool.last_used), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {tool.usage_count} uses
                  </Badge>
                </div>
                <Progress value={progressValue} className="h-2" />
              </div>
            );
          })}
          
          {toolUsage.length > 6 && (
            <div className="text-center pt-4">
              <Badge variant="outline" className="text-xs">
                +{toolUsage.length - 6} more tools
              </Badge>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default ToolUsageWidget;
