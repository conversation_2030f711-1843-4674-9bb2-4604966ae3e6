import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Plus, 
  FolderOpen, 
  MoreHorizontal, 
  Play, 
  Pause, 
  Archive,
  Users,
  Calendar,
  FileText
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useDashboard } from '@/hooks/useDashboard';
import { formatDistanceToNow } from 'date-fns';
import CreateProjectDialog from './CreateProjectDialog';

const ActiveProjects = () => {
  const { 
    projects, 
    updateProject, 
    trackToolUsage, 
    checkCanCreateProject, 
    isLoading 
  } = useDashboard();
  
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  const activeProjects = projects.filter(p => p.status === 'active' && !p.is_archived);
  const recentProjects = projects.slice(0, 6);

  const handleProjectAction = async (projectId: string, action: string) => {
    switch (action) {
      case 'pause':
        updateProject({ projectId, updates: { status: 'draft' } });
        trackToolUsage({ toolName: 'project_management', eventData: { action: 'pause_project' } });
        break;
      case 'resume':
        updateProject({ projectId, updates: { status: 'active' } });
        trackToolUsage({ toolName: 'project_management', eventData: { action: 'resume_project' } });
        break;
      case 'archive':
        updateProject({ projectId, updates: { is_archived: true } });
        trackToolUsage({ toolName: 'project_management', eventData: { action: 'archive_project' } });
        break;
      case 'complete':
        updateProject({ projectId, updates: { status: 'completed' } });
        trackToolUsage({ toolName: 'project_management', eventData: { action: 'complete_project' } });
        break;
    }
  };

  const handleCreateProject = async () => {
    const canCreate = await checkCanCreateProject();
    if (canCreate) {
      setShowCreateDialog(true);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500/10 text-green-500 border-green-500/20';
      case 'draft': return 'bg-yellow-500/10 text-yellow-500 border-yellow-500/20';
      case 'completed': return 'bg-blue-500/10 text-blue-500 border-blue-500/20';
      default: return 'bg-gray-500/10 text-gray-500 border-gray-500/20';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="p-6 animate-pulse">
          <div className="h-6 bg-muted rounded mb-4"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-muted rounded"></div>
            ))}
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Projects</h2>
        <Button onClick={handleCreateProject} className="gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </div>

      {/* Active Projects Summary */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Active Projects</h3>
          <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
            {activeProjects.length} Active
          </Badge>
        </div>
        
        {activeProjects.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <FolderOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No active projects yet. Create your first project to get started!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {activeProjects.map((project) => (
              <Card key={project.id} className="p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm mb-1">{project.title}</h4>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {project.description || 'No description'}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'pause')}>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'complete')}>
                        <FileText className="h-4 w-4 mr-2" />
                        Mark Complete
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'archive')}>
                        <Archive className="h-4 w-4 mr-2" />
                        Archive
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className={getStatusColor(project.status)}>
                      {project.status}
                    </Badge>
                    {project.collaborator_count > 0 && (
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {project.collaborator_count}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDistanceToNow(new Date(project.updated_at), { addSuffix: true })}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}
      </Card>

      {/* Recent Projects */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Projects</h3>
        <div className="space-y-3">
          {recentProjects.map((project) => (
            <div key={project.id} className="flex items-center justify-between p-3 rounded-lg border">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 rounded-full bg-primary"></div>
                <div>
                  <h4 className="font-medium text-sm">{project.title}</h4>
                  <p className="text-xs text-muted-foreground">
                    Updated {formatDistanceToNow(new Date(project.updated_at), { addSuffix: true })}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className={getStatusColor(project.status)}>
                  {project.status}
                </Badge>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {project.status === 'draft' && (
                      <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'resume')}>
                        <Play className="h-4 w-4 mr-2" />
                        Activate
                      </DropdownMenuItem>
                    )}
                    {project.status === 'active' && (
                      <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'pause')}>
                        <Pause className="h-4 w-4 mr-2" />
                        Pause
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem onClick={() => handleProjectAction(project.id, 'archive')}>
                      <Archive className="h-4 w-4 mr-2" />
                      Archive
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
          
          {recentProjects.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <p>No projects found. Create your first project to get started!</p>
            </div>
          )}
        </div>
      </Card>

      <CreateProjectDialog 
        open={showCreateDialog} 
        onOpenChange={setShowCreateDialog}
      />
    </div>
  );
};

export default ActiveProjects;
