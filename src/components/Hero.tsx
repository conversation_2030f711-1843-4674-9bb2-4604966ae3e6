
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const Hero = () => {
  const scrollToPricing = () => {
    const pricingSection = document.getElementById('pricing');
    if (pricingSection) {
      pricingSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-20 left-10 w-64 h-64 bg-primary/10 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-float delay-300"></div>
      </div>
      
      <div className="container mx-auto px-6 py-16 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-playfair font-bold mb-6 animate-slide-up">
            From <span className="gold-gradient">Idea</span> to 
            <br />
            Industry-Ready <span className="gold-gradient">Screenplay</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-slide-up delay-200">
            The all-in-one creative suite for screenwriters. AI-powered writing, collaborative tools, 
            and production features built for the modern film industry.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8 animate-slide-up delay-300">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-lg px-8 py-4 group"
              onClick={scrollToPricing}
            >
              Get Started
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-lg px-8 py-4">
              Watch Demo
            </Button>
          </div>
          
          <div className="cinema-card rounded-2xl p-8 max-w-3xl mx-auto animate-fade-scale delay-400">
            <div className="aspect-video bg-gradient-to-br from-muted to-muted/50 rounded-xl flex items-center justify-center">
              <div className="text-6xl opacity-50">🎬</div>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              See ScriptGenius in action - from first draft to final production
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
