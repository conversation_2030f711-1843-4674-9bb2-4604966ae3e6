
import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  twitterHandle?: string;
  noIndex?: boolean;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

const SEO: React.FC<SEOProps> = ({
  title = 'ScriptGenius - Professional Screenwriting Tools',
  description = 'Transform your ideas into professional screenplays with AI-powered writing tools. Collaborate, create, and bring your stories to life with ScriptGenius.',
  keywords = ['screenwriting', 'screenplay', 'AI writing', 'script editor', 'film production', 'storytelling', 'creative writing'],
  image = 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=1200&h=630&fit=crop',
  url,
  type = 'website',
  twitterHandle = '@scriptgenius',
  noIndex = false,
  author = 'ScriptGenius',
  publishedTime,
  modifiedTime,
}) => {
  const siteTitle = title.includes('ScriptGenius') ? title : `${title} | ScriptGenius`;
  const currentUrl = url || (typeof window !== 'undefined' ? window.location.href : 'https://scriptgenius.com');
  const canonicalUrl = url || (typeof window !== 'undefined' ? window.location.origin + window.location.pathname : 'https://scriptgenius.com');

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{siteTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      <link rel="canonical" href={canonicalUrl} />
      {noIndex && <meta name="robots" content="noindex,nofollow" />}

      {/* Open Graph meta tags */}
      <meta property="og:site_name" content="ScriptGenius" />
      <meta property="og:title" content={siteTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:alt" content={`${title} - ScriptGenius`} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:locale" content="en_US" />
      
      {/* Article specific meta tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && (
        <meta property="article:author" content={author} />
      )}

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={twitterHandle} />
      <meta name="twitter:creator" content={twitterHandle} />
      <meta name="twitter:title" content={siteTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={`${title} - ScriptGenius`} />

      {/* Additional meta tags */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="theme-color" content="#1a1a1a" />
    </Helmet>
  );
};

export default SEO;
