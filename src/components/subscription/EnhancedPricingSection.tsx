
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Loader2, Check, Star, Zap } from 'lucide-react';
import { useEnhancedSubscription } from '@/hooks/useEnhancedSubscription';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface EnhancedPricingSectionProps {
  showCurrentPlan?: boolean;
  onPlanSelect?: (planId: string) => void;
}

const EnhancedPricingSection: React.FC<EnhancedPricingSectionProps> = ({
  showCurrentPlan = true,
  onPlanSelect
}) => {
  const { user } = useAuth();
  const { plans, subscription, loading, createCheckoutSession } = useEnhancedSubscription();
  const [isYearly, setIsYearly] = useState(false);
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);

  const handleSelectPlan = async (planId: string) => {
    if (!user) {
      toast.error('Please sign in to subscribe');
      return;
    }

    if (onPlanSelect) {
      onPlanSelect(planId);
      return;
    }

    setLoadingPlan(planId);
    try {
      const result = await createCheckoutSession(planId, isYearly ? 'yearly' : 'monthly');
      if (result?.url) {
        window.location.href = result.url;
      }
    } catch (error) {
      console.error('Checkout error:', error);
    } finally {
      setLoadingPlan(null);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  const getYearlySavings = (monthly: number, yearly?: number) => {
    if (!yearly) return 0;
    const yearlySavings = (monthly * 12) - yearly;
    return Math.round((yearlySavings / (monthly * 12)) * 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Choose Your Plan</h2>
          <p className="text-lg text-muted-foreground mb-8">
            Scale your screenwriting journey with the right tools
          </p>
          
          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={`text-sm ${!isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
              Monthly
            </span>
            <Switch
              checked={isYearly}
              onCheckedChange={setIsYearly}
              className="data-[state=checked]:bg-primary"
            />
            <span className={`text-sm ${isYearly ? 'font-semibold' : 'text-muted-foreground'}`}>
              Yearly
            </span>
            {isYearly && (
              <Badge variant="secondary" className="ml-2">
                Save up to 20%
              </Badge>
            )}
          </div>
        </div>

        {/* Current Plan Notice */}
        {showCurrentPlan && subscription && (
          <div className="text-center mb-8">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm">
              <Star className="h-4 w-4" />
              Current Plan: {subscription.plan_name}
            </div>
          </div>
        )}

        {/* Plans Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {plans.map((plan) => {
            const currentPrice = isYearly && plan.price_yearly ? plan.price_yearly : plan.price_monthly;
            const originalPrice = plan.price_monthly * (isYearly ? 12 : 1);
            const savings = getYearlySavings(plan.price_monthly, plan.price_yearly);
            const isCurrentPlan = subscription?.plan_id === plan.plan_id;
            const isLoading = loadingPlan === plan.plan_id;

            return (
              <Card 
                key={plan.id} 
                className={`relative transition-all duration-200 hover:shadow-lg ${
                  plan.is_popular ? 'ring-2 ring-primary shadow-lg scale-105' : ''
                } ${isCurrentPlan ? 'bg-primary/5' : ''}`}
              >
                {plan.is_popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge variant="default" className="px-3 py-1">
                      <Star className="h-3 w-3 mr-1" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl">{plan.display_name}</CardTitle>
                  <CardDescription className="text-sm">
                    {plan.description}
                  </CardDescription>
                  
                  {/* Pricing */}
                  <div className="mt-4">
                    <div className="flex items-baseline justify-center gap-2">
                      <span className="text-3xl font-bold">
                        {formatPrice(currentPrice)}
                      </span>
                      <span className="text-muted-foreground">
                        /{isYearly ? 'year' : 'month'}
                      </span>
                    </div>
                    
                    {isYearly && savings > 0 && (
                      <div className="mt-1">
                        <span className="text-sm text-muted-foreground line-through">
                          {formatPrice(originalPrice)}
                        </span>
                        <Badge variant="secondary" className="ml-2 text-xs">
                          Save {savings}%
                        </Badge>
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Features List */}
                  <div className="space-y-3 mb-6">
                    {Object.entries(plan.features).map(([key, value]) => {
                      if (value !== true) return null;
                      
                      const featureLabels: Record<string, string> = {
                        screenplay_editor: 'Screenplay Editor',
                        basic_ai_tools: 'Basic AI Tools',
                        advanced_ai_tools: 'Advanced AI Tools',
                        pdf_export: 'PDF Export',
                        all_export_formats: 'All Export Formats',
                        marketplace_submission: 'Marketplace Access',
                        team_collaboration: 'Team Collaboration',
                        scene_planning: 'Scene Planning',
                        storyboarding: 'AI Storyboarding',
                        production_tools: 'Production Tools',
                        real_time_editing: 'Real-time Editing',
                        version_history: 'Version History',
                        admin_controls: 'Admin Controls',
                        priority_support: 'Priority Support',
                        email_support: 'Email Support',
                        custom_integrations: 'Custom Integrations',
                        advanced_analytics: 'Advanced Analytics',
                        script_discovery: 'Script Discovery'
                      };

                      return (
                        <div key={key} className="flex items-center gap-2 text-sm">
                          <Check className="h-4 w-4 text-primary flex-shrink-0" />
                          <span>{featureLabels[key] || key}</span>
                        </div>
                      );
                    })}
                    
                    {/* Limits */}
                    {Object.entries(plan.limits).map(([key, value]) => {
                      const limitLabels: Record<string, string> = {
                        active_projects: 'Active Projects',
                        ai_generations_per_day: 'AI Generations/Day',
                        team_members: 'Team Members'
                      };

                      return (
                        <div key={key} className="flex items-center gap-2 text-sm">
                          <Zap className="h-4 w-4 text-orange-500 flex-shrink-0" />
                          <span>
                            {limitLabels[key] || key}: {value === -1 ? 'Unlimited' : value}
                          </span>
                        </div>
                      );
                    })}
                  </div>

                  {/* Action Button */}
                  <Button
                    onClick={() => handleSelectPlan(plan.plan_id)}
                    disabled={isCurrentPlan || isLoading}
                    className="w-full"
                    variant={plan.is_popular ? 'default' : 'outline'}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : isCurrentPlan ? (
                      'Current Plan'
                    ) : (
                      `Get ${plan.display_name}`
                    )}
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-sm text-muted-foreground mb-4">
            All plans include free updates and 30-day money-back guarantee
          </p>
          <p className="text-xs text-muted-foreground">
            Need a custom solution? <a href="/contact" className="text-primary hover:underline">Contact us</a>
          </p>
        </div>
      </div>
    </section>
  );
};

export default EnhancedPricingSection;
