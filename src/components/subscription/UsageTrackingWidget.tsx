
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Zap, TrendingUp, Al<PERSON>Triangle, ArrowUp } from 'lucide-react';
import { useEnhancedSubscription } from '@/hooks/useEnhancedSubscription';
import { useNavigate } from 'react-router-dom';

interface UsageTrackingWidgetProps {
  className?: string;
  showUpgradeButton?: boolean;
}

const UsageTrackingWidget: React.FC<UsageTrackingWidgetProps> = ({
  className,
  showUpgradeButton = true
}) => {
  const { subscription, loading } = useEnhancedSubscription();
  const navigate = useNavigate();

  if (loading || !subscription) {
    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="animate-pulse">Loading usage data...</div>
        </CardContent>
      </Card>
    );
  }

  const getUsageItems = () => {
    const items = [];
    
    // AI Generations usage
    const aiLimit = subscription.limits.ai_generations_per_day || 0;
    const aiUsage = subscription.usage_summary.ai_generations || 0;
    
    if (aiLimit > 0) {
      items.push({
        name: 'AI Generations',
        current: aiUsage,
        limit: aiLimit,
        unit: 'per day',
        icon: <Zap className="h-4 w-4" />,
        isUnlimited: aiLimit === -1
      });
    }

    // Coverage reports usage
    const coverageLimit = subscription.limits.coverage_reports_per_month || 0;
    const coverageUsage = subscription.usage_summary.coverage_reports || 0;
    
    if (coverageLimit > 0) {
      items.push({
        name: 'Coverage Reports',
        current: coverageUsage,
        limit: coverageLimit,
        unit: 'per month',
        icon: <TrendingUp className="h-4 w-4" />,
        isUnlimited: coverageLimit === -1
      });
    }

    return items;
  };

  const usageItems = getUsageItems();
  const hasUsageLimits = usageItems.length > 0;

  if (!hasUsageLimits) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Zap className="h-4 w-4" />
            Usage Tracking
          </CardTitle>
          <CardDescription>
            {subscription.subscribed ? 'No usage limits to track' : 'Upgrade to track your usage'}
          </CardDescription>
        </CardHeader>
        {!subscription.subscribed && showUpgradeButton && (
          <CardContent>
            <Button 
              onClick={() => navigate('/pricing')} 
              size="sm" 
              className="w-full"
            >
              <ArrowUp className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          </CardContent>
        )}
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-sm">
          <Zap className="h-4 w-4" />
          Usage Tracking
        </CardTitle>
        <CardDescription>
          Current usage for your {subscription.plan_name} plan
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {usageItems.map((item) => {
          const percentage = item.isUnlimited ? 0 : (item.current / item.limit) * 100;
          const isAtLimit = !item.isUnlimited && item.current >= item.limit;
          const isNearLimit = !item.isUnlimited && percentage > 80;

          return (
            <div key={item.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {item.icon}
                  <span className="text-sm font-medium">{item.name}</span>
                  {isAtLimit && (
                    <Badge variant="destructive" className="text-xs">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Limit Reached
                    </Badge>
                  )}
                  {isNearLimit && !isAtLimit && (
                    <Badge variant="secondary" className="text-xs">
                      Near Limit
                    </Badge>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {item.isUnlimited ? (
                    <Badge variant="outline" className="text-xs">Unlimited</Badge>
                  ) : (
                    `${item.current} / ${item.limit} ${item.unit}`
                  )}
                </div>
              </div>
              
              {!item.isUnlimited && (
                <Progress 
                  value={percentage} 
                  className={`h-2 ${isAtLimit ? '[&>div]:bg-red-500' : isNearLimit ? '[&>div]:bg-yellow-500' : ''}`}
                />
              )}
            </div>
          );
        })}

        {showUpgradeButton && !subscription.subscribed && (
          <Button 
            onClick={() => navigate('/pricing')} 
            size="sm" 
            className="w-full mt-4"
          >
            <ArrowUp className="h-4 w-4 mr-2" />
            Upgrade Plan
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default UsageTrackingWidget;
