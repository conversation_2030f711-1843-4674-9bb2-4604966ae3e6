
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Calendar, CreditCard, AlertTriangle, Settings, Star, Users, Zap } from 'lucide-react';
import { useEnhancedSubscription } from '@/hooks/useEnhancedSubscription';
import { format } from 'date-fns';

interface SubscriptionStatusCardProps {
  showManageButton?: boolean;
  className?: string;
}

const SubscriptionStatusCard: React.FC<SubscriptionStatusCardProps> = ({
  showManageButton = true,
  className
}) => {
  const { subscription, loading, openCustomerPortal } = useEnhancedSubscription();

  if (loading || !subscription) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-6">
            <div className="animate-pulse">Loading subscription details...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'trialing':
        return 'bg-blue-500';
      case 'past_due':
        return 'bg-yellow-500';
      case 'canceled':
      case 'incomplete':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'trialing':
        return 'Trial';
      case 'past_due':
        return 'Past Due';
      case 'canceled':
        return 'Canceled';
      case 'incomplete':
        return 'Incomplete';
      default:
        return 'Inactive';
    }
  };

  const isTrialing = subscription.trial_end && new Date(subscription.trial_end) > new Date();
  const renewalDate = subscription.current_period_end ? new Date(subscription.current_period_end) : null;
  const trialEndDate = subscription.trial_end ? new Date(subscription.trial_end) : null;

  // Calculate usage progress for key limits
  const activeProjectsLimit = subscription.limits.active_projects || 0;
  const aiGenerationsLimit = subscription.limits.ai_generations_per_day || 0;
  const currentAiUsage = subscription.usage_summary.ai_generations || 0;
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Subscription Status
          </div>
          <Badge 
            variant="secondary"
            className={`${getStatusColor(subscription.status)} text-white`}
          >
            {getStatusLabel(subscription.status)}
          </Badge>
        </CardTitle>
        <CardDescription>
          Current plan: <span className="font-semibold">{subscription.plan_name}</span>
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Plan Highlights */}
        <div className="flex items-center gap-2 p-3 bg-primary/5 rounded-lg">
          <Star className="h-4 w-4 text-primary" />
          <span className="text-sm font-medium">
            {subscription.subscribed ? 'Premium Features Active' : 'Free Plan Active'}
          </span>
        </div>

        {/* Trial or Renewal Info */}
        {isTrialing && trialEndDate ? (
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-blue-500" />
            <span>Trial ends {format(trialEndDate, 'PPP')}</span>
          </div>
        ) : renewalDate ? (
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>
              {subscription.cancel_at_period_end ? 'Cancels' : 'Renews'} on {format(renewalDate, 'PPP')}
            </span>
          </div>
        ) : null}

        {/* Cancellation Warning */}
        {subscription.cancel_at_period_end && (
          <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">
              Your subscription will cancel at the end of the current period
            </span>
          </div>
        )}

        {/* Seats Information */}
        {subscription.seats_info.type === 'organization' && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Team Seats</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Used / Total</span>
              <span>{subscription.seats_info.used} / {subscription.seats_info.total}</span>
            </div>
            <Progress 
              value={(subscription.seats_info.used / subscription.seats_info.total) * 100} 
              className="h-2" 
            />
          </div>
        )}

        {/* Usage Limits (for premium plans) */}
        {subscription.subscribed && (
          <div className="space-y-4">
            <h4 className="font-medium text-sm flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Usage & Limits
            </h4>
            
            {/* Active Projects */}
            {activeProjectsLimit > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Active Projects</span>
                  <span>{activeProjectsLimit === -1 ? 'Unlimited' : `0 / ${activeProjectsLimit}`}</span>
                </div>
                {activeProjectsLimit !== -1 && (
                  <Progress value={0} className="h-2" />
                )}
              </div>
            )}

            {/* AI Generations */}
            {aiGenerationsLimit > 0 && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>AI Generations (Daily)</span>
                  <span>
                    {aiGenerationsLimit === -1 ? 'Unlimited' : `${currentAiUsage} / ${aiGenerationsLimit}`}
                  </span>
                </div>
                {aiGenerationsLimit !== -1 && (
                  <Progress 
                    value={(currentAiUsage / aiGenerationsLimit) * 100} 
                    className="h-2" 
                  />
                )}
              </div>
            )}
          </div>
        )}

        {/* Active Add-ons */}
        {subscription.addons && subscription.addons.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Active Add-ons</h4>
            <div className="space-y-2">
              {subscription.addons.map((addon) => (
                <div key={addon.addon_key} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                  <span className="text-sm font-medium">{addon.name}</span>
                  <Badge variant="outline" className="text-xs">Active</Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {showManageButton && subscription.subscribed && (
          <Button
            onClick={openCustomerPortal}
            variant="outline"
            className="w-full"
          >
            <Settings className="h-4 w-4 mr-2" />
            Manage Subscription
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default SubscriptionStatusCard;
