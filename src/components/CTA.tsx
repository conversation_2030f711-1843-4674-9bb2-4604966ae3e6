
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const CTA = () => {
  const scrollToPricing = () => {
    const pricingSection = document.getElementById('pricing');
    if (pricingSection) {
      pricingSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="py-16 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-1/4 w-72 h-72 bg-primary rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-accent rounded-full blur-3xl animate-float delay-200"></div>
      </div>
      
      <div className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-playfair font-bold mb-6 animate-slide-up">
            Ready to Write Your <span className="gold-gradient">Masterpiece</span>?
          </h2>
          
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto animate-slide-up delay-100">
            Join thousands of screenwriters who are already creating their next breakthrough with ScriptGenius.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8 animate-slide-up delay-200">
            <Button 
              size="lg" 
              className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xl px-12 py-6 group animate-glow"
              onClick={scrollToPricing}
            >
              Get Started Today
              <ArrowRight className="ml-3 h-6 w-6 group-hover:translate-x-1 transition-transform" />
            </Button>
            <Button size="lg" variant="outline" className="border-muted-foreground text-foreground hover:bg-secondary text-xl px-12 py-6">
              Schedule a Demo
            </Button>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8 max-w-3xl mx-auto animate-fade-scale delay-300">
            <div className="text-center">
              <div className="text-3xl font-bold gold-gradient mb-2">Professional</div>
              <div className="text-muted-foreground">Grade Tools</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gold-gradient mb-2">No Lock-in</div>
              <div className="text-muted-foreground">Cancel Anytime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold gold-gradient mb-2">24/7</div>
              <div className="text-muted-foreground">Support</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
