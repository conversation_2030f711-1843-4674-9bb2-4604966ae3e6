
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { env } from '@/lib/config/environment';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'feature';
  name?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

/**
 * Comprehensive Error Boundary with reporting and recovery options
 */
class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Generate unique error ID for tracking
    const errorId = `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Call custom error handler if provided
    this.props.onError?.(error, errorInfo);

    // Log error details
    this.logError(error, errorInfo);

    // Report error to monitoring service
    this.reportError(error, errorInfo);
  }

  private logError(error: Error, errorInfo: ErrorInfo) {
    const errorDetails = {
      errorId: this.state.errorId,
      name: error.name,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      level: this.props.level || 'component',
      boundaryName: this.props.name || 'Unknown',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
    };

    console.error('Error Boundary Caught Error:', errorDetails);

    // Store error in localStorage for debugging
    try {
      const storedErrors = JSON.parse(localStorage.getItem('errorBoundaryLogs') || '[]');
      storedErrors.push(errorDetails);

      // Keep only last 10 errors
      if (storedErrors.length > 10) {
        storedErrors.splice(0, storedErrors.length - 10);
      }

      localStorage.setItem('errorBoundaryLogs', JSON.stringify(storedErrors));
    } catch (e) {
      console.warn('Failed to store error in localStorage:', e);
    }
  }

  private async reportError(error: Error, errorInfo: ErrorInfo) {
    if (!env.monitoring.errorEndpoint) {
      return;
    }

    try {
      const errorReport = {
        errorId: this.state.errorId,
        name: error.name,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        level: this.props.level || 'component',
        boundaryName: this.props.name || 'Unknown',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: this.getCurrentUserId(),
        appVersion: env.app.version,
        environment: env.app.environment,
      };

      await fetch(env.monitoring.errorEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport),
      });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  }

  private getCurrentUserId(): string | null {
    try {
      // Try to get user ID from various sources
      const user = JSON.parse(localStorage.getItem('supabase.auth.token') || '{}');
      return user?.user?.id || null;
    } catch {
      return null;
    }
  }

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
      });
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private handleReportBug = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
    };

    const subject = encodeURIComponent(`Bug Report: ${this.state.error?.name || 'Unknown Error'}`);
    const body = encodeURIComponent(`
Error ID: ${errorDetails.errorId}
Error Message: ${errorDetails.message}
Component: ${this.props.name || 'Unknown'}
Level: ${this.props.level || 'component'}

Please describe what you were doing when this error occurred:


Technical Details:
${JSON.stringify(errorDetails, null, 2)}
    `);

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  public render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Render appropriate error UI based on level
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  private renderErrorUI() {
    const { level = 'component', name } = this.props;
    const { error, errorId } = this.state;
    const canRetry = this.retryCount < this.maxRetries;

    if (level === 'page') {
      return this.renderPageError();
    }

    if (level === 'feature') {
      return this.renderFeatureError();
    }

    // Component level error
    return (
      <Card className="border-red-200 bg-red-50">
        <CardHeader className="pb-3">
          <CardTitle className="text-red-800 flex items-center text-sm">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Component Error
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <Alert>
            <AlertDescription className="text-sm">
              {name ? `The ${name} component` : 'This component'} encountered an error and couldn't render properly.
            </AlertDescription>
          </Alert>

          <div className="flex gap-2">
            {canRetry && (
              <Button
                onClick={this.handleRetry}
                size="sm"
                variant="outline"
                className="text-xs"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry ({this.maxRetries - this.retryCount} left)
              </Button>
            )}

            <Button
              onClick={this.handleReload}
              size="sm"
              variant="outline"
              className="text-xs"
            >
              Reload Page
            </Button>
          </div>

          {env.app.environment === 'development' && (
            <details className="text-xs">
              <summary className="cursor-pointer text-red-700 font-medium">
                Error Details (Development)
              </summary>
              <pre className="mt-2 p-2 bg-red-100 rounded text-xs overflow-auto">
                {error?.stack}
              </pre>
            </details>
          )}

          <div className="text-xs text-gray-500">
            Error ID: {errorId}
          </div>
        </CardContent>
      </Card>
    );
  }

  private renderPageError() {
    const { error, errorId } = this.state;

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
        <Card className="max-w-md w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Something went wrong</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-gray-600">
              We're sorry, but something unexpected happened. Our team has been notified.
            </p>

            <div className="flex flex-col gap-2">
              <Button onClick={this.handleReload} className="w-full">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>

              <Button onClick={this.handleGoHome} variant="outline" className="w-full">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>

              <Button onClick={this.handleReportBug} variant="ghost" className="w-full">
                <Bug className="h-4 w-4 mr-2" />
                Report Bug
              </Button>
            </div>

            <div className="text-xs text-gray-400 pt-4 border-t">
              Error ID: {errorId}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  private renderFeatureError() {
    const { name } = this.props;
    const { errorId } = this.state;
    const canRetry = this.retryCount < this.maxRetries;

    return (
      <Card className="border-orange-200 bg-orange-50">
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-orange-800 mb-2">
            {name || 'Feature'} Unavailable
          </h3>
          <p className="text-orange-700 mb-4">
            This feature is temporarily unavailable due to an error. Please try again.
          </p>

          <div className="flex justify-center gap-2">
            {canRetry && (
              <Button onClick={this.handleRetry} size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            )}

            <Button onClick={this.handleReload} variant="outline" size="sm">
              Reload Page
            </Button>
          </div>

          <div className="text-xs text-gray-500 mt-4">
            Error ID: {errorId}
          </div>
        </CardContent>
      </Card>
    );
  }
}
