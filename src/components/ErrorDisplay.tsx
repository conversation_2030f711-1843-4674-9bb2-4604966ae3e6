
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, XCircle, AlertTriangle } from 'lucide-react';
import type { AppError } from '@/lib/error-handling/ErrorHandler';

interface ErrorDisplayProps {
  error?: AppError | string | null;
  errors?: Record<string, string | string[]>;
  title?: string;
  showIcon?: boolean;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  errors, 
  title = "Error",
  showIcon = true,
  className 
}) => {
  // Handle no errors
  if (!error && (!errors || Object.keys(errors).length === 0)) {
    return null;
  }

  // Determine error severity and icon - always return valid Alert variant
  const getSeverityVariant = (severity?: string): "default" | "destructive" => {
    // Always return destructive for errors - this is more appropriate for error display
    return 'destructive';
  };

  const getSeverityIcon = (severity?: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4" />;
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <AlertCircle className="h-4 w-4" />;
    }
  };

  // Handle single error
  if (error) {
    const appError = typeof error === 'string' 
      ? { userMessage: error, severity: 'medium' as const }
      : error;

    return (
      <Alert variant={getSeverityVariant(appError.severity)} className={className}>
        {showIcon && getSeverityIcon(appError.severity)}
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{appError.userMessage}</AlertDescription>
      </Alert>
    );
  }

  // Handle validation errors
  if (errors) {
    return (
      <Alert variant="destructive" className={className}>
        {showIcon && <AlertCircle className="h-4 w-4" />}
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>
          Please fix the following errors:
          <ul className="list-disc list-inside mt-2 space-y-1">
            {Object.entries(errors).map(([field, message]) => {
              const fieldName = field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
              const errorMessage = Array.isArray(message) ? message.join(', ') : message;
              return (
                <li key={field} className="text-sm">
                  <strong>{fieldName}:</strong> {errorMessage}
                </li>
              );
            })}
          </ul>
        </AlertDescription>
      </Alert>
    );
  }

  return null;
};

export default ErrorDisplay;
