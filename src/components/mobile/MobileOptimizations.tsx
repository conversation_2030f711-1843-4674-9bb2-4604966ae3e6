
import React, { useEffect } from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface MobileOptimizationsProps {
  children: React.ReactNode;
  className?: string;
  enableTouchOptimizations?: boolean;
  enableViewportOptimizations?: boolean;
  reducedAnimations?: boolean;
}

const MobileOptimizations: React.FC<MobileOptimizationsProps> = ({ 
  children, 
  className,
  enableTouchOptimizations = true,
  enableViewportOptimizations = true,
  reducedAnimations = false
}) => {
  const isMobile = useIsMobile();

  useEffect(() => {
    if (!isMobile || typeof document === 'undefined') return;

    try {
      // Optimize for mobile performance
      if (enableViewportOptimizations) {
        // Prevent zoom on input focus
        const viewport = document.querySelector('meta[name="viewport"]');
        if (viewport) {
          viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover');
        }

        // Add mobile-specific CSS custom properties
        document.documentElement.style.setProperty('--mobile-safe-area-top', 'env(safe-area-inset-top)');
        document.documentElement.style.setProperty('--mobile-safe-area-bottom', 'env(safe-area-inset-bottom)');
      }

      if (enableTouchOptimizations) {
        // Improve touch responsiveness
        document.body.style.setProperty('-webkit-tap-highlight-color', 'transparent');
        document.body.style.setProperty('-webkit-touch-callout', 'none');
        document.body.style.setProperty('touch-action', 'manipulation');
      }

      // Reduce animations on mobile for better performance
      if (reducedAnimations) {
        document.body.style.setProperty('--animation-duration', '0.1s');
      }
    } catch (error) {
      console.warn('Mobile optimization setup failed:', error);
    }

    return () => {
      try {
        // Cleanup on unmount
        if (enableViewportOptimizations) {
          const viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, viewport-fit=cover');
          }
        }
      } catch (error) {
        console.warn('Mobile optimization cleanup failed:', error);
      }
    };
  }, [isMobile, enableTouchOptimizations, enableViewportOptimizations, reducedAnimations]);

  return (
    <div className={cn(
      "w-full",
      isMobile && [
        "px-2 py-1", // Reduced padding on mobile
        "text-sm", // Smaller text on mobile
        "touch-manipulation", // Better touch handling
        reducedAnimations && "motion-reduce", // Reduced motion class
      ],
      className
    )} style={{
      // Mobile-specific optimizations
      ...(isMobile && {
        paddingTop: 'max(0.25rem, env(safe-area-inset-top))',
        paddingBottom: 'max(0.25rem, env(safe-area-inset-bottom))',
        paddingLeft: 'max(0.5rem, env(safe-area-inset-left))',
        paddingRight: 'max(0.5rem, env(safe-area-inset-right))',
      })
    }}>
      {children}
    </div>
  );
};

export default MobileOptimizations;
