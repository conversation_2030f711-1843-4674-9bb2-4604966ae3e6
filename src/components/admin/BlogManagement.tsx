
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Edit, Trash2, Eye, Calendar, User } from 'lucide-react';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  author: string;
  date: string;
  category: string;
  status: 'published' | 'draft' | 'scheduled';
  featured: boolean;
}

const BlogManagement = () => {
  const [posts, setPosts] = useState<BlogPost[]>([
    {
      id: 1,
      title: "The Future of Screenwriting: How AI is Revolutionizing Creative Collaboration",
      excerpt: "Exploring the transformative impact of artificial intelligence on the screenwriting process...",
      author: "<PERSON>",
      date: "2024-06-10",
      category: "AI Collaboration",
      status: "published",
      featured: true
    },
    {
      id: 2,
      title: "Breaking Into Television: A Writer's Guide to the Small Screen Revolution",
      excerpt: "With streaming platforms dominating entertainment, television writing has never been more competitive...",
      author: "<PERSON>",
      date: "2024-06-08",
      category: "Industry Insights",
      status: "published",
      featured: false
    },
    {
      id: 3,
      title: "Character Development in the Digital Age: Balancing Human Intuition with AI Insights",
      excerpt: "Modern screenwriters have unprecedented tools for character development...",
      author: "Emma Thompson",
      date: "2024-06-05",
      category: "Writing Craft",
      status: "published",
      featured: true
    },
    {
      id: 4,
      title: "From Script to Screen: Streamlining Production with Modern Technology",
      excerpt: "How digital tools are transforming film production workflows...",
      author: "David Kim",
      date: "2024-06-03",
      category: "Production",
      status: "draft",
      featured: false
    },
    {
      id: 5,
      title: "The Psychology of Fear: Why Writers Resist AI and How to Overcome It",
      excerpt: "Understanding the psychological barriers that prevent creative professionals from adopting AI tools...",
      author: "Dr. Jennifer Walsh",
      date: "2024-06-01",
      category: "AI Collaboration",
      status: "published",
      featured: false
    },
    {
      id: 6,
      title: "Interview: Oscar Winner Shares Secrets of Collaborative Storytelling",
      excerpt: "An exclusive conversation with Academy Award-winning screenwriter about the evolving nature of collaboration...",
      author: "Alex Morgan",
      date: "2024-05-28",
      category: "Interviews",
      status: "scheduled",
      featured: true
    }
  ]);

  const [selectedCategory, setSelectedCategory] = useState('All');
  const categories = ['All', 'Writing Craft', 'AI Collaboration', 'Industry Insights', 'Production', 'Technology', 'Interviews'];

  const filteredPosts = selectedCategory === 'All' 
    ? posts 
    : posts.filter(post => post.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-500';
      case 'draft': return 'bg-yellow-500';
      case 'scheduled': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const handleDeletePost = (id: number) => {
    setPosts(posts.filter(post => post.id !== id));
  };

  const handleToggleFeatured = (id: number) => {
    setPosts(posts.map(post => 
      post.id === id ? { ...post, featured: !post.featured } : post
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Blog Management</h1>
          <p className="text-gray-600 mt-2">Manage blog posts, categories, and content</p>
        </div>
        <Button className="bg-primary hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          New Post
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{posts.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{posts.filter(p => p.status === 'published').length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{posts.filter(p => p.status === 'draft').length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Featured</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{posts.filter(p => p.featured).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Category Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Filter by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Posts List */}
      <Card>
        <CardHeader>
          <CardTitle>Blog Posts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredPosts.map((post) => (
              <div key={post.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="font-semibold text-lg">{post.title}</h3>
                      {post.featured && (
                        <Badge variant="outline" className="text-xs">Featured</Badge>
                      )}
                      <Badge className={`text-xs text-white ${getStatusColor(post.status)}`}>
                        {post.status}
                      </Badge>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{post.excerpt}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {post.author}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(post.date).toLocaleDateString()}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {post.category}
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleToggleFeatured(post.id)}
                      className={post.featured ? "text-yellow-600" : ""}
                    >
                      ⭐
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleDeletePost(post.id)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default BlogManagement;
