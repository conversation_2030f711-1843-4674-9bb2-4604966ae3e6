
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Calendar, Users, TrendingUp, DollarSign, Activity, Copy } from 'lucide-react';
import { promoCampaignsApi, type PromoCampaign, type PromoCampaignUsage, type PromoCampaignAnalytics } from '@/lib/api/promo-campaigns';
import { toast } from 'sonner';

interface PromoCampaignDetailProps {
  campaign: PromoCampaign;
  open: boolean;
  onClose: () => void;
}

export const PromoCampaignDetail: React.FC<PromoCampaignDetailProps> = ({
  campaign,
  open,
  onClose
}) => {
  const [usage, setUsage] = useState<PromoCampaignUsage[]>([]);
  const [analytics, setAnalytics] = useState<PromoCampaignAnalytics[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (open && campaign) {
      fetchCampaignData();
    }
  }, [open, campaign]);

  const fetchCampaignData = async () => {
    setLoading(true);
    try {
      const [usageResult, analyticsResult] = await Promise.all([
        promoCampaignsApi.getCampaignUsage(campaign.id),
        promoCampaignsApi.getCampaignAnalytics(campaign.id)
      ]);

      if (usageResult.success && usageResult.data) {
        setUsage(usageResult.data);
      }

      if (analyticsResult.success && analyticsResult.data) {
        setAnalytics(analyticsResult.data);
      }
    } catch (error) {
      console.error('Failed to fetch campaign data:', error);
    } finally {
      setLoading(false);
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(campaign.campaign_code);
    toast.success('Promo code copied to clipboard!');
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      active: 'default',
      paused: 'outline',
      completed: 'secondary',
      cancelled: 'destructive'
    } as const;
    
    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
  };

  const getDiscountDisplay = (type: string, value: number) => {
    if (type === 'percentage') return `${value}%`;
    if (type === 'fixed_amount') return `$${value}`;
    if (type === 'free_trial') return 'Free Trial';
    return `${value}`;
  };

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;

  const totalDiscount = usage.reduce((sum, u) => sum + (u.discount_amount || 0), 0);
  const totalRevenue = usage.reduce((sum, u) => sum + (u.final_amount || 0), 0);
  const uniqueUsers = new Set(usage.map(u => u.user_id)).size;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{campaign.name}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Campaign Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Campaign Details
                {getStatusBadge(campaign.status)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <strong>Promo Code:</strong>
                    <code className="bg-muted px-2 py-1 rounded">{campaign.campaign_code}</code>
                    <Button size="sm" variant="ghost" onClick={copyCode}>
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div><strong>Discount:</strong> {getDiscountDisplay(campaign.discount_type, campaign.discount_value)}</div>
                  <div><strong>Target Audience:</strong> {campaign.target_audience.replace('_', ' ')}</div>
                </div>
                <div className="space-y-2">
                  <div><strong>Start Date:</strong> {new Date(campaign.start_date).toLocaleDateString()}</div>
                  <div><strong>End Date:</strong> {campaign.end_date ? new Date(campaign.end_date).toLocaleDateString() : 'No end date'}</div>
                  <div><strong>Usage Limit:</strong> {campaign.usage_limit || 'Unlimited'}</div>
                </div>
              </div>
              {campaign.description && (
                <div className="mt-4">
                  <strong>Description:</strong>
                  <p className="text-muted-foreground mt-1">{campaign.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Statistics */}
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Uses</p>
                    <p className="text-2xl font-bold">{usage.length}</p>
                  </div>
                  <Activity className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Unique Users</p>
                    <p className="text-2xl font-bold">{uniqueUsers}</p>
                  </div>
                  <Users className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Discount</p>
                    <p className="text-2xl font-bold">{formatCurrency(totalDiscount)}</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                    <p className="text-2xl font-bold">{formatCurrency(totalRevenue)}</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-emerald-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Data */}
          <Tabs defaultValue="usage" className="space-y-4">
            <TabsList>
              <TabsTrigger value="usage">Usage History</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="usage" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Usage History</CardTitle>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Date</TableHead>
                          <TableHead>User ID</TableHead>
                          <TableHead>Original Amount</TableHead>
                          <TableHead>Discount</TableHead>
                          <TableHead>Final Amount</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {usage.map((usageRecord) => (
                          <TableRow key={usageRecord.id}>
                            <TableCell>
                              {new Date(usageRecord.applied_at).toLocaleDateString()}
                            </TableCell>
                            <TableCell>
                              <code className="text-xs">{usageRecord.user_id}</code>
                            </TableCell>
                            <TableCell>{formatCurrency(usageRecord.original_amount)}</TableCell>
                            <TableCell className="text-red-600">
                              -{formatCurrency(usageRecord.discount_amount)}
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatCurrency(usageRecord.final_amount)}
                            </TableCell>
                          </TableRow>
                        ))}
                        {usage.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                              No usage data available
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Daily Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  {analytics.length > 0 ? (
                    <div className="h-64">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={analytics}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="date" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="applications" fill="#8884d8" name="Applications" />
                          <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                        </BarChart>
                      </ResponsiveContainer>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      No analytics data available yet
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};
