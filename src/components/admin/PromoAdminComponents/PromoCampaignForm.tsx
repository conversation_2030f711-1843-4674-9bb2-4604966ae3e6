
import React from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { usePromoCampaigns } from '@/hooks/usePromoCampaigns';
import type { PromoCampaign, PromoCampaignInsert, PromoCampaignUpdate } from '@/lib/api/promo-campaigns';

interface PromoCampaignFormProps {
  campaign?: PromoCampaign;
  open: boolean;
  onClose: () => void;
}

type DiscountType = 'percentage' | 'fixed_amount' | 'free_trial';
type TargetAudience = 'all_users' | 'new_users' | 'existing_users' | 'specific_plans' | 'custom_segment';

export const PromoCampaignForm: React.FC<PromoCampaignFormProps> = ({
  campaign,
  open,
  onClose
}) => {
  const { createCampaign, updateCampaign, creating, updating } = usePromoCampaigns();
  const [startDate, setStartDate] = React.useState<Date>(campaign ? new Date(campaign.start_date) : new Date());
  const [endDate, setEndDate] = React.useState<Date | undefined>(
    campaign && campaign.end_date ? new Date(campaign.end_date) : undefined
  );
  const [showAdvanced, setShowAdvanced] = React.useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      name: campaign?.name || '',
      description: campaign?.description || '',
      campaign_code: campaign?.campaign_code || '',
      discount_type: campaign?.discount_type || 'percentage' as DiscountType,
      discount_value: campaign?.discount_value || 0,
      target_audience: campaign?.target_audience || 'all_users' as TargetAudience,
      usage_limit: campaign?.usage_limit || undefined,
      usage_limit_per_user: campaign?.usage_limit_per_user || 1,
      is_active: campaign?.is_active ?? true,
      status: campaign?.status || 'draft'
    }
  });

  const discountType = watch('discount_type');
  const targetAudience = watch('target_audience');
  const isEditing = !!campaign;

  const generateCode = () => {
    const code = Math.random().toString(36).substring(2, 10).toUpperCase();
    setValue('campaign_code', code);
  };

  const onSubmit = async (data: any) => {
    const campaignData = {
      ...data,
      start_date: startDate.toISOString(),
      end_date: endDate?.toISOString(),
      discount_value: Number(data.discount_value),
      usage_limit: data.usage_limit ? Number(data.usage_limit) : null,
      usage_limit_per_user: Number(data.usage_limit_per_user)
    };

    let result;
    if (isEditing) {
      result = await updateCampaign(campaign.id, campaignData as PromoCampaignUpdate);
    } else {
      result = await createCampaign(campaignData as PromoCampaignInsert);
    }

    if (result) {
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? 'Edit Campaign' : 'Create New Campaign'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Campaign Name *</Label>
                  <Input
                    id="name"
                    {...register('name', { required: 'Campaign name is required' })}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive">{errors.name.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="campaign_code">Promo Code *</Label>
                  <div className="flex gap-2">
                    <Input
                      id="campaign_code"
                      {...register('campaign_code', { required: 'Promo code is required' })}
                    />
                    <Button type="button" variant="outline" onClick={generateCode}>
                      Generate
                    </Button>
                  </div>
                  {errors.campaign_code && (
                    <p className="text-sm text-destructive">{errors.campaign_code.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Optional description of the campaign"
                />
              </div>
            </CardContent>
          </Card>

          {/* Discount Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Discount Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="discount_type">Discount Type *</Label>
                  <Select
                    value={discountType}
                    onValueChange={(value: DiscountType) => setValue('discount_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">Percentage</SelectItem>
                      <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                      <SelectItem value="free_trial">Free Trial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="discount_value">
                    {discountType === 'percentage' ? 'Percentage (%)' : 
                     discountType === 'fixed_amount' ? 'Amount ($)' : 'Trial Days'}
                  </Label>
                  <Input
                    id="discount_value"
                    type="number"
                    min="0"
                    {...register('discount_value', { 
                      required: 'Discount value is required',
                      min: { value: 0, message: 'Value must be positive' }
                    })}
                  />
                  {errors.discount_value && (
                    <p className="text-sm text-destructive">{errors.discount_value.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Schedule</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Start Date *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {format(startDate, 'PPP')}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={(date) => date && setStartDate(date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="space-y-2">
                  <Label>End Date (Optional)</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, 'PPP') : 'No end date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={setEndDate}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Advanced Settings */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Advanced Settings</CardTitle>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  {showAdvanced ? 'Hide' : 'Show'} Advanced
                </Button>
              </div>
            </CardHeader>
            {showAdvanced && (
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="usage_limit">Total Usage Limit</Label>
                    <Input
                      id="usage_limit"
                      type="number"
                      min="1"
                      {...register('usage_limit')}
                      placeholder="Unlimited"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="usage_limit_per_user">Uses Per User *</Label>
                    <Input
                      id="usage_limit_per_user"
                      type="number"
                      min="1"
                      {...register('usage_limit_per_user', { 
                        required: 'Uses per user is required',
                        min: { value: 1, message: 'Must be at least 1' }
                      })}
                    />
                    {errors.usage_limit_per_user && (
                      <p className="text-sm text-destructive">{errors.usage_limit_per_user.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="target_audience">Target Audience</Label>
                  <Select
                    value={targetAudience}
                    onValueChange={(value: TargetAudience) => setValue('target_audience', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all_users">All Users</SelectItem>
                      <SelectItem value="new_users">New Users</SelectItem>
                      <SelectItem value="existing_users">Existing Users</SelectItem>
                      <SelectItem value="specific_plans">Specific Plans</SelectItem>
                      <SelectItem value="custom_segment">Custom Segment</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="is_active">Active Campaign</Label>
                    <div className="text-sm text-muted-foreground">
                      Enable this campaign for users
                    </div>
                  </div>
                  <Switch
                    id="is_active"
                    checked={watch('is_active')}
                    onCheckedChange={(checked) => setValue('is_active', checked)}
                  />
                </div>
              </CardContent>
            )}
          </Card>

          {/* Actions */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={creating || updating}>
              {creating || updating ? 'Saving...' : isEditing ? 'Update Campaign' : 'Create Campaign'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
