
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Search, Copy } from 'lucide-react';
import { promoCampaignsApi } from '@/lib/api/promo-campaigns';
import { toast } from 'sonner';

export const PromoCodeValidator: React.FC = () => {
  const [code, setCode] = useState('');
  const [validationResult, setValidationResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [testAmount, setTestAmount] = useState('100');

  const validateCode = async () => {
    if (!code.trim()) {
      toast.error('Please enter a promo code');
      return;
    }

    setLoading(true);
    try {
      const result = await promoCampaignsApi.validatePromoCode(code.trim());
      if (result.success && result.data) {
        setValidationResult(result.data);
      } else {
        setValidationResult({ valid: false, error_message: 'Failed to validate code' });
      }
    } catch (error) {
      console.error('Validation error:', error);
      setValidationResult({ valid: false, error_message: 'Validation failed' });
    } finally {
      setLoading(false);
    }
  };

  const testApplication = async () => {
    if (!validationResult?.valid || !testAmount) {
      toast.error('Please validate a code first and enter a test amount');
      return;
    }

    setLoading(true);
    try {
      // Note: In real implementation, you might want to use a test user ID
      const result = await promoCampaignsApi.applyPromoCode(
        code,
        'test-user-id', // Replace with actual test user ID
        Number(testAmount)
      );
      
      if (result.success && result.data) {
        toast.success('Test application successful!');
        console.log('Application result:', result.data);
      } else {
        toast.error(result.data?.error_message || 'Test application failed');
      }
    } catch (error) {
      console.error('Application error:', error);
      toast.error('Test application failed');
    } finally {
      setLoading(false);
    }
  };

  const copyCode = () => {
    navigator.clipboard.writeText(code);
    toast.success('Code copied to clipboard!');
  };

  const getDiscountDisplay = (type: string, value: number) => {
    if (type === 'percentage') return `${value}%`;
    if (type === 'fixed_amount') return `$${value}`;
    if (type === 'free_trial') return 'Free Trial';
    return `${value}`;
  };

  const calculateDiscount = (originalAmount: number, discountType: string, discountValue: number) => {
    if (discountType === 'percentage') {
      return originalAmount * (discountValue / 100);
    } else if (discountType === 'fixed_amount') {
      return Math.min(discountValue, originalAmount);
    }
    return 0;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Promo Code Validator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="promoCode">Promo Code</Label>
              <div className="flex gap-2">
                <Input
                  id="promoCode"
                  value={code}
                  onChange={(e) => setCode(e.target.value.toUpperCase())}
                  placeholder="Enter promo code"
                  className="font-mono"
                />
                <Button variant="outline" size="icon" onClick={copyCode} disabled={!code}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="testAmount">Test Amount ($)</Label>
              <Input
                id="testAmount"
                type="number"
                value={testAmount}
                onChange={(e) => setTestAmount(e.target.value)}
                placeholder="100.00"
              />
            </div>
            <div className="flex items-end">
              <Button onClick={validateCode} disabled={loading || !code} className="w-full">
                {loading ? 'Validating...' : 'Validate Code'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Results */}
      {validationResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {validationResult.valid ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              Validation Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            {validationResult.valid ? (
              <div className="space-y-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    ✅ Promo code is valid and active!
                  </AlertDescription>
                </Alert>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <h4 className="font-semibold">Campaign Details</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Campaign ID:</span>
                        <code className="text-xs bg-muted px-2 py-1 rounded">
                          {validationResult.campaign_id}
                        </code>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Discount Type:</span>
                        <Badge variant="outline">{validationResult.discount_type}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Discount Value:</span>
                        <Badge variant="secondary">
                          {getDiscountDisplay(validationResult.discount_type, validationResult.discount_value)}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <h4 className="font-semibold">Test Calculation</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Original Amount:</span>
                        <span>${Number(testAmount).toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Discount Amount:</span>
                        <span className="text-red-600">
                          -${calculateDiscount(Number(testAmount), validationResult.discount_type, validationResult.discount_value).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between font-semibold border-t pt-2">
                        <span>Final Amount:</span>
                        <span className="text-green-600">
                          ${(Number(testAmount) - calculateDiscount(Number(testAmount), validationResult.discount_type, validationResult.discount_value)).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button onClick={testApplication} disabled={loading}>
                    {loading ? 'Testing...' : 'Test Application'}
                  </Button>
                  <Button variant="outline" onClick={() => setValidationResult(null)}>
                    Clear Results
                  </Button>
                </div>
              </div>
            ) : (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  ❌ {validationResult.error_message || 'Invalid promo code'}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Quick Test Codes */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Use these buttons to quickly test with existing promo codes:
            </p>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCode('WELCOME10')}
              >
                WELCOME10
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCode('SAVE20')}
              >
                SAVE20
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCode('FREEMONTH')}
              >
                FREEMONTH
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
