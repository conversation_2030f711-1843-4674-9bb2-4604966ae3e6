
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, DollarSign, Users, Activity } from 'lucide-react';
import { usePromoCampaigns, usePromoCampaignAnalytics } from '@/hooks/usePromoCampaigns';

export const PromoAnalyticsDashboard: React.FC = () => {
  const { campaigns } = usePromoCampaigns();
  const { analytics } = usePromoCampaignAnalytics();
  const [selectedPeriod, setSelectedPeriod] = useState('30');

  const activeCampaigns = campaigns.filter(c => c.status === 'active');
  const totalUsage = campaigns.reduce((sum, c) => sum + (c.usage_limit || 0), 0);

  // Mock data for charts - in real implementation, this would come from API
  const usageData = [
    { name: 'Mon', applications: 24, conversions: 18 },
    { name: 'Tue', applications: 13, conversions: 10 },
    { name: 'Wed', applications: 98, conversions: 76 },
    { name: 'Thu', applications: 39, conversions: 32 },
    { name: 'Fri', applications: 48, conversions: 38 },
    { name: 'Sat', applications: 38, conversions: 29 },
    { name: 'Sun', applications: 43, conversions: 35 },
  ];

  const revenueData = [
    { name: 'Week 1', revenue: 4000, discount: 800 },
    { name: 'Week 2', revenue: 3000, discount: 600 },
    { name: 'Week 3', revenue: 2000, discount: 400 },
    { name: 'Week 4', revenue: 2780, discount: 556 },
  ];

  const campaignPerformance = campaigns.slice(0, 5).map(campaign => ({
    name: campaign.name,
    applications: Math.floor(Math.random() * 100),
    conversions: Math.floor(Math.random() * 80),
    revenue: Math.floor(Math.random() * 5000)
  }));

  const discountTypeData = [
    { name: 'Percentage', value: campaigns.filter(c => c.discount_type === 'percentage').length, color: '#8884d8' },
    { name: 'Fixed Amount', value: campaigns.filter(c => c.discount_type === 'fixed_amount').length, color: '#82ca9d' },
    { name: 'Free Trial', value: campaigns.filter(c => c.discount_type === 'free_trial').length, color: '#ffc658' },
  ];

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Analytics Dashboard</h2>
        <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Campaigns</p>
                <p className="text-2xl font-bold">{activeCampaigns.length}</p>
                <p className="text-xs text-muted-foreground">
                  {campaigns.length} total campaigns
                </p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Applications</p>
                <p className="text-2xl font-bold">{analytics?.total_usage || 0}</p>
                <p className="text-xs text-green-600">+12% from last period</p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">${analytics?.total_revenue?.toFixed(2) || '0.00'}</p>
                <p className="text-xs text-green-600">+8% from last period</p>
              </div>
              <DollarSign className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Discounts</p>
                <p className="text-2xl font-bold">${analytics?.total_discount_given?.toFixed(2) || '0.00'}</p>
                <p className="text-xs text-orange-600">+5% from last period</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Usage Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Usage Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={usageData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="applications" fill="#8884d8" name="Applications" />
                  <Bar dataKey="conversions" fill="#82ca9d" name="Conversions" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Revenue vs Discounts */}
        <Card>
          <CardHeader>
            <CardTitle>Revenue vs Discounts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" name="Revenue" />
                  <Line type="monotone" dataKey="discount" stroke="#82ca9d" name="Discounts" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Campaign Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performing Campaigns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={campaignPerformance} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="name" type="category" width={80} />
                  <Tooltip />
                  <Bar dataKey="conversions" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Discount Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Discount Type Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={discountTypeData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {discountTypeData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Campaign Performance Table */}
      <Card>
        <CardHeader>
          <CardTitle>Campaign Performance Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Campaign</th>
                  <th className="text-right p-2">Applications</th>
                  <th className="text-right p-2">Conversions</th>
                  <th className="text-right p-2">Conversion Rate</th>
                  <th className="text-right p-2">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {campaignPerformance.map((campaign, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2 font-medium">{campaign.name}</td>
                    <td className="p-2 text-right">{campaign.applications}</td>
                    <td className="p-2 text-right">{campaign.conversions}</td>
                    <td className="p-2 text-right">
                      {((campaign.conversions / campaign.applications) * 100).toFixed(1)}%
                    </td>
                    <td className="p-2 text-right">${campaign.revenue.toFixed(2)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
