
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Search, MoreHorizontal, Edit, Trash2, Play, Pause, BarChart3 } from 'lucide-react';
import { usePromoCampaigns } from '@/hooks/usePromoCampaigns';
import { PromoCampaignForm } from './PromoCampaignForm';
import { PromoCampaignDetail } from './PromoCampaignDetail';
import type { PromoCampaign } from '@/lib/api/promo-campaigns';

export const PromoCampaignsList: React.FC = () => {
  const { campaigns, loading, updateCampaign, deleteCampaign } = usePromoCampaigns();
  const [searchTerm, setSearchTerm] = useState('');
  const [editingCampaign, setEditingCampaign] = useState<PromoCampaign | null>(null);
  const [viewingCampaign, setViewingCampaign] = useState<PromoCampaign | null>(null);

  const filteredCampaigns = campaigns.filter(campaign =>
    campaign.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    campaign.campaign_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const variants = {
      draft: 'secondary',
      active: 'default',
      paused: 'outline',
      completed: 'secondary',
      cancelled: 'destructive'
    } as const;
    
    return <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>{status}</Badge>;
  };

  const getDiscountDisplay = (type: string, value: number) => {
    if (type === 'percentage') return `${value}%`;
    if (type === 'fixed_amount') return `$${value}`;
    if (type === 'free_trial') return 'Free Trial';
    return `${value}`;
  };

  const handleStatusToggle = async (campaign: PromoCampaign) => {
    const newStatus = campaign.status === 'active' ? 'paused' : 'active';
    await updateCampaign(campaign.id, { status: newStatus });
  };

  const handleDelete = async (campaign: PromoCampaign) => {
    if (confirm(`Are you sure you want to delete the campaign "${campaign.name}"?`)) {
      await deleteCampaign(campaign.id);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Promotional Campaigns</CardTitle>
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search campaigns..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Discount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCampaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{campaign.name}</div>
                      {campaign.description && (
                        <div className="text-sm text-muted-foreground">
                          {campaign.description}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <code className="bg-muted px-2 py-1 rounded text-sm">
                      {campaign.campaign_code}
                    </code>
                  </TableCell>
                  <TableCell>
                    {getDiscountDisplay(campaign.discount_type, campaign.discount_value)}
                  </TableCell>
                  <TableCell>{getStatusBadge(campaign.status)}</TableCell>
                  <TableCell>
                    {new Date(campaign.start_date).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {campaign.end_date 
                      ? new Date(campaign.end_date).toLocaleDateString() 
                      : 'No end date'
                    }
                  </TableCell>
                  <TableCell>
                    {campaign.usage_limit ? `0/${campaign.usage_limit}` : 'Unlimited'}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setViewingCampaign(campaign)}>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => setEditingCampaign(campaign)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusToggle(campaign)}>
                          {campaign.status === 'active' ? (
                            <>
                              <Pause className="mr-2 h-4 w-4" />
                              Pause
                            </>
                          ) : (
                            <>
                              <Play className="mr-2 h-4 w-4" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(campaign)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
              {filteredCampaigns.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    No campaigns found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Edit Campaign Form */}
      {editingCampaign && (
        <PromoCampaignForm
          campaign={editingCampaign}
          open={!!editingCampaign}
          onClose={() => setEditingCampaign(null)}
        />
      )}

      {/* Campaign Detail View */}
      {viewingCampaign && (
        <PromoCampaignDetail
          campaign={viewingCampaign}
          open={!!viewingCampaign}
          onClose={() => setViewingCampaign(null)}
        />
      )}
    </>
  );
};
