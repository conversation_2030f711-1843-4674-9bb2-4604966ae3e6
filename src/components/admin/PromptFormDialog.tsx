
import React from 'react';
import { useForm } from 'react-hook-form';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { promptsApi, type PromptLibraryItem, type CreatePromptData } from '@/lib/api/prompts';
import { toast } from '@/components/ui/sonner';

interface PromptFormDialogProps {
  open: boolean;
  onClose: () => void;
  prompt?: PromptLibraryItem | null;
}

const TOOL_OPTIONS = [
  'CoverageGenerator',
  'StoryboardStudio',
  'ScriptEditor',
  'CharacterBuilder',
  'LocationBuilder'
];

const ROLE_OPTIONS = [
  'system',
  'user',
  'assistant'
];

const OUTPUT_FORMAT_OPTIONS = [
  { value: 'text', label: 'Text' },
  { value: 'list', label: 'List' },
  { value: 'table', label: 'Table' },
  { value: 'scorecard', label: 'Scorecard' },
  { value: 'json', label: 'JSON' },
  { value: 'conversation', label: 'Conversation' }
];

const PromptFormDialog: React.FC<PromptFormDialogProps> = ({ open, onClose, prompt }) => {
  const { handleError } = useErrorHandler();
  const isEdit = !!prompt;

  const form = useForm<CreatePromptData>({
    defaultValues: {
      tool_name: prompt?.tool_name || '',
      version: prompt?.version || 'v1.0',
      role: prompt?.role || 'system',
      cot_enabled: prompt?.cot_enabled ?? true,
      output_format: prompt?.output_format || 'text',
      prompt_content: prompt?.prompt_content || ''
    }
  });

  React.useEffect(() => {
    if (prompt) {
      form.reset({
        tool_name: prompt.tool_name,
        version: prompt.version,
        role: prompt.role,
        cot_enabled: prompt.cot_enabled,
        output_format: prompt.output_format,
        prompt_content: prompt.prompt_content || ''
      });
    } else {
      form.reset({
        tool_name: '',
        version: 'v1.0',
        role: 'system',
        cot_enabled: true,
        output_format: 'text',
        prompt_content: ''
      });
    }
  }, [prompt, form]);

  const onSubmit = async (data: CreatePromptData) => {
    try {
      if (isEdit && prompt) {
        const result = await promptsApi.updatePrompt({ ...data, id: prompt.id });
        if (result.success) {
          toast.success('Prompt updated successfully');
          onClose();
        } else {
          handleError(result.error, 'Failed to update prompt');
        }
      } else {
        const result = await promptsApi.createPrompt(data);
        if (result.success) {
          toast.success('Prompt created successfully');
          onClose();
        } else {
          handleError(result.error, 'Failed to create prompt');
        }
      }
    } catch (error) {
      handleError(error, isEdit ? 'Failed to update prompt' : 'Failed to create prompt');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEdit ? 'Edit Prompt' : 'Create New Prompt'}</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="tool_name"
                rules={{ required: 'Tool name is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tool Name</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a tool" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TOOL_OPTIONS.map((tool) => (
                          <SelectItem key={tool} value={tool}>
                            {tool}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="version"
                rules={{ required: 'Version is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Version</FormLabel>
                    <FormControl>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select version" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="v1.0">v1.0</SelectItem>
                          <SelectItem value="v1.1">v1.1</SelectItem>
                          <SelectItem value="v1.2">v1.2</SelectItem>
                          <SelectItem value="v2.0">v2.0</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="role"
                rules={{ required: 'Role is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ROLE_OPTIONS.map((role) => (
                          <SelectItem key={role} value={role}>
                            {role}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="output_format"
                rules={{ required: 'Output format is required' }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Output Format</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select output format" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {OUTPUT_FORMAT_OPTIONS.map((format) => (
                          <SelectItem key={format.value} value={format.value}>
                            {format.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="cot_enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Chain of Thought</FormLabel>
                    <div className="text-sm text-gray-500">
                      Enable chain of thought reasoning in the prompt
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="prompt_content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prompt Content</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the prompt content..."
                      className="min-h-[200px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit">
                {isEdit ? 'Update' : 'Create'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default PromptFormDialog;
