import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  Upload, 
  Trash2, 
  Shield, 
  Clock, 
  Database,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { backupManager } from '@/lib/backup/backupStrategy';
import { useToast } from '@/hooks/use-toast';
import { formatBytes, formatDistanceToNow } from '@/lib/utils';

interface BackupMetadata {
  id: string;
  timestamp: string;
  size: number;
  checksum: string;
  tables: string[];
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  destination: string;
  retentionDate: string;
}

interface BackupStats {
  totalBackups: number;
  totalSize: number;
  oldestBackup: string | null;
  newestBackup: string | null;
  failedBackups: number;
}

export function BackupManagement() {
  const [backups, setBackups] = useState<BackupMetadata[]>([]);
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [restoring, setRestoring] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    loadBackups();
    loadStats();
  }, []);

  const loadBackups = async () => {
    try {
      const backupList = await backupManager.listBackups();
      setBackups(backupList);
    } catch (error) {
      console.error('Failed to load backups:', error);
      toast({
        title: "Error",
        description: "Failed to load backup list",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const backupStats = await backupManager.getBackupStats();
      setStats(backupStats);
    } catch (error) {
      console.error('Failed to load backup stats:', error);
    }
  };

  const handleCreateBackup = async () => {
    setCreating(true);
    try {
      await backupManager.createBackup({
        description: 'Manual backup created from admin dashboard',
      });
      
      toast({
        title: "Success",
        description: "Backup created successfully",
      });
      
      await loadBackups();
      await loadStats();
    } catch (error) {
      console.error('Failed to create backup:', error);
      toast({
        title: "Error",
        description: "Failed to create backup",
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const handleRestoreBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore from this backup? This will overwrite current data.')) {
      return;
    }

    setRestoring(backupId);
    try {
      await backupManager.restoreBackup(backupId);
      
      toast({
        title: "Success",
        description: "Backup restored successfully",
      });
    } catch (error) {
      console.error('Failed to restore backup:', error);
      toast({
        title: "Error",
        description: "Failed to restore backup",
        variant: "destructive",
      });
    } finally {
      setRestoring(null);
    }
  };

  const handleDeleteBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      await backupManager.deleteBackup(backupId);
      
      toast({
        title: "Success",
        description: "Backup deleted successfully",
      });
      
      await loadBackups();
      await loadStats();
    } catch (error) {
      console.error('Failed to delete backup:', error);
      toast({
        title: "Error",
        description: "Failed to delete backup",
        variant: "destructive",
      });
    }
  };

  const handleVerifyBackup = async (backupId: string) => {
    try {
      const isValid = await backupManager.verifyBackup(backupId);
      
      toast({
        title: isValid ? "Success" : "Warning",
        description: isValid ? "Backup integrity verified" : "Backup integrity check failed",
        variant: isValid ? "default" : "destructive",
      });
    } catch (error) {
      console.error('Failed to verify backup:', error);
      toast({
        title: "Error",
        description: "Failed to verify backup",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'in_progress':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'default',
      failed: 'destructive',
      in_progress: 'secondary',
      pending: 'outline',
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading backup information...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Backup Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Backups</p>
                  <p className="text-2xl font-bold">{stats.totalBackups}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Download className="h-5 w-5 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Total Size</p>
                  <p className="text-2xl font-bold">{formatBytes(stats.totalSize)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="text-sm font-medium">Latest Backup</p>
                  <p className="text-sm">
                    {stats.newestBackup 
                      ? formatDistanceToNow(new Date(stats.newestBackup), { addSuffix: true })
                      : 'None'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Failed Backups</p>
                  <p className="text-2xl font-bold">{stats.failedBackups}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Backup Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Backup Management</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button 
              onClick={handleCreateBackup} 
              disabled={creating}
              className="flex items-center space-x-2"
            >
              {creating ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              <span>{creating ? 'Creating...' : 'Create Backup'}</span>
            </Button>

            <Button 
              variant="outline" 
              onClick={loadBackups}
              className="flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </Button>
          </div>

          <Alert className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Backups are automatically created daily at 2 AM and retained for 30 days. 
              Manual backups follow the same retention policy.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Backup List */}
      <Card>
        <CardHeader>
          <CardTitle>Available Backups</CardTitle>
        </CardHeader>
        <CardContent>
          {backups.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No backups available
            </div>
          ) : (
            <div className="space-y-4">
              {backups.map((backup) => (
                <div 
                  key={backup.id} 
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(backup.status)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{backup.id}</p>
                        {getStatusBadge(backup.status)}
                      </div>
                      <p className="text-sm text-gray-500">
                        {new Date(backup.timestamp).toLocaleString()} • {formatBytes(backup.size)}
                      </p>
                      <p className="text-xs text-gray-400">
                        Tables: {backup.tables.join(', ')}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleVerifyBackup(backup.id)}
                      disabled={backup.status !== 'completed'}
                    >
                      <Shield className="h-3 w-3 mr-1" />
                      Verify
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleRestoreBackup(backup.id)}
                      disabled={backup.status !== 'completed' || restoring === backup.id}
                    >
                      {restoring === backup.id ? (
                        <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                      ) : (
                        <Upload className="h-3 w-3 mr-1" />
                      )}
                      Restore
                    </Button>

                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDeleteBackup(backup.id)}
                      disabled={backup.status === 'in_progress'}
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default BackupManagement;
