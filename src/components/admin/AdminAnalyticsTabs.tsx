
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import type { UserAnalytics, SystemUsage } from '@/lib/api/admin-analytics';
import { AdminAnalyticsOverview } from './AdminAnalyticsOverview';
import { UserAnalyticsTable } from './UserAnalyticsTable';
import { SystemUsageCharts } from './SystemUsageCharts';
import PerformanceMonitoringPanel from './PerformanceMonitoringPanel';
import UserSatisfactionMetrics from './UserSatisfactionMetrics';
import AdminOverridePanel from './AdminOverridePanel';
import BulkOperationsPanel from './BulkOperationsPanel';
import UsageReportingPanel from './UsageReportingPanel';

interface AdminAnalyticsTabsProps {
  userAnalytics: UserAnalytics[];
  platformStats: any;
  systemUsage: SystemUsage | null;
}

export const AdminAnalyticsTabs: React.FC<AdminAnalyticsTabsProps> = ({
  userAnalytics,
  platformStats,
  systemUsage
}) => {
  return (
    <Tabs defaultValue="overview" className="space-y-4">
      <TabsList className="grid w-full grid-cols-8">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="users">Users</TabsTrigger>
        <TabsTrigger value="usage">Usage</TabsTrigger>
        <TabsTrigger value="performance">Performance</TabsTrigger>
        <TabsTrigger value="satisfaction">Satisfaction</TabsTrigger>
        <TabsTrigger value="admin">Admin Tools</TabsTrigger>
        <TabsTrigger value="bulk">Bulk Ops</TabsTrigger>
        <TabsTrigger value="reporting">Reporting</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="space-y-4">
        <AdminAnalyticsOverview
          platformStats={platformStats}
          systemUsage={systemUsage}
          userAnalytics={userAnalytics}
        />
      </TabsContent>

      <TabsContent value="users" className="space-y-4">
        <UserAnalyticsTable users={userAnalytics} />
      </TabsContent>

      <TabsContent value="usage" className="space-y-4">
        {systemUsage && <SystemUsageCharts usage={systemUsage} />}
      </TabsContent>

      <TabsContent value="performance" className="space-y-4">
        <PerformanceMonitoringPanel />
      </TabsContent>

      <TabsContent value="satisfaction" className="space-y-4">
        <UserSatisfactionMetrics />
      </TabsContent>

      <TabsContent value="admin" className="space-y-4">
        <AdminOverridePanel />
      </TabsContent>

      <TabsContent value="bulk" className="space-y-4">
        <BulkOperationsPanel />
      </TabsContent>

      <TabsContent value="reporting" className="space-y-4">
        <UsageReportingPanel />
      </TabsContent>
    </Tabs>
  );
};
