
import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { adminAnalyticsApi, type UserAnalytics, type PlatformStats, type SystemUsage } from '@/lib/api/admin-analytics';
import { AdminAnalyticsHeader } from './AdminAnalyticsHeader';
import { AdminAnalyticsTabs } from './AdminAnalyticsTabs';
import { AdminAnalyticsLoadingState } from './AdminAnalyticsLoadingState';

const AdminAnalyticsDashboard: React.FC = () => {
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [systemUsage, setSystemUsage] = useState<SystemUsage | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadAnalyticsData();
  }, []);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const [usersResult, statsResult, usageResult] = await Promise.all([
        adminAnalyticsApi.userAnalytics.getUserAnalytics(),
        adminAnalyticsApi.getPlatformStats(),
        adminAnalyticsApi.getSystemUsage()
      ]);

      if (usersResult.success && usersResult.data) {
        setUserAnalytics(usersResult.data.users);
      }

      if (statsResult.success && statsResult.data) {
        setPlatformStats(statsResult.data);
      }

      if (usageResult.success && usageResult.data) {
        setSystemUsage(usageResult.data);
      }

      if (!usersResult.success || !statsResult.success || !usageResult.success) {
        toast({
          title: "Warning",
          description: "Some analytics data could not be loaded",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const exportUserData = () => {
    const csvContent = [
      ['Email', 'Full Name', 'Username', 'Join Date', 'Plan', 'Organization', 'Coverage Reports', 'Scenes', 'Screenplays', 'Last Activity'].join(','),
      ...userAnalytics.map(user => [
        user.email,
        user.full_name || '',
        user.username || '',
        new Date(user.created_at).toLocaleDateString(),
        user.organization_plan,
        user.organization_name,
        user.total_coverage_reports,
        user.total_scenes,
        user.total_screenplays,
        user.last_activity ? new Date(user.last_activity).toLocaleDateString() : 'Never'
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `platform-users-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);

    toast({
      title: "Export Complete",
      description: "User data has been exported to CSV",
    });
  };

  if (loading) {
    return <AdminAnalyticsLoadingState />;
  }

  return (
    <div className="space-y-6">
      <AdminAnalyticsHeader
        onRefresh={loadAnalyticsData}
        onExport={exportUserData}
      />
      
      <AdminAnalyticsTabs
        userAnalytics={userAnalytics}
        platformStats={platformStats}
        systemUsage={systemUsage}
      />
    </div>
  );
};

export default AdminAnalyticsDashboard;
