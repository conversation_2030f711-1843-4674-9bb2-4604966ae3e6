
import React, { useState, useEffect, memo, useCallback, useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { adminAnalyticsApi, type UserAnalytics, type PlatformStats, type SystemUsage } from '@/lib/api/admin-analytics';
import { AdminAnalyticsHeader } from './AdminAnalyticsHeader';
import { AdminAnalyticsTabs } from './AdminAnalyticsTabs';
import { AdminAnalyticsLoadingState } from './AdminAnalyticsLoadingState';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

// Cache for analytics data to prevent unnecessary refetches
const analyticsCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

const AdminAnalyticsDashboard: React.FC = memo(() => {
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics[]>([]);
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [systemUsage, setSystemUsage] = useState<SystemUsage | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalUsers, setTotalUsers] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();
  const { useDeepCompareMemo } = usePerformanceOptimization();

  // Pagination settings
  const USERS_PER_PAGE = 50;
  const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // Memoized analytics data to prevent unnecessary re-renders
  const memoizedUserAnalytics = useDeepCompareMemo(
    () => userAnalytics,
    [userAnalytics]
  );

  const memoizedPlatformStats = useDeepCompareMemo(
    () => platformStats,
    [platformStats]
  );

  const memoizedSystemUsage = useDeepCompareMemo(
    () => systemUsage,
    [systemUsage]
  );

  useEffect(() => {
    loadAnalyticsData();
  }, [currentPage]);

  // Cache helper functions
  const getCachedData = (key: string) => {
    const cached = analyticsCache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    return null;
  };

  const setCachedData = (key: string, data: any, ttl: number = CACHE_TTL) => {
    analyticsCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  };

  const loadAnalyticsData = useCallback(async (forceRefresh = false) => {
    if (!forceRefresh) {
      setLoading(true);
    } else {
      setIsRefreshing(true);
    }

    try {
      // Check cache first for non-user data (platform stats and system usage don't need pagination)
      const statsKey = 'platform-stats';
      const usageKey = 'system-usage';

      let statsResult, usageResult;

      if (!forceRefresh) {
        const cachedStats = getCachedData(statsKey);
        const cachedUsage = getCachedData(usageKey);

        if (cachedStats) {
          setPlatformStats(cachedStats);
          statsResult = { success: true, data: cachedStats };
        }

        if (cachedUsage) {
          setSystemUsage(cachedUsage);
          usageResult = { success: true, data: cachedUsage };
        }
      }

      // Prepare API calls
      const apiCalls = [];

      // Always fetch users with pagination
      apiCalls.push(
        adminAnalyticsApi.userAnalytics.getUserAnalytics(
          undefined, // filters
          currentPage,
          USERS_PER_PAGE
        )
      );

      // Only fetch stats and usage if not cached or force refresh
      if (!statsResult || forceRefresh) {
        apiCalls.push(adminAnalyticsApi.getPlatformStats());
      }

      if (!usageResult || forceRefresh) {
        apiCalls.push(adminAnalyticsApi.getSystemUsage());
      }

      const results = await Promise.all(apiCalls);
      let usersResult = results[0];

      // Handle results based on what was fetched
      let resultIndex = 1;
      if (!statsResult || forceRefresh) {
        statsResult = results[resultIndex++];
      }
      if (!usageResult || forceRefresh) {
        usageResult = results[resultIndex];
      }

      // Process user analytics with pagination
      if (usersResult.success && usersResult.data) {
        setUserAnalytics(usersResult.data.users || []);
        setTotalUsers(usersResult.data.total || 0);
      }

      // Process platform stats
      if (statsResult && statsResult.success && statsResult.data) {
        setPlatformStats(statsResult.data);
        setCachedData(statsKey, statsResult.data);
      }

      // Process system usage
      if (usageResult && usageResult.success && usageResult.data) {
        setSystemUsage(usageResult.data);
        setCachedData(usageKey, usageResult.data);
      }

      // Show warning if any requests failed
      if (!usersResult.success || (statsResult && !statsResult.success) || (usageResult && !usageResult.success)) {
        toast({
          title: "Warning",
          description: "Some analytics data could not be loaded",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  }, [currentPage, toast]);

  // Memoized export function to prevent recreation
  const exportUserData = useCallback(async () => {
    try {
      // For export, we need all users, not just current page
      const allUsersResult = await adminAnalyticsApi.userAnalytics.getUserAnalytics(
        undefined, // filters
        1, // page
        totalUsers || 1000 // get all users
      );

      if (!allUsersResult.success || !allUsersResult.data) {
        throw new Error('Failed to fetch user data for export');
      }

      const allUsers = allUsersResult.data.users || [];

      const csvContent = [
        ['Email', 'Full Name', 'Username', 'Join Date', 'Plan', 'Organization', 'Coverage Reports', 'Scenes', 'Screenplays', 'Last Activity'].join(','),
        ...allUsers.map(user => [
          user.email,
          user.full_name || '',
          user.username || '',
          new Date(user.created_at).toLocaleDateString(),
          user.organization_plan,
          user.organization_name,
          user.total_coverage_reports,
          user.total_scenes,
          user.total_screenplays,
          user.last_activity ? new Date(user.last_activity).toLocaleDateString() : 'Never'
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `platform-users-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Complete",
        description: `Exported ${allUsers.length} users to CSV`,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: "Export Failed",
        description: "Unable to export user data. Please try again.",
        variant: "destructive"
      });
    }
  }, [totalUsers, toast]);

  // Memoized refresh handler
  const handleRefresh = useCallback(() => {
    loadAnalyticsData(true);
  }, [loadAnalyticsData]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Memoized pagination info
  const paginationInfo = useMemo(() => ({
    currentPage,
    totalPages: Math.ceil(totalUsers / USERS_PER_PAGE),
    totalUsers,
    usersPerPage: USERS_PER_PAGE,
    startIndex: (currentPage - 1) * USERS_PER_PAGE + 1,
    endIndex: Math.min(currentPage * USERS_PER_PAGE, totalUsers)
  }), [currentPage, totalUsers]);

  if (loading) {
    return <AdminAnalyticsLoadingState />;
  }

  return (
    <div className="space-y-6">
      <AdminAnalyticsHeader
        onRefresh={handleRefresh}
        onExport={exportUserData}
        isRefreshing={isRefreshing}
        paginationInfo={paginationInfo}
      />

      <AdminAnalyticsTabs
        userAnalytics={memoizedUserAnalytics}
        platformStats={memoizedPlatformStats}
        systemUsage={memoizedSystemUsage}
        paginationInfo={paginationInfo}
        onPageChange={handlePageChange}
      />
    </div>
  );
});

AdminAnalyticsDashboard.displayName = 'AdminAnalyticsDashboard';

export default AdminAnalyticsDashboard;
