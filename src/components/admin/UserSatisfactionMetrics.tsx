
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import { Star, MessageSquare, ThumbsUp, ThumbsDown, TrendingUp, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface SatisfactionMetric {
  period: string;
  nps: number;
  csat: number;
  responseRate: number;
  totalResponses: number;
}

interface FeedbackItem {
  id: string;
  userId: string;
  userEmail: string;
  rating: number;
  comment: string;
  category: string;
  status: 'open' | 'in-progress' | 'resolved';
  timestamp: string;
  feature: string;
}

const UserSatisfactionMetrics: React.FC = () => {
  const [metrics, setMetrics] = useState<SatisfactionMetric[]>([]);
  const [feedback, setFeedback] = useState<FeedbackItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const { toast } = useToast();

  useEffect(() => {
    loadSatisfactionData();
  }, []);

  const loadSatisfactionData = async () => {
    setLoading(true);
    try {
      // Mock satisfaction metrics
      const mockMetrics: SatisfactionMetric[] = [
        { period: 'This Week', nps: 72, csat: 4.2, responseRate: 18, totalResponses: 156 },
        { period: 'Last Week', nps: 68, csat: 4.0, responseRate: 15, totalResponses: 142 },
        { period: 'This Month', nps: 70, csat: 4.1, responseRate: 16, totalResponses: 623 },
        { period: 'Last Month', nps: 65, csat: 3.9, responseRate: 14, totalResponses: 598 }
      ];

      const mockFeedback: FeedbackItem[] = [
        {
          id: '1',
          userId: 'user-123',
          userEmail: '<EMAIL>',
          rating: 5,
          comment: 'The coverage generation feature is amazing! Saves me hours of work.',
          category: 'feature',
          status: 'open',
          timestamp: new Date().toISOString(),
          feature: 'Coverage Generator'
        },
        {
          id: '2',
          userId: 'user-456',
          userEmail: '<EMAIL>',
          rating: 2,
          comment: 'The interface is confusing and hard to navigate. Need better UX.',
          category: 'ux',
          status: 'in-progress',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          feature: 'Dashboard'
        },
        {
          id: '3',
          userId: 'user-789',
          userEmail: '<EMAIL>',
          rating: 4,
          comment: 'Great tool overall, but would love more customization options.',
          category: 'feature',
          status: 'resolved',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          feature: 'Production Tools'
        }
      ];

      setMetrics(mockMetrics);
      setFeedback(mockFeedback);
    } catch (error) {
      console.error('Error loading satisfaction data:', error);
      toast({
        title: "Error",
        description: "Failed to load satisfaction data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const updateFeedbackStatus = async (feedbackId: string, newStatus: 'open' | 'in-progress' | 'resolved') => {
    setFeedback(prev => prev.map(item => 
      item.id === feedbackId ? { ...item, status: newStatus } : item
    ));
    
    toast({
      title: "Status Updated",
      description: `Feedback marked as ${newStatus}`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'destructive';
      case 'in-progress': return 'default';
      case 'resolved': return 'secondary';
      default: return 'outline';
    }
  };

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return 'text-green-600';
    if (rating >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getNPSCategory = (nps: number) => {
    if (nps >= 70) return { label: 'Excellent', color: 'text-green-600' };
    if (nps >= 50) return { label: 'Good', color: 'text-yellow-600' };
    if (nps >= 30) return { label: 'Needs Improvement', color: 'text-orange-600' };
    return { label: 'Critical', color: 'text-red-600' };
  };

  const filteredFeedback = selectedFilter === 'all' 
    ? feedback 
    : feedback.filter(item => item.status === selectedFilter);

  const ratingDistribution = [
    { rating: '5 Stars', count: feedback.filter(f => f.rating === 5).length, fill: '#10b981' },
    { rating: '4 Stars', count: feedback.filter(f => f.rating === 4).length, fill: '#84cc16' },
    { rating: '3 Stars', count: feedback.filter(f => f.rating === 3).length, fill: '#eab308' },
    { rating: '2 Stars', count: feedback.filter(f => f.rating === 2).length, fill: '#f97316' },
    { rating: '1 Star', count: feedback.filter(f => f.rating === 1).length, fill: '#ef4444' }
  ];

  const currentNPS = metrics.length > 0 ? metrics[0].nps : 0;
  const npsCategory = getNPSCategory(currentNPS);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">User Satisfaction Metrics</h2>
        <Button onClick={loadSatisfactionData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Key Satisfaction Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Promoter Score</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${npsCategory.color}`}>
              {currentNPS}
            </div>
            <p className={`text-xs ${npsCategory.color}`}>
              {npsCategory.label}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customer Satisfaction</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.length > 0 ? metrics[0].csat.toFixed(1) : 0}/5.0
            </div>
            <p className="text-xs text-muted-foreground">
              Average rating
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.length > 0 ? metrics[0].responseRate : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Survey participation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {feedback.length}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution and Trends */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Rating Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={ratingDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ rating, count }) => count > 0 ? `${rating}: ${count}` : ''}
                >
                  {ratingDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Satisfaction Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={metrics.slice(0, 4).reverse()}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="nps" fill="#8884d8" name="NPS Score" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Feedback Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>User Feedback</CardTitle>
            <Select value={selectedFilter} onValueChange={setSelectedFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Feedback</SelectItem>
                <SelectItem value="open">Open</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="resolved">Resolved</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredFeedback.length === 0 ? (
              <p className="text-center text-muted-foreground py-8">No feedback to display</p>
            ) : (
              filteredFeedback.map((item) => (
                <div key={item.id} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge variant={getStatusColor(item.status)}>
                          {item.status}
                        </Badge>
                        <div className={`flex items-center ${getRatingColor(item.rating)}`}>
                          {Array.from({ length: item.rating }).map((_, i) => (
                            <Star key={i} className="h-4 w-4 fill-current" />
                          ))}
                          <span className="ml-1 text-sm font-medium">{item.rating}/5</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {item.feature}
                        </span>
                      </div>
                      <p className="text-sm mb-2">{item.comment}</p>
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                        <span>{item.userEmail}</span>
                        <span>{new Date(item.timestamp).toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      {item.status !== 'resolved' && (
                        <>
                          {item.status === 'open' && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => updateFeedbackStatus(item.id, 'in-progress')}
                            >
                              Start Progress
                            </Button>
                          )}
                          <Button 
                            size="sm" 
                            variant="default"
                            onClick={() => updateFeedbackStatus(item.id, 'resolved')}
                          >
                            Resolve
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserSatisfactionMetrics;
