import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  CheckCircle,
  XCircle,
  Clock,
  Users,
  Mail,
  Filter,
  Download,
  RefreshCw,
  Eye,
  MessageSquare,
  Sparkles,
  Copy,
  ExternalLink
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface BetaRequest {
  id: string;
  email: string;
  name: string;
  company?: string;
  use_case: string;
  referral_source: string;
  status: 'pending' | 'approved' | 'rejected' | 'converted';
  requested_at: string;
  approved_at?: string;
  rejected_at?: string;
  promo_code?: string;
  promo_code_used_at?: string;
  conversion_tier?: string;
  conversion_amount?: number;
  notes?: string;
  auto_approved: boolean;
}

interface BetaStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  converted: number;
  conversionRate: number;
}

export function BetaRequestsManagement() {
  const [requests, setRequests] = useState<BetaRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<BetaRequest[]>([]);
  const [selectedRequests, setSelectedRequests] = useState<string[]>([]);
  const [stats, setStats] = useState<BetaStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRequest, setSelectedRequest] = useState<BetaRequest | null>(null);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadBetaRequests();
  }, []);

  useEffect(() => {
    filterRequests();
  }, [requests, filter, searchTerm]);

  const loadBetaRequests = async () => {
    try {
      const { data, error } = await supabase
        .from('beta_requests')
        .select('*')
        .order('requested_at', { ascending: false });

      if (error) throw error;

      setRequests(data || []);
      calculateStats(data || []);
    } catch (error) {
      console.error('Failed to load beta requests:', error);
      toast({
        title: "Error",
        description: "Failed to load beta requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data: BetaRequest[]) => {
    const total = data.length;
    const pending = data.filter(r => r.status === 'pending').length;
    const approved = data.filter(r => r.status === 'approved').length;
    const rejected = data.filter(r => r.status === 'rejected').length;
    const converted = data.filter(r => r.status === 'converted').length;
    const conversionRate = approved > 0 ? (converted / approved) * 100 : 0;

    setStats({
      total,
      pending,
      approved,
      rejected,
      converted,
      conversionRate,
    });
  };

  const filterRequests = () => {
    let filtered = requests;

    // Filter by status
    if (filter !== 'all') {
      filtered = filtered.filter(request => request.status === filter);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(request =>
        request.email.toLowerCase().includes(term) ||
        request.name.toLowerCase().includes(term) ||
        request.company?.toLowerCase().includes(term) ||
        request.use_case.toLowerCase().includes(term)
      );
    }

    setFilteredRequests(filtered);
  };

  const handleApproveSelected = async () => {
    if (selectedRequests.length === 0) return;

    setBulkActionLoading(true);
    try {
      for (const requestId of selectedRequests) {
        await approveRequest(requestId);
      }
      
      toast({
        title: "Success",
        description: `Approved ${selectedRequests.length} requests`,
      });
      
      setSelectedRequests([]);
      await loadBetaRequests();
    } catch (error) {
      console.error('Bulk approval failed:', error);
      toast({
        title: "Error",
        description: "Some approvals may have failed",
        variant: "destructive",
      });
    } finally {
      setBulkActionLoading(false);
    }
  };

  const approveRequest = async (requestId: string) => {
    const { data, error } = await supabase.rpc('approve_beta_request', {
      request_id: requestId,
      approver_id: (await supabase.auth.getUser()).data.user?.id,
    });

    if (error) throw error;
    return data;
  };

  const rejectRequest = async (requestId: string, reason?: string) => {
    const { error } = await supabase.rpc('reject_beta_request', {
      request_id: requestId,
      rejector_id: (await supabase.auth.getUser()).data.user?.id,
      reason: reason || null,
    });

    if (error) throw error;
  };

  const handleRejectSelected = async () => {
    if (selectedRequests.length === 0) return;

    const reason = prompt('Rejection reason (optional):');
    
    setBulkActionLoading(true);
    try {
      for (const requestId of selectedRequests) {
        await rejectRequest(requestId, reason || undefined);
      }
      
      toast({
        title: "Success",
        description: `Rejected ${selectedRequests.length} requests`,
      });
      
      setSelectedRequests([]);
      await loadBetaRequests();
    } catch (error) {
      console.error('Bulk rejection failed:', error);
      toast({
        title: "Error",
        description: "Some rejections may have failed",
        variant: "destructive",
      });
    } finally {
      setBulkActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: 'outline' as const, icon: Clock, color: 'text-yellow-600' },
      approved: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      rejected: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      converted: { variant: 'secondary' as const, icon: Sparkles, color: 'text-purple-600' },
    };

    const config = variants[status as keyof typeof variants] || variants.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{status}</span>
      </Badge>
    );
  };

  const exportRequests = () => {
    const csvContent = [
      ['Email', 'Name', 'Company', 'Status', 'Requested At', 'Use Case', 'Referral Source'].join(','),
      ...filteredRequests.map(request => [
        request.email,
        request.name,
        request.company || '',
        request.status,
        new Date(request.requested_at).toLocaleDateString(),
        `"${request.use_case.replace(/"/g, '""')}"`,
        request.referral_source,
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `beta-requests-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading beta requests...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-gray-600">Total</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
              <div className="text-sm text-gray-600">Pending</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
              <div className="text-sm text-gray-600">Approved</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
              <div className="text-sm text-gray-600">Rejected</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">{stats.converted}</div>
              <div className="text-sm text-gray-600">Converted</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.conversionRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Conv. Rate</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Management Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Beta Access Requests</span>
          </CardTitle>
          
          {/* Filters and Actions */}
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <Select value={filter} onValueChange={setFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="converted">Converted</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Input
              placeholder="Search requests..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />

            <div className="flex space-x-2">
              <Button
                onClick={handleApproveSelected}
                disabled={selectedRequests.length === 0 || bulkActionLoading}
                size="sm"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Approve ({selectedRequests.length})
              </Button>
              
              <Button
                onClick={handleRejectSelected}
                disabled={selectedRequests.length === 0 || bulkActionLoading}
                variant="destructive"
                size="sm"
              >
                <XCircle className="h-4 w-4 mr-1" />
                Reject ({selectedRequests.length})
              </Button>

              <Button onClick={exportRequests} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>

              <Button onClick={loadBetaRequests} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedRequests.length === filteredRequests.length && filteredRequests.length > 0}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedRequests(filteredRequests.map(r => r.id));
                        } else {
                          setSelectedRequests([]);
                        }
                      }}
                    />
                  </TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Company</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Requested</TableHead>
                  <TableHead>Source</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedRequests.includes(request.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRequests([...selectedRequests, request.id]);
                          } else {
                            setSelectedRequests(selectedRequests.filter(id => id !== request.id));
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{request.name}</div>
                        <div className="text-sm text-gray-500">{request.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{request.company || '-'}</TableCell>
                    <TableCell>{getStatusBadge(request.status)}</TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDistanceToNow(new Date(request.requested_at), { addSuffix: true })}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {request.referral_source}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setSelectedRequest(request)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        
                        {request.status === 'pending' && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => approveRequest(request.id).then(() => loadBetaRequests())}
                            >
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => rejectRequest(request.id).then(() => loadBetaRequests())}
                            >
                              <XCircle className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                        
                        {request.promo_code && (
                          <Button size="sm" variant="outline">
                            <Mail className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredRequests.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No beta requests found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Request Detail Modal */}
      {selectedRequest && (
        <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Beta Request Details</DialogTitle>
            </DialogHeader>

            <div className="space-y-6">
              {/* User Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Name</label>
                  <p className="font-medium">{selectedRequest.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <div className="flex items-center space-x-2">
                    <p className="font-medium">{selectedRequest.email}</p>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => navigator.clipboard.writeText(selectedRequest.email)}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Company</label>
                  <p>{selectedRequest.company || 'Not provided'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Referral Source</label>
                  <Badge variant="outline">{selectedRequest.referral_source}</Badge>
                </div>
              </div>

              {/* Status Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedRequest.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Requested</label>
                  <p>{new Date(selectedRequest.requested_at).toLocaleString()}</p>
                </div>
                {selectedRequest.approved_at && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Approved</label>
                    <p>{new Date(selectedRequest.approved_at).toLocaleString()}</p>
                  </div>
                )}
                {selectedRequest.promo_code && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Promo Code</label>
                    <div className="flex items-center space-x-2">
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                        {selectedRequest.promo_code}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => navigator.clipboard.writeText(selectedRequest.promo_code!)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                )}
              </div>

              {/* Use Case */}
              <div>
                <label className="text-sm font-medium text-gray-500">Use Case</label>
                <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm whitespace-pre-wrap">{selectedRequest.use_case}</p>
                </div>
              </div>

              {/* Notes */}
              {selectedRequest.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Admin Notes</label>
                  <div className="mt-1 p-3 bg-yellow-50 rounded-lg">
                    <p className="text-sm">{selectedRequest.notes}</p>
                  </div>
                </div>
              )}

              {/* Actions */}
              {selectedRequest.status === 'pending' && (
                <div className="flex space-x-2 pt-4 border-t">
                  <Button
                    onClick={async () => {
                      await approveRequest(selectedRequest.id);
                      await loadBetaRequests();
                      setSelectedRequest(null);
                      toast({
                        title: "Success",
                        description: "Request approved and promo code generated",
                      });
                    }}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve Request
                  </Button>

                  <Button
                    variant="destructive"
                    onClick={async () => {
                      const reason = prompt('Rejection reason (optional):');
                      await rejectRequest(selectedRequest.id, reason || undefined);
                      await loadBetaRequests();
                      setSelectedRequest(null);
                      toast({
                        title: "Success",
                        description: "Request rejected",
                      });
                    }}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Reject Request
                  </Button>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}

export default BetaRequestsManagement;
