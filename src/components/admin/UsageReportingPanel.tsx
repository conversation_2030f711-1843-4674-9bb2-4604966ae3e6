
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Bar<PERSON>hart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { CalendarIcon, Download, FileText, DollarSign, TrendingUp, Users, Package } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UsageReport {
  period: string;
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  coverageGenerations: number;
  productionHours: number;
  storageUsed: number;
}

interface BillingData {
  organizationId: string;
  organizationName: string;
  plan: string;
  monthlyFee: number;
  usageCharges: number;
  totalBill: number;
  lastPayment: string;
  status: 'paid' | 'pending' | 'overdue';
}

const UsageReportingPanel: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<string>('month');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [usageData, setUsageData] = useState<UsageReport[]>([]);
  const [billingData, setBillingData] = useState<BillingData[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadUsageData();
  }, [selectedPeriod]);

  const loadUsageData = async () => {
    setLoading(true);
    try {
      // Mock usage data
      const mockUsageData: UsageReport[] = [
        {
          period: '2024-06',
          totalUsers: 1250,
          activeUsers: 890,
          totalRevenue: 45600,
          coverageGenerations: 3420,
          productionHours: 1280,
          storageUsed: 567
        },
        {
          period: '2024-05',
          totalUsers: 1180,
          activeUsers: 825,
          totalRevenue: 42300,
          coverageGenerations: 3120,
          productionHours: 1150,
          storageUsed: 523
        },
        {
          period: '2024-04',
          totalUsers: 1095,
          activeUsers: 756,
          totalRevenue: 38900,
          coverageGenerations: 2890,
          productionHours: 1050,
          storageUsed: 487
        }
      ];

      const mockBillingData: BillingData[] = [
        {
          organizationId: 'org-1',
          organizationName: 'Example Studios',
          plan: 'studio',
          monthlyFee: 299,
          usageCharges: 120,
          totalBill: 419,
          lastPayment: '2024-06-01',
          status: 'paid'
        },
        {
          organizationId: 'org-2',
          organizationName: 'Independent Films',
          plan: 'pro-team',
          monthlyFee: 99,
          usageCharges: 45,
          totalBill: 144,
          lastPayment: '2024-06-03',
          status: 'pending'
        },
        {
          organizationId: 'org-3',
          organizationName: 'Film Corporation',
          plan: 'enterprise',
          monthlyFee: 999,
          usageCharges: 250,
          totalBill: 1249,
          lastPayment: '2024-05-25',
          status: 'overdue'
        }
      ];

      setUsageData(mockUsageData);
      setBillingData(mockBillingData);
    } catch (error) {
      console.error('Error loading usage data:', error);
      toast({
        title: "Error",
        description: "Failed to load usage data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const exportReport = (format: 'csv' | 'pdf') => {
    // Mock export functionality
    toast({
      title: "Export Started",
      description: `Generating ${format.toUpperCase()} report...`,
    });

    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: `Usage report exported as ${format.toUpperCase()}`,
      });
    }, 2000);
  };

  const getBillingStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'secondary';
      case 'pending': return 'default';
      case 'overdue': return 'destructive';
      default: return 'outline';
    }
  };

  const planDistribution = billingData.reduce((acc, org) => {
    acc[org.plan] = (acc[org.plan] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const planChartData = Object.entries(planDistribution).map(([plan, count]) => ({
    name: plan,
    value: count,
    fill: plan === 'enterprise' ? '#8884d8' : plan === 'studio' ? '#82ca9d' : plan === 'pro-team' ? '#ffc658' : '#ff7300'
  }));

  const totalRevenue = billingData.reduce((sum, org) => sum + org.totalBill, 0);
  const overdueBills = billingData.filter(org => org.status === 'overdue').length;

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Usage Reporting & Billing</h2>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">This Week</SelectItem>
              <SelectItem value="month">This Month</SelectItem>
              <SelectItem value="quarter">This Quarter</SelectItem>
              <SelectItem value="year">This Year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => exportReport('csv')} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button onClick={() => exportReport('pdf')} variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="billing">Billing Reports</TabsTrigger>
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Key Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  Current billing cycle
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {usageData.length > 0 ? usageData[0].activeUsers.toLocaleString() : 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last 30 days
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Coverage Generations</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {usageData.length > 0 ? usageData[0].coverageGenerations.toLocaleString() : 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  This month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Overdue Payments</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{overdueBills}</div>
                <p className="text-xs text-muted-foreground">
                  Require attention
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Usage Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={usageData.slice().reverse()}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="period" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="activeUsers" stroke="#8884d8" name="Active Users" />
                    <Line type="monotone" dataKey="coverageGenerations" stroke="#82ca9d" name="Coverage Generations" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Plan Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={planChartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}`}
                    >
                      {planChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Usage Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={usageData.slice().reverse()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="coverageGenerations" fill="#8884d8" name="Coverage Generations" />
                  <Bar dataKey="productionHours" fill="#82ca9d" name="Production Hours" />
                  <Bar dataKey="storageUsed" fill="#ffc658" name="Storage (GB)" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Billing Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {billingData.map((org) => (
                  <div key={org.organizationId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium">{org.organizationName}</h4>
                        <Badge variant="outline">{org.plan}</Badge>
                        <Badge variant={getBillingStatusColor(org.status)}>
                          {org.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Last payment: {new Date(org.lastPayment).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">${org.totalBill}</div>
                      <div className="text-sm text-muted-foreground">
                        Base: ${org.monthlyFee} + Usage: ${org.usageCharges}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={usageData.slice().reverse()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Revenue']} />
                  <Bar dataKey="totalRevenue" fill="#10b981" name="Total Revenue" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Monthly Recurring Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  ${billingData.reduce((sum, org) => sum + org.monthlyFee, 0).toLocaleString()}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Base subscription fees</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Usage Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  ${billingData.reduce((sum, org) => sum + org.usageCharges, 0).toLocaleString()}
                </div>
                <p className="text-sm text-muted-foreground mt-1">Additional usage charges</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Revenue Per User</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">
                  ${Math.round(totalRevenue / billingData.length)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">ARPU this month</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UsageReportingPanel;
