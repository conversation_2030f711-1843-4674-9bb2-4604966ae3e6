import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Sparkles, 
  Copy, 
  Eye, 
  Plus,
  RefreshCw,
  Calendar,
  Users,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface PromoCode {
  id: string;
  code: string;
  discount_percentage: number;
  valid_tiers: string[];
  max_uses: number;
  current_uses: number;
  expires_at?: string;
  beta_request_id?: string;
  is_active: boolean;
  created_at: string;
  beta_request?: {
    email: string;
    name: string;
  };
}

interface PromoStats {
  total: number;
  active: number;
  used: number;
  expired: number;
  totalRevenue: number;
}

export function PromoCodesManagement() {
  const [promoCodes, setPromoCodes] = useState<PromoCode[]>([]);
  const [stats, setStats] = useState<PromoStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadPromoCodes();
  }, []);

  const loadPromoCodes = async () => {
    try {
      const { data, error } = await supabase
        .from('promo_codes')
        .select(`
          *,
          beta_request:beta_requests(email, name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setPromoCodes(data || []);
      calculateStats(data || []);
    } catch (error) {
      console.error('Failed to load promo codes:', error);
      toast({
        title: "Error",
        description: "Failed to load promo codes",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateStats = (data: PromoCode[]) => {
    const total = data.length;
    const active = data.filter(p => p.is_active && (!p.expires_at || new Date(p.expires_at) > new Date())).length;
    const used = data.filter(p => p.current_uses >= p.max_uses).length;
    const expired = data.filter(p => p.expires_at && new Date(p.expires_at) <= new Date()).length;
    
    // Estimate revenue (this would be more accurate with actual conversion data)
    const totalRevenue = data.reduce((sum, code) => {
      if (code.current_uses > 0) {
        // Estimate based on discount percentage and tier
        const avgTierPrice = code.valid_tiers.includes('pro') ? 199 : 99;
        const discountedPrice = avgTierPrice * (code.discount_percentage / 100);
        return sum + (discountedPrice * code.current_uses);
      }
      return sum;
    }, 0);

    setStats({
      total,
      active,
      used,
      expired,
      totalRevenue,
    });
  };

  const getStatusBadge = (promoCode: PromoCode) => {
    const now = new Date();
    const isExpired = promoCode.expires_at && new Date(promoCode.expires_at) <= now;
    const isUsedUp = promoCode.current_uses >= promoCode.max_uses;
    
    if (!promoCode.is_active) {
      return <Badge variant="secondary">Inactive</Badge>;
    }
    if (isExpired) {
      return <Badge variant="destructive">Expired</Badge>;
    }
    if (isUsedUp) {
      return <Badge variant="outline">Used Up</Badge>;
    }
    return <Badge variant="default">Active</Badge>;
  };

  const copyPromoCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: "Copied",
      description: "Promo code copied to clipboard",
    });
  };

  const togglePromoCodeStatus = async (id: string, isActive: boolean) => {
    try {
      const { error } = await supabase
        .from('promo_codes')
        .update({ is_active: !isActive })
        .eq('id', id);

      if (error) throw error;

      await loadPromoCodes();
      toast({
        title: "Success",
        description: `Promo code ${!isActive ? 'activated' : 'deactivated'}`,
      });
    } catch (error) {
      console.error('Failed to toggle promo code status:', error);
      toast({
        title: "Error",
        description: "Failed to update promo code status",
        variant: "destructive",
      });
    }
  };

  const filteredPromoCodes = promoCodes.filter(promoCode => {
    const matchesSearch = searchTerm === '' || 
      promoCode.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      promoCode.beta_request?.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      promoCode.beta_request?.name.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterStatus === 'all' || 
      (filterStatus === 'active' && promoCode.is_active) ||
      (filterStatus === 'inactive' && !promoCode.is_active) ||
      (filterStatus === 'used' && promoCode.current_uses >= promoCode.max_uses) ||
      (filterStatus === 'expired' && promoCode.expires_at && new Date(promoCode.expires_at) <= new Date());

    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading promo codes...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold">{stats.total}</div>
              <div className="text-sm text-gray-600">Total Codes</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <div className="text-sm text-gray-600">Active</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.used}</div>
              <div className="text-sm text-gray-600">Used</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-red-600">{stats.expired}</div>
              <div className="text-sm text-gray-600">Expired</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">${stats.totalRevenue.toFixed(0)}</div>
              <div className="text-sm text-gray-600">Est. Revenue</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Management Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5" />
            <span>Promo Codes Management</span>
          </CardTitle>
          
          <div className="flex flex-wrap gap-4 items-center">
            <Input
              placeholder="Search codes or users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64"
            />

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="used">Used</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={loadPromoCodes} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>

            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-1" />
                  Create Code
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create Custom Promo Code</DialogTitle>
                </DialogHeader>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Custom promo code creation coming soon. Currently, codes are auto-generated when beta requests are approved.
                  </AlertDescription>
                </Alert>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>

        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Code</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Discount</TableHead>
                  <TableHead>Valid Tiers</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPromoCodes.map((promoCode) => (
                  <TableRow key={promoCode.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">
                          {promoCode.code}
                        </code>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => copyPromoCode(promoCode.code)}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      {promoCode.beta_request ? (
                        <div>
                          <div className="font-medium">{promoCode.beta_request.name}</div>
                          <div className="text-sm text-gray-500">{promoCode.beta_request.email}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">Manual</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {promoCode.discount_percentage}% off
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        {promoCode.valid_tiers.map(tier => (
                          <Badge key={tier} variant="outline" className="text-xs">
                            {tier}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {promoCode.current_uses} / {promoCode.max_uses}
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(promoCode)}</TableCell>
                    <TableCell>
                      {promoCode.expires_at ? (
                        <div className="text-sm">
                          {formatDistanceToNow(new Date(promoCode.expires_at), { addSuffix: true })}
                        </div>
                      ) : (
                        <span className="text-gray-400">Never</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => togglePromoCodeStatus(promoCode.id, promoCode.is_active)}
                        >
                          {promoCode.is_active ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredPromoCodes.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No promo codes found matching your criteria.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default PromoCodesManagement;
