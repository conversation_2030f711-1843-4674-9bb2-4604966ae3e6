
import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search } from 'lucide-react';

interface UserAnalyticsFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  planFilter: string;
  onPlanFilterChange: (value: string) => void;
  sortBy: 'created_at' | 'last_activity' | 'reports';
  onSortChange: (value: 'created_at' | 'last_activity' | 'reports') => void;
}

export const UserAnalyticsFilters: React.FC<UserAnalyticsFiltersProps> = ({
  searchTerm,
  onSearchChange,
  planFilter,
  onPlanFilterChange,
  sortBy,
  onSortChange
}) => {
  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search by email, name, or username..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
      </div>
      
      <Select value={planFilter} onValueChange={onPlanFilterChange}>
        <SelectTrigger className="w-[140px]">
          <SelectValue placeholder="Filter by plan" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Plans</SelectItem>
          <SelectItem value="starter">Starter</SelectItem>
          <SelectItem value="pro-solo">Pro Solo</SelectItem>
          <SelectItem value="pro-team">Pro Team</SelectItem>
          <SelectItem value="studio">Studio</SelectItem>
          <SelectItem value="enterprise">Enterprise</SelectItem>
        </SelectContent>
      </Select>
      
      <Select value={sortBy} onValueChange={onSortChange}>
        <SelectTrigger className="w-[140px]">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="created_at">Join Date</SelectItem>
          <SelectItem value="last_activity">Last Activity</SelectItem>
          <SelectItem value="reports">Reports Count</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};
