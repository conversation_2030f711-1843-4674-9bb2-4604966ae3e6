import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  AlertTriangle, 
  Clock, 
  Users, 
  FileText,
  CheckCircle,
  Phone,
  Server,
  Database,
  Globe
} from 'lucide-react';

export function DisasterRecovery() {
  const [activeIncident, setActiveIncident] = useState<string | null>(null);

  const recoveryPlans = [
    {
      id: 'database-failure',
      name: 'Database Failure Recovery',
      priority: 1,
      rto: 60, // minutes
      rpo: 15, // minutes
      description: 'Complete database failure or corruption',
      status: 'ready',
      lastTested: '2024-01-15',
    },
    {
      id: 'application-failure',
      name: 'Application Server Failure',
      priority: 2,
      rto: 30,
      rpo: 5,
      description: 'Frontend application or API failure',
      status: 'ready',
      lastTested: '2024-01-10',
    },
    {
      id: 'security-breach',
      name: 'Security Incident Response',
      priority: 1,
      rto: 15,
      rpo: 0,
      description: 'Data breach or security compromise',
      status: 'ready',
      lastTested: '2024-01-05',
    },
  ];

  const emergencyContacts = [
    {
      role: 'Technical Lead',
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '******-0101',
      primary: true,
    },
    {
      role: 'System Administrator',
      name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '******-0102',
      primary: false,
    },
    {
      role: 'Management',
      name: 'Bob Johnson',
      email: '<EMAIL>',
      phone: '******-0103',
      primary: false,
    },
  ];

  const systemDependencies = [
    {
      name: 'Supabase',
      type: 'Database & Auth',
      status: 'operational',
      criticality: 'critical',
      fallback: 'Local backup restore',
    },
    {
      name: 'Vercel',
      type: 'Hosting',
      status: 'operational',
      criticality: 'critical',
      fallback: 'Alternative hosting provider',
    },
    {
      name: 'OpenAI',
      type: 'AI Services',
      status: 'operational',
      criticality: 'high',
      fallback: 'Disable AI features temporarily',
    },
    {
      name: 'Stripe',
      type: 'Payments',
      status: 'operational',
      criticality: 'high',
      fallback: 'Manual payment processing',
    },
  ];

  const getPriorityBadge = (priority: number) => {
    const variants = {
      1: { variant: 'destructive' as const, label: 'Critical' },
      2: { variant: 'default' as const, label: 'High' },
      3: { variant: 'secondary' as const, label: 'Medium' },
    };
    
    const config = variants[priority as keyof typeof variants] || variants[3];
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      operational: { variant: 'default' as const, color: 'text-green-600' },
      degraded: { variant: 'secondary' as const, color: 'text-yellow-600' },
      down: { variant: 'destructive' as const, color: 'text-red-600' },
    };
    
    const config = variants[status as keyof typeof variants] || variants.operational;
    return <Badge variant={config.variant}>{status}</Badge>;
  };

  const getCriticalityBadge = (criticality: string) => {
    const variants = {
      critical: 'destructive' as const,
      high: 'default' as const,
      medium: 'secondary' as const,
      low: 'outline' as const,
    };
    
    return <Badge variant={variants[criticality as keyof typeof variants] || 'outline'}>{criticality}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Emergency Alert */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Emergency Hotline:</strong> ******-EMERGENCY (24/7 support)
          <br />
          <strong>Incident Email:</strong> <EMAIL>
        </AlertDescription>
      </Alert>

      <Tabs defaultValue="plans" className="space-y-4">
        <TabsList>
          <TabsTrigger value="plans">Recovery Plans</TabsTrigger>
          <TabsTrigger value="contacts">Emergency Contacts</TabsTrigger>
          <TabsTrigger value="dependencies">System Status</TabsTrigger>
          <TabsTrigger value="procedures">Procedures</TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Disaster Recovery Plans</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recoveryPlans.map((plan) => (
                  <div key={plan.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold">{plan.name}</h3>
                        {getPriorityBadge(plan.priority)}
                      </div>
                      <Badge variant="outline">{plan.status}</Badge>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4 text-blue-500" />
                        <span>RTO: {plan.rto}m</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Database className="h-4 w-4 text-green-500" />
                        <span>RPO: {plan.rpo}m</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <CheckCircle className="h-4 w-4 text-purple-500" />
                        <span>Last tested: {plan.lastTested}</span>
                      </div>
                    </div>
                    
                    <div className="mt-3 flex space-x-2">
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => setActiveIncident(plan.id)}
                      >
                        Activate Plan
                      </Button>
                      <Button size="sm" variant="ghost">
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Emergency Contacts</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {emergencyContacts.map((contact, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{contact.role}</h3>
                      {contact.primary && <Badge>Primary</Badge>}
                    </div>
                    
                    <div className="space-y-1 text-sm">
                      <p><strong>Name:</strong> {contact.name}</p>
                      <div className="flex items-center space-x-1">
                        <Phone className="h-3 w-3" />
                        <span>{contact.phone}</span>
                      </div>
                      <p><strong>Email:</strong> {contact.email}</p>
                    </div>
                    
                    <div className="mt-3 flex space-x-2">
                      <Button size="sm" variant="outline">
                        Call
                      </Button>
                      <Button size="sm" variant="ghost">
                        Email
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dependencies" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Server className="h-5 w-5" />
                <span>System Dependencies</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {systemDependencies.map((dep, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold">{dep.name}</h3>
                        {getStatusBadge(dep.status)}
                      </div>
                      {getCriticalityBadge(dep.criticality)}
                    </div>
                    
                    <div className="space-y-1 text-sm">
                      <p><strong>Type:</strong> {dep.type}</p>
                      <p><strong>Fallback:</strong> {dep.fallback}</p>
                    </div>
                    
                    <div className="mt-3">
                      <Button size="sm" variant="outline">
                        <Globe className="h-3 w-3 mr-1" />
                        Check Status
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="procedures" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Recovery Procedures</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold mb-3">Database Failure Recovery</h3>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Assess the scope of the database failure</li>
                    <li>Notify stakeholders using the emergency contact list</li>
                    <li>Identify the most recent valid backup</li>
                    <li>Verify backup integrity using the backup management system</li>
                    <li>Restore database from the verified backup</li>
                    <li>Verify data integrity post-restore</li>
                    <li>Update DNS/routing if necessary</li>
                    <li>Notify users of service restoration</li>
                    <li>Conduct post-incident review</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Application Failure Recovery</h3>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Identify the failing component (frontend/API/services)</li>
                    <li>Check system logs and error monitoring</li>
                    <li>Attempt automatic restart/redeploy</li>
                    <li>If automatic recovery fails, escalate to technical team</li>
                    <li>Implement manual failover if available</li>
                    <li>Communicate status to users via status page</li>
                    <li>Monitor system stability post-recovery</li>
                  </ol>
                </div>

                <div>
                  <h3 className="font-semibold mb-3">Security Incident Response</h3>
                  <ol className="list-decimal list-inside space-y-2 text-sm">
                    <li>Immediately isolate affected systems</li>
                    <li>Notify security team and management</li>
                    <li>Preserve evidence for investigation</li>
                    <li>Assess scope of potential data exposure</li>
                    <li>Implement containment measures</li>
                    <li>Notify relevant authorities if required</li>
                    <li>Communicate with affected users</li>
                    <li>Conduct thorough security audit</li>
                    <li>Implement additional security measures</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Active Incident Alert */}
      {activeIncident && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Active Incident:</strong> Recovery plan "{activeIncident}" has been activated.
            <Button 
              size="sm" 
              variant="outline" 
              className="ml-2"
              onClick={() => setActiveIncident(null)}
            >
              Resolve
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}

export default DisasterRecovery;
