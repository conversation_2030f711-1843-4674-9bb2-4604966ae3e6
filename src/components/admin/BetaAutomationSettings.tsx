import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Zap, 
  TrendingUp, 
  CheckCircle, 
  XCircle,
  Flag,
  RefreshCw,
  AlertTriangle,
  BarChart3
} from 'lucide-react';
import { betaAutomationService } from '@/lib/automation/betaAutomation';
import { emailService } from '@/lib/email/emailService';
import { useToast } from '@/hooks/use-toast';

interface AutomationStats {
  totalProcessed: number;
  autoApproved: number;
  autoRejected: number;
  flaggedForReview: number;
  automationRate: number;
}

interface EmailStats {
  totalSent: number;
  approvalsSent: number;
  rejectionsSent: number;
  adminNotificationsSent: number;
  successRate: number;
}

export function BetaAutomationSettings() {
  const [automationEnabled, setAutomationEnabled] = useState(false);
  const [automationStats, setAutomationStats] = useState<AutomationStats | null>(null);
  const [emailStats, setEmailStats] = useState<EmailStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setLoading(true);
      
      // Load automation stats
      const autoStats = await betaAutomationService.getAutomationStats();
      setAutomationStats(autoStats);
      
      // Load email stats
      const emailStatsData = await emailService.getEmailStats();
      setEmailStats(emailStatsData);
      
      // Check if automation is enabled (this would need to be implemented)
      // For now, we'll assume it's disabled by default
      setAutomationEnabled(false);
      
    } catch (error) {
      console.error('Failed to load automation settings:', error);
      toast({
        title: "Error",
        description: "Failed to load automation settings",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleToggleAutomation = async (enabled: boolean) => {
    setUpdating(true);
    try {
      const success = await betaAutomationService.updateAutomationSettings(enabled);
      
      if (success) {
        setAutomationEnabled(enabled);
        toast({
          title: "Success",
          description: `Automation ${enabled ? 'enabled' : 'disabled'}`,
        });
      } else {
        throw new Error('Failed to update automation settings');
      }
    } catch (error) {
      console.error('Failed to toggle automation:', error);
      toast({
        title: "Error",
        description: "Failed to update automation settings",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const testEmailConfiguration = async () => {
    try {
      const result = await emailService.testEmailConfiguration();
      
      if (result.success) {
        toast({
          title: "Success",
          description: "Test email sent successfully",
        });
      } else {
        throw new Error(result.error || 'Email test failed');
      }
    } catch (error) {
      console.error('Email test failed:', error);
      toast({
        title: "Error",
        description: "Email test failed",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            Loading automation settings...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Automation Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="h-5 w-5" />
            <span>Beta Request Automation</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Automatic Processing</h3>
              <p className="text-sm text-gray-600">
                Automatically approve, reject, or flag beta requests based on predefined rules
              </p>
            </div>
            <Switch
              checked={automationEnabled}
              onCheckedChange={handleToggleAutomation}
              disabled={updating}
            />
          </div>

          {automationEnabled && (
            <Alert className="mt-4">
              <Zap className="h-4 w-4" />
              <AlertDescription>
                Automation is active. New beta requests will be processed automatically based on your rules.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Tabs defaultValue="stats" className="space-y-4">
        <TabsList>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
          <TabsTrigger value="rules">Automation Rules</TabsTrigger>
          <TabsTrigger value="email">Email Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="stats" className="space-y-4">
          {/* Automation Statistics */}
          {automationStats && (
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold">{automationStats.totalProcessed}</div>
                  <div className="text-sm text-gray-600">Total Processed</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">{automationStats.autoApproved}</div>
                  <div className="text-sm text-gray-600">Auto Approved</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-red-600">{automationStats.autoRejected}</div>
                  <div className="text-sm text-gray-600">Auto Rejected</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-yellow-600">{automationStats.flaggedForReview}</div>
                  <div className="text-sm text-gray-600">Flagged</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">{automationStats.automationRate.toFixed(1)}%</div>
                  <div className="text-sm text-gray-600">Automation Rate</div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Email Statistics */}
          {emailStats && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Email Statistics</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{emailStats.totalSent}</div>
                    <div className="text-sm text-gray-600">Total Sent</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{emailStats.approvalsSent}</div>
                    <div className="text-sm text-gray-600">Approvals</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{emailStats.rejectionsSent}</div>
                    <div className="text-sm text-gray-600">Rejections</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{emailStats.successRate.toFixed(1)}%</div>
                    <div className="text-sm text-gray-600">Success Rate</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rules" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automation Rules</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">Auto-approve detailed requests</div>
                    <div className="text-sm text-gray-600">
                      Use case > 100 characters, non-disposable email
                    </div>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">Auto-approve company requests</div>
                    <div className="text-sm text-gray-600">
                      Has company name, use case > 50 characters
                    </div>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <div className="font-medium">Auto-reject spam patterns</div>
                    <div className="text-sm text-gray-600">
                      Use case < 10 characters
                    </div>
                  </div>
                  <Badge variant="destructive">Active</Badge>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-red-50 rounded-lg">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <div>
                    <div className="font-medium">Auto-reject disposable emails</div>
                    <div className="text-sm text-gray-600">
                      Known disposable email providers
                    </div>
                  </div>
                  <Badge variant="destructive">Active</Badge>
                </div>

                <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                  <Flag className="h-5 w-5 text-yellow-600" />
                  <div>
                    <div className="font-medium">Flag suspicious requests</div>
                    <div className="text-sm text-gray-600">
                      Contains "test" in use case
                    </div>
                  </div>
                  <Badge variant="secondary">Active</Badge>
                </div>
              </div>

              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Custom rule configuration coming soon. Current rules are based on best practices for beta access management.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Email Service</h3>
                    <p className="text-sm text-gray-600">
                      Automated emails for approvals, rejections, and notifications
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">From Address:</span>
                    <div className="text-gray-600">ScriptGenius &lt;<EMAIL>&gt;</div>
                  </div>
                  <div>
                    <span className="font-medium">Admin Email:</span>
                    <div className="text-gray-600"><EMAIL></div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button onClick={testEmailConfiguration} variant="outline">
                    Test Email Configuration
                  </Button>
                  <Button onClick={loadSettings} variant="outline">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Stats
                  </Button>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Email templates are automatically generated. Custom templates and advanced email configuration coming soon.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default BetaAutomationSettings;
