
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { type PromptLibraryItem } from '@/lib/api/prompts';

interface PromptDuplicateDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (newVersion: string) => void;
  originalPrompt: PromptLibraryItem | null;
}

const VERSION_OPTIONS = ['v1.0', 'v1.1', 'v1.2', 'v2.0', 'v2.1', 'v3.0'];

const PromptDuplicateDialog: React.FC<PromptDuplicateDialogProps> = ({
  open,
  onClose,
  onConfirm,
  originalPrompt
}) => {
  const [newVersion, setNewVersion] = useState('');

  const handleConfirm = () => {
    if (newVersion) {
      onConfirm(newVersion);
      setNewVersion('');
    }
  };

  const handleClose = () => {
    setNewVersion('');
    onClose();
  };

  const availableVersions = VERSION_OPTIONS.filter(
    version => version !== originalPrompt?.version
  );

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Duplicate Prompt</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            Create a duplicate of "{originalPrompt?.tool_name}" with a new version.
          </p>
          
          <div className="space-y-2">
            <Label htmlFor="version">New Version</Label>
            <Select value={newVersion} onValueChange={setNewVersion}>
              <SelectTrigger>
                <SelectValue placeholder="Select a version" />
              </SelectTrigger>
              <SelectContent>
                {availableVersions.map((version) => (
                  <SelectItem key={version} value={version}>
                    {version}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm} disabled={!newVersion}>
            Duplicate
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PromptDuplicateDialog;
