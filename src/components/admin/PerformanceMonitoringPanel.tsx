
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import { AlertTriangle, CheckCircle, Clock, Zap, TrendingUp, TrendingDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface PerformanceMetric {
  timestamp: string;
  responseTime: number;
  errorRate: number;
  throughput: number;
  cpuUsage: number;
  memoryUsage: number;
}

interface ErrorLog {
  id: string;
  timestamp: string;
  errorType: string;
  message: string;
  userId?: string;
  stackTrace?: string;
  resolved: boolean;
}

const PerformanceMonitoringPanel: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [alertThresholds, setAlertThresholds] = useState({
    responseTime: 2000,
    errorRate: 5,
    cpuUsage: 80,
    memoryUsage: 85
  });
  const { toast } = useToast();

  useEffect(() => {
    loadPerformanceData();
  }, []);

  const loadPerformanceData = async () => {
    setLoading(true);
    try {
      // Simulate performance data - in production this would come from monitoring services
      const mockMetrics: PerformanceMetric[] = Array.from({ length: 24 }, (_, i) => ({
        timestamp: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
        responseTime: Math.random() * 1000 + 200,
        errorRate: Math.random() * 10,
        throughput: Math.random() * 100 + 50,
        cpuUsage: Math.random() * 30 + 40,
        memoryUsage: Math.random() * 20 + 60
      }));

      const mockErrors: ErrorLog[] = [
        {
          id: '1',
          timestamp: new Date().toISOString(),
          errorType: 'Database Connection',
          message: 'Connection timeout to database',
          userId: 'user-123',
          resolved: false
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          errorType: 'API Rate Limit',
          message: 'Rate limit exceeded for OpenAI API',
          resolved: true
        }
      ];

      setMetrics(mockMetrics);
      setErrors(mockErrors);
    } catch (error) {
      console.error('Error loading performance data:', error);
      toast({
        title: "Error",
        description: "Failed to load performance data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getCurrentStatus = () => {
    if (metrics.length === 0) return { status: 'unknown', color: 'text-gray-500' };
    
    const latest = metrics[metrics.length - 1];
    const activeErrors = errors.filter(e => !e.resolved).length;
    
    if (activeErrors > 0 || latest.errorRate > alertThresholds.errorRate) {
      return { status: 'critical', color: 'text-red-600' };
    }
    
    if (latest.responseTime > alertThresholds.responseTime || latest.cpuUsage > alertThresholds.cpuUsage) {
      return { status: 'warning', color: 'text-yellow-600' };
    }
    
    return { status: 'healthy', color: 'text-green-600' };
  };

  const systemStatus = getCurrentStatus();
  const activeErrors = errors.filter(e => !e.resolved);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Performance Monitoring</h2>
        <div className="flex items-center space-x-2">
          <div className={`flex items-center space-x-1 ${systemStatus.color}`}>
            {systemStatus.status === 'healthy' && <CheckCircle className="h-5 w-5" />}
            {systemStatus.status === 'warning' && <AlertTriangle className="h-5 w-5" />}
            {systemStatus.status === 'critical' && <AlertTriangle className="h-5 w-5" />}
            <span className="font-medium capitalize">{systemStatus.status}</span>
          </div>
          <Button onClick={loadPerformanceData} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.length > 0 ? Math.round(metrics[metrics.length - 1].responseTime) : 0}ms
            </div>
            <p className="text-xs text-muted-foreground">
              Target: &lt;{alertThresholds.responseTime}ms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.length > 0 ? metrics[metrics.length - 1].errorRate.toFixed(1) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Target: &lt;{alertThresholds.errorRate}%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Errors</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {activeErrors.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Throughput</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {metrics.length > 0 ? Math.round(metrics[metrics.length - 1].throughput) : 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Requests per minute
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList>
          <TabsTrigger value="performance">Performance Trends</TabsTrigger>
          <TabsTrigger value="errors">Error Monitoring</TabsTrigger>
          <TabsTrigger value="system">System Resources</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Response Time & Error Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: any, name: string) => [
                      name === 'responseTime' ? `${Math.round(value)}ms` : `${value.toFixed(1)}%`,
                      name === 'responseTime' ? 'Response Time' : 'Error Rate'
                    ]}
                  />
                  <Line yAxisId="left" type="monotone" dataKey="responseTime" stroke="#8884d8" name="responseTime" />
                  <Line yAxisId="right" type="monotone" dataKey="errorRate" stroke="#82ca9d" name="errorRate" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="errors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Errors</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {errors.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">No errors to display</p>
                ) : (
                  errors.map((error) => (
                    <div key={error.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge variant={error.resolved ? "secondary" : "destructive"}>
                            {error.errorType}
                          </Badge>
                          <span className="text-sm text-muted-foreground">
                            {new Date(error.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <p className="text-sm">{error.message}</p>
                        {error.userId && (
                          <p className="text-xs text-muted-foreground mt-1">User: {error.userId}</p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        {error.resolved ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <Button size="sm" variant="outline">
                            Resolve
                          </Button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Resource Usage</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                  />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    labelFormatter={(value) => new Date(value).toLocaleString()}
                    formatter={(value: any, name: string) => [`${Math.round(value)}%`, name === 'cpuUsage' ? 'CPU' : 'Memory']}
                  />
                  <Area type="monotone" dataKey="cpuUsage" stackId="1" stroke="#8884d8" fill="#8884d8" name="cpuUsage" />
                  <Area type="monotone" dataKey="memoryUsage" stackId="2" stroke="#82ca9d" fill="#82ca9d" name="memoryUsage" />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PerformanceMonitoringPanel;
