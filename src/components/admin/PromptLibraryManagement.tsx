
import React, { useState } from 'react';
import { Plus, <PERSON>cil, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { usePrompts } from '@/hooks/usePrompts';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { promptsApi, type PromptLibraryItem } from '@/lib/api/prompts';
import PromptFormDialog from './PromptFormDialog';
import PromptDeleteDialog from './PromptDeleteDialog';
import PromptDuplicateDialog from './PromptDuplicateDialog';
import { toast } from '@/components/ui/sonner';

const PromptLibraryManagement: React.FC = () => {
  const { prompts, loading, refreshPrompts } = usePrompts();
  const { handleError } = useErrorHandler();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState(false);
  const [isDuplicateOpen, setIsDuplicateOpen] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState<PromptLibraryItem | null>(null);

  const handleEdit = (prompt: PromptLibraryItem) => {
    setSelectedPrompt(prompt);
    setIsFormOpen(true);
  };

  const handleDelete = (prompt: PromptLibraryItem) => {
    setSelectedPrompt(prompt);
    setIsDeleteOpen(true);
  };

  const handleDuplicate = (prompt: PromptLibraryItem) => {
    setSelectedPrompt(prompt);
    setIsDuplicateOpen(true);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedPrompt(null);
    refreshPrompts();
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPrompt) return;

    try {
      const result = await promptsApi.deletePrompt(selectedPrompt.id);
      if (result.success) {
        toast.success('Prompt deleted successfully');
        refreshPrompts();
      } else {
        handleError(result.error, 'Failed to delete prompt');
      }
    } catch (error) {
      handleError(error, 'Failed to delete prompt');
    }

    setIsDeleteOpen(false);
    setSelectedPrompt(null);
  };

  const handleDuplicateConfirm = async (newVersion: string) => {
    if (!selectedPrompt) return;

    try {
      const result = await promptsApi.duplicatePrompt(selectedPrompt.id, newVersion);
      if (result.success) {
        toast.success('Prompt duplicated successfully');
        refreshPrompts();
      } else {
        handleError(result.error, 'Failed to duplicate prompt');
      }
    } catch (error) {
      handleError(error, 'Failed to duplicate prompt');
    }

    setIsDuplicateOpen(false);
    setSelectedPrompt(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOutputFormatColor = (format: string) => {
    const colors = {
      text: 'bg-blue-100 text-blue-800',
      list: 'bg-green-100 text-green-800',
      table: 'bg-purple-100 text-purple-800',
      scorecard: 'bg-orange-100 text-orange-800',
      json: 'bg-gray-100 text-gray-800',
      conversation: 'bg-pink-100 text-pink-800'
    };
    return colors[format as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-lg">Loading prompts...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Prompt Library</h2>
          <p className="text-gray-600">Manage AI prompts for different tools and roles</p>
        </div>
        <Button onClick={() => setIsFormOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Prompt
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Prompts ({prompts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {prompts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No prompts found</p>
              <Button onClick={() => setIsFormOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create your first prompt
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tool</TableHead>
                  <TableHead>Version</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>CoT</TableHead>
                  <TableHead>Output Format</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {prompts.map((prompt) => (
                  <TableRow key={prompt.id}>
                    <TableCell className="font-medium">{prompt.tool_name}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{prompt.version}</Badge>
                    </TableCell>
                    <TableCell>{prompt.role}</TableCell>
                    <TableCell>
                      <Badge variant={prompt.cot_enabled ? "default" : "secondary"}>
                        {prompt.cot_enabled ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getOutputFormatColor(prompt.output_format)}>
                        {prompt.output_format}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(prompt.last_updated)}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(prompt)}
                        >
                          <Pencil className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDuplicate(prompt)}
                        >
                          Duplicate
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(prompt)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      <PromptFormDialog
        open={isFormOpen}
        onClose={handleFormClose}
        prompt={selectedPrompt}
      />

      <PromptDeleteDialog
        open={isDeleteOpen}
        onClose={() => setIsDeleteOpen(false)}
        onConfirm={handleDeleteConfirm}
        promptName={selectedPrompt?.tool_name || ''}
      />

      <PromptDuplicateDialog
        open={isDuplicateOpen}
        onClose={() => setIsDuplicateOpen(false)}
        onConfirm={handleDuplicateConfirm}
        originalPrompt={selectedPrompt}
      />
    </div>
  );
};

export default PromptLibraryManagement;
