
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Shield, User, CreditCard, Settings, AlertTriangle, CheckCircle, <PERSON> } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UserAccount {
  id: string;
  email: string;
  name: string;
  plan: string;
  status: 'active' | 'suspended' | 'pending';
  lastActive: string;
  totalSpent: number;
  supportTickets: number;
}

interface OverrideAction {
  id: string;
  type: 'plan_upgrade' | 'credit_adjustment' | 'account_unlock' | 'feature_enable';
  userId: string;
  userEmail: string;
  reason: string;
  performedBy: string;
  timestamp: string;
  status: 'pending' | 'approved' | 'rejected';
}

const AdminOverridePanel: React.FC = () => {
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [overrideType, setOverrideType] = useState<string>('');
  const [overrideReason, setOverrideReason] = useState('');
  const [overrideValue, setOverrideValue] = useState('');
  const [recentActions, setRecentActions] = useState<OverrideAction[]>([]);
  const { toast } = useToast();

  // Mock user data
  const users: UserAccount[] = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'John Producer',
      plan: 'pro-team',
      status: 'active',
      lastActive: '2024-06-15T10:30:00Z',
      totalSpent: 299,
      supportTickets: 2
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      name: 'Jane Director',
      plan: 'studio',
      status: 'suspended',
      lastActive: '2024-06-14T15:45:00Z',
      totalSpent: 1299,
      supportTickets: 5
    }
  ];

  const mockActions: OverrideAction[] = [
    {
      id: 'action-1',
      type: 'plan_upgrade',
      userId: 'user-1',
      userEmail: '<EMAIL>',
      reason: 'Customer support escalation - billing issue resolved',
      performedBy: '<EMAIL>',
      timestamp: new Date().toISOString(),
      status: 'approved'
    },
    {
      id: 'action-2',
      type: 'account_unlock',
      userId: 'user-2',
      userEmail: '<EMAIL>',
      reason: 'Security verification completed',
      performedBy: '<EMAIL>',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: 'pending'
    }
  ];

  React.useEffect(() => {
    setRecentActions(mockActions);
  }, []);

  const handleOverrideSubmit = async () => {
    if (!selectedUser || !overrideType || !overrideReason) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    const selectedUserData = users.find(u => u.id === selectedUser);
    if (!selectedUserData) return;

    const newAction: OverrideAction = {
      id: `action-${Date.now()}`,
      type: overrideType as any,
      userId: selectedUser,
      userEmail: selectedUserData.email,
      reason: overrideReason,
      performedBy: '<EMAIL>',
      timestamp: new Date().toISOString(),
      status: 'pending'
    };

    setRecentActions(prev => [newAction, ...prev]);

    // Reset form
    setSelectedUser('');
    setOverrideType('');
    setOverrideReason('');
    setOverrideValue('');

    toast({
      title: "Override Submitted",
      description: "Administrative override has been submitted for review",
    });
  };

  const approveAction = (actionId: string) => {
    setRecentActions(prev => prev.map(action => 
      action.id === actionId ? { ...action, status: 'approved' as const } : action
    ));
    
    toast({
      title: "Action Approved",
      description: "Override action has been approved and executed",
    });
  };

  const rejectAction = (actionId: string) => {
    setRecentActions(prev => prev.map(action => 
      action.id === actionId ? { ...action, status: 'rejected' as const } : action
    ));
    
    toast({
      title: "Action Rejected",
      description: "Override action has been rejected",
      variant: "destructive"
    });
  };

  const getActionTypeLabel = (type: string) => {
    switch (type) {
      case 'plan_upgrade': return 'Plan Upgrade';
      case 'credit_adjustment': return 'Credit Adjustment';
      case 'account_unlock': return 'Account Unlock';
      case 'feature_enable': return 'Feature Enable';
      default: return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'secondary';
      case 'rejected': return 'destructive';
      case 'pending': return 'default';
      default: return 'outline';
    }
  };

  const filteredUsers = users.filter(user => 
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Shield className="h-6 w-6 text-red-600" />
        <h2 className="text-2xl font-bold">Admin Override Panel</h2>
        <Badge variant="destructive">High Privilege</Badge>
      </div>

      <Tabs defaultValue="override" className="space-y-4">
        <TabsList>
          <TabsTrigger value="override">Create Override</TabsTrigger>
          <TabsTrigger value="history">Override History</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        <TabsContent value="override" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
                Administrative Override
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="user-search">Search User</Label>
                  <Input
                    id="user-search"
                    placeholder="Search by email or name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                  {searchQuery && (
                    <div className="mt-2 space-y-1">
                      {filteredUsers.map(user => (
                        <div 
                          key={user.id}
                          className={`p-2 border rounded cursor-pointer hover:bg-gray-50 ${
                            selectedUser === user.id ? 'border-primary bg-primary/5' : ''
                          }`}
                          onClick={() => setSelectedUser(user.id)}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <p className="font-medium">{user.name}</p>
                              <p className="text-sm text-muted-foreground">{user.email}</p>
                            </div>
                            <div className="text-right">
                              <Badge variant="outline">{user.plan}</Badge>
                              <p className="text-xs text-muted-foreground mt-1">{user.status}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="override-type">Override Type</Label>
                  <Select value={overrideType} onValueChange={setOverrideType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select override type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="plan_upgrade">Plan Upgrade</SelectItem>
                      <SelectItem value="credit_adjustment">Credit Adjustment</SelectItem>
                      <SelectItem value="account_unlock">Account Unlock</SelectItem>
                      <SelectItem value="feature_enable">Feature Enable</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {overrideType && (
                <div className="space-y-2">
                  <Label htmlFor="override-value">Override Value</Label>
                  <Input
                    id="override-value"
                    placeholder={
                      overrideType === 'plan_upgrade' ? 'e.g., studio' :
                      overrideType === 'credit_adjustment' ? 'e.g., 100' :
                      overrideType === 'feature_enable' ? 'e.g., advanced_analytics' :
                      'Override value'
                    }
                    value={overrideValue}
                    onChange={(e) => setOverrideValue(e.target.value)}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="reason">Reason (Required)</Label>
                <Textarea
                  id="reason"
                  placeholder="Detailed reason for this administrative override..."
                  value={overrideReason}
                  onChange={(e) => setOverrideReason(e.target.value)}
                  rows={3}
                />
              </div>

              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    className="w-full" 
                    variant="destructive"
                    disabled={!selectedUser || !overrideType || !overrideReason}
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Execute Administrative Override
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Confirm Administrative Override</AlertDialogTitle>
                    <AlertDialogDescription>
                      This action will perform an administrative override that bypasses normal system controls. 
                      Are you sure you want to proceed?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleOverrideSubmit}>
                      Confirm Override
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Override Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActions.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">No override actions found</p>
                ) : (
                  recentActions.map((action) => (
                    <div key={action.id} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <Badge variant={getStatusColor(action.status)}>
                              {action.status}
                            </Badge>
                            <Badge variant="outline">
                              {getActionTypeLabel(action.type)}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(action.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <p className="font-medium mb-1">{action.userEmail}</p>
                          <p className="text-sm text-muted-foreground mb-2">{action.reason}</p>
                          <p className="text-xs text-muted-foreground">
                            Performed by: {action.performedBy}
                          </p>
                        </div>
                        {action.status === 'pending' && (
                          <div className="flex space-x-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => approveAction(action.id)}
                            >
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                            <Button 
                              size="sm" 
                              variant="destructive"
                              onClick={() => rejectAction(action.id)}
                            >
                              Reject
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Detailed audit logs are maintained for compliance</p>
                <p className="text-sm">All administrative actions are logged and reviewed</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminOverridePanel;
