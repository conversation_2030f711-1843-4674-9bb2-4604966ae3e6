
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Package, Users, Mail, FileText, Play, Pause, CheckCircle, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface BulkOperation {
  id: string;
  type: 'user_migration' | 'plan_update' | 'email_campaign' | 'data_export';
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  startedAt?: string;
  completedAt?: string;
  error?: string;
}

interface UserSelection {
  id: string;
  email: string;
  name: string;
  plan: string;
  organization: string;
  selected: boolean;
}

const BulkOperationsPanel: React.FC = () => {
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [targetCriteria, setTargetCriteria] = useState<string>('');
  const [operationData, setOperationData] = useState<string>('');
  const [operations, setOperations] = useState<BulkOperation[]>([]);
  const [userSelection, setUserSelection] = useState<UserSelection[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  // Mock user data for bulk operations
  const mockUsers: UserSelection[] = [
    {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'John Producer',
      plan: 'pro-team',
      organization: 'Example Studios',
      selected: false
    },
    {
      id: 'user-2',
      email: '<EMAIL>',
      name: 'Jane Director',
      plan: 'studio',
      organization: 'Jane\'s Studio',
      selected: false
    },
    {
      id: 'user-3',
      email: '<EMAIL>',
      name: 'Film Producer',
      plan: 'enterprise',
      organization: 'Film Corp',
      selected: false
    }
  ];

  React.useEffect(() => {
    setUserSelection(mockUsers);
  }, []);

  const startBulkOperation = async () => {
    if (!selectedOperation) {
      toast({
        title: "Validation Error",
        description: "Please select an operation type",
        variant: "destructive"
      });
      return;
    }

    const selectedUsers = userSelection.filter(u => u.selected);
    if (selectedUsers.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one user",
        variant: "destructive"
      });
      return;
    }

    const newOperation: BulkOperation = {
      id: `op-${Date.now()}`,
      type: selectedOperation as any,
      name: `${getOperationLabel(selectedOperation)} - ${selectedUsers.length} users`,
      status: 'running',
      progress: 0,
      totalItems: selectedUsers.length,
      processedItems: 0,
      startedAt: new Date().toISOString()
    };

    setOperations(prev => [newOperation, ...prev]);
    setIsRunning(true);

    // Simulate bulk operation progress
    simulateProgress(newOperation.id, selectedUsers.length);

    toast({
      title: "Operation Started",
      description: `Bulk operation started for ${selectedUsers.length} users`,
    });
  };

  const simulateProgress = (operationId: string, totalItems: number) => {
    let processed = 0;
    const interval = setInterval(() => {
      processed += 1;
      const progress = (processed / totalItems) * 100;

      setOperations(prev => prev.map(op => 
        op.id === operationId 
          ? { 
              ...op, 
              progress, 
              processedItems: processed,
              status: processed === totalItems ? 'completed' : 'running',
              completedAt: processed === totalItems ? new Date().toISOString() : undefined
            } 
          : op
      ));

      if (processed === totalItems) {
        clearInterval(interval);
        setIsRunning(false);
        toast({
          title: "Operation Completed",
          description: `Bulk operation completed successfully`,
        });
      }
    }, 1000);
  };

  const getOperationLabel = (type: string) => {
    switch (type) {
      case 'user_migration': return 'User Migration';
      case 'plan_update': return 'Plan Update';
      case 'email_campaign': return 'Email Campaign';
      case 'data_export': return 'Data Export';
      default: return type;
    }
  };

  const getOperationIcon = (type: string) => {
    switch (type) {
      case 'user_migration': return Users;
      case 'plan_update': return Package;
      case 'email_campaign': return Mail;
      case 'data_export': return FileText;
      default: return Package;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'secondary';
      case 'running': return 'default';
      case 'failed': return 'destructive';
      case 'pending': return 'outline';
      default: return 'outline';
    }
  };

  const toggleUserSelection = (userId: string) => {
    setUserSelection(prev => prev.map(user => 
      user.id === userId ? { ...user, selected: !user.selected } : user
    ));
  };

  const selectAllUsers = () => {
    const allSelected = userSelection.every(u => u.selected);
    setUserSelection(prev => prev.map(user => ({ ...user, selected: !allSelected })));
  };

  const selectedCount = userSelection.filter(u => u.selected).length;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Package className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold">Bulk Operations</h2>
        <Badge variant="outline">Enterprise</Badge>
      </div>

      <Tabs defaultValue="create" className="space-y-4">
        <TabsList>
          <TabsTrigger value="create">Create Operation</TabsTrigger>
          <TabsTrigger value="history">Operation History</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Operation Setup</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="operation-type">Operation Type</Label>
                  <Select value={selectedOperation} onValueChange={setSelectedOperation}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select operation type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user_migration">User Migration</SelectItem>
                      <SelectItem value="plan_update">Plan Update</SelectItem>
                      <SelectItem value="email_campaign">Email Campaign</SelectItem>
                      <SelectItem value="data_export">Data Export</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="target-criteria">Target Criteria</Label>
                  <Input
                    id="target-criteria"
                    placeholder="e.g., plan:starter, last_active:30d"
                    value={targetCriteria}
                    onChange={(e) => setTargetCriteria(e.target.value)}
                  />
                </div>

                {selectedOperation && (
                  <div className="space-y-2">
                    <Label htmlFor="operation-data">Operation Data</Label>
                    <Textarea
                      id="operation-data"
                      placeholder={
                        selectedOperation === 'plan_update' ? 'New plan: pro-team' :
                        selectedOperation === 'email_campaign' ? 'Email template content...' :
                        selectedOperation === 'data_export' ? 'Export fields: email, plan, usage' :
                        'Operation configuration...'
                      }
                      value={operationData}
                      onChange={(e) => setOperationData(e.target.value)}
                      rows={3}
                    />
                  </div>
                )}

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button 
                      className="w-full" 
                      disabled={!selectedOperation || selectedCount === 0 || isRunning}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Start Bulk Operation ({selectedCount} users)
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Confirm Bulk Operation</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will perform a bulk operation on {selectedCount} users. This action cannot be undone.
                        Are you sure you want to proceed?
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={startBulkOperation}>
                        Start Operation
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>
                  User Selection ({selectedCount} of {userSelection.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="select-all"
                      checked={userSelection.length > 0 && userSelection.every(u => u.selected)}
                      onCheckedChange={selectAllUsers}
                    />
                    <Label htmlFor="select-all">Select All Users</Label>
                  </div>

                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {userSelection.map((user) => (
                      <div key={user.id} className="flex items-center space-x-2 p-2 border rounded">
                        <Checkbox
                          id={`user-${user.id}`}
                          checked={user.selected}
                          onCheckedChange={() => toggleUserSelection(user.id)}
                        />
                        <div className="flex-1">
                          <p className="font-medium">{user.name}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">{user.plan}</Badge>
                            <span className="text-xs text-muted-foreground">{user.organization}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operation History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {operations.length === 0 ? (
                  <p className="text-center text-muted-foreground py-8">No operations found</p>
                ) : (
                  operations.map((operation) => {
                    const Icon = getOperationIcon(operation.type);
                    return (
                      <div key={operation.id} className="border rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <Icon className="h-5 w-5" />
                            <span className="font-medium">{operation.name}</span>
                            <Badge variant={getStatusColor(operation.status)}>
                              {operation.status}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {operation.startedAt && new Date(operation.startedAt).toLocaleString()}
                          </div>
                        </div>

                        {operation.status === 'running' && (
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Progress</span>
                              <span>{operation.processedItems} / {operation.totalItems}</span>
                            </div>
                            <Progress value={operation.progress} className="h-2" />
                          </div>
                        )}

                        {operation.status === 'completed' && (
                          <div className="flex items-center space-x-2 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                            <span className="text-sm">
                              Completed {operation.totalItems} items in {
                                operation.completedAt && operation.startedAt 
                                  ? Math.round((new Date(operation.completedAt).getTime() - new Date(operation.startedAt).getTime()) / 1000)
                                  : 0
                              } seconds
                            </span>
                          </div>
                        )}

                        {operation.status === 'failed' && (
                          <div className="flex items-center space-x-2 text-red-600">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="text-sm">{operation.error || 'Operation failed'}</span>
                          </div>
                        )}
                      </div>
                    );
                  })
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Operation Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <h4 className="font-medium mb-2">Plan Migration</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Migrate users from starter to pro plans with usage history analysis
                  </p>
                  <Badge variant="outline">Template</Badge>
                </div>

                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <h4 className="font-medium mb-2">Welcome Campaign</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Send onboarding emails to new users with personalized content
                  </p>
                  <Badge variant="outline">Template</Badge>
                </div>

                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <h4 className="font-medium mb-2">Data Export</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Export user data with GDPR compliance for specific date ranges
                  </p>
                  <Badge variant="outline">Template</Badge>
                </div>

                <div className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
                  <h4 className="font-medium mb-2">Credit Adjustment</h4>
                  <p className="text-sm text-muted-foreground mb-3">
                    Bulk credit adjustments for billing corrections and promotions
                  </p>
                  <Badge variant="outline">Template</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BulkOperationsPanel;
