
import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Mail, Calendar } from 'lucide-react';
import type { UserAnalytics } from '@/lib/api/admin-analytics';

interface UserAnalyticsTableRowProps {
  user: UserAnalytics;
}

export const UserAnalyticsTableRow: React.FC<UserAnalyticsTableRowProps> = ({ user }) => {
  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'starter': return 'secondary';
      case 'pro-solo': return 'default';
      case 'pro-team': return 'outline';
      case 'studio': return 'default';
      case 'enterprise': return 'destructive';
      default: return 'secondary';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getActivityStatus = (lastActivity: string | null) => {
    if (!lastActivity) return { status: 'Never', color: 'text-gray-400' };
    
    const daysSince = Math.floor((Date.now() - new Date(lastActivity).getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSince === 0) return { status: 'Today', color: 'text-green-600' };
    if (daysSince <= 7) return { status: `${daysSince}d ago`, color: 'text-green-600' };
    if (daysSince <= 30) return { status: `${daysSince}d ago`, color: 'text-yellow-600' };
    return { status: `${daysSince}d ago`, color: 'text-red-600' };
  };

  const activityStatus = getActivityStatus(user.last_activity);

  return (
    <TableRow>
      <TableCell>
        <div>
          <p className="font-medium">
            {user.full_name || user.username || 'Anonymous'}
          </p>
          <p className="text-sm text-muted-foreground">
            Joined {formatDate(user.created_at)}
          </p>
        </div>
      </TableCell>
      
      <TableCell>
        <div className="flex items-center">
          <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm">{user.email}</span>
        </div>
      </TableCell>
      
      <TableCell>
        <Badge variant={getPlanColor(user.organization_plan)}>
          {user.organization_plan}
        </Badge>
      </TableCell>
      
      <TableCell>
        <span className="text-sm">{user.organization_name}</span>
      </TableCell>
      
      <TableCell>
        <span className={`text-sm font-medium ${activityStatus.color}`}>
          {activityStatus.status}
        </span>
      </TableCell>
      
      <TableCell>
        <div className="text-sm space-y-1">
          <div>Reports: {user.total_coverage_reports}</div>
          <div>Scenes: {user.total_scenes}</div>
          <div>Scripts: {user.total_screenplays}</div>
        </div>
      </TableCell>
      
      <TableCell>
        <div className="flex items-center text-sm text-muted-foreground">
          <Calendar className="h-4 w-4 mr-1" />
          {user.last_activity ? formatDate(user.last_activity) : 'Never'}
        </div>
      </TableCell>
    </TableRow>
  );
};
