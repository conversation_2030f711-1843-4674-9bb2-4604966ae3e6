
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Activity } from 'lucide-react';
import type { UserAnalytics, PlatformStats, SystemUsage } from '@/lib/api/admin-analytics';
import { PlatformStatsCards } from './PlatformStatsCards';

interface AdminAnalyticsOverviewProps {
  platformStats: PlatformStats | null;
  systemUsage: SystemUsage | null;
  userAnalytics: UserAnalytics[];
}

export const AdminAnalyticsOverview: React.FC<AdminAnalyticsOverviewProps> = ({
  platformStats,
  systemUsage,
  userAnalytics
}) => {
  return (
    <div className="space-y-4">
      {platformStats && <PlatformStatsCards stats={platformStats} />}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {userAnalytics.slice(0, 5).map((user) => (
                <div key={user.id} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{user.full_name || user.username || 'Anonymous'}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                  <Badge variant="outline">{user.organization_plan}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {systemUsage && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                System Usage Today
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span>Coverage Generations</span>
                  <span className="font-bold">{systemUsage.coverage_generations_today}</span>
                </div>
                <div className="flex justify-between">
                  <span>Active Organizations</span>
                  <span className="font-bold">{systemUsage.active_organizations}</span>
                </div>
                <div className="flex justify-between">
                  <span>Avg Reports/User</span>
                  <span className="font-bold">{systemUsage.average_reports_per_user}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
