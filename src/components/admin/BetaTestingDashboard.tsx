
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BetaInvitationManager } from './beta/BetaInvitationManager';
import { BetaFeedbackManager } from './beta/BetaFeedbackManager';
import { BetaAnalyticsDashboard } from './beta/BetaAnalyticsDashboard';
import { BetaCohortManager } from './beta/BetaCohortManager';
import { BetaPromoManager } from './beta/BetaPromoManager';
import BetaRequestsManagement from './BetaRequestsManagement';
import PromoCodesManagement from './PromoCodesManagement';
import BetaAutomationSettings from './BetaAutomationSettings';
import { useBetaMetrics } from '@/hooks/useBetaMetrics';
import { Users, MessageSquare, TrendingUp, Target } from 'lucide-react';

export const BetaTestingDashboard: React.FC = () => {
  const { metrics, loading } = useBetaMetrics();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Beta Testing Dashboard</h1>
          <p className="text-muted-foreground">
            Manage beta invitations, collect feedback, and track user engagement
          </p>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Beta Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.activeUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.acceptanceRate || 0}% acceptance rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Feedback Items</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.feedbackCount || 0}</div>
            <p className="text-xs text-muted-foreground">
              {metrics?.bugReports || 0} bug reports
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.retentionRate || 0}%</div>
            <p className="text-xs text-muted-foreground">7-day retention</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">NPS Score</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.npsScore || 0}</div>
            <p className="text-xs text-muted-foreground">Net Promoter Score</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="requests" className="space-y-4">
        <TabsList>
          <TabsTrigger value="requests">Beta Requests</TabsTrigger>
          <TabsTrigger value="promo-codes">Promo Codes</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="invitations">Invitations</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="cohorts">Cohorts</TabsTrigger>
          <TabsTrigger value="promo">Beta Promo</TabsTrigger>
        </TabsList>

        <TabsContent value="requests">
          <BetaRequestsManagement />
        </TabsContent>

        <TabsContent value="promo-codes">
          <PromoCodesManagement />
        </TabsContent>

        <TabsContent value="automation">
          <BetaAutomationSettings />
        </TabsContent>

        <TabsContent value="invitations">
          <BetaInvitationManager />
        </TabsContent>

        <TabsContent value="feedback">
          <BetaFeedbackManager />
        </TabsContent>

        <TabsContent value="analytics">
          <BetaAnalyticsDashboard />
        </TabsContent>

        <TabsContent value="cohorts">
          <BetaCohortManager />
        </TabsContent>

        <TabsContent value="promo">
          <BetaPromoManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};
