
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { useBetaInvitations } from '@/hooks/useBetaInvitations';
import { toast } from '@/hooks/use-toast';
import { Send, Copy, Trash2 } from 'lucide-react';

export const BetaInvitationManager: React.FC = () => {
  const [email, setEmail] = useState('');
  const [phase, setPhase] = useState('1');
  const [bulkEmails, setBulkEmails] = useState('');
  const { invitations, sendInvitation, bulkSendInvitations, revokeInvitation, loading } = useBetaInvitations();

  const handleSendInvitation = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    try {
      await sendInvitation(email, parseInt(phase));
      setEmail('');
      toast({
        title: 'Invitation sent',
        description: `Beta invitation sent to ${email}`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send invitation',
        variant: 'destructive',
      });
    }
  };

  const handleBulkSend = async () => {
    if (!bulkEmails.trim()) return;

    const emails = bulkEmails
      .split('\n')
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));

    if (emails.length === 0) {
      toast({
        title: 'Error',
        description: 'Please enter valid email addresses',
        variant: 'destructive',
      });
      return;
    }

    try {
      await bulkSendInvitations(emails, parseInt(phase));
      setBulkEmails('');
      toast({
        title: 'Invitations sent',
        description: `Sent ${emails.length} beta invitations`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to send bulk invitations',
        variant: 'destructive',
      });
    }
  };

  const copyInvitationCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast({
      title: 'Copied',
      description: 'Invitation code copied to clipboard',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'accepted':
        return <Badge variant="default">Accepted</Badge>;
      case 'expired':
        return <Badge variant="destructive">Expired</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Single Invitation */}
        <Card>
          <CardHeader>
            <CardTitle>Send Beta Invitation</CardTitle>
            <CardDescription>
              Send a beta invitation to a single user
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSendInvitation} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phase">Beta Phase</Label>
                <Select value={phase} onValueChange={setPhase}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">Phase 1 (50 users)</SelectItem>
                    <SelectItem value="2">Phase 2 (150 users)</SelectItem>
                    <SelectItem value="3">Phase 3 (250 users)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Button type="submit" disabled={loading} className="w-full">
                <Send className="w-4 h-4 mr-2" />
                Send Invitation
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Bulk Invitations */}
        <Card>
          <CardHeader>
            <CardTitle>Bulk Invitations</CardTitle>
            <CardDescription>
              Send invitations to multiple users at once
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bulk-emails">Email Addresses (one per line)</Label>
              <textarea
                id="bulk-emails"
                className="w-full h-32 p-3 border rounded-md resize-none"
                placeholder="<EMAIL>&#10;<EMAIL>&#10;<EMAIL>"
                value={bulkEmails}
                onChange={(e) => setBulkEmails(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label>Beta Phase</Label>
              <Select value={phase} onValueChange={setPhase}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">Phase 1 (50 users)</SelectItem>
                  <SelectItem value="2">Phase 2 (150 users)</SelectItem>
                  <SelectItem value="3">Phase 3 (250 users)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleBulkSend} disabled={loading} className="w-full">
              <Send className="w-4 h-4 mr-2" />
              Send Bulk Invitations
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Invitations Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Invitations</CardTitle>
          <CardDescription>
            Manage and track beta invitations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Phase</TableHead>
                <TableHead>Code</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Sent</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invitations?.map((invitation) => (
                <TableRow key={invitation.id}>
                  <TableCell>{invitation.email}</TableCell>
                  <TableCell>Phase {invitation.phase}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {invitation.invitation_code}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyInvitationCode(invitation.invitation_code)}
                      >
                        <Copy className="w-3 h-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(invitation.status)}</TableCell>
                  <TableCell>
                    {new Date(invitation.invited_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {invitation.status === 'pending' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => revokeInvitation(invitation.id)}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
