
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

// Mock data for demonstration
const mockUsageData = [
  { name: 'Mon', page_views: 120, feature_usage: 45, conversions: 8 },
  { name: 'Tu<PERSON>', page_views: 140, feature_usage: 52, conversions: 12 },
  { name: 'Wed', page_views: 180, feature_usage: 68, conversions: 15 },
  { name: 'Thu', page_views: 160, feature_usage: 61, conversions: 11 },
  { name: 'Fri', page_views: 200, feature_usage: 78, conversions: 18 },
  { name: 'Sat', page_views: 95, feature_usage: 35, conversions: 6 },
  { name: 'Sun', page_views: 85, feature_usage: 28, conversions: 4 },
];

const mockFeatureData = [
  { name: 'Script Editor', usage: 245 },
  { name: 'Character Builder', usage: 189 },
  { name: 'Storyboard', usage: 156 },
  { name: 'Coverage Generator', usage: 134 },
  { name: 'Collaboration Tools', usage: 98 },
];

export const BetaAnalyticsDashboard: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>User Engagement Trends</CardTitle>
          <CardDescription>Daily user activity over the past week</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={mockUsageData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="page_views" 
                stroke="#8884d8" 
                name="Page Views"
              />
              <Line 
                type="monotone" 
                dataKey="feature_usage" 
                stroke="#82ca9d" 
                name="Feature Usage"
              />
              <Line 
                type="monotone" 
                dataKey="conversions" 
                stroke="#ffc658" 
                name="Conversions"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Feature Usage</CardTitle>
          <CardDescription>Most popular features among beta users</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={mockFeatureData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="usage" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Average Session Duration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24.5 min</div>
            <p className="text-xs text-muted-foreground">+12% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Bounce Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18.2%</div>
            <p className="text-xs text-muted-foreground">-5% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-base">Feature Adoption Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78.9%</div>
            <p className="text-xs text-muted-foreground">+8% from last week</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
