
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useBetaFeedback } from '@/hooks/useBetaFeedback';
import { AlertTriangle, Bug, Lightbulb, MessageSquare, Eye } from 'lucide-react';

interface FeedbackItem {
  id: string;
  title: string;
  description: string;
  feedback_type: string;
  category: string;
  severity: string;
  status: string;
  created_at: string;
  user_id: string;
  page_url?: string;
  steps_to_reproduce?: string;
  expected_behavior?: string;
  actual_behavior?: string;
}

export const BetaFeedbackManager: React.FC = () => {
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackItem | null>(null);
  const { feedback, updateFeedbackStatus, loading } = useBetaFeedback();

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'bug_report':
        return <Bug className="w-4 h-4 text-red-500" />;
      case 'feature_request':
        return <Lightbulb className="w-4 h-4 text-blue-500" />;
      case 'usability_issue':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-500" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'high':
        return <Badge className="bg-orange-500">High</Badge>;
      case 'medium':
        return <Badge variant="outline">Medium</Badge>;
      case 'low':
        return <Badge variant="secondary">Low</Badge>;
      default:
        return <Badge variant="secondary">{severity}</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="outline">Open</Badge>;
      case 'in_progress':
        return <Badge className="bg-blue-500">In Progress</Badge>;
      case 'resolved':
        return <Badge variant="default">Resolved</Badge>;
      case 'closed':
        return <Badge variant="secondary">Closed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredFeedback = feedback?.filter((item) => {
    if (statusFilter !== 'all' && item.status !== statusFilter) return false;
    if (typeFilter !== 'all' && item.feedback_type !== typeFilter) return false;
    return true;
  });

  const handleStatusUpdate = async (feedbackId: string, newStatus: string) => {
    try {
      await updateFeedbackStatus(feedbackId, newStatus);
    } catch (error) {
      console.error('Failed to update feedback status:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Feedback</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="bug_report">Bug Report</SelectItem>
                  <SelectItem value="feature_request">Feature Request</SelectItem>
                  <SelectItem value="usability_issue">Usability Issue</SelectItem>
                  <SelectItem value="general_feedback">General Feedback</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Table */}
      <Card>
        <CardHeader>
          <CardTitle>Beta Feedback</CardTitle>
          <CardDescription>
            Manage feedback from beta testers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFeedback?.map((item) => (
                <TableRow key={item.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getTypeIcon(item.feedback_type)}
                      <span className="capitalize">
                        {item.feedback_type.replace('_', ' ')}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">{item.title}</TableCell>
                  <TableCell className="capitalize">{item.category}</TableCell>
                  <TableCell>{getSeverityBadge(item.severity)}</TableCell>
                  <TableCell>{getStatusBadge(item.status)}</TableCell>
                  <TableCell>
                    {new Date(item.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedFeedback(item)}
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>{selectedFeedback?.title}</DialogTitle>
                          </DialogHeader>
                          {selectedFeedback && (
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <label className="text-sm font-medium">Type</label>
                                  <p className="capitalize">
                                    {selectedFeedback.feedback_type.replace('_', ' ')}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Category</label>
                                  <p className="capitalize">{selectedFeedback.category}</p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Severity</label>
                                  <div>{getSeverityBadge(selectedFeedback.severity)}</div>
                                </div>
                                <div>
                                  <label className="text-sm font-medium">Status</label>
                                  <Select
                                    value={selectedFeedback.status}
                                    onValueChange={(value) => 
                                      handleStatusUpdate(selectedFeedback.id, value)
                                    }
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="open">Open</SelectItem>
                                      <SelectItem value="in_progress">In Progress</SelectItem>
                                      <SelectItem value="resolved">Resolved</SelectItem>
                                      <SelectItem value="closed">Closed</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>

                              <div>
                                <label className="text-sm font-medium">Description</label>
                                <p className="mt-1 text-sm text-muted-foreground">
                                  {selectedFeedback.description}
                                </p>
                              </div>

                              {selectedFeedback.steps_to_reproduce && (
                                <div>
                                  <label className="text-sm font-medium">Steps to Reproduce</label>
                                  <p className="mt-1 text-sm text-muted-foreground whitespace-pre-wrap">
                                    {selectedFeedback.steps_to_reproduce}
                                  </p>
                                </div>
                              )}

                              {selectedFeedback.expected_behavior && (
                                <div>
                                  <label className="text-sm font-medium">Expected Behavior</label>
                                  <p className="mt-1 text-sm text-muted-foreground">
                                    {selectedFeedback.expected_behavior}
                                  </p>
                                </div>
                              )}

                              {selectedFeedback.actual_behavior && (
                                <div>
                                  <label className="text-sm font-medium">Actual Behavior</label>
                                  <p className="mt-1 text-sm text-muted-foreground">
                                    {selectedFeedback.actual_behavior}
                                  </p>
                                </div>
                              )}

                              {selectedFeedback.page_url && (
                                <div>
                                  <label className="text-sm font-medium">Page URL</label>
                                  <p className="mt-1 text-sm text-blue-600 break-all">
                                    {selectedFeedback.page_url}
                                  </p>
                                </div>
                              )}
                            </div>
                          )}
                        </DialogContent>
                      </Dialog>

                      <Select
                        value={item.status}
                        onValueChange={(value) => handleStatusUpdate(item.id, value)}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="open">Open</SelectItem>
                          <SelectItem value="in_progress">In Progress</SelectItem>
                          <SelectItem value="resolved">Resolved</SelectItem>
                          <SelectItem value="closed">Closed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
