
import React from 'react';
import { <PERSON>, CardContent, Card<PERSON>es<PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Progress } from '@/components/ui/progress';

// Mock data for demonstration
const mockCohorts = [
  {
    id: '1',
    user_id: 'user1',
    cohort_name: 'phase_1_beta',
    joined_at: '2024-01-15T10:00:00Z',
    is_active: true,
    onboarding_completed: true,
    last_activity_at: '2024-01-20T15:30:00Z',
    user_email: '<EMAIL>',
  },
  {
    id: '2',
    user_id: 'user2',
    cohort_name: 'phase_1_beta',
    joined_at: '2024-01-16T14:00:00Z',
    is_active: true,
    onboarding_completed: false,
    last_activity_at: '2024-01-19T09:15:00Z',
    user_email: '<EMAIL>',
  },
  {
    id: '3',
    user_id: 'user3',
    cohort_name: 'phase_2_beta',
    joined_at: '2024-01-18T16:00:00Z',
    is_active: true,
    onboarding_completed: true,
    last_activity_at: '2024-01-20T11:45:00Z',
    user_email: '<EMAIL>',
  },
];

const cohortStats = {
  phase_1_beta: { total: 45, active: 38, onboarded: 42 },
  phase_2_beta: { total: 28, active: 25, onboarded: 24 },
  phase_3_beta: { total: 12, active: 11, onboarded: 8 },
};

export const BetaCohortManager: React.FC = () => {
  const getCohortBadge = (cohortName: string) => {
    switch (cohortName) {
      case 'phase_1_beta':
        return <Badge variant="default">Phase 1</Badge>;
      case 'phase_2_beta':
        return <Badge className="bg-blue-500">Phase 2</Badge>;
      case 'phase_3_beta':
        return <Badge className="bg-purple-500">Phase 3</Badge>;
      default:
        return <Badge variant="secondary">{cohortName}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Cohort Overview */}
      <div className="grid gap-4 md:grid-cols-3">
        {Object.entries(cohortStats).map(([cohortName, stats]) => (
          <Card key={cohortName}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="capitalize">{cohortName.replace('_', ' ')}</span>
                {getCohortBadge(cohortName)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex justify-between text-sm">
                  <span>Active Users</span>
                  <span>{stats.active}/{stats.total}</span>
                </div>
                <Progress value={(stats.active / stats.total) * 100} className="mt-1" />
              </div>
              
              <div>
                <div className="flex justify-between text-sm">
                  <span>Onboarding Complete</span>
                  <span>{stats.onboarded}/{stats.total}</span>
                </div>
                <Progress value={(stats.onboarded / stats.total) * 100} className="mt-1" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* User Details */}
      <Card>
        <CardHeader>
          <CardTitle>Beta User Details</CardTitle>
          <CardDescription>
            Individual user progress and activity
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Cohort</TableHead>
                <TableHead>Joined</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Onboarding</TableHead>
                <TableHead>Last Activity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mockCohorts.map((cohort) => (
                <TableRow key={cohort.id}>
                  <TableCell>{cohort.user_email}</TableCell>
                  <TableCell>{getCohortBadge(cohort.cohort_name)}</TableCell>
                  <TableCell>
                    {new Date(cohort.joined_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {cohort.is_active ? (
                      <Badge variant="default">Active</Badge>
                    ) : (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {cohort.onboarding_completed ? (
                      <Badge variant="default">Complete</Badge>
                    ) : (
                      <Badge variant="outline">Pending</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {cohort.last_activity_at ? 
                      new Date(cohort.last_activity_at).toLocaleDateString() : 
                      'Never'
                    }
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
