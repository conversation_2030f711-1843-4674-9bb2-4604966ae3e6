
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { usePromoCampaigns } from '@/hooks/usePromoCampaigns';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';
import { Sparkles, Users, DollarSign, TrendingUp } from 'lucide-react';

export const BetaPromoManager: React.FC = () => {
  const { campaigns, createCampaign, updateCampaign, loading } = usePromoCampaigns();
  const [betaStats, setBetaStats] = useState({
    totalBetaUsers: 0,
    promoUsage: 0,
    totalSavings: 0,
    conversionRate: 0
  });

  const createBetaPromoCampaign = async () => {
    try {
      const betaCampaign = {
        name: 'Beta Lifetime 90% Discount',
        description: 'Exclusive 90% lifetime discount for beta testers',
        campaign_code: 'BETA-LIFETIME-90',
        discount_type: 'percentage' as const,
        discount_value: 90,
        target_audience: 'custom_segment' as const,
        custom_criteria: {
          beta_users_only: true,
          cohort_types: ['phase_1_beta', 'phase_2_beta', 'phase_3_beta']
        },
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days
        usage_limit: 250,
        usage_limit_per_user: 1,
        is_active: true,
        status: 'active' as const
      };

      await createCampaign(betaCampaign);
      toast({
        title: 'Success',
        description: 'Beta promo campaign created successfully',
      });
    } catch (error) {
      console.error('Error creating beta promo campaign:', error);
      toast({
        title: 'Error',
        description: 'Failed to create beta promo campaign',
        variant: 'destructive',
      });
    }
  };

  const fetchBetaStats = async () => {
    try {
      // Get total beta users
      const { data: betaUsers, error: betaError } = await supabase
        .from('beta_cohorts')
        .select('id')
        .eq('is_active', true);

      if (betaError) throw betaError;

      // Get beta promo campaign
      const betaCampaign = campaigns.find(c => c.campaign_code === 'BETA-LIFETIME-90');
      let promoUsage = 0;
      let totalSavings = 0;

      if (betaCampaign) {
        const { data: usage, error: usageError } = await supabase
          .from('promo_campaign_usage')
          .select('discount_amount')
          .eq('campaign_id', betaCampaign.id);

        if (!usageError && usage) {
          promoUsage = usage.length;
          totalSavings = usage.reduce((sum, u) => sum + (u.discount_amount || 0), 0);
        }
      }

      setBetaStats({
        totalBetaUsers: betaUsers?.length || 0,
        promoUsage,
        totalSavings,
        conversionRate: betaUsers?.length > 0 ? (promoUsage / betaUsers.length) * 100 : 0
      });
    } catch (error) {
      console.error('Error fetching beta stats:', error);
    }
  };

  useEffect(() => {
    fetchBetaStats();
  }, [campaigns]);

  const betaCampaign = campaigns.find(c => c.campaign_code === 'BETA-LIFETIME-90');

  return (
    <div className="space-y-6">
      {/* Beta Promo Overview */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Beta Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{betaStats.totalBetaUsers}</div>
            <p className="text-xs text-muted-foreground">Active beta testers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Promo Usage</CardTitle>
            <Sparkles className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{betaStats.promoUsage}/250</div>
            <p className="text-xs text-muted-foreground">Lifetime discounts used</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${betaStats.totalSavings.toFixed(0)}</div>
            <p className="text-xs text-muted-foreground">Given to beta users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{betaStats.conversionRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">Beta to paid conversion</p>
          </CardContent>
        </Card>
      </div>

      {/* Campaign Management */}
      <Card>
        <CardHeader>
          <CardTitle>Beta Promo Campaign</CardTitle>
          <CardDescription>
            Manage the exclusive 90% lifetime discount for beta testers
          </CardDescription>
        </CardHeader>
        <CardContent>
          {!betaCampaign ? (
            <div className="text-center py-8">
              <Sparkles className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Beta Promo Campaign</h3>
              <p className="text-muted-foreground mb-4">
                Create the exclusive beta promo campaign to start offering 90% lifetime discounts
              </p>
              <Button 
                onClick={createBetaPromoCampaign}
                disabled={loading}
                className="bg-gradient-to-r from-primary to-purple-600"
              >
                {loading ? 'Creating...' : 'Create Beta Promo Campaign'}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold">{betaCampaign.name}</h3>
                  <p className="text-sm text-muted-foreground">{betaCampaign.description}</p>
                </div>
                <Badge 
                  className={betaCampaign.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}
                >
                  {betaCampaign.status.toUpperCase()}
                </Badge>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Code:</span>
                  <p className="text-muted-foreground">{betaCampaign.campaign_code}</p>
                </div>
                <div>
                  <span className="font-medium">Discount:</span>
                  <p className="text-muted-foreground">{betaCampaign.discount_value}%</p>
                </div>
                <div>
                  <span className="font-medium">Usage Limit:</span>
                  <p className="text-muted-foreground">{betaCampaign.usage_limit}</p>
                </div>
                <div>
                  <span className="font-medium">Per User:</span>
                  <p className="text-muted-foreground">{betaCampaign.usage_limit_per_user}</p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => updateCampaign(betaCampaign.id, { 
                    status: betaCampaign.status === 'active' ? 'paused' : 'active',
                    is_active: betaCampaign.status !== 'active'
                  })}
                  disabled={loading}
                >
                  {betaCampaign.status === 'active' ? 'Pause' : 'Activate'} Campaign
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
