
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Users, Building, FileText, TrendingUp, Activity, DollarSign } from 'lucide-react';
import type { PlatformStats } from '@/lib/api/admin-analytics';

interface PlatformStatsCardsProps {
  stats: PlatformStats;
}

export const PlatformStatsCards: React.FC<PlatformStatsCardsProps> = ({ stats }) => {
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getTotalRevenue = () => {
    // Estimate revenue based on subscription distribution
    const pricing = {
      'starter': 0,
      'pro-solo': 29,
      'pro-team': 99,
      'studio': 299,
      'enterprise': 999
    };

    return Object.entries(stats.subscription_distribution).reduce((total, [plan, count]) => {
      return total + (pricing[plan as keyof typeof pricing] || 0) * count;
    }, 0);
  };

  const getGrowthRate = () => {
    if (stats.monthly_signups.length < 2) return 0;
    const current = stats.monthly_signups[stats.monthly_signups.length - 1]?.count || 0;
    const previous = stats.monthly_signups[stats.monthly_signups.length - 2]?.count || 0;
    if (previous === 0) return 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  const statsCards = [
    {
      title: 'Total Users',
      value: formatNumber(stats.total_users),
      icon: Users,
      description: 'Registered users',
      color: 'text-blue-600'
    },
    {
      title: 'Organizations',
      value: formatNumber(stats.total_organizations),
      icon: Building,
      description: 'Active organizations',
      color: 'text-green-600'
    },
    {
      title: 'Coverage Reports',
      value: formatNumber(stats.total_coverage_reports),
      icon: FileText,
      description: 'Generated reports',
      color: 'text-purple-600'
    },
    {
      title: 'Scenes Created',
      value: formatNumber(stats.total_scenes),
      icon: Activity,
      description: 'Total scenes',
      color: 'text-orange-600'
    },
    {
      title: 'Screenplays',
      value: formatNumber(stats.total_screenplays),
      icon: FileText,
      description: 'Scripts in system',
      color: 'text-red-600'
    },
    {
      title: 'Monthly Revenue',
      value: `$${formatNumber(getTotalRevenue())}`,
      icon: DollarSign,
      description: 'Estimated MRR',
      color: 'text-green-600'
    },
    {
      title: 'Growth Rate',
      value: `${getGrowthRate()}%`,
      icon: TrendingUp,
      description: 'Month over month',
      color: getGrowthRate() >= 0 ? 'text-green-600' : 'text-red-600'
    },
    {
      title: 'Active Users',
      value: formatNumber(stats.active_users_30_days),
      icon: Users,
      description: 'Last 30 days',
      color: 'text-blue-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statsCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <Icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">{stat.description}</p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
