
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tag, BarChart3, Users, TrendingUp, Plus, Activity } from 'lucide-react';
import { usePromoCampaignAnalytics } from '@/hooks/usePromoCampaigns';
import { PromoCampaignsList } from './PromoAdminComponents/PromoCampaignsList';
import { PromoCampaignForm } from './PromoAdminComponents/PromoCampaignForm';
import { PromoAnalyticsDashboard } from './PromoAdminComponents/PromoAnalyticsDashboard';
import { PromoCodeValidator } from './PromoAdminComponents/PromoCodeValidator';

const PromoAdminDashboard: React.FC = () => {
  const { analytics, loading: analyticsLoading } = usePromoCampaignAnalytics();
  const [showCreateForm, setShowCreateForm] = React.useState(false);

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Tag className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Promo Admin Dashboard</h1>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Campaign
        </Button>
      </div>

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Campaigns</p>
                <p className="text-2xl font-bold">
                  {analyticsLoading ? '-' : analytics?.total_campaigns || 0}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Campaigns</p>
                <p className="text-2xl font-bold">
                  {analyticsLoading ? '-' : analytics?.active_campaigns || 0}
                </p>
              </div>
              <Activity className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Usage</p>
                <p className="text-2xl font-bold">
                  {analyticsLoading ? '-' : analytics?.total_usage || 0}
                </p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {analyticsLoading ? '-' : formatCurrency(analytics?.total_revenue || 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-emerald-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="validator">Code Validator</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <PromoCampaignsList />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <PromoAnalyticsDashboard />
        </TabsContent>

        <TabsContent value="validator" className="space-y-4">
          <PromoCodeValidator />
        </TabsContent>
      </Tabs>

      {/* Create Campaign Form Dialog */}
      {showCreateForm && (
        <PromoCampaignForm
          open={showCreateForm}
          onClose={() => setShowCreateForm(false)}
        />
      )}
    </div>
  );
};

export default PromoAdminDashboard;
