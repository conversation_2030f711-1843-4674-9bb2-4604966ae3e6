
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Shield, Activity, Download } from 'lucide-react';

interface AdminAnalyticsHeaderProps {
  onRefresh: () => void;
  onExport: () => void;
}

export const AdminAnalyticsHeader: React.FC<AdminAnalyticsHeaderProps> = ({
  onRefresh,
  onExport
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-2">
        <Shield className="h-6 w-6 text-primary" />
        <h1 className="text-3xl font-bold">Platform Analytics</h1>
        <Badge variant="destructive">Super Admin Only</Badge>
      </div>
      <div className="flex space-x-2">
        <Button onClick={onRefresh} variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
        <Button onClick={onExport} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Users
        </Button>
      </div>
    </div>
  );
};
