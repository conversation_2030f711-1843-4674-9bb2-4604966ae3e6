import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { 
  Save, 
  X, 
  Upload, 
  Eye, 
  Calendar,
  Tag,
  Image as ImageIcon,
  FileText,
  Globe
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { blogApi, type BlogPost, type BlogCategory } from '@/lib/api/blog';
import { useAsyncOperation } from '@/hooks/useAsyncOperation';

interface BlogPostEditorProps {
  post?: BlogPost | null;
  categories: BlogCategory[];
  onClose: () => void;
}

interface FormData {
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  category_id: string;
  tags: string[];
  status: 'draft' | 'published' | 'scheduled';
  featured: boolean;
  featured_image_url: string;
  seo_title: string;
  seo_description: string;
  seo_keywords: string[];
  scheduled_for: string;
}

const BlogPostEditor: React.FC<BlogPostEditorProps> = ({ post, categories, onClose }) => {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    category_id: '',
    tags: [],
    status: 'draft',
    featured: false,
    featured_image_url: '',
    seo_title: '',
    seo_description: '',
    seo_keywords: [],
    scheduled_for: ''
  });

  const [tagInput, setTagInput] = useState('');
  const [keywordInput, setKeywordInput] = useState('');
  const [imageUploading, setImageUploading] = useState(false);
  const { toast } = useToast();

  const { execute: executeSave, loading: saving } = useAsyncOperation<BlogPost>({
    errorMessage: 'Failed to save blog post'
  });

  const { execute: executeUploadImage } = useAsyncOperation<string>({
    errorMessage: 'Failed to upload image'
  });

  // Initialize form data when editing
  useEffect(() => {
    if (post) {
      setFormData({
        title: post.title,
        slug: post.slug,
        excerpt: post.excerpt || '',
        content: post.content,
        category_id: post.category_id || '',
        tags: post.tags || [],
        status: post.status,
        featured: post.featured,
        featured_image_url: post.featured_image_url || '',
        seo_title: post.seo_title || '',
        seo_description: post.seo_description || '',
        seo_keywords: post.seo_keywords || [],
        scheduled_for: post.scheduled_for || ''
      });
    }
  }, [post]);

  // Auto-generate slug from title
  useEffect(() => {
    if (!post && formData.title) {
      const slug = formData.title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.title, post]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleAddKeyword = () => {
    if (keywordInput.trim() && !formData.seo_keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        seo_keywords: [...prev.seo_keywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const handleRemoveKeyword = (keywordToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      seo_keywords: prev.seo_keywords.filter(keyword => keyword !== keywordToRemove)
    }));
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImageUploading(true);
    const { data, success } = await executeUploadImage(async () => {
      const result = await blogApi.uploadImage(file, `Featured image for ${formData.title}`);
      if (result.success && result.data) {
        return result.data;
      }
      throw new Error(result.error || 'Failed to upload image');
    });

    if (success && data) {
      setFormData(prev => ({ ...prev, featured_image_url: data }));
      toast({
        title: 'Image uploaded',
        description: 'Featured image has been uploaded successfully.',
      });
    }
    setImageUploading(false);
  };

  const handleSave = async (status: 'draft' | 'published' | 'scheduled') => {
    if (!formData.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Title is required.',
        variant: 'destructive'
      });
      return;
    }

    if (!formData.content.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Content is required.',
        variant: 'destructive'
      });
      return;
    }

    if (status === 'scheduled' && !formData.scheduled_for) {
      toast({
        title: 'Validation Error',
        description: 'Scheduled date is required for scheduled posts.',
        variant: 'destructive'
      });
      return;
    }

    const saveData = {
      ...formData,
      status,
      published_at: status === 'published' ? new Date().toISOString() : undefined
    };

    const { success } = await executeSave(async () => {
      if (post) {
        const result = await blogApi.updatePost({ id: post.id, ...saveData });
        if (result.success && result.data) {
          return result.data;
        }
        throw new Error(result.error || 'Failed to update post');
      } else {
        const result = await blogApi.createPost(saveData);
        if (result.success && result.data) {
          return result.data;
        }
        throw new Error(result.error || 'Failed to create post');
      }
    });

    if (success) {
      toast({
        title: post ? 'Post updated' : 'Post created',
        description: `Blog post has been ${status === 'published' ? 'published' : 'saved'} successfully.`,
      });
      onClose();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">
            {post ? 'Edit Blog Post' : 'Create New Blog Post'}
          </h1>
          <p className="text-gray-600 mt-2">
            {post ? 'Update your blog post content and settings' : 'Write and publish a new blog post'}
          </p>
        </div>
        <Button variant="outline" onClick={onClose}>
          <X className="h-4 w-4 mr-2" />
          Cancel
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter post title..."
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="slug">URL Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="url-friendly-slug"
                  className="mt-1"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  URL: /blog/{formData.slug}
                </p>
              </div>

              <div>
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => handleInputChange('excerpt', e.target.value)}
                  placeholder="Brief description of the post..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="content">Content *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  placeholder="Write your blog post content here..."
                  className="mt-1"
                  rows={15}
                />
                <p className="text-sm text-muted-foreground mt-1">
                  Supports HTML and Markdown formatting
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                Publish Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value: 'draft' | 'published' | 'scheduled') =>
                    handleInputChange('status', value)
                  }
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formData.status === 'scheduled' && (
                <div>
                  <Label htmlFor="scheduled_for">Scheduled For</Label>
                  <Input
                    id="scheduled_for"
                    type="datetime-local"
                    value={formData.scheduled_for}
                    onChange={(e) => handleInputChange('scheduled_for', e.target.value)}
                    className="mt-1"
                  />
                </div>
              )}

              <div className="flex items-center space-x-2">
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) => handleInputChange('featured', checked)}
                />
                <Label htmlFor="featured">Featured Post</Label>
              </div>
            </CardContent>
          </Card>

          {/* Category & Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Tag className="h-5 w-5 mr-2" />
                Category & Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formData.category_id}
                  onValueChange={(value) => handleInputChange('category_id', value)}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="tags">Tags</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Add tag..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                  />
                  <Button type="button" onClick={handleAddTag} size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="cursor-pointer">
                      {tag}
                      <X
                        className="h-3 w-3 ml-1"
                        onClick={() => handleRemoveTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                Featured Image
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.featured_image_url ? (
                <div>
                  <img
                    src={formData.featured_image_url}
                    alt="Featured"
                    className="w-full h-32 object-cover rounded-md"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleInputChange('featured_image_url', '')}
                    className="mt-2 w-full"
                  >
                    Remove Image
                  </Button>
                </div>
              ) : (
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <Label htmlFor="image-upload" className="cursor-pointer">
                    <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-gray-400 transition-colors">
                      {imageUploading ? (
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                      ) : (
                        <>
                          <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-sm text-gray-600">
                            Click to upload featured image
                          </p>
                          <p className="text-xs text-gray-400 mt-1">
                            PNG, JPG, WebP up to 5MB
                          </p>
                        </>
                      )}
                    </div>
                  </Label>
                </div>
              )}
            </CardContent>
          </Card>

          {/* SEO Settings */}
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seo_title">SEO Title</Label>
                <Input
                  id="seo_title"
                  value={formData.seo_title}
                  onChange={(e) => handleInputChange('seo_title', e.target.value)}
                  placeholder="SEO optimized title..."
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="seo_description">SEO Description</Label>
                <Textarea
                  id="seo_description"
                  value={formData.seo_description}
                  onChange={(e) => handleInputChange('seo_description', e.target.value)}
                  placeholder="SEO meta description..."
                  className="mt-1"
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="seo_keywords">SEO Keywords</Label>
                <div className="flex gap-2 mt-1">
                  <Input
                    value={keywordInput}
                    onChange={(e) => setKeywordInput(e.target.value)}
                    placeholder="Add keyword..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddKeyword())}
                  />
                  <Button type="button" onClick={handleAddKeyword} size="sm">
                    Add
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {formData.seo_keywords.map((keyword) => (
                    <Badge key={keyword} variant="outline" className="cursor-pointer">
                      {keyword}
                      <X
                        className="h-3 w-3 ml-1"
                        onClick={() => handleRemoveKeyword(keyword)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-2">
                <Button
                  onClick={() => handleSave('draft')}
                  variant="outline"
                  className="w-full"
                  disabled={saving}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save as Draft
                </Button>

                <Button
                  onClick={() => handleSave('published')}
                  className="w-full"
                  disabled={saving}
                >
                  <Globe className="h-4 w-4 mr-2" />
                  {post?.status === 'published' ? 'Update' : 'Publish'}
                </Button>

                {formData.status === 'scheduled' && (
                  <Button
                    onClick={() => handleSave('scheduled')}
                    variant="secondary"
                    className="w-full"
                    disabled={saving}
                  >
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Post
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BlogPostEditor;
