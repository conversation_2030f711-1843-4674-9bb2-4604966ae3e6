
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import type { UserAnalytics } from '@/lib/api/admin-analytics';
import { UserAnalyticsTableHeader } from './UserAnalyticsTableHeader';
import { UserAnalyticsTableRow } from './UserAnalyticsTableRow';

interface UserAnalyticsTableProps {
  users: UserAnalytics[];
}

export const UserAnalyticsTable: React.FC<UserAnalyticsTableProps> = ({ users }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [planFilter, setPlanFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'created_at' | 'last_activity' | 'reports'>('created_at');

  const filteredUsers = users
    .filter(user => {
      const matchesSearch = searchTerm === '' || 
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.username?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesPlan = planFilter === 'all' || user.organization_plan === planFilter;
      
      return matchesSearch && matchesPlan;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'created_at':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'last_activity':
          const aActivity = a.last_activity ? new Date(a.last_activity).getTime() : 0;
          const bActivity = b.last_activity ? new Date(b.last_activity).getTime() : 0;
          return bActivity - aActivity;
        case 'reports':
          return b.total_coverage_reports - a.total_coverage_reports;
        default:
          return 0;
      }
    });

  return (
    <Card>
      <UserAnalyticsTableHeader
        userCount={filteredUsers.length}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        planFilter={planFilter}
        onPlanFilterChange={setPlanFilter}
        sortBy={sortBy}
        onSortChange={setSortBy}
      />
      
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Organization</TableHead>
                <TableHead>Activity</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Last Seen</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <UserAnalyticsTableRow key={user.id} user={user} />
              ))}
            </TableBody>
          </Table>
        </div>
        
        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No users found matching your criteria
          </div>
        )}
      </CardContent>
    </Card>
  );
};
