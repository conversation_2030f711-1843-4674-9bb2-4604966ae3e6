
import React from 'react';
import { CardHeader, CardTitle } from '@/components/ui/card';
import { Eye } from 'lucide-react';
import { UserAnalyticsFilters } from './UserAnalyticsFilters';

interface UserAnalyticsTableHeaderProps {
  userCount: number;
  searchTerm: string;
  onSearchChange: (value: string) => void;
  planFilter: string;
  onPlanFilterChange: (value: string) => void;
  sortBy: 'created_at' | 'last_activity' | 'reports';
  onSortChange: (value: 'created_at' | 'last_activity' | 'reports') => void;
}

export const UserAnalyticsTableHeader: React.FC<UserAnalyticsTableHeaderProps> = ({
  userCount,
  searchTerm,
  onSearchChange,
  planFilter,
  onPlanFilterChange,
  sortBy,
  onSortChange
}) => {
  return (
    <CardHeader>
      <CardTitle className="flex items-center">
        <Eye className="h-5 w-5 mr-2" />
        User Analytics ({userCount} users)
      </CardTitle>
      
      <UserAnalyticsFilters
        searchTerm={searchTerm}
        onSearchChange={onSearchChange}
        planFilter={planFilter}
        onPlanFilterChange={onPlanFilterChange}
        sortBy={sortBy}
        onSortChange={onSortChange}
      />
    </CardHeader>
  );
};
