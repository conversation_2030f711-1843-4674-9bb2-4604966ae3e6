
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, LineChart, Line } from 'recharts';
import { Activity, Zap, Clock, TrendingUp } from 'lucide-react';
import type { SystemUsage } from '@/lib/api/admin-analytics';

interface SystemUsageChartsProps {
  usage: SystemUsage;
}

export const SystemUsageCharts: React.FC<SystemUsageChartsProps> = ({ usage }) => {
  const usageData = [
    { period: 'Today', generations: usage.coverage_generations_today },
    { period: 'This Week', generations: usage.coverage_generations_week },
    { period: 'This Month', generations: usage.coverage_generations_month }
  ];

  const featureUsageData = usage.top_features_used.map(feature => ({
    name: feature.feature,
    value: feature.usage_count
  }));

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'];

  const performanceMetrics = [
    {
      title: 'Coverage Generations',
      value: usage.coverage_generations_today,
      subtitle: 'Today',
      icon: Zap,
      color: 'text-yellow-600'
    },
    {
      title: 'Active Organizations',
      value: usage.active_organizations,
      subtitle: 'With paid plans',
      icon: Activity,
      color: 'text-green-600'
    },
    {
      title: 'Avg Reports per User',
      value: usage.average_reports_per_user,
      subtitle: 'Platform average',
      icon: TrendingUp,
      color: 'text-blue-600'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {performanceMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                <Icon className={`h-4 w-4 ${metric.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <p className="text-xs text-muted-foreground">{metric.subtitle}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Usage Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Coverage Generation Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={usageData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="generations" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Feature Usage Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={featureUsageData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {featureUsageData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Usage Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Detailed Usage Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{usage.coverage_generations_today}</div>
              <div className="text-sm text-muted-foreground">Generations Today</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">{usage.coverage_generations_week}</div>
              <div className="text-sm text-muted-foreground">Generations This Week</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{usage.coverage_generations_month}</div>
              <div className="text-sm text-muted-foreground">Generations This Month</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{usage.active_organizations}</div>
              <div className="text-sm text-muted-foreground">Active Organizations</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
