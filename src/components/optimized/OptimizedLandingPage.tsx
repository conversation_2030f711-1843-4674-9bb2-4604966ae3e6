
import React, { Suspense, lazy } from 'react';
import LoadingSpinner from '@/components/LoadingSpinner';
import Header from '@/components/Header';
import Hero from '@/components/Hero';
import Features from '@/components/Features';
import PopularFAQSection from '@/components/faq/PopularFAQSection';

// Lazy load heavy components
const Pricing = lazy(() => import('@/components/Pricing'));

const OptimizedLandingPage: React.FC = () => {
  const handleViewAllFAQs = () => {
    window.location.href = '/faq';
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <Hero />
      <Features />
      
      {/* Popular FAQ Section */}
      <PopularFAQSection onViewAll={handleViewAllFAQs} />
      
      <Suspense fallback={
        <div className="py-16 flex justify-center">
          <LoadingSpinner size="lg" />
        </div>
      }>
        <Pricing />
      </Suspense>
    </div>
  );
};

export default OptimizedLandingPage;
