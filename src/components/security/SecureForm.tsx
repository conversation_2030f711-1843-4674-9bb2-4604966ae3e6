import React, { FormEvent, ReactNode, useEffect, useState } from 'react';
import { z } from 'zod';
import { useCSRFProtection } from '@/lib/security/csrfProtection';
import { SecurityMiddleware } from '@/lib/security/securityMiddleware';
import { SecurityHeaders } from '@/lib/security/securityHeaders';
import { RATE_LIMITS } from '@/lib/security/rateLimiter';
import { useToast } from '@/hooks/use-toast';

interface SecureFormProps {
  children: ReactNode;
  onSubmit: (data: FormData, csrfToken: string) => Promise<void>;
  rateLimitType?: keyof typeof RATE_LIMITS;
  className?: string;
  validateInput?: boolean;
}

export const SecureForm: React.FC<SecureFormProps> = ({
  children,
  onSubmit,
  rateLimitType = 'API_GENERAL',
  className = '',
  validateInput = true
}) => {
  const { token, validateAndRefreshToken } = useCSRFProtection();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    if (isSubmitting) return;
    
    setIsSubmitting(true);

    try {
      // Check rate limiting
      await SecurityMiddleware.applyRateLimit(rateLimitType);

      if (!token) {
        throw new Error('CSRF token not available');
      }

      // Validate CSRF token
      const isValidToken = await validateAndRefreshToken(token);
      if (!isValidToken) {
        throw new Error('Invalid CSRF token');
      }

      const formData = new FormData(event.currentTarget);
      
      // Validate and sanitize form data if enabled
      if (validateInput) {
        const sanitizedData = new FormData();
        for (const [key, value] of formData.entries()) {
          if (typeof value === 'string') {
            const sanitized = SecurityMiddleware.sanitizeAndValidate(
              value,
              z.string().max(10000) // Basic validation
            );
            
            if (sanitized.success) {
              sanitizedData.append(key, sanitized.data);
            } else {
              throw new Error(`Invalid input for field: ${key}`);
            }
          } else {
            sanitizedData.append(key, value);
          }
        }
        
        await onSubmit(sanitizedData, token);
      } else {
        await onSubmit(formData, token);
      }

      toast({
        title: 'Success',
        description: 'Form submitted successfully',
      });

    } catch (error) {
      console.error('Form submission error:', error);
      
      let errorMessage = 'An error occurred while submitting the form';
      
      if (error instanceof Error) {
        if (error.message.includes('Rate limit')) {
          errorMessage = 'Too many requests. Please try again later.';
        } else if (error.message.includes('CSRF')) {
          errorMessage = 'Security validation failed. Please refresh the page and try again.';
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={className}>
      {/* Hidden CSRF token field */}
      {token && (
        <input type="hidden" name="csrf_token" value={token} />
      )}
      
      {children}
    </form>
  );
};

// Higher-order component for securing existing forms
export const withSecurityProtection = <P extends Record<string, any>>(
  WrappedComponent: React.ComponentType<P>,
  options: {
    rateLimitType?: keyof typeof RATE_LIMITS;
    validateInput?: boolean;
  } = {}
) => {
  const SecurityWrapper: React.FC<P> = (props) => {
    const { token } = useCSRFProtection();

    useEffect(() => {
      // Apply security headers
      SecurityHeaders.injectMetaTags();
    }, []);

    return (
      <div data-csrf-token={token}>
        <WrappedComponent {...props} />
      </div>
    );
  };

  SecurityWrapper.displayName = `withSecurityProtection(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return SecurityWrapper;
};
