
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Shield, CheckCircle, AlertTriangle, Lock, Eye, FileCheck } from 'lucide-react';
import { rateLimiter } from '@/lib/security/rateLimiter';

interface SecurityStatusProps {
  className?: string;
}

export const SecurityStatus: React.FC<SecurityStatusProps> = ({ className }) => {
  const [securityMetrics, setSecurityMetrics] = useState({
    rlsPoliciesActive: true,
    inputValidationActive: true,
    rateLimitingActive: true,
    auditLoggingActive: true,
    lastSecurityCheck: new Date().toISOString(),
  });

  const [rateLimitStatus, setRateLimitStatus] = useState({
    apiRequests: { current: 0, limit: 1000, window: '15 minutes' },
    createOps: { current: 0, limit: 30, window: '1 minute' },
    searches: { current: 0, limit: 60, window: '1 minute' },
  });

  useEffect(() => {
    // Simulate checking security status
    const checkSecurityStatus = () => {
      // In a real implementation, this would check actual security metrics
      setSecurityMetrics(prev => ({
        ...prev,
        lastSecurityCheck: new Date().toISOString(),
      }));
    };

    checkSecurityStatus();
    const interval = setInterval(checkSecurityStatus, 60000); // Check every minute

    return () => clearInterval(interval);
  }, []);

  const securityItems = [
    {
      name: 'Row Level Security',
      status: securityMetrics.rlsPoliciesActive,
      icon: Shield,
      description: 'Database access controls active',
    },
    {
      name: 'Input Validation',
      status: securityMetrics.inputValidationActive,
      icon: FileCheck,
      description: 'Data sanitization and validation enabled',
    },
    {
      name: 'Rate Limiting',
      status: securityMetrics.rateLimitingActive,
      icon: Lock,
      description: 'API abuse protection active',
    },
    {
      name: 'Audit Logging',
      status: securityMetrics.auditLoggingActive,
      icon: Eye,
      description: 'Security events being monitored',
    },
  ];

  const overallSecurityScore = securityItems.filter(item => item.status).length / securityItems.length;
  const securityLevel = overallSecurityScore >= 1 ? 'high' : overallSecurityScore >= 0.75 ? 'medium' : 'low';

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>Security Status</span>
            <Badge 
              variant={securityLevel === 'high' ? 'default' : securityLevel === 'medium' ? 'secondary' : 'destructive'}
            >
              {securityLevel.toUpperCase()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall Security Score */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Security Score</span>
            <span className="text-2xl font-bold text-green-600">
              {Math.round(overallSecurityScore * 100)}%
            </span>
          </div>

          {/* Security Features Status */}
          <div className="space-y-3">
            {securityItems.map((item) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <item.icon className={`h-4 w-4 ${item.status ? 'text-green-500' : 'text-red-500'}`} />
                  <div>
                    <div className="text-sm font-medium">{item.name}</div>
                    <div className="text-xs text-muted-foreground">{item.description}</div>
                  </div>
                </div>
                {item.status ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                )}
              </div>
            ))}
          </div>

          {/* Rate Limiting Status */}
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium mb-2">Rate Limiting Status</h4>
            <div className="space-y-2">
              {Object.entries(rateLimitStatus).map(([key, status]) => (
                <div key={key} className="flex justify-between text-xs">
                  <span className="capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                  <span className="text-muted-foreground">
                    {status.current}/{status.limit} ({status.window})
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Security Alerts */}
          {securityLevel !== 'high' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Some security features may not be fully active. Check your configuration.
              </AlertDescription>
            </Alert>
          )}

          <div className="text-xs text-muted-foreground">
            Last checked: {new Date(securityMetrics.lastSecurityCheck).toLocaleString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
