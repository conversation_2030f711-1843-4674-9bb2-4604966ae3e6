
import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { productionConfig } from '@/utils/productionConfig';

interface SecurityContextType {
  isSecure: boolean;
  enableHeaders: boolean;
  enableLogging: boolean;
}

const SecurityContext = createContext<SecurityContextType>({
  isSecure: false,
  enableHeaders: false,
  enableLogging: false
});

export const useSecurityContext = () => useContext(SecurityContext);

interface SecurityProviderProps {
  children: ReactNode;
  enableHeaders?: boolean;
  enableLogging?: boolean;
}

export const SecurityProvider: React.FC<SecurityProviderProps> = ({
  children,
  enableHeaders = true,
  enableLogging = true
}) => {
  useEffect(() => {
    // Only apply security headers in production or when explicitly enabled
    if (!enableHeaders) return;

    // Mobile-optimized CSP - more permissive for better compatibility
    const meta = document.createElement('meta');
    meta.httpEquiv = 'Content-Security-Policy';
    meta.content = [
      "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://*.supabase.co https://*.lovableproject.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: blob: https://*.supabase.co https://*.lovableproject.com",
      "connect-src 'self' https://*.supabase.co https://*.lovableproject.com wss://*.supabase.co"
    ].join('; ');
    
    // Remove existing CSP meta tag if present
    const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (existingMeta) {
      existingMeta.remove();
    }
    
    // Only add if document head exists
    if (document.head) {
      document.head.appendChild(meta);
    }

    // Mobile-specific viewport settings
    const viewport = document.querySelector('meta[name="viewport"]') || document.createElement('meta');
    viewport.setAttribute('name', 'viewport');
    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover');
    if (document.head && !document.head.contains(viewport)) {
      document.head.appendChild(viewport);
    }

    return () => {
      if (document.head && document.head.contains(meta)) {
        document.head.removeChild(meta);
      }
    };
  }, [enableHeaders]);

  const contextValue = React.useMemo(() => ({
    isSecure: true,
    enableHeaders,
    enableLogging
  }), [enableHeaders, enableLogging]);

  return (
    <SecurityContext.Provider value={contextValue}>
      {children}
    </SecurityContext.Provider>
  );
};
