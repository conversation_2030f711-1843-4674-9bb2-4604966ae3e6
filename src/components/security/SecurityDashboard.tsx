
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Key, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Activity,
  Lock,
  Unlock,
  RefreshCw
} from 'lucide-react';
import { useApiKeyRotation } from '@/lib/security/apiKeyRotation';
import { useAuditLogging } from '@/lib/security/auditLogger';
import { useOrganization } from '@/contexts/OrganizationContext';

interface SecurityMetrics {
  overallScore: number;
  lastAudit: string;
  apiKeysStatus: 'healthy' | 'warning' | 'critical';
  rateLimitStatus: 'normal' | 'elevated' | 'critical';
  auditLogStatus: 'active' | 'inactive';
}

interface ApiKeyStatus {
  keyName: string;
  needsRotation: boolean;
  daysUntilExpiration: number;
  lastUsed?: string;
}

export const SecurityDashboard: React.FC = () => {
  const { currentOrganization } = useOrganization();
  const { logAccess } = useAuditLogging();
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics>({
    overallScore: 85,
    lastAudit: new Date().toISOString(),
    apiKeysStatus: 'healthy',
    rateLimitStatus: 'normal',
    auditLogStatus: 'active'
  });
  const [apiKeys, setApiKeys] = useState<ApiKeyStatus[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentOrganization) {
      logAccess('security-dashboard', 'view');
      loadSecurityStatus();
    }
  }, [currentOrganization]);

  const loadSecurityStatus = async () => {
    setLoading(true);
    try {
      // Check API key status
      const keyNames = ['OPENAI_API_KEY', 'STRIPE_SECRET_KEY'];
      const keyStatuses = await Promise.all(
        keyNames.map(async (keyName) => {
          const { checkRotationStatus } = useApiKeyRotation(keyName);
          const status = await checkRotationStatus();
          return {
            keyName,
            needsRotation: status.needsRotation,
            daysUntilExpiration: status.daysUntilExpiration,
            lastUsed: status.keyInfo?.lastUsed
          };
        })
      );
      
      setApiKeys(keyStatuses);
      
      // Update overall metrics based on key status
      const criticalKeys = keyStatuses.filter(k => k.needsRotation && k.daysUntilExpiration <= 0);
      const warningKeys = keyStatuses.filter(k => k.needsRotation && k.daysUntilExpiration <= 7);
      
      setSecurityMetrics(prev => ({
        ...prev,
        apiKeysStatus: criticalKeys.length > 0 ? 'critical' : warningKeys.length > 0 ? 'warning' : 'healthy'
      }));
    } catch (error) {
      console.error('Failed to load security status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRotateKey = async (keyName: string) => {
    const { rotateKey } = useApiKeyRotation(keyName);
    setLoading(true);
    try {
      const result = await rotateKey();
      if (result.success) {
        await loadSecurityStatus();
      }
    } catch (error) {
      console.error('Failed to rotate key:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'normal':
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'warning':
      case 'elevated':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
      case 'inactive':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Security Dashboard</h1>
        <p className="text-muted-foreground">
          Monitor and manage your organization's security posture
        </p>
      </div>

      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Security Score</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(securityMetrics.overallScore)}`}>
              {securityMetrics.overallScore}%
            </div>
            <Progress value={securityMetrics.overallScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Keys</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getStatusColor(securityMetrics.apiKeysStatus)}>
              {securityMetrics.apiKeysStatus}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              {apiKeys.length} keys monitored
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rate Limiting</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getStatusColor(securityMetrics.rateLimitStatus)}>
              {securityMetrics.rateLimitStatus}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              Active protection
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Audit Logging</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getStatusColor(securityMetrics.auditLogStatus)}>
              {securityMetrics.auditLogStatus}
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              All actions logged
            </p>
          </CardContent>
        </Card>
      </div>

      {/* API Key Management */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Key className="h-5 w-5 mr-2" />
            API Key Management
          </CardTitle>
          <CardDescription>
            Monitor and rotate API keys to maintain security
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {apiKeys.map((key) => (
              <div key={key.keyName} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    {key.needsRotation ? (
                      <Unlock className="h-5 w-5 text-red-500 mr-2" />
                    ) : (
                      <Lock className="h-5 w-5 text-green-500 mr-2" />
                    )}
                    <div>
                      <p className="font-medium">{key.keyName}</p>
                      <p className="text-sm text-muted-foreground">
                        {key.needsRotation 
                          ? `Expires in ${key.daysUntilExpiration} days`
                          : 'Healthy'
                        }
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {key.needsRotation && (
                    <Alert className="mr-4">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Rotation recommended
                      </AlertDescription>
                    </Alert>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRotateKey(key.keyName)}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Rotate
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Security Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Security Recommendations
          </CardTitle>
          <CardDescription>
            Actions to improve your security posture
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-4 bg-blue-50 rounded-lg">
              <CheckCircle className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium text-blue-900">Enable Two-Factor Authentication</p>
                <p className="text-sm text-blue-700">
                  Add an extra layer of security to your account
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-yellow-50 rounded-lg">
              <Clock className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div>
                <p className="font-medium text-yellow-900">Review Access Permissions</p>
                <p className="text-sm text-yellow-700">
                  Audit team member permissions and remove unused access
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
              <Shield className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="font-medium text-green-900">Security Audit Completed</p>
                <p className="text-sm text-green-700">
                  Your last security audit was completed successfully
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
