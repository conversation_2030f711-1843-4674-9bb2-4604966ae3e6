
import React, { useEffect } from 'react';
import { productionConfig } from '@/utils/productionConfig';

const SecurityHeaders: React.FC = () => {
  useEffect(() => {
    // Add HSTS header suggestion for production
    if (productionConfig.isProduction() && location.protocol === 'https:') {
      // Log HSTS recommendation for server configuration
      console.info('🔒 HSTS recommended: Add "Strict-Transport-Security: max-age=31536000; includeSubDomains" header');
    }

    // Set security-related meta tags
    const securityMeta = [
      {
        name: 'referrer',
        content: 'strict-origin-when-cross-origin'
      },
      {
        name: 'permissions-policy',
        content: 'camera=(), microphone=(), geolocation=()'
      }
    ];

    securityMeta.forEach(meta => {
      const existing = document.querySelector(`meta[name="${meta.name}"]`);
      if (!existing) {
        const metaElement = document.createElement('meta');
        metaElement.name = meta.name;
        metaElement.content = meta.content;
        document.head.appendChild(metaElement);
      }
    });
  }, []);

  return null; // This is a utility component with no render
};

export default SecurityHeaders;
