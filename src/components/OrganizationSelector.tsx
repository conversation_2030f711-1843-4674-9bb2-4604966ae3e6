
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Building2, Plus } from 'lucide-react';
import { useOrganization } from '@/contexts/OrganizationContext';

const OrganizationSelector = () => {
  const { currentOrganization, organizations, switchOrganization } = useOrganization();

  return (
    <div className="flex items-center gap-2 w-full sm:w-auto">
      <Building2 className="w-4 h-4 text-muted-foreground flex-shrink-0" />
      <Select
        value={currentOrganization?.id || ''}
        onValueChange={switchOrganization}
      >
        <SelectTrigger className="w-full sm:w-48 bg-cinema-800 border-cinema-700 min-w-0">
          <SelectValue placeholder="Select organization" />
        </SelectTrigger>
        <SelectContent className="bg-cinema-800 border-cinema-700 w-full sm:w-48">
          {organizations.map((org) => (
            <SelectItem key={org.id} value={org.id} className="hover:bg-cinema-700">
              <div className="flex flex-col min-w-0 w-full">
                <span className="font-medium truncate">{org.name}</span>
                <span className="text-xs text-muted-foreground capitalize truncate">
                  {org.plan} plan
                </span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Button variant="outline" size="sm" className="border-cinema-700 flex-shrink-0">
        <Plus className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default OrganizationSelector;
