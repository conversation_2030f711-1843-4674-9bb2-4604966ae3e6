
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Send, CheckCircle, MoreVertical, Trash2, Edit } from 'lucide-react';
import { useStoryboardComments } from '@/hooks/useStoryboardComments';
import { formatDistanceToNow } from 'date-fns';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface StoryboardCommentsPanelProps {
  panelId: string;
  orgId: string;
  className?: string;
}

const StoryboardCommentsPanel: React.FC<StoryboardCommentsPanelProps> = ({
  panelId,
  orgId,
  className
}) => {
  const { comments, loading, creating, createComment, updateComment, deleteComment } = useStoryboardComments(panelId);
  const [newComment, setNewComment] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    const result = await createComment({
      panel_id: panelId,
      org_id: orgId,
      content: newComment.trim()
    });

    if (result) {
      setNewComment('');
    }
  };

  const handleEditComment = (comment: any) => {
    setEditingId(comment.id);
    setEditContent(comment.content);
  };

  const handleSaveEdit = async () => {
    if (!editingId || !editContent.trim()) return;

    await updateComment(editingId, { content: editContent.trim() });
    setEditingId(null);
    setEditContent('');
  };

  const handleToggleResolved = async (comment: any) => {
    await updateComment(comment.id, { resolved: !comment.resolved });
  };

  const handleDeleteComment = async (commentId: string) => {
    await deleteComment(commentId);
  };

  const unresolvedCount = comments.filter(c => !c.resolved).length;

  return (
    <Card className={`p-4 space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <MessageSquare className="h-4 w-4" />
          <h3 className="font-medium">Comments</h3>
          {unresolvedCount > 0 && (
            <Badge variant="destructive" className="text-xs">
              {unresolvedCount} unresolved
            </Badge>
          )}
        </div>
        <Badge variant="outline" className="text-xs">
          {comments.length} total
        </Badge>
      </div>

      {/* Comments List */}
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {loading ? (
          <div className="text-center text-muted-foreground text-sm">Loading comments...</div>
        ) : comments.length === 0 ? (
          <div className="text-center text-muted-foreground text-sm">No comments yet</div>
        ) : (
          comments.map((comment) => (
            <div 
              key={comment.id} 
              className={`p-3 rounded-lg border transition-colors ${
                comment.resolved 
                  ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                  : 'bg-muted/50'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="font-medium text-sm">
                    {comment.profiles?.full_name || 'Unknown User'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                  </span>
                  {comment.resolved && (
                    <Badge variant="outline" className="text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Resolved
                    </Badge>
                  )}
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                      <MoreVertical className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditComment(comment)}>
                      <Edit className="h-3 w-3 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleToggleResolved(comment)}>
                      <CheckCircle className="h-3 w-3 mr-2" />
                      {comment.resolved ? 'Mark Unresolved' : 'Mark Resolved'}
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleDeleteComment(comment.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-3 w-3 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              {editingId === comment.id ? (
                <div className="space-y-2">
                  <Textarea
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    className="text-sm"
                    rows={2}
                  />
                  <div className="flex space-x-2">
                    <Button size="sm" onClick={handleSaveEdit}>Save</Button>
                    <Button size="sm" variant="outline" onClick={() => setEditingId(null)}>Cancel</Button>
                  </div>
                </div>
              ) : (
                <p className="text-sm text-foreground leading-relaxed">
                  {comment.content}
                </p>
              )}
            </div>
          ))
        )}
      </div>

      {/* Add Comment */}
      <div className="space-y-2 pt-2 border-t">
        <Textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="text-sm"
          rows={2}
        />
        <Button 
          onClick={handleSubmitComment}
          disabled={!newComment.trim() || creating}
          size="sm"
          className="w-full"
        >
          <Send className="h-3 w-3 mr-2" />
          {creating ? 'Adding...' : 'Add Comment'}
        </Button>
      </div>
    </Card>
  );
};

export default StoryboardCommentsPanel;
