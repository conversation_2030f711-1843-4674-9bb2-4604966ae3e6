
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Plus, 
  Download, 
  Search, 
  FileText, 
  BarChart3,
  Settings
} from 'lucide-react';
import StoryboardTemplatesDialog from './StoryboardTemplatesDialog';

interface StoryboardStudioHeaderProps {
  selectedStoryboard: any;
  exportStoryboard: () => void;
  showCreateForm: boolean;
  setShowCreateForm: (show: boolean) => void;
  usage?: { dailyLimit: number; remaining: number };
  usageLoading?: boolean;
  onSearch?: (query: string) => void;
  onUseTemplate?: (templateData: any) => void;
  orgId?: string;
}

const StoryboardStudioHeader: React.FC<StoryboardStudioHeaderProps> = ({
  selectedStoryboard,
  exportStoryboard,
  showCreateForm,
  setShowCreateForm,
  usage,
  usageLoading,
  onSearch,
  onUseTemplate,
  orgId
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    onSearch?.(query);
  };

  return (
    <div className="space-y-4">
      {/* Main Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <h1 className="text-xl md:text-2xl font-playfair font-bold">
            Storyboard Studio
          </h1>
          {usage && (
            <Badge variant={usage.remaining > 0 ? "secondary" : "destructive"}>
              {usageLoading ? "..." : `${usage.remaining}/${usage.dailyLimit} left`}
            </Badge>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {orgId && onUseTemplate && (
            <StoryboardTemplatesDialog
              orgId={orgId}
              currentStoryboard={selectedStoryboard}
              onUseTemplate={onUseTemplate}
            />
          )}
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCreateForm(!showCreateForm)}
          >
            <Plus className="h-4 w-4 mr-2" />
            New Storyboard
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={exportStoryboard}
            disabled={!selectedStoryboard}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      {onSearch && (
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            value={searchQuery}
            onChange={handleSearch}
            placeholder="Search storyboards and panels..."
            className="pl-10"
          />
        </div>
      )}

      {/* Selected Storyboard Info */}
      {selectedStoryboard && (
        <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
          <div>
            <h3 className="font-medium">{selectedStoryboard.title}</h3>
            {selectedStoryboard.description && (
              <p className="text-sm text-muted-foreground">
                {selectedStoryboard.description}
              </p>
            )}
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <BarChart3 className="h-4 w-4" />
              <span>{selectedStoryboard.fidelity}</span>
            </div>
            {selectedStoryboard.panels && (
              <Badge variant="outline">
                {selectedStoryboard.panels.length} panels
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default StoryboardStudioHeader;
