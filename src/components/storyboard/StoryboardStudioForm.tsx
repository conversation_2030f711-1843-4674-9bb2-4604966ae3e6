
import React from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Sparkles, Edit, Palette, Camera } from 'lucide-react';

interface StoryboardStudioFormProps {
  showCreateForm: boolean;
  newStoryboard: {
    title: string;
    description: string;
    fidelity: string;
  };
  loading: boolean;
  onStoryboardChange: (field: string, value: string) => void;
  onCreateStoryboard: () => void;
  onCancel: () => void;
}

const fidelityOptions = [
  { 
    value: 'Sketch', 
    label: 'Sketch', 
    description: 'Rough pencil-style frames for shot planning',
    icon: Edit,
    color: 'bg-gray-500/20 text-gray-300'
  },
  { 
    value: 'Illustrated', 
    label: 'Illustrated', 
    description: 'Flat color illustrations for pitches',
    icon: Palette,
    color: 'bg-blue-500/20 text-blue-300'
  },
  { 
    value: 'Cinematic', 
    label: 'Cinematic', 
    description: 'High-fidelity visual boards with atmospheric effects',
    icon: Camera,
    color: 'bg-purple-500/20 text-purple-300'
  }
];

const StoryboardStudioForm: React.FC<StoryboardStudioFormProps> = ({
  showCreateForm,
  newStoryboard,
  loading,
  onStoryboardChange,
  onCreateStoryboard,
  onCancel
}) => {
  if (!showCreateForm) return null;

  return (
    <Card className="p-4 border-cinema-700">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Create New Storyboard</h3>
        
        <div>
          <Label htmlFor="storyboard-title">Title</Label>
          <Input
            id="storyboard-title"
            value={newStoryboard.title}
            onChange={(e) => onStoryboardChange('title', e.target.value)}
            placeholder="Enter storyboard title..."
            className="bg-cinema-800 border-cinema-700"
          />
        </div>
        
        <div>
          <Label htmlFor="storyboard-description">Description</Label>
          <Textarea
            id="storyboard-description"
            value={newStoryboard.description}
            onChange={(e) => onStoryboardChange('description', e.target.value)}
            placeholder="Describe your storyboard..."
            className="bg-cinema-800 border-cinema-700"
            rows={3}
          />
        </div>
        
        <div>
          <Label className="text-sm font-medium mb-2 block">Visual Style</Label>
          <div className="flex flex-wrap gap-2">
            {fidelityOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <Badge
                  key={option.value}
                  className={cn(
                    "cursor-pointer transition-all hover:scale-105 flex items-center space-x-1",
                    newStoryboard.fidelity === option.value 
                      ? option.color 
                      : "bg-cinema-700 text-gray-300 hover:bg-cinema-600"
                  )}
                  onClick={() => onStoryboardChange('fidelity', option.value)}
                >
                  <IconComponent className="h-3 w-3" />
                  <span>{option.label}</span>
                </Badge>
              );
            })}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {fidelityOptions.find(opt => opt.value === newStoryboard.fidelity)?.description}
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button onClick={onCreateStoryboard} disabled={loading || !newStoryboard.title.trim()}>
            <Sparkles className="h-4 w-4 mr-2" />
            Create Storyboard
          </Button>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default StoryboardStudioForm;
