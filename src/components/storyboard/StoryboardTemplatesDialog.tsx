import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { FileText, Plus, Eye, Download, Trash2 } from 'lucide-react';
import { useStoryboardTemplates } from '@/hooks/useStoryboardTemplates';
import { formatDistanceToNow } from 'date-fns';

interface StoryboardTemplatesDialogProps {
  orgId: string;
  currentStoryboard?: any;
  onUseTemplate: (templateData: any) => void;
  trigger?: React.ReactNode;
}

const StoryboardTemplatesDialog: React.FC<StoryboardTemplatesDialogProps> = ({
  orgId,
  currentStoryboard,
  onUseTemplate,
  trigger
}) => {
  const { templates, loading, creating, createTemplate, deleteTemplate, useTemplate } = useStoryboardTemplates(orgId);
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('browse');
  
  // Create Template Form State
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [isPublic, setIsPublic] = useState(false);

  const handleCreateTemplate = async () => {
    if (!templateName.trim() || !currentStoryboard) return;

    const templateData = {
      storyboard: {
        title: currentStoryboard.title,
        description: currentStoryboard.description,
        fidelity: currentStoryboard.fidelity
      },
      panels: currentStoryboard.panels || []
    };

    const result = await createTemplate({
      org_id: orgId,
      name: templateName.trim(),
      description: templateDescription.trim() || undefined,
      is_public: isPublic,
      template_data: templateData
    });

    if (result) {
      setTemplateName('');
      setTemplateDescription('');
      setIsPublic(false);
      setActiveTab('browse');
    }
  };

  const handleUseTemplate = async (template: any) => {
    const templateData = await useTemplate(template);
    if (templateData) {
      onUseTemplate(templateData);
      setOpen(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <FileText className="h-4 w-4 mr-2" />
      Templates
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Storyboard Templates</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="browse">Browse Templates</TabsTrigger>
            <TabsTrigger value="create" disabled={!currentStoryboard}>
              Create Template
            </TabsTrigger>
          </TabsList>

          <TabsContent value="browse" className="space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-pulse">Loading templates...</div>
              </div>
            ) : templates.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No templates available</p>
                <p className="text-sm">Create your first template to get started</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <Card key={template.id} className="p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium">{template.name}</h3>
                        {template.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {template.description}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleUseTemplate(template)}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Use
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => deleteTemplate(template.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <div className="flex items-center space-x-2">
                        <span>By {template.profiles?.full_name || 'Unknown'}</span>
                        {template.is_public && (
                          <Badge variant="secondary" className="text-xs">Public</Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span>{template.usage_count} uses</span>
                        <span>•</span>
                        <span>{formatDistanceToNow(new Date(template.created_at), { addSuffix: true })}</span>
                      </div>
                    </div>

                    {template.template_data?.panels && (
                      <div className="text-xs text-muted-foreground">
                        {template.template_data.panels.length} panels
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="create" className="space-y-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name..."
                />
              </div>

              <div>
                <Label htmlFor="template-description">Description (Optional)</Label>
                <Textarea
                  id="template-description"
                  value={templateDescription}
                  onChange={(e) => setTemplateDescription(e.target.value)}
                  placeholder="Describe what this template is for..."
                  rows={3}
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="public-template"
                  checked={isPublic}
                  onCheckedChange={setIsPublic}
                />
                <Label htmlFor="public-template">
                  Make this template public (visible to all users)
                </Label>
              </div>

              {currentStoryboard && (
                <Card className="p-4 bg-muted/50">
                  <h4 className="font-medium mb-2">Template Preview</h4>
                  <div className="text-sm text-muted-foreground space-y-1">
                    <p><strong>Storyboard:</strong> {currentStoryboard.title}</p>
                    <p><strong>Panels:</strong> {currentStoryboard.panels?.length || 0}</p>
                    <p><strong>Fidelity:</strong> {currentStoryboard.fidelity}</p>
                  </div>
                </Card>
              )}

              <Button
                onClick={handleCreateTemplate}
                disabled={!templateName.trim() || creating}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                {creating ? 'Creating...' : 'Create Template'}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default StoryboardTemplatesDialog;
