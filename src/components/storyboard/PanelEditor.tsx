
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ArrowUp, ArrowDown, Trash2, Image, MessageSquare, Sparkles } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import type { StoryboardPanel } from '@/lib/api/storyboards';
import StoryboardCommentsPanel from './StoryboardCommentsPanel';

interface PanelEditorProps {
  panel: StoryboardPanel;
  index: number;
  totalPanels: number;
  onUpdateDialogue: (panelId: string, dialogue: string) => void;
  onMoveUp: (index: number) => void;
  onMoveDown: (index: number) => void;
  onDelete: (panelId: string) => void;
  orgId?: string;
  remaining?: number;
  dailyLimit?: number;
}

const PanelEditor: React.FC<PanelEditorProps> = ({
  panel,
  index,
  totalPanels,
  onUpdateDialogue,
  onMoveUp,
  onMoveDown,
  onDelete,
  orgId,
  remaining,
  dailyLimit
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState(panel.image_url);

  const handleAIGenerate = async () => {
    setErrorMsg(null);
    setLoading(true);
    try {
      const prompt = panel.dialogue && panel.dialogue.trim().length > 0
        ? panel.dialogue.slice(0, 300)
        : `a shot description or dialogue for a storyboard panel`;

      const { data, error } = (window as any).supabase.auth.getSession
        ? await (window as any).supabase.auth.getSession()
        : { data: { session: null }, error: null };

      let accessToken = data?.session?.access_token;

      if (!orgId) {
        throw new Error('Missing orgId for quota management.');
      }

      const res = await fetch("/functions/v1/ai-generate-storyboard-image", {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          ...(accessToken ? { Authorization: `Bearer ${accessToken}` } : {})
        },
        body: JSON.stringify({ prompt, style: (panel as any)?.fidelity || 'Sketch', org_id: orgId })
      });

      const dataRes = await res.json();

      if (res.status === 429) {
        setErrorMsg(dataRes.error || 'Daily quota reached.');
        toast({
          title: "Quota Exceeded",
          description: (dataRes.error || "You have used up your daily storyboard generations."),
          variant: "destructive"
        });
        return;
      } else if (!res.ok || dataRes.error) {
        throw new Error(dataRes.error || "Image generation failed");
      }

      setImageUrl(dataRes.image);
      toast({
        title: "AI Art Generated!",
        description: dataRes.remaining_generations !== undefined
          ? `Image generated. ${dataRes.remaining_generations} generations left today.`
          : "The panel image was created with AI."
      });

    } catch (err: any) {
      setErrorMsg(typeof err === "string" ? err : err?.message || "Failed to generate image");
      toast({
        title: "Generation Failed",
        description: typeof err === "string" ? err : err?.message || "Failed to generate image",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="p-4 space-y-3">
      <div className="flex items-center justify-between">
        <Badge variant="outline" className="text-xs">
          Panel {index + 1}
        </Badge>
        <div className="flex space-x-1">
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-6 w-6 p-0"
            onClick={() => onMoveUp(index)}
            disabled={index === 0}
          >
            <ArrowUp className="h-3 w-3" />
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-6 w-6 p-0"
            onClick={() => onMoveDown(index)}
            disabled={index === totalPanels - 1}
          >
            <ArrowDown className="h-3 w-3" />
          </Button>
          <Button 
            size="sm" 
            variant="ghost" 
            className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
            onClick={() => onDelete(panel.id)}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {dailyLimit !== undefined && remaining !== undefined && (
        <div className="mb-2 text-xs flex items-center gap-2">
          <Badge variant="secondary">
            {remaining > 0
              ? `${remaining}/${dailyLimit} generations left today`
              : `Quota reached (${dailyLimit}/day)`}
          </Badge>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="panel" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="panel">Panel</TabsTrigger>
          <TabsTrigger value="comments">
            <MessageSquare className="h-3 w-3 mr-1" />
            Comments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="panel" className="space-y-3">
          {/* Image Placeholder */}
          <div className="aspect-video bg-cinema-800 rounded-lg flex items-center justify-center border border-cinema-700 group hover:border-cinema-600 transition-colors relative">
            {imageUrl ? (
              <img 
                src={imageUrl} 
                alt={`Panel ${index + 1}`} 
                className="w-full h-full object-cover rounded-lg"
                style={{ minHeight: 120 }}
              />
            ) : (
              <div className="text-center text-muted-foreground w-full">
                <Image className="h-8 w-8 mx-auto mb-2 opacity-50 group-hover:opacity-70 transition-opacity" />
                <p className="text-xs">Generate Image</p>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className={`text-xs mt-1 opacity-0 group-hover:opacity-100 transition-opacity`}
                  onClick={handleAIGenerate}
                  disabled={loading || (remaining !== undefined && remaining <= 0)}
                >
                  {loading
                    ? (<><Sparkles className="h-3 w-3 mr-1 animate-spin" /> Generating...</>)
                    : (<><Sparkles className="h-3 w-3 mr-1" /> AI Generate</>)
                  }
                </Button>
                {errorMsg && (
                  <div className="text-red-500 text-xs mt-2">{errorMsg}</div>
                )}
              </div>
            )}
          </div>

          {/* Dialogue */}
          <div className="space-y-2">
            <Label htmlFor={`dialogue-${panel.id}`} className="text-xs flex items-center">
              <MessageSquare className="h-3 w-3 mr-1" />
              Dialogue
            </Label>
            <Textarea
              id={`dialogue-${panel.id}`}
              value={panel.dialogue || ''}
              onChange={(e) => onUpdateDialogue(panel.id, e.target.value)}
              placeholder="Panel dialogue or notes..."
              className="bg-cinema-800 border-cinema-700 text-xs"
              rows={2}
            />
          </div>
        </TabsContent>

        <TabsContent value="comments">
          {orgId && (
            <StoryboardCommentsPanel 
              panelId={panel.id}
              orgId={orgId}
            />
          )}
        </TabsContent>
      </Tabs>
    </Card>
  );
};

export default PanelEditor;
