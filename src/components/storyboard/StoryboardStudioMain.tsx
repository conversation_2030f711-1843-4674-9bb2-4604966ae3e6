
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import StoryboardStudioHeader from './StoryboardStudioHeader';
import StoryboardStudioForm from './StoryboardStudioForm';
import StoryboardList from './StoryboardList';
import StoryboardEditor from './StoryboardEditor';
import { useStoryboardStudio } from '@/hooks/useStoryboardStudio';
import { storyboardsApi } from '@/lib/api/storyboards';

interface StoryboardStudioMainProps {
  className?: string;
}

const StoryboardStudioMain: React.FC<StoryboardStudioMainProps> = ({ className }) => {
  const studio = useStoryboardStudio();
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredStoryboards, setFilteredStoryboards] = useState(studio.storyboards);

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    
    if (!studio.currentOrganization) return;
    
    if (!query.trim()) {
      setFilteredStoryboards(studio.storyboards);
      return;
    }

    try {
      const result = await storyboardsApi.searchStoryboards(studio.currentOrganization.id, query);
      if (result.success && result.data) {
        setFilteredStoryboards(result.data);
      }
    } catch (error) {
      console.error('Search failed:', error);
      setFilteredStoryboards(studio.storyboards);
    }
  };

  const handleUseTemplate = async (templateData: any) => {
    if (!studio.currentOrganization || !templateData) return;

    try {
      // Create new storyboard from template using the API directly
      const storyboardData = {
        org_id: studio.currentOrganization.id,
        title: `${templateData.storyboard.title} (From Template)`,
        description: templateData.storyboard.description,
        fidelity: templateData.storyboard.fidelity
      };

      const result = await storyboardsApi.createStoryboard(storyboardData);

      if (result.success && result.data && templateData.panels) {
        const newStoryboard = result.data;
        
        // Create panels from template
        for (let i = 0; i < templateData.panels.length; i++) {
          const panelData = templateData.panels[i];
          await storyboardsApi.createStoryboardPanel({
            storyboard_id: newStoryboard.id,
            org_id: studio.currentOrganization.id,
            dialogue: panelData.dialogue,
            order_index: i,
            image_url: panelData.image_url,
            feedback: panelData.feedback
          });
        }
        
        // Refresh the storyboards list and select the new one
        studio.refetch();
        studio.selectStoryboard(newStoryboard);
      }
    } catch (error) {
      console.error('Failed to use template:', error);
    }
  };

  // Update filtered storyboards when main storyboards change
  React.useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredStoryboards(studio.storyboards);
    }
  }, [studio.storyboards, searchQuery]);

  if (!studio.currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground text-center px-4">Please select an organization to use Storyboard Studio</p>
      </div>
    );
  }

  return (
    <Card className={cn("cinema-card p-3 md:p-6", className)}>
      <div className="space-y-4 md:space-y-6">
        {/* Enhanced Header */}
        <StoryboardStudioHeader
          selectedStoryboard={studio.selectedStoryboard}
          exportStoryboard={studio.exportStoryboard}
          showCreateForm={studio.showCreateForm}
          setShowCreateForm={studio.setShowCreateForm}
          usage={studio.usage}
          usageLoading={studio.usageLoading}
          onSearch={handleSearch}
          onUseTemplate={handleUseTemplate}
          orgId={studio.currentOrganization.id}
        />

        {/* Create Form */}
        <StoryboardStudioForm
          showCreateForm={studio.showCreateForm}
          newStoryboard={studio.newStoryboard}
          loading={studio.loading}
          onStoryboardChange={studio.handleStoryboardChange}
          onCreateStoryboard={studio.createStoryboard}
          onCancel={() => studio.setShowCreateForm(false)}
        />

        <div className={cn(
          "grid gap-4 md:gap-6",
          isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-3"
        )}>
          {/* Storyboards List with Search Results */}
          <div className={isMobile ? "order-2" : ""}>
            <StoryboardList
              storyboards={searchQuery.trim() ? filteredStoryboards : studio.storyboards}
              selectedStoryboard={studio.selectedStoryboard}
              loading={studio.loading}
              onSelectStoryboard={studio.selectStoryboard}
            />
          </div>

          {/* Enhanced Storyboard Editor */}
          <div className={cn(
            isMobile ? "order-1" : "lg:col-span-2"
          )}>
            <StoryboardEditor
              selectedStoryboard={studio.selectedStoryboard}
              panels={studio.panels}
              onAddPanel={studio.addPanel}
              onUpdatePanelDialogue={studio.updatePanelDialogue}
              onMovePanelUp={studio.movePanelUp}
              onMovePanelDown={studio.movePanelDown}
              onDeletePanel={studio.deletePanel}
              orgId={studio.currentOrganization.id}
              usage={studio.usage}
            />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StoryboardStudioMain;
