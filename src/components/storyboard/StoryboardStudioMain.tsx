
import React, { useState, memo, useCallback, useMemo, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import StoryboardStudioHeader from './StoryboardStudioHeader';
import StoryboardStudioForm from './StoryboardStudioForm';
import StoryboardList from './StoryboardList';
import StoryboardEditor from './StoryboardEditor';
import { useStoryboardStudio } from '@/hooks/useStoryboardStudio';
import { storyboardsApi } from '@/lib/api/storyboards';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

interface StoryboardStudioMainProps {
  className?: string;
}

const StoryboardStudioMain: React.FC<StoryboardStudioMainProps> = memo(({ className }) => {
  const studio = useStoryboardStudio();
  const isMobile = useIsMobile();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredStoryboards, setFilteredStoryboards] = useState(studio.storyboards);
  const [isSearching, setIsSearching] = useState(false);
  const { useDebounce, useDeepCompareMemo } = usePerformanceOptimization();

  // Debounce search query to prevent excessive API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  // Memoize storyboards to prevent unnecessary re-renders
  const memoizedStoryboards = useDeepCompareMemo(
    () => studio.storyboards,
    [studio.storyboards]
  );

  // Memoized search function
  const performSearch = useCallback(async (query: string) => {
    if (!studio.currentOrganization) return;

    if (!query.trim()) {
      setFilteredStoryboards(memoizedStoryboards);
      return;
    }

    setIsSearching(true);
    try {
      const result = await storyboardsApi.searchStoryboards(studio.currentOrganization.id, query);
      if (result.success && result.data) {
        setFilteredStoryboards(result.data);
      }
    } catch (error) {
      console.error('Search failed:', error);
      setFilteredStoryboards(memoizedStoryboards);
    } finally {
      setIsSearching(false);
    }
  }, [studio.currentOrganization, memoizedStoryboards]);

  // Effect to handle debounced search
  useEffect(() => {
    performSearch(debouncedSearchQuery);
  }, [debouncedSearchQuery, performSearch]);

  // Memoized search handler for immediate UI updates
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Memoized template handler
  const handleUseTemplate = useCallback(async (templateData: any) => {
    if (!studio.currentOrganization || !templateData) return;

    try {
      // Create new storyboard from template using the API directly
      const storyboardData = {
        org_id: studio.currentOrganization.id,
        title: `${templateData.storyboard.title} (From Template)`,
        description: templateData.storyboard.description,
        fidelity: templateData.storyboard.fidelity
      };

      const result = await storyboardsApi.createStoryboard(storyboardData);

      if (result.success && result.data && templateData.panels) {
        const newStoryboard = result.data;

        // Create panels from template in parallel for better performance
        const panelPromises = templateData.panels.map((panelData: any, i: number) =>
          storyboardsApi.createStoryboardPanel({
            storyboard_id: newStoryboard.id,
            org_id: studio.currentOrganization!.id,
            dialogue: panelData.dialogue,
            order_index: i,
            image_url: panelData.image_url,
            feedback: panelData.feedback
          })
        );

        await Promise.all(panelPromises);

        // Refresh the storyboards list and select the new one
        studio.refetch();
        studio.selectStoryboard(newStoryboard);
      }
    } catch (error) {
      console.error('Failed to use template:', error);
    }
  }, [studio]);

  // Update filtered storyboards when main storyboards change (only when not searching)
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredStoryboards(memoizedStoryboards);
    }
  }, [memoizedStoryboards, searchQuery]);

  // Memoized organization check
  const hasOrganization = useMemo(() => {
    return Boolean(studio.currentOrganization);
  }, [studio.currentOrganization]);

  if (!hasOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground text-center px-4">Please select an organization to use Storyboard Studio</p>
      </div>
    );
  }

  // Memoized storyboards for display
  const displayStoryboards = useMemo(() => {
    return searchQuery.trim() ? filteredStoryboards : memoizedStoryboards;
  }, [searchQuery, filteredStoryboards, memoizedStoryboards]);

  return (
    <Card className={cn("cinema-card p-3 md:p-6", className)}>
      <div className="space-y-4 md:space-y-6">
        {/* Enhanced Header */}
        <StoryboardStudioHeader
          selectedStoryboard={studio.selectedStoryboard}
          exportStoryboard={studio.exportStoryboard}
          showCreateForm={studio.showCreateForm}
          setShowCreateForm={studio.setShowCreateForm}
          usage={studio.usage}
          usageLoading={studio.usageLoading}
          onSearch={handleSearch}
          onUseTemplate={handleUseTemplate}
          orgId={studio.currentOrganization!.id}
          isSearching={isSearching}
        />

        {/* Create Form */}
        <StoryboardStudioForm
          showCreateForm={studio.showCreateForm}
          newStoryboard={studio.newStoryboard}
          loading={studio.loading}
          onStoryboardChange={studio.handleStoryboardChange}
          onCreateStoryboard={studio.createStoryboard}
          onCancel={() => studio.setShowCreateForm(false)}
        />

        <div className={cn(
          "grid gap-4 md:gap-6",
          isMobile ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-3"
        )}>
          {/* Storyboards List with Search Results */}
          <div className={isMobile ? "order-2" : ""}>
            <StoryboardList
              storyboards={displayStoryboards}
              selectedStoryboard={studio.selectedStoryboard}
              loading={studio.loading || isSearching}
              onSelectStoryboard={studio.selectStoryboard}
            />
          </div>

          {/* Enhanced Storyboard Editor */}
          <div className={cn(
            isMobile ? "order-1" : "lg:col-span-2"
          )}>
            <StoryboardEditor
              selectedStoryboard={studio.selectedStoryboard}
              panels={studio.panels}
              onAddPanel={studio.addPanel}
              onUpdatePanelDialogue={studio.updatePanelDialogue}
              onMovePanelUp={studio.movePanelUp}
              onMovePanelDown={studio.movePanelDown}
              onDeletePanel={studio.deletePanel}
              orgId={studio.currentOrganization!.id}
              usage={studio.usage}
            />
          </div>
        </div>
      </div>
    </Card>
  );
});

StoryboardStudioMain.displayName = 'StoryboardStudioMain';

export default StoryboardStudioMain;
