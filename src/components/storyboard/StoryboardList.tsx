
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Storyboard } from '@/lib/api/storyboards';
import { formatDistanceToNow } from 'date-fns';
import { BarChart3, Clock } from 'lucide-react';

interface StoryboardListProps {
  storyboards: Storyboard[];
  selectedStoryboard?: Storyboard | null;
  loading: boolean;
  onSelectStoryboard: (storyboard: Storyboard) => void;
}

const StoryboardList: React.FC<StoryboardListProps> = ({
  storyboards,
  selectedStoryboard,
  loading,
  onSelectStoryboard
}) => {
  if (loading) {
    return (
      <div className="space-y-3">
        <h3 className="text-lg font-semibold mb-4">Storyboards</h3>
        <div className="space-y-2">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="p-4 animate-pulse">
              <div className="h-4 bg-muted rounded mb-2"></div>
              <div className="h-3 bg-muted rounded w-2/3"></div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold mb-4">
        Storyboards ({storyboards.length})
      </h3>
      
      {storyboards.length === 0 ? (
        <Card className="p-6 text-center">
          <p className="text-muted-foreground">No storyboards found</p>
          <p className="text-sm text-muted-foreground mt-1">
            Create your first storyboard to get started
          </p>
        </Card>
      ) : (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {storyboards.map((storyboard) => (
            <Card 
              key={storyboard.id}
              className={`p-4 cursor-pointer transition-colors hover:bg-muted/50 ${
                selectedStoryboard?.id === storyboard.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => onSelectStoryboard(storyboard)}
            >
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <h4 className="font-medium text-sm line-clamp-1">
                    {storyboard.title}
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    <BarChart3 className="h-3 w-3 mr-1" />
                    {storyboard.fidelity}
                  </Badge>
                </div>
                
                {storyboard.description && (
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {storyboard.description}
                  </p>
                )}
                
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>
                      {formatDistanceToNow(new Date(storyboard.created_at), { addSuffix: true })}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default StoryboardList;
