
import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';
import PanelEditor from './PanelEditor';

interface StoryboardEditorProps {
  selectedStoryboard: any;
  panels: any[];
  onAddPanel: () => void;
  onUpdatePanelDialogue: (panelId: string, dialogue: string) => void;
  onMovePanelUp: (index: number) => void;
  onMovePanelDown: (index: number) => void;
  onDeletePanel: (panelId: string) => void;
  orgId?: string;
  usage?: { dailyLimit: number; remaining: number };
}

const StoryboardEditor: React.FC<StoryboardEditorProps> = ({
  selectedStoryboard,
  panels,
  onAddPanel,
  onUpdatePanelDialogue,
  onMovePanelUp,
  onMovePanelDown,
  onDeletePanel,
  orgId,
  usage
}) => {
  const isMobile = useIsMobile();

  if (!selectedStoryboard) {
    return (
      <div className="h-full flex flex-col items-center justify-center text-center text-muted-foreground min-h-40 px-4">
        <span className="text-sm md:text-base">Select a storyboard to begin editing panels.</span>
      </div>
    );
  }

  return (
    <div>
      <div className={cn(
        "flex justify-between mb-4",
        isMobile ? "flex-col gap-3" : "flex-row items-center"
      )}>
        <h3 className="text-lg font-semibold">Storyboard Panels</h3>
        <button
          className={cn(
            "btn-glow px-3 py-2 rounded font-medium",
            isMobile ? "w-full" : ""
          )}
          onClick={onAddPanel}
          disabled={panels.length >= 20}
        >
          Add Panel
        </button>
      </div>
      <div className="space-y-4">
        {panels.length === 0 && (
          <div className="text-sm text-muted-foreground text-center py-8">
            No panels yet. Click "Add Panel" to start.
          </div>
        )}
        {panels.map((panel, idx) => (
          <PanelEditor
            key={panel.id}
            panel={panel}
            index={idx}
            totalPanels={panels.length}
            onUpdateDialogue={onUpdatePanelDialogue}
            onMoveUp={onMovePanelUp}
            onMoveDown={onMovePanelDown}
            onDelete={onDeletePanel}
            orgId={orgId}
            remaining={usage?.remaining}
            dailyLimit={usage?.dailyLimit}
          />
        ))}
      </div>
    </div>
  );
};

export default StoryboardEditor;
