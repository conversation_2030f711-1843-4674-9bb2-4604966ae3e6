
import React from 'react';
import { Card } from '@/components/ui/card';
import { useOrganization } from '@/contexts/OrganizationContext';

interface Post {
  id: string;
  title: string;
  content: string;
  created_at: string;
  org_id: string;
  profiles: {
    username: string;
    full_name: string;
  };
}

interface PostsFeedProps {
  posts: Post[];
}

const PostsFeed: React.FC<PostsFeedProps> = ({ posts }) => {
  const { currentOrganization } = useOrganization();

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">
        Recent Posts {currentOrganization && `- ${currentOrganization.name}`}
      </h2>
      <div className="space-y-4">
        {posts.length === 0 ? (
          <Card className="p-6 cinema-card text-center">
            <p className="text-muted-foreground">
              {currentOrganization 
                ? "No posts yet. Be the first to create one!" 
                : "Please select an organization to view posts."
              }
            </p>
          </Card>
        ) : (
          posts.map((post) => (
            <Card key={post.id} className="p-6 cinema-card">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-semibold">{post.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    by {post.profiles?.full_name || post.profiles?.username || 'Anonymous'}
                  </p>
                </div>
                <span className="text-xs text-muted-foreground">
                  {new Date(post.created_at).toLocaleDateString()}
                </span>
              </div>
              {post.content && (
                <p className="text-foreground whitespace-pre-wrap">{post.content}</p>
              )}
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default PostsFeed;
