
import React, { useState, useEffect, useReducer, useCallback, useMemo, memo } from 'react';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ContextAnalyzer, ScreenplayContext, ContentAnalysis } from '@/lib/ai/contextAnalyzer';
import { SmartSuggestionEngine, SmartSuggestion } from '@/lib/ai/smartSuggestionEngine';
import AIHeader from './ai-panel/AIHeader';
import ContentAnalysisCard from './ai-panel/ContentAnalysisCard';
import SmartSuggestionsPanel from './ai-panel/SmartSuggestionsPanel';
import ContextualTipsPanel from './ai-panel/ContextualTipsPanel';
import AIMessage from './ai-panel/AIMessage';
import EnhancedTypingIndicator from './ai-panel/EnhancedTypingIndicator';
import ChatInput from './ai-panel/ChatInput';
import { EnhancedSkeleton } from '@/components/ui/enhanced-skeleton';
import { usePerformanceOptimization } from '@/hooks/usePerformanceOptimization';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface AIPanelProps {
  className?: string;
  context?: {
    currentScene?: string;
    characters?: string[];
    genre?: string;
  };
  currentContent?: string;
  onContentUpdate?: (content: string) => void;
}

// Consolidated state interface
interface AIPanelState {
  messages: ChatMessage[];
  inputValue: string;
  isTyping: boolean;
  isAnalyzing: boolean;
  smartSuggestions: SmartSuggestion[];
  contentAnalysis: ContentAnalysis | null;
  screenplayContext: ScreenplayContext | null;
  contextualTips: string[];
}

// Action types for reducer
type AIPanelAction =
  | { type: 'SET_INPUT_VALUE'; payload: string }
  | { type: 'ADD_MESSAGE'; payload: ChatMessage }
  | { type: 'SET_MESSAGES'; payload: ChatMessage[] }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'SET_ANALYZING'; payload: boolean }
  | { type: 'SET_ANALYSIS_RESULTS'; payload: {
      contentAnalysis: ContentAnalysis;
      screenplayContext: ScreenplayContext;
      smartSuggestions: SmartSuggestion[];
      contextualTips: string[];
    }}
  | { type: 'CLEAR_INPUT' }
  | { type: 'RESET_ANALYSIS' };

// Initial state
const initialState: AIPanelState = {
  messages: [
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your enhanced AI writing assistant. I analyze your screenplay in real-time and provide contextual suggestions to improve dialogue, action lines, character development, and story structure. What would you like to work on?',
      timestamp: new Date()
    }
  ],
  inputValue: '',
  isTyping: false,
  isAnalyzing: false,
  smartSuggestions: [],
  contentAnalysis: null,
  screenplayContext: null,
  contextualTips: []
};

// Reducer function
const aiPanelReducer = (state: AIPanelState, action: AIPanelAction): AIPanelState => {
  switch (action.type) {
    case 'SET_INPUT_VALUE':
      return { ...state, inputValue: action.payload };
    case 'ADD_MESSAGE':
      return { ...state, messages: [...state.messages, action.payload] };
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload };
    case 'SET_TYPING':
      return { ...state, isTyping: action.payload };
    case 'SET_ANALYZING':
      return { ...state, isAnalyzing: action.payload };
    case 'SET_ANALYSIS_RESULTS':
      return {
        ...state,
        contentAnalysis: action.payload.contentAnalysis,
        screenplayContext: action.payload.screenplayContext,
        smartSuggestions: action.payload.smartSuggestions,
        contextualTips: action.payload.contextualTips,
        isAnalyzing: false
      };
    case 'CLEAR_INPUT':
      return { ...state, inputValue: '' };
    case 'RESET_ANALYSIS':
      return {
        ...state,
        contentAnalysis: null,
        screenplayContext: null,
        smartSuggestions: [],
        contextualTips: [],
        isAnalyzing: false
      };
    default:
      return state;
  }
};

const AIPanel: React.FC<AIPanelProps> = memo(({
  className,
  context: propsContext,
  currentContent = '',
  onContentUpdate
}) => {
  const [state, dispatch] = useReducer(aiPanelReducer, initialState);
  const { toast } = useToast();
  const { useDebounce } = usePerformanceOptimization();

  // Debounce content for analysis to prevent excessive processing
  const debouncedContent = useDebounce(currentContent, 500);

  // Memoize characters array to prevent unnecessary re-analysis
  const characters = useMemo(() => propsContext?.characters || [], [propsContext?.characters]);

  // Real-time content analysis with optimized debouncing
  useEffect(() => {
    if (debouncedContent.trim()) {
      dispatch({ type: 'SET_ANALYZING', payload: true });

      // Use requestIdleCallback for non-blocking analysis
      const performAnalysis = () => {
        try {
          const analysis = ContextAnalyzer.analyzeContentQuality(debouncedContent);
          const analyzedContext = ContextAnalyzer.analyzeContent(debouncedContent, characters);
          const suggestions = SmartSuggestionEngine.generateSuggestions(
            debouncedContent,
            analyzedContext,
            characters
          );
          const tips = SmartSuggestionEngine.generateContextualHelp(analyzedContext);

          dispatch({
            type: 'SET_ANALYSIS_RESULTS',
            payload: {
              contentAnalysis: analysis,
              screenplayContext: analyzedContext,
              smartSuggestions: suggestions,
              contextualTips: tips
            }
          });
        } catch (error) {
          console.error('Analysis error:', error);
          dispatch({ type: 'SET_ANALYZING', payload: false });
        }
      };

      // Use requestIdleCallback if available, otherwise setTimeout
      if (typeof requestIdleCallback !== 'undefined') {
        const idleId = requestIdleCallback(performAnalysis, { timeout: 1000 });
        return () => cancelIdleCallback(idleId);
      } else {
        const timeoutId = setTimeout(performAnalysis, 100);
        return () => clearTimeout(timeoutId);
      }
    } else {
      dispatch({ type: 'RESET_ANALYSIS' });
    }
  }, [debouncedContent, characters]);

  const handleSendMessage = useCallback(async () => {
    if (!state.inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: state.inputValue,
      timestamp: new Date()
    };

    dispatch({ type: 'ADD_MESSAGE', payload: userMessage });
    const currentInput = state.inputValue;
    dispatch({ type: 'CLEAR_INPUT' });
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Enhanced context for AI assistant
      const enhancedContext = {
        ...propsContext,
        contentAnalysis: state.contentAnalysis,
        screenplayContext: state.screenplayContext,
        currentContent: currentContent.slice(-1000) // Last 1000 characters for context
      };

      const { data, error } = await supabase.functions.invoke('ai-writing-assistant', {
        body: {
          message: currentInput,
          context: enhancedContext
        }
      });

      if (error) throw error;

      const aiMessage: ChatMessage = {
        id: `ai_${Date.now()}`,
        type: 'ai',
        content: data.response,
        timestamp: new Date()
      };

      dispatch({ type: 'ADD_MESSAGE', payload: aiMessage });
    } catch (error) {
      console.error('Error getting AI response:', error);
      toast({
        title: "Connection Error",
        description: "Unable to reach AI assistant. Please check your connection and try again.",
        variant: "destructive"
      });

      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'ai',
        content: 'I apologize, but I\'m having trouble connecting right now. Please try again in a moment.',
        timestamp: new Date()
      };
      dispatch({ type: 'ADD_MESSAGE', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_TYPING', payload: false });
    }
  }, [state.inputValue, state.contentAnalysis, state.screenplayContext, propsContext, currentContent, toast]);

  const handleApplySuggestion = useCallback((suggestion: SmartSuggestion) => {
    // Enhanced feedback for applied suggestions
    const confirmMessage: ChatMessage = {
      id: `confirm_${Date.now()}`,
      type: 'ai',
      content: `✅ Applied: ${suggestion.title}\n\n${suggestion.description}\n\nYour screenplay has been updated with this improvement.`,
      timestamp: new Date()
    };
    dispatch({ type: 'ADD_MESSAGE', payload: confirmMessage });

    toast({
      title: "Suggestion Applied",
      description: suggestion.title,
      duration: 3000,
    });
  }, [toast]);

  // Memoized input change handler
  const handleInputChange = useCallback((value: string) => {
    dispatch({ type: 'SET_INPUT_VALUE', payload: value });
  }, []);

  return (
    <Card className={`cinema-editor h-full flex flex-col ${className}`}>
      <AIHeader screenplayContext={state.screenplayContext} isAnalyzing={state.isAnalyzing} />

      {/* Loading state for analysis */}
      {state.isAnalyzing ? (
        <div className="border-b border-border/50 p-3">
          <EnhancedSkeleton variant="card" count={1} />
        </div>
      ) : (
        state.contentAnalysis && (
          <ContentAnalysisCard contentAnalysis={state.contentAnalysis} />
        )
      )}

      <SmartSuggestionsPanel
        smartSuggestions={state.smartSuggestions}
        onApplySuggestion={handleApplySuggestion}
      />

      <ContextualTipsPanel contextualTips={state.contextualTips} />

      {/* Chat Messages */}
      <div className="flex-1 p-4 overflow-y-auto cinema-scrollbar space-y-3">
        {state.messages.map((message, index) => (
          <AIMessage key={message.id} message={message} index={index} />
        ))}

        {state.isTyping && (
          <EnhancedTypingIndicator
            variant="processing"
            message="Analyzing your request and generating response..."
          />
        )}
      </div>

      <ChatInput
        inputValue={state.inputValue}
        setInputValue={handleInputChange}
        onSendMessage={handleSendMessage}
        isTyping={state.isTyping}
      />
    </Card>
  );
});

AIPanel.displayName = 'AIPanel';

export default AIPanel;
