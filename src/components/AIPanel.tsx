
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ContextAnalyzer, ScreenplayContext, ContentAnalysis } from '@/lib/ai/contextAnalyzer';
import { SmartSuggestionEngine, SmartSuggestion } from '@/lib/ai/smartSuggestionEngine';
import AIHeader from './ai-panel/AIHeader';
import ContentAnalysisCard from './ai-panel/ContentAnalysisCard';
import SmartSuggestionsPanel from './ai-panel/SmartSuggestionsPanel';
import ContextualTipsPanel from './ai-panel/ContextualTipsPanel';
import AIMessage from './ai-panel/AIMessage';
import EnhancedTypingIndicator from './ai-panel/EnhancedTypingIndicator';
import ChatInput from './ai-panel/ChatInput';
import { EnhancedSkeleton } from '@/components/ui/enhanced-skeleton';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

interface AIPanelProps {
  className?: string;
  context?: {
    currentScene?: string;
    characters?: string[];
    genre?: string;
  };
  currentContent?: string;
  onContentUpdate?: (content: string) => void;
}

const AIPanel: React.FC<AIPanelProps> = ({
  className,
  context: propsContext,
  currentContent = '',
  onContentUpdate
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'ai',
      content: 'Hello! I\'m your enhanced AI writing assistant. I analyze your screenplay in real-time and provide contextual suggestions to improve dialogue, action lines, character development, and story structure. What would you like to work on?',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [smartSuggestions, setSmartSuggestions] = useState<SmartSuggestion[]>([]);
  const [contentAnalysis, setContentAnalysis] = useState<ContentAnalysis | null>(null);
  const [screenplayContext, setScreenplayContext] = useState<ScreenplayContext | null>(null);
  const [contextualTips, setContextualTips] = useState<string[]>([]);
  const { toast } = useToast();

  // Real-time content analysis with loading state
  useEffect(() => {
    if (currentContent.trim()) {
      setIsAnalyzing(true);
      
      // Debounce analysis to avoid excessive processing
      const analysisTimer = setTimeout(() => {
        try {
          const analysis = ContextAnalyzer.analyzeContentQuality(currentContent);
          const analyzedContext = ContextAnalyzer.analyzeContent(currentContent, propsContext?.characters || []);
          const suggestions = SmartSuggestionEngine.generateSuggestions(
            currentContent, 
            analyzedContext, 
            propsContext?.characters || []
          );
          const tips = SmartSuggestionEngine.generateContextualHelp(analyzedContext);
          
          setContentAnalysis(analysis);
          setScreenplayContext(analyzedContext);
          setSmartSuggestions(suggestions);
          setContextualTips(tips);
        } catch (error) {
          console.error('Analysis error:', error);
        } finally {
          setIsAnalyzing(false);
        }
      }, 500);

      return () => clearTimeout(analysisTimer);
    } else {
      setIsAnalyzing(false);
    }
  }, [currentContent, propsContext?.characters]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    setInputValue('');
    setIsTyping(true);

    try {
      // Enhanced context for AI assistant
      const enhancedContext = {
        ...propsContext,
        contentAnalysis,
        screenplayContext,
        currentContent: currentContent.slice(-1000) // Last 1000 characters for context
      };

      const { data, error } = await supabase.functions.invoke('ai-writing-assistant', {
        body: {
          message: currentInput,
          context: enhancedContext
        }
      });

      if (error) throw error;

      const aiMessage: ChatMessage = {
        id: `ai_${Date.now()}`,
        type: 'ai',
        content: data.response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      toast({
        title: "Connection Error",
        description: "Unable to reach AI assistant. Please check your connection and try again.",
        variant: "destructive"
      });

      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'ai',
        content: 'I apologize, but I\'m having trouble connecting right now. Please try again in a moment.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleApplySuggestion = (suggestion: SmartSuggestion) => {
    // Enhanced feedback for applied suggestions
    const confirmMessage: ChatMessage = {
      id: `confirm_${Date.now()}`,
      type: 'ai',
      content: `✅ Applied: ${suggestion.title}\n\n${suggestion.description}\n\nYour screenplay has been updated with this improvement.`,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, confirmMessage]);
    
    toast({
      title: "Suggestion Applied",
      description: suggestion.title,
      duration: 3000,
    });
  };

  return (
    <Card className={`cinema-editor h-full flex flex-col ${className}`}>
      <AIHeader screenplayContext={screenplayContext} isAnalyzing={isAnalyzing} />

      {/* Loading state for analysis */}
      {isAnalyzing ? (
        <div className="border-b border-border/50 p-3">
          <EnhancedSkeleton variant="card" count={1} />
        </div>
      ) : (
        contentAnalysis && (
          <ContentAnalysisCard contentAnalysis={contentAnalysis} />
        )
      )}

      <SmartSuggestionsPanel 
        smartSuggestions={smartSuggestions}
        onApplySuggestion={handleApplySuggestion}
      />

      <ContextualTipsPanel contextualTips={contextualTips} />

      {/* Chat Messages */}
      <div className="flex-1 p-4 overflow-y-auto cinema-scrollbar space-y-3">
        {messages.map((message, index) => (
          <AIMessage key={message.id} message={message} index={index} />
        ))}
        
        {isTyping && (
          <EnhancedTypingIndicator 
            variant="processing" 
            message="Analyzing your request and generating response..."
          />
        )}
      </div>

      <ChatInput 
        inputValue={inputValue}
        setInputValue={setInputValue}
        onSendMessage={handleSendMessage}
        isTyping={isTyping}
      />
    </Card>
  );
};

export default AIPanel;
