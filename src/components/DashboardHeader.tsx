
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import OrganizationSelector from './OrganizationSelector';

const DashboardHeader = () => {
  const { user, profile, signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleColor = (role?: string) => {
    switch (role) {
      case 'admin':
      case 'super_admin':
        return 'bg-red-100 text-red-800';
      case 'director':
        return 'bg-purple-100 text-purple-800';
      case 'producer':
        return 'bg-blue-100 text-blue-800';
      case 'writer':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 sm:px-6 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-playfair font-bold gold-gradient truncate">
              Dashboard
            </h1>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-muted-foreground text-sm sm:text-base truncate">
                Welcome back, {profile?.full_name || user?.email}
              </p>
              {profile?.role && (
                <Badge variant="outline" className={getRoleColor(profile.role)}>
                  {profile.role}
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-3 flex-shrink-0">
            <div className="hidden sm:block">
              <OrganizationSelector />
            </div>
            
            {/* User Avatar */}
            <Avatar className="h-8 w-8">
              <AvatarImage 
                src={profile?.avatar_url} 
                alt={profile?.full_name || user?.email || 'User'} 
              />
              <AvatarFallback className="text-xs">
                {getInitials(profile?.full_name)}
              </AvatarFallback>
            </Avatar>
            
            <Button onClick={handleSignOut} variant="outline" size="sm">
              Sign Out
            </Button>
          </div>
        </div>
        
        {/* Mobile organization selector */}
        <div className="sm:hidden mt-4">
          <OrganizationSelector />
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;
