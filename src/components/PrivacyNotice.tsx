
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Shield, <PERSON>ie, Eye, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLocalStorage } from '@/hooks/useLocalStorage';

const PrivacyNotice: React.FC = () => {
  const [hasAccepted, setHasAccepted] = useLocalStorage('privacy-notice-accepted', false);
  const [isVisible, setIsVisible] = useState(!hasAccepted);

  const handleAccept = () => {
    setHasAccepted(true);
    setIsVisible(false);
  };

  const handleDecline = () => {
    setIsVisible(false);
    // In a real app, you might want to disable certain features or redirect
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-background/95 backdrop-blur-sm border-t">
      <Card className="max-w-4xl mx-auto p-4">
        <div className="flex items-start gap-4">
          <div className="flex items-center gap-2 flex-shrink-0">
            <Shield className="h-5 w-5 text-primary" />
            <Cookie className="h-5 w-5 text-primary" />
          </div>
          
          <div className="flex-1 space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">Privacy & Cookies</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsVisible(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground">
              We use cookies and similar technologies to enhance your experience, analyze usage, and provide personalized content. 
              Your privacy is important to us.
            </p>
            
            <div className="flex flex-wrap items-center gap-3 pt-2">
              <Button onClick={handleAccept} size="sm">
                Accept All
              </Button>
              <Button onClick={handleDecline} variant="outline" size="sm">
                Decline
              </Button>
              <div className="flex items-center gap-2 text-xs">
                <Link 
                  to="/privacy" 
                  className="text-primary hover:underline flex items-center gap-1"
                >
                  <Eye className="h-3 w-3" />
                  Privacy Policy
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link 
                  to="/cookies" 
                  className="text-primary hover:underline flex items-center gap-1"
                >
                  <Cookie className="h-3 w-3" />
                  Cookie Policy
                </Link>
                <span className="text-muted-foreground">•</span>
                <Link 
                  to="/gdpr" 
                  className="text-primary hover:underline"
                >
                  GDPR Rights
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PrivacyNotice;
