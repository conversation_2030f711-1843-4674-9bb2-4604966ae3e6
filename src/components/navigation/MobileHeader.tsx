
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Menu, X } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import SearchBar from './SearchBar';

const MobileHeader: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();
  const { user, signOut } = useAuth();

  const handleNavigation = (path: string) => {
    navigate(path);
    setIsOpen(false);
  };

  const handleSignOut = async () => {
    await signOut();
    setIsOpen(false);
  };

  const handleSearch = (query: string) => {
    console.log('Search query:', query);
    // Implement search functionality here
  };

  return (
    <div className="md:hidden flex items-center gap-2">
      <SearchBar onSearch={handleSearch} />
      
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="sm" className="p-2">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-[300px] sm:w-[400px]">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between pb-4">
              <h2 className="text-lg font-semibold">Navigation</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="p-2"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <nav className="flex flex-col space-y-4 flex-1">
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => handleNavigation('/')}
              >
                Home
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  const element = document.getElementById('features');
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                    setIsOpen(false);
                  }
                }}
              >
                Features
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => {
                  const element = document.getElementById('pricing');
                  if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                    setIsOpen(false);
                  }
                }}
              >
                Pricing
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => handleNavigation('/marketplace')}
              >
                Marketplace
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => handleNavigation('/production-tools')}
              >
                Production Tools
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => handleNavigation('/showcase')}
              >
                Demo
              </Button>
              <Button
                variant="ghost"
                className="justify-start"
                onClick={() => handleNavigation('/blog')}
              >
                Blog
              </Button>
            </nav>
            
            <div className="border-t pt-4 space-y-4">
              {user ? (
                <>
                  <div className="text-sm text-muted-foreground px-2">
                    Welcome, {user.email}
                  </div>
                  <Button
                    variant="ghost"
                    className="justify-start w-full"
                    onClick={handleSignOut}
                  >
                    Sign Out
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="ghost"
                    className="justify-start w-full"
                    onClick={() => handleNavigation('/auth')}
                  >
                    Sign In
                  </Button>
                  <Button
                    className="w-full"
                    onClick={() => {
                      const element = document.getElementById('pricing');
                      if (element) {
                        element.scrollIntoView({ behavior: 'smooth' });
                        setIsOpen(false);
                      }
                    }}
                  >
                    Get Started
                  </Button>
                </>
              )}
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default MobileHeader;
