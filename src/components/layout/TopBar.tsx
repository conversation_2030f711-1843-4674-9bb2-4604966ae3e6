
import React from 'react';

interface TopBarProps {
  activeLabel: string;
}

const TopBar: React.FC<TopBarProps> = ({ activeLabel }) => {
  return (
    <div className="glass border-b border-border p-3 sm:p-4">
      <div className="flex items-center justify-between">
        <div className="min-w-0">
          <h2 className="text-lg sm:text-xl font-playfair font-semibold truncate">
            {activeLabel}
          </h2>
          <p className="text-xs sm:text-sm text-muted-foreground truncate">
            Professional filmmaking tools and interfaces
          </p>
        </div>
      </div>
    </div>
  );
};

export default TopBar;
