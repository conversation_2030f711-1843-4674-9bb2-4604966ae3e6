
import React from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import SidebarNavigation from './SidebarNavigation';

interface SidebarItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  component: React.ReactNode;
}

interface SidebarContentProps {
  collapsed: boolean;
  onToggle: () => void;
  items: SidebarItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  collapsed,
  onToggle,
  items,
  activeTab,
  onTabChange
}) => {
  return (
    <div className={cn(
      "cinema-sidebar transition-all duration-300 flex flex-col",
      collapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="p-3 sm:p-4 border-b border-border">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div className="flex items-center space-x-2 sm:space-x-3 min-w-0">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-primary rounded-lg flex items-center justify-center flex-shrink-0">
                <span className="text-primary-foreground font-bold text-xs sm:text-sm">S</span>
              </div>
              <div className="min-w-0">
                <h1 className="text-sm sm:text-lg font-playfair font-bold gold-gradient truncate">
                  ScriptGenius
                </h1>
                <p className="text-xs text-muted-foreground truncate">Professional Suite</p>
              </div>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="ml-auto hover:bg-accent text-muted-foreground flex-shrink-0"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Navigation */}
      <SidebarNavigation
        items={items}
        activeTab={activeTab}
        collapsed={collapsed}
        onTabChange={onTabChange}
      />

      {/* Footer */}
      <div className="p-3 sm:p-4 border-t border-border mt-auto">
        {!collapsed && (
          <div className="text-xs text-muted-foreground space-y-1">
            <p className="truncate">ScriptGenius v2.0</p>
            <p className="truncate">Professional Screenwriting Suite</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SidebarContent;
