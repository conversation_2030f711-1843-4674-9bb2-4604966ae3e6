
import React from 'react';
import TopBar from './TopBar';

interface MainContentProps {
  activeLabel: string;
  children: React.ReactNode;
}

const MainContent: React.FC<MainContentProps> = ({ activeLabel, children }) => {
  return (
    <div className="flex-1 flex flex-col min-h-screen">
      <TopBar activeLabel={activeLabel} />
      
      {/* Content */}
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
};

export default MainContent;
