
import React from 'react';
import { cn } from '@/lib/utils';

interface SidebarItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  component: React.ReactNode;
}

interface SidebarNavigationProps {
  items: SidebarItem[];
  activeTab: string;
  collapsed: boolean;
  onTabChange: (tabId: string) => void;
}

const SidebarNavigation: React.FC<SidebarNavigationProps> = ({
  items,
  activeTab,
  collapsed,
  onTabChange
}) => {
  return (
    <nav className="flex-1 p-4 space-y-2">
      {items.map((item) => {
        const Icon = item.icon;
        const isActive = activeTab === item.id;
        
        return (
          <button
            key={item.id}
            onClick={() => onTabChange(item.id)}
            className={cn(
              "sidebar-item w-full transition-all duration-200 group relative",
              isActive 
                ? "active" 
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            <Icon className="h-5 w-5 flex-shrink-0" />
            {!collapsed && (
              <span className="font-medium truncate">{item.label}</span>
            )}
            
            {/* Tooltip for collapsed state */}
            {collapsed && (
              <div className="absolute left-16 ml-2 px-3 py-2 bg-popover text-popover-foreground text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 whitespace-nowrap border border-border">
                {item.label}
              </div>
            )}
          </button>
        );
      })}
    </nav>
  );
};

export default SidebarNavigation;
