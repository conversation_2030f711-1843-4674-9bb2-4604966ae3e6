
import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { ChevronLeft, ChevronRight, Menu, X } from 'lucide-react';
import { EnhancedButton } from '@/components/ui/enhanced-button';

interface EnhancedSidebarContentProps {
  children: React.ReactNode;
  collapsed: boolean;
  onToggle: () => void;
  className?: string;
}

export const EnhancedSidebarContent: React.FC<EnhancedSidebarContentProps> = ({
  children,
  collapsed,
  onToggle,
  className
}) => {
  const isMobile = useIsMobile();
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);

  // Close mobile overlay when clicking outside
  useEffect(() => {
    if (!isMobile) return;

    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById('mobile-sidebar');
      const trigger = document.getElementById('mobile-sidebar-trigger');
      
      if (isOverlayOpen && 
          sidebar && 
          !sidebar.contains(event.target as Node) &&
          trigger &&
          !trigger.contains(event.target as Node)) {
        setIsOverlayOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOverlayOpen, isMobile]);

  // Prevent body scroll when mobile overlay is open
  useEffect(() => {
    if (isMobile && isOverlayOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isMobile, isOverlayOpen]);

  // Mobile trigger button
  const MobileTrigger = () => (
    <EnhancedButton
      id="mobile-sidebar-trigger"
      variant="outline"
      size="sm"
      className="fixed top-4 left-4 z-50 lg:hidden bg-background/80 backdrop-blur-sm border shadow-lg"
      onClick={() => setIsOverlayOpen(!isOverlayOpen)}
      touchOptimized
      aria-label="Toggle navigation menu"
    >
      {isOverlayOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
    </EnhancedButton>
  );

  if (isMobile) {
    return (
      <>
        <MobileTrigger />
        
        {/* Mobile Overlay */}
        {isOverlayOpen && (
          <div className="fixed inset-0 z-40 lg:hidden">
            <div 
              className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
              onClick={() => setIsOverlayOpen(false)}
            />
            <div
              id="mobile-sidebar"
              className={cn(
                "fixed left-0 top-0 h-full w-72 bg-background border-r shadow-xl",
                "transform transition-transform duration-300 ease-out",
                "cinema-sidebar",
                className
              )}
            >
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="font-semibold">Navigation</h2>
                <EnhancedButton
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOverlayOpen(false)}
                  touchOptimized
                  aria-label="Close navigation"
                >
                  <X className="h-4 w-4" />
                </EnhancedButton>
              </div>
              <div className="overflow-y-auto h-[calc(100%-64px)] cinema-scrollbar">
                {children}
              </div>
            </div>
          </div>
        )}
      </>
    );
  }

  // Desktop sidebar
  return (
    <aside
      className={cn(
        "relative h-screen bg-background border-r transition-all duration-300 ease-out cinema-sidebar",
        collapsed ? "w-16" : "w-64",
        className
      )}
    >
      {/* Desktop toggle button */}
      <EnhancedButton
        variant="ghost"
        size="sm"
        className="absolute -right-3 top-6 z-10 h-6 w-6 rounded-full border bg-background shadow-md hover:shadow-lg"
        onClick={onToggle}
        aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
      >
        {collapsed ? (
          <ChevronRight className="h-3 w-3" />
        ) : (
          <ChevronLeft className="h-3 w-3" />
        )}
      </EnhancedButton>

      <div className="flex flex-col h-full">
        <div className="flex-1 overflow-y-auto cinema-scrollbar">
          {children}
        </div>
      </div>
    </aside>
  );
};

export default EnhancedSidebarContent;
