
import React from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import BreadcrumbNav from '@/components/navigation/BreadcrumbNav';
import { cn } from '@/lib/utils';

interface PageLayoutProps {
  children: React.ReactNode;
  showBreadcrumbs?: boolean;
  breadcrumbItems?: Array<{ label: string; href?: string }>;
  className?: string;
  title?: string;
  description?: string;
}

const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  showBreadcrumbs = false,
  breadcrumbItems,
  className,
  title,
  description
}) => {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Header />
      
      {/* Main Content Area */}
      <main className={cn("flex-1 pt-16 sm:pt-20", className)}>
        {showBreadcrumbs && (
          <div className="container mx-auto px-4 sm:px-6 py-4">
            <BreadcrumbNav items={breadcrumbItems} />
          </div>
        )}
        
        {(title || description) && (
          <div className="container mx-auto px-4 sm:px-6 py-6">
            {title && (
              <h1 className="text-2xl sm:text-3xl font-playfair font-bold mb-2">
                {title}
              </h1>
            )}
            {description && (
              <p className="text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}
        
        {children}
      </main>
      
      <Footer />
    </div>
  );
};

export default PageLayout;
