import React, { Suspense, lazy } from 'react';
import { 
  Home, 
  Edit, 
  FileText, 
  PenTool, 
  Film, 
  Users, 
  ShoppingCart, 
  Clapperboard,
  Bot
} from 'lucide-react';
import LoadingSpinner from '@/components/LoadingSpinner';
import { usePosts } from '@/hooks/usePosts';
import { preloadOnHover } from '@/utils/dynamicImports';

// Lazy load heavy components for better performance
const LazyProseMirrorEditor = lazy(() => import('@/components/lazy/LazyProseMirrorEditor'));
const BeatBoard = lazy(() => import('../BeatBoard'));
const StoryboardStudio = lazy(() => import('../StoryboardStudio'));
const CoverageGenerator = lazy(() => import('@/features/coverage').then(module => ({ default: module.CoverageGenerator })));
const ScreenplayMarketplace = lazy(() => import('@/features/marketplace').then(module => ({ default: module.ScreenplayMarketplace })));
const ProductionTools = lazy(() => import('@/features/production').then(module => ({ default: module.ProductionTools })));
const TeamManagement = lazy(() => import('../TeamManagement'));

// Keep lightweight components as direct imports
import PostCreationForm from '../PostCreationForm';
import PostsFeed from '../PostsFeed';

interface SidebarItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  component: React.ReactNode;
}

/**
 * Component loading fallback for sidebar items
 */
const ComponentLoadingFallback = ({ name }: { name: string }) => (
  <div className="min-h-[50vh] flex items-center justify-center">
    <div className="text-center space-y-4">
      <LoadingSpinner size="md" />
      <p className="text-sm text-muted-foreground">Loading {name}...</p>
    </div>
  </div>
);

export const createSidebarItems = (): SidebarItem[] => {
  const { posts, fetchPosts } = usePosts();

  return [
    {
      id: 'dashboard',
      icon: Home,
      label: 'Dashboard',
      component: (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 p-6">
          <div className="lg:col-span-1">
            <PostCreationForm onPostCreated={fetchPosts} />
          </div>
          <div className="lg:col-span-2">
            <PostsFeed posts={posts} />
          </div>
        </div>
      )
    },
    {
      id: 'script-editor',
      icon: Edit,
      label: 'Script Editor',
      component: (
        <div {...preloadOnHover('editor')}>
          <Suspense fallback={<ComponentLoadingFallback name="Script Editor" />}>
            <LazyProseMirrorEditor initialContent="" />
          </Suspense>
        </div>
      )
    },
    {
      id: 'ai-writer',
      icon: Bot,
      label: 'AI Writer',
      component: (
        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-playfair font-bold gold-gradient mb-4">AI Writing Assistant</h2>
              <p className="text-muted-foreground">Enhance your screenplay with AI-powered writing tools</p>
            </div>
            <div className="cinema-card p-6">
              <h3 className="text-xl font-semibold mb-4">Coming Soon</h3>
              <p className="text-muted-foreground">AI-powered writing assistance is under development.</p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'coverage',
      icon: FileText,
      label: 'Coverage Generator',
      component: (
        <Suspense fallback={<ComponentLoadingFallback name="Coverage Generator" />}>
          <CoverageGenerator />
        </Suspense>
      )
    },
    {
      id: 'beat-board',
      icon: PenTool,
      label: 'Beat Board',
      component: (
        <Suspense fallback={<ComponentLoadingFallback name="Beat Board" />}>
          <BeatBoard />
        </Suspense>
      )
    },
    {
      id: 'storyboard',
      icon: Film,
      label: 'Storyboard Studio',
      component: (
        <Suspense fallback={<ComponentLoadingFallback name="Storyboard Studio" />}>
          <StoryboardStudio />
        </Suspense>
      )
    },
    {
      id: 'marketplace',
      icon: ShoppingCart,
      label: 'Marketplace',
      component: (
        <Suspense fallback={<ComponentLoadingFallback name="Marketplace" />}>
          <ScreenplayMarketplace />
        </Suspense>
      )
    },
    {
      id: 'production',
      icon: Clapperboard,
      label: 'Production',
      component: (
        <div {...preloadOnHover('charts')}>
          <Suspense fallback={<ComponentLoadingFallback name="Production Tools" />}>
            <ProductionTools />
          </Suspense>
        </div>
      )
    },
    {
      id: 'team',
      icon: Users,
      label: 'Team',
      component: (
        <Suspense fallback={<ComponentLoadingFallback name="Team Management" />}>
          <TeamManagement />
        </Suspense>
      )
    }
  ];
};
