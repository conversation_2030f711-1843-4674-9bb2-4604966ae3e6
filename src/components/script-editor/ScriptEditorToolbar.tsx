
import React from 'react';
import { Button } from '@/components/ui/button';
import { ScriptEditorToolbarProps, ScriptLine } from './ScriptEditorTypes';

const ScriptEditorToolbar: React.FC<ScriptEditorToolbarProps> = ({
  onAddLine
}) => {
  const elementTypes: ScriptLine['type'][] = ['action', 'character', 'dialogue', 'parenthetical', 'transition'];

  return (
    <div className="border-b border-border/50 p-3 bg-muted/20">
      <div className="flex items-center space-x-2">
        <span className="text-sm font-medium text-muted-foreground mr-3">Add:</span>
        {elementTypes.map((type) => (
          <Button
            key={type}
            variant="outline"
            size="sm"
            onClick={() => onAddLine(type)}
            className="text-xs capitalize"
          >
            {type}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default ScriptEditorToolbar;
