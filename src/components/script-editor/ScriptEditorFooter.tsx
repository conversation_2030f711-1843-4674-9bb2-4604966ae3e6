
import React from 'react';
import { Button } from '@/components/ui/button';
import { Save, Download } from 'lucide-react';
import { ScriptEditorFooterProps } from './ScriptEditorTypes';

const ScriptEditorFooter: React.FC<ScriptEditorFooterProps> = ({
  script,
  onSave,
  onExport
}) => {
  const handleSave = () => {
    onSave?.();
    console.log('Script saved');
  };

  const handleExport = () => {
    onExport?.();
    console.log('Script exported');
  };

  return (
    <div className="border-t border-border/50 p-4 bg-muted/20">
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center space-x-4">
          <span>Lines: {script.length}</span>
          <span>Characters: {script.filter(l => l.type === 'character').length}</span>
          <span>Pages: ~{Math.ceil(script.length / 10)}</span>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleSave}>
            <Save className="h-4 w-4 mr-1" />
            Save Draft
          </Button>
          <Button variant="default" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-1" />
            Export PDF
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditorFooter;
