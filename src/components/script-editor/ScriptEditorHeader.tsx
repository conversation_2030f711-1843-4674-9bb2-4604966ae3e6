
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Play, Pause, Save, FileText } from 'lucide-react';
import PDFImportButton from '@/components/pdf-import/PDFImportButton';
import { ScriptEditorHeaderProps } from './ScriptEditorTypes';

const ScriptEditorHeader: React.FC<ScriptEditorHeaderProps> = ({
  isPlaying,
  onTogglePlayback,
  onPDFImport
}) => {
  return (
    <div className="flex items-center justify-between p-4 border-b border-border/50 bg-muted/20">
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        </div>
        <h2 className="text-lg font-playfair font-semibold">Script Editor</h2>
      </div>
      
      <div className="flex items-center space-x-2">
        {onPDFImport && (
          <PDFImportButton
            onImport={onPDFImport}
            variant="outline"
            size="sm"
          />
        )}
        
        <Button
          variant="outline"
          size="sm"
          onClick={onTogglePlayback}
          className="flex items-center space-x-1"
        >
          {isPlaying ? (
            <Pause className="h-4 w-4" />
          ) : (
            <Play className="h-4 w-4" />
          )}
          <span className="hidden sm:inline">
            {isPlaying ? 'Pause' : 'Play'}
          </span>
        </Button>
        
        <Button variant="outline" size="sm">
          <Save className="h-4 w-4 mr-1" />
          <span className="hidden sm:inline">Save</span>
        </Button>
      </div>
    </div>
  );
};

export default ScriptEditorHeader;
