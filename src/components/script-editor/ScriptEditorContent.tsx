
import React from 'react';
import { Button } from '@/components/ui/button';
import { FileText } from 'lucide-react';
import { ScriptEditorContentProps, ScriptLine } from './ScriptEditorTypes';

const ScriptEditorContent: React.FC<ScriptEditorContentProps> = ({
  script,
  selectedLineId,
  onLineSelect,
  onLineEdit,
  onAddLine
}) => {
  const getLineStyle = (type: ScriptLine['type']) => {
    switch (type) {
      case 'action':
        return 'text-left font-normal';
      case 'character':
        return 'text-center font-bold uppercase tracking-wider';
      case 'dialogue':
        return 'text-center font-normal max-w-md mx-auto';
      case 'parenthetical':
        return 'text-center font-normal italic text-muted-foreground';
      case 'transition':
        return 'text-right font-bold uppercase';
      default:
        return 'text-left font-normal';
    }
  };

  return (
    <div className="flex-1 p-6 overflow-y-auto cinema-scrollbar">
      <div className="max-w-4xl mx-auto space-y-4">
        {script.map((line, index) => (
          <div
            key={line.id}
            className={`script-line ${getLineStyle(line.type)} animate-fade-in-up`}
            style={{ animationDelay: `${index * 50}ms` }}
            onClick={() => onLineSelect(line.id)}
          >
            {line.type === 'character' && line.character && (
              <div className="text-center font-bold uppercase tracking-wider mb-1">
                {line.character}
              </div>
            )}
            
            {selectedLineId === line.id ? (
              <textarea
                className="w-full bg-transparent border border-primary/50 rounded p-2 text-inherit resize-none"
                value={line.content}
                onChange={(e) => onLineEdit(line.id, e.target.value)}
                onBlur={() => onLineSelect('')}
                autoFocus
                rows={Math.max(1, Math.ceil(line.content.length / 50))}
              />
            ) : (
              <div className={`p-2 rounded hover:bg-muted/20 transition-colors cursor-text ${getLineStyle(line.type)}`}>
                {line.content}
              </div>
            )}
          </div>
        ))}
        
        {/* Add new line prompt */}
        <div className="text-center py-8">
          <p className="text-muted-foreground text-sm mb-3">Click to add a new scene element</p>
          <div className="flex justify-center space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onAddLine('action')}
              className="animate-fade-in-up delay-100"
            >
              <FileText className="h-4 w-4 mr-1" />
              Action
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => onAddLine('dialogue')}
              className="animate-fade-in-up delay-200"
            >
              💬 Dialogue
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScriptEditorContent;
