export interface ScriptLine {
  id: string;
  type: 'action' | 'dialogue' | 'character' | 'parenthetical' | 'transition';
  content: string;
  character?: string;
}

export interface ScriptEditorProps {
  initialScript?: ScriptLine[];
  onScriptChange?: (script: ScriptLine[]) => void;
  className?: string;
}

export interface ScriptEditorHeaderProps {
  isPlaying: boolean;
  onTogglePlayback: () => void;
  onPDFImport?: (content: string, elements: any[]) => void;
}

export interface ScriptEditorToolbarProps {
  onAddLine: (type: ScriptLine['type']) => void;
}

export interface ScriptEditorContentProps {
  script: ScriptLine[];
  selectedLineId: string | null;
  onLineSelect: (id: string) => void;
  onLineEdit: (id: string, content: string) => void;
  onAddLine: (type: ScriptLine['type']) => void;
}

export interface ScriptEditorFooterProps {
  script: ScriptLine[];
  onSave?: () => void;
  onExport?: () => void;
}
