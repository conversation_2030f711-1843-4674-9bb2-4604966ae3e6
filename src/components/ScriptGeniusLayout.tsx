
import React, { useState } from 'react';
import SidebarContent from './layout/SidebarContent';
import MainContent from './layout/MainContent';
import { createSidebarItems } from './layout/sidebarItems';
import OnboardingTour from './onboarding/OnboardingTour';
import FeedbackWidget from './feedback/FeedbackWidget';
import PrivacyNotice from './PrivacyNotice';
import SecurityHeaders from './security/SecurityHeaders';
import MobileOptimizations from './mobile/MobileOptimizations';
import { useOnboarding } from '@/hooks/useOnboarding';
import { useIsMobile } from '@/hooks/use-mobile';

interface ScriptGeniusLayoutProps {
  children: React.ReactNode;
}

const ScriptGeniusLayout: React.FC<ScriptGeniusLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const isMobile = useIsMobile();
  
  const { showOnboarding, completeOnboarding, skipOnboarding } = useOnboarding();

  const sidebarItems = createSidebarItems();
  const activeItem = sidebarItems.find(item => item.id === activeTab);

  const handleToggle = () => {
    setCollapsed(!collapsed);
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };

  return (
    <>
      <SecurityHeaders />
      <MobileOptimizations>
        <div className="min-h-screen bg-background flex">
          <SidebarContent
            collapsed={collapsed && !isMobile} // Don't collapse on mobile
            onToggle={handleToggle}
            items={sidebarItems}
            activeTab={activeTab}
            onTabChange={handleTabChange}
          />

          <MainContent 
            activeLabel={activeItem?.label || 'Dashboard'}
          >
            {children || activeItem?.component}
          </MainContent>
        </div>
      </MobileOptimizations>

      {/* Global UI Components */}
      <FeedbackWidget />
      <PrivacyNotice />
      
      {/* Onboarding for new users */}
      {showOnboarding && (
        <OnboardingTour
          onComplete={completeOnboarding}
          onSkip={skipOnboarding}
        />
      )}
    </>
  );
};

export default ScriptGeniusLayout;
