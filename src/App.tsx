
import React from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { MobileMonitoringProvider } from "@/components/monitoring/MobileMonitoringProvider";
import { SecurityProvider } from "@/components/security/SecurityProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { useIsMobile } from "@/hooks/use-mobile";
import FeedbackWidget from "@/components/feedback/FeedbackWidget";
import Index from "./pages/Index";
import FAQ from "./pages/FAQ";

// Mobile-optimized query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // 30 seconds - shorter for mobile
      gcTime: 5 * 60 * 1000, // 5 minutes - shorter garbage collection
      retry: (failureCount, error: any) => {
        // More aggressive retry logic for mobile
        if (error?.status >= 500) return failureCount < 2;
        if (error?.message?.includes('Network')) return failureCount < 1;
        return false;
      },
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 3000), // Faster retries
    },
    mutations: {
      retry: 1 // Reduce mutation retries
    }
  }
});

function AppContent() {
  const isMobile = useIsMobile();

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <OrganizationProvider>
            <SecurityProvider enableHeaders={true} enableLogging={!isMobile}>
              <MobileMonitoringProvider enableHealthMonitoring={!isMobile}>
                <TooltipProvider>
                  <Toaster />
                  <Sonner />
                  <BrowserRouter>
                    <Routes>
                      <Route path="/" element={<Index />} />
                      <Route path="/faq" element={<FAQ />} />
                    </Routes>
                    <FeedbackWidget />
                  </BrowserRouter>
                </TooltipProvider>
              </MobileMonitoringProvider>
            </SecurityProvider>
          </OrganizationProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <HelmetProvider>
      <AppContent />
    </HelmetProvider>
  );
}

export default App;
