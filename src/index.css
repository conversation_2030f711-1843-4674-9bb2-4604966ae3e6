
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&family=JetBrains+Mono:wght@300;400;500;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* ScriptGenius Design System - Enhanced Cinematic Theme */

@layer base {
  :root {
    --background: 220 27% 8%;
    --foreground: 210 40% 95%;

    --card: 220 25% 12%;
    --card-foreground: 210 40% 95%;

    --popover: 220 25% 12%;
    --popover-foreground: 210 40% 95%;

    --primary: 45 85% 60%;
    --primary-foreground: 220 27% 8%;

    --secondary: 220 20% 18%;
    --secondary-foreground: 210 40% 95%;

    --muted: 220 15% 15%;
    --muted-foreground: 215 15% 60%;

    --accent: 210 70% 55%;
    --accent-foreground: 220 27% 8%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 220 15% 20%;
    --input: 220 15% 20%;
    --ring: 45 85% 60%;

    --radius: 0.75rem;

    /* Enhanced cinematic gradients */
    --gradient-cinema: linear-gradient(135deg, hsl(220, 27%, 8%) 0%, hsl(230, 35%, 12%) 50%, hsl(240, 40%, 10%) 100%);
    --gradient-gold: linear-gradient(135deg, hsl(45, 85%, 60%) 0%, hsl(35, 80%, 55%) 100%);
    --gradient-feature: linear-gradient(135deg, hsl(220, 25%, 12%) 0%, hsl(220, 30%, 15%) 100%);
    --gradient-sidebar: linear-gradient(180deg, hsl(220, 25%, 12%) 0%, hsl(220, 30%, 10%) 100%);
    --gradient-editor: linear-gradient(135deg, hsl(220, 20%, 8%) 0%, hsl(220, 25%, 12%) 100%);
    --gradient-story: linear-gradient(135deg, hsl(240, 50%, 15%) 0%, hsl(240, 45%, 20%) 100%);
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background: var(--gradient-cinema);
    min-height: 100vh;
  }

  /* Enhanced typography */
  .font-inter { font-family: 'Inter', sans-serif; }
  .font-playfair { font-family: 'Playfair Display', serif; }
  .font-mono { font-family: 'JetBrains Mono', 'Monaco', 'Consolas', monospace; }
  
  /* Enhanced glassmorphism utilities */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(24px);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
  
  /* Enhanced cinema card effects */
  .cinema-card {
    background: var(--gradient-feature);
    border: 1px solid hsl(var(--border));
    box-shadow: var(--shadow-cinema);
  }

  .cinema-sidebar {
    background: var(--gradient-sidebar);
    border-right: 1px solid hsl(var(--border));
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.2);
  }

  .cinema-editor {
    background: var(--gradient-editor);
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
  }
  
  /* Gold accent gradient text */
  .gold-gradient {
    background: var(--gradient-gold);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Component-specific styles */
  .script-line {
    @apply font-mono text-sm leading-relaxed;
  }

  .sidebar-item {
    @apply flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200;
  }

  .sidebar-item:hover {
    @apply bg-white/5 backdrop-blur-sm;
  }

  .sidebar-item.active {
    @apply bg-primary/10 text-primary border-l-2 border-primary;
  }

  /* Story canvas specific styles */
  .story-node {
    @apply transition-all duration-200 hover:scale-105;
  }

  .story-node.selected {
    @apply ring-2 ring-primary scale-105;
  }

  /* Enhanced button hover effects */
  .btn-glow:hover {
    box-shadow: 0 0 20px rgba(251, 191, 36, 0.4);
  }

  /* Custom scrollbar for canvas */
  .canvas-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .canvas-scrollbar::-webkit-scrollbar {
    display: none;
  }
}

/* Enhanced custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(251, 191, 36, 0.3); }
  50% { box-shadow: 0 0 40px rgba(251, 191, 36, 0.6); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes bounce-in {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-float { animation: float 6s ease-in-out infinite; }
.animate-glow { animation: glow 2s ease-in-out infinite; }
.animate-slide-up { animation: slideInUp 0.8s ease-out; }
.animate-fade-scale { animation: fadeInScale 0.6s ease-out; }
.animate-typewriter { animation: typewriter 2s steps(40, end); }
.animate-shimmer { animation: shimmer 2s infinite; }
.animate-bounce-in { animation: bounce-in 0.6s ease-out; }

/* Enhanced staggered animation delays */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Scrollbar styling */
.cinema-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted)) transparent;
}

.cinema-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.cinema-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.cinema-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted));
  border-radius: 3px;
}

.cinema-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}

/* Interactive element effects */
.interactive-hover {
  @apply transition-all duration-200 hover:scale-105 hover:shadow-lg;
}

.text-shimmer {
  background: linear-gradient(90deg, 
    hsl(var(--muted-foreground)) 0%, 
    hsl(var(--foreground)) 50%, 
    hsl(var(--muted-foreground)) 100%);
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: shimmer 3s ease-in-out infinite;
}

/* Component state utilities */
.component-enter {
  opacity: 0;
  transform: translateY(20px) scale(0.95);
}

.component-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.3s ease-out;
}

.component-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.component-exit-active {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
  transition: all 0.3s ease-out;
}
