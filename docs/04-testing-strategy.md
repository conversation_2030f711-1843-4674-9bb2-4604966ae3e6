# 4. Testing Strategy

We employ a multi-layered testing strategy to ensure code quality and application stability.

## Unit Tests

*   **Framework:** Vitest
*   **Location:** `__tests__` directories or `*.test.ts(x)` files co-located with the source code.
*   **Purpose:** Test individual functions, components, and hooks in isolation.
*   **Guidelines:** Mock all external dependencies (APIs, timers, etc.). Focus on testing business logic, not implementation details.

## Integration Tests

*   **Framework:** Vitest (with React Testing Library)
*   **Location:** `__tests__/integration/`
*   **Purpose:** Test how multiple components work together. For example, testing an entire form flow or a feature page.
*   **Guidelines:** Render components within necessary providers (e.g., `AuthContext`). Mock server responses using tools like `msw` (Mock Service Worker).

## End-to-End (E2E) Tests

*   **Framework:** Playwright
*   **Location:** `/e2e` directory.
*   **Purpose:** Test critical user flows from start to finish in a real browser environment.
*   **Examples:** User login flow, creating a new project, submitting a script to the marketplace.
*   **Guidelines:** E2E tests run against a real backend (either a dedicated test environment or a seeded local Supabase instance).

## Performance Tests

*   **Framework:** Vitest + Custom Performance Utilities
*   **Location:** `*.perf.test.ts` files and `/src/components/PerformanceTestSuite.tsx`
*   **Purpose:** Test component render performance, memory usage, and optimization effectiveness.
*   **Guidelines:** Focus on critical components and user interactions that impact perceived performance.

### Performance Testing Categories

#### 1. Component Performance Tests
```typescript
// Example: AIPanel performance test
describe('AIPanel Performance', () => {
  it('should render within performance budget', async () => {
    const startTime = performance.now();
    render(<AIPanel />);
    const renderTime = performance.now() - startTime;

    expect(renderTime).toBeLessThan(50); // 50ms budget
  });

  it('should not exceed memory threshold', () => {
    const initialMemory = performance.memory?.usedJSHeapSize || 0;
    render(<AIPanel />);
    const memoryUsed = (performance.memory?.usedJSHeapSize || 0) - initialMemory;

    expect(memoryUsed).toBeLessThan(5 * 1024 * 1024); // 5MB threshold
  });
});
```

#### 2. Hook Performance Tests
```typescript
// Example: useOptimizedQuery performance test
describe('useOptimizedQuery Performance', () => {
  it('should cache results effectively', async () => {
    const queryFn = vi.fn().mockResolvedValue('data');

    const { result, rerender } = renderHook(() =>
      useOptimizedQuery(['test'], queryFn)
    );

    await waitFor(() => expect(result.current.isSuccess).toBe(true));

    rerender();

    // Should not call queryFn again due to caching
    expect(queryFn).toHaveBeenCalledTimes(1);
  });
});
```

#### 3. Integration Performance Tests
```typescript
// Example: Full page load performance
describe('Dashboard Performance', () => {
  it('should load within performance budget', async () => {
    const startTime = performance.now();

    render(
      <QueryClient client={queryClient}>
        <Dashboard />
      </QueryClient>
    );

    await waitFor(() =>
      expect(screen.getByText('Dashboard')).toBeInTheDocument()
    );

    const loadTime = performance.now() - startTime;
    expect(loadTime).toBeLessThan(1000); // 1 second budget
  });
});
```

## Visual Regression Tests

*   **Framework:** Playwright + Percy/Chromatic
*   **Location:** `/e2e/visual/`
*   **Purpose:** Detect unintended visual changes in UI components and pages.
*   **Guidelines:** Focus on critical user interfaces and responsive design breakpoints.

## Load Testing

*   **Framework:** Artillery.io / k6
*   **Location:** `/load-tests/`
*   **Purpose:** Test API performance under various load conditions.
*   **Guidelines:** Test critical API endpoints with realistic user scenarios.

### Load Test Scenarios

1. **User Authentication Flow**
   - Concurrent user logins
   - Session management under load
   - Rate limiting validation

2. **Content Creation**
   - Simultaneous screenplay editing
   - Real-time collaboration stress testing
   - File upload performance

3. **Data Analytics**
   - Admin dashboard with large datasets
   - Report generation under load
   - Database query performance

## Performance Monitoring

### Development Monitoring

*   **PerformanceMonitor Component**: Real-time performance tracking
*   **PerformanceTestSuite Component**: Comprehensive benchmarking
*   **React DevTools Profiler**: Component render analysis
*   **Chrome DevTools**: Memory and performance profiling

### Production Monitoring

*   **Core Web Vitals**: FCP, LCP, TTI, CLS tracking
*   **Custom Metrics**: Component render times, API response times
*   **Error Tracking**: Performance-related error monitoring
*   **User Experience**: Real user monitoring (RUM)

## Testing Commands

```bash
# Unit and integration tests
npm run test

# Performance tests only
npm run test:performance

# E2E tests
npm run test:e2e

# Visual regression tests
npm run test:visual

# Load tests
npm run test:load

# Performance audit
npm run performance:audit

# Bundle analysis
npm run analyze
```

## Performance Benchmarks

### Target Performance Metrics

| Metric | Target | Current | Status |
|--------|--------|---------|--------|
| First Contentful Paint | <1.5s | 1.2s | ✅ |
| Largest Contentful Paint | <2.5s | 2.1s | ✅ |
| Time to Interactive | <3.0s | 2.8s | ✅ |
| Bundle Size | <300KB | 245KB | ✅ |
| Memory Usage | <60MB | 45MB | ✅ |

### Component Performance Budgets

| Component | Render Time | Memory | Re-renders |
|-----------|-------------|--------|------------|
| AIPanel | <50ms | <5MB | <5 per interaction |
| AdminAnalyticsDashboard | <100ms | <10MB | <3 per page |
| TeamManagement | <30ms | <3MB | <2 per tab switch |
| StoryboardStudioMain | <75ms | <8MB | <4 per search |
| ProseMirrorEditor | <40ms | <6MB | <3 per edit |

## Continuous Integration

### Performance Testing Pipeline

1. **Pre-commit**: Run performance tests for changed components
2. **Pull Request**: Full performance test suite + visual regression
3. **Staging**: Load testing + performance audit
4. **Production**: Continuous monitoring + alerting

### Performance Regression Detection

```yaml
# GitHub Actions example
- name: Performance Regression Test
  run: |
    npm run test:performance
    npm run performance:audit
    node scripts/compare-performance.js
```

## Best Practices

### Writing Performance Tests

1. **Isolate Performance Concerns**: Test one performance aspect at a time
2. **Use Realistic Data**: Test with production-like data volumes
3. **Mock External Dependencies**: Focus on component performance, not network
4. **Set Realistic Budgets**: Based on user experience requirements
5. **Monitor Trends**: Track performance over time, not just absolute values

### Performance Test Maintenance

1. **Regular Review**: Update performance budgets as features evolve
2. **Baseline Updates**: Adjust baselines after intentional performance changes
3. **Flaky Test Management**: Identify and fix unstable performance tests
4. **Documentation**: Keep performance requirements documented and updated

## Conclusion

Our comprehensive testing strategy ensures that ScriptGenius maintains high performance standards while delivering new features. The combination of unit, integration, E2E, and performance tests provides confidence in both functionality and user experience.