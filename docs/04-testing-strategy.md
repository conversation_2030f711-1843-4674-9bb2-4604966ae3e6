# 4. Testing Strategy

We employ a multi-layered testing strategy to ensure code quality and application stability.

## Unit Tests

*   **Framework:** Vitest
*   **Location:** `__tests__` directories or `*.test.ts(x)` files co-located with the source code.
*   **Purpose:** Test individual functions, components, and hooks in isolation.
*   **Guidelines:** Mock all external dependencies (APIs, timers, etc.). Focus on testing business logic, not implementation details.

## Integration Tests

*   **Framework:** Vitest (with React Testing Library)
*   **Location:** `__tests__/integration/`
*   **Purpose:** Test how multiple components work together. For example, testing an entire form flow or a feature page.
*   **Guidelines:** Render components within necessary providers (e.g., `AuthContext`). Mock server responses using tools like `msw` (Mock Service Worker).

## End-to-End (E2E) Tests

*   **Framework:** Playwright
*   **Location:** `/e2e` directory.
*   **Purpose:** Test critical user flows from start to finish in a real browser environment.
*   **Examples:** User login flow, creating a new project, submitting a script to the marketplace.
*   **Guidelines:** E2E tests run against a real backend (either a dedicated test environment or a seeded local Supabase instance). 