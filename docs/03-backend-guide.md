# 3. Backend Guide (Supabase)

This document covers conventions and processes for working with the Supabase backend.

## Database Migrations

All schema changes must be done via migrations.

1.  **Create a new migration file:**
    ```bash
    supabase migration new <a_descriptive_name>
    ```
2.  **Write your SQL changes** in the newly created migration file.
3.  **Apply the migrations locally** to test them by resetting your local database:
    ```bash
    supabase db reset
    ```
4.  Commit the new migration file. Migrations will be applied automatically during CI/CD deployment.

## Edge Functions

Edge Functions contain our core server-side business logic. They are located in `supabase/functions/`.

### Writing a New Function

1.  Create a new folder under `supabase/functions/` (e.g., `my-new-function`).
2.  Add an `index.ts` file.
3.  Write your function logic using Deno and the Supabase libraries.
4.  Ensure you handle CORS and authentication appropriately at the start of every function.

### Testing Functions Locally

You can serve and test functions locally:

```bash
# Serve all functions
supabase functions serve

# In another terminal, invoke a function
supabase functions invoke my-new-function --payload-file ./path/to/payload.json
```

## Security: Row Level Security (RLS)

**RLS is enabled on all tables by default.** No user can access any data unless an explicit policy allows them to.

When creating a new table, you **must** also create RLS policies for `SELECT`, `INSERT`, `UPDATE`, and `DELETE` operations. The most common policy is to ensure that a user can only access rows where their `user.id` matches a `user_id` column on the table. 