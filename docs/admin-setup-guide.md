# 🔐 Admin Setup Guide

## Overview

This guide covers the secure setup and management of administrative roles in ScriptGenius. Administrative access is required for beta management, user administration, and system configuration.

## 🏗️ Role Hierarchy

ScriptGenius uses a role-based access control system with the following hierarchy:

### User Roles
- **Writer** (Default): Basic script creation and editing capabilities
- **Producer**: Project management and team coordination features
- **Director**: Creative oversight and approval workflows
- **Admin**: User management and system configuration access
- **Super_Admin**: Full system access including security and backup controls

### Role Capabilities

#### Super_Admin Access
- Beta access request management and automation
- Promo code creation and tracking systems
- User role assignment and management
- System backup and disaster recovery
- Security settings and monitoring
- Blog management and content control
- Performance monitoring and analytics

#### Admin Access
- User management within organizations
- Basic system configuration
- Content moderation capabilities
- Limited analytics access

## 🚀 Initial Setup Process

### Prerequisites
- Deployed ScriptGenius application
- Database access (Supabase dashboard or CLI)
- Valid user account in the system

### Step 1: Create Your Account
1. Sign up through the application interface
2. Complete the profile setup process
3. Verify your account is created in the system

### Step 2: Assign Initial Super_Admin Role
**Important**: This step requires database-level access and should only be performed by authorized personnel.

The initial Super_Admin role must be assigned through secure database access. Contact your system administrator or follow your organization's security protocols for role assignment.

### Step 3: Verify Access
1. Log out and log back in to refresh permissions
2. Navigate to the admin dashboard
3. Verify access to all administrative features
4. Test beta management functionality

### Step 4: Configure System Settings
1. Set up email configuration for notifications
2. Configure automation rules for beta requests
3. Verify backup systems are operational
4. Test security monitoring features

## 👥 User Role Management

### Managing User Roles (Super_Admin Only)

Once the initial Super_Admin is established, role management can be performed through the secure admin interface:

1. **Access Role Management**
   - Navigate to Admin Dashboard → Beta Testing → User Roles
   - View all users and their current roles
   - Access role change functionality

2. **Role Assignment Process**
   - Select the target user from the list
   - Choose the appropriate new role
   - Review security warnings for elevated roles
   - Confirm the role change
   - Verify the change in the audit log

3. **Security Considerations**
   - All role changes are logged for audit purposes
   - Super_Admin role should only be assigned to trusted personnel
   - Regular review of user roles is recommended
   - Immediate revocation capabilities for security incidents

### Role Assignment Guidelines

#### When to Assign Admin Roles
- **Admin**: For users who need to manage teams and basic configuration
- **Super_Admin**: Only for users who require full system access and security responsibilities

#### Security Best Practices
- Limit the number of Super_Admin users
- Regular audit of administrative access
- Immediate role revocation for departing personnel
- Two-person authorization for critical role changes (recommended)

## 🔒 Security Protocols

### Access Control
- Role-based permissions with principle of least privilege
- Row-level security on all sensitive data
- Audit logging for all administrative actions
- Session management and timeout controls

### Data Protection
- Encrypted data storage and transmission
- Minimal data collection principles
- GDPR and privacy compliance measures
- Secure backup and recovery procedures

### Monitoring and Alerts
- Real-time security monitoring
- Automated alerts for suspicious activities
- Performance monitoring and optimization
- Error tracking and resolution

## 🛠️ Administrative Features

### Beta Access Management
- Automated request processing with configurable rules
- Manual review and approval workflows
- Promo code generation and tracking
- Conversion analytics and reporting

### User Administration
- Comprehensive user management interface
- Role assignment and permission control
- Activity monitoring and audit trails
- Account management and security settings

### System Configuration
- Email system configuration and testing
- Automation rule management
- Performance optimization settings
- Security policy configuration

### Backup and Recovery
- Automated backup scheduling
- Disaster recovery planning and testing
- Data integrity verification
- Recovery procedure documentation

## 📊 Monitoring and Analytics

### Administrative Dashboard
- Real-time system health monitoring
- User activity and engagement metrics
- Beta program performance analytics
- Security incident tracking

### Reporting Capabilities
- User role and permission reports
- Beta conversion and revenue analytics
- System performance and optimization reports
- Security audit and compliance reports

## 🆘 Troubleshooting

### Common Issues

#### Access Denied Errors
- Verify role assignment in database
- Clear browser cache and session data
- Check for recent permission changes
- Contact system administrator if issues persist

#### Role Assignment Problems
- Ensure proper Super_Admin authentication
- Verify target user exists in system
- Check for database connectivity issues
- Review audit logs for error details

#### System Configuration Issues
- Verify environment variable configuration
- Check database migration status
- Test email system connectivity
- Review security policy settings

### Support Escalation
For issues requiring immediate attention:
1. Document the specific error or issue
2. Gather relevant system logs and user information
3. Follow your organization's incident response procedures
4. Contact technical support with detailed information

## 📋 Maintenance Procedures

### Regular Maintenance Tasks
- Weekly review of user roles and permissions
- Monthly security audit and compliance check
- Quarterly backup and recovery testing
- Annual security policy review and updates

### System Updates
- Follow secure deployment procedures
- Test role and permission functionality after updates
- Verify backup systems remain operational
- Update documentation as needed

### Security Reviews
- Regular assessment of administrative access
- Review and update security policies
- Monitor for new security threats and vulnerabilities
- Implement security improvements and patches

## 📚 Additional Resources

### Documentation References
- [Security Guide](./security.md) - Comprehensive security documentation
- [Beta Access System](./beta-access-system.md) - Beta management features
- [Deployment Guide](./05-deployment.md) - Production deployment procedures

### Support Contacts
- Technical Support: Follow your organization's support procedures
- Security Issues: Use designated security incident response channels
- Documentation Updates: Submit through proper change management processes

---

**Important**: This guide contains sensitive administrative information. Access should be restricted to authorized personnel only. Follow your organization's security policies and procedures when implementing these guidelines.
