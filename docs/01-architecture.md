# 1. Architecture Overview

This document provides a high-level overview of the ScriptGenius system architecture.

## Guiding Principles

*   **Scalability:** Leverage serverless and managed services to handle growth without significant infrastructure overhead.
*   **Maintainability:** Use a modular, feature-based architecture to keep the codebase organized and easy to navigate.
*   **Security:** Implement security at every layer, from the frontend to the database (Row Level Security).

## Core Components

The application is comprised of three main layers:

1.  **Frontend:** A modern single-page application (SPA) built with React.
2.  **Backend Platform:** Supabase provides the core backend services (database, authentication, storage, serverless functions).
3.  **Third-Party Services:** Integrated services for specialized tasks like payments and AI.

### System Architecture Diagram

```mermaid
graph TD
    subgraph User Browser
        A[React Frontend - Vite/TypeScript]
    end

    subgraph Supabase Platform
        B[Supabase Auth] --> C
        C[PostgreSQL Database] --> D
        D[Supabase Storage]
        E[Edge Functions]
    end

    subgraph Third-Party Services
        F[Stripe API]
        G[OpenAI API]
    end

    A -- API Calls --> E
    A -- Direct DB/Auth/Storage Calls --> B
    E -- Interacts with --> C
    E -- Calls out to --> F
    E -- Calls out to --> G
```

### Technology Stack

*   **Framework:** React (with Vite)
*   **Language:** TypeScript
*   **UI:** Shadcn UI, Tailwind CSS
*   **State Management:** React Query, useReducer patterns, React Context
*   **Performance:** React.memo, useMemo, useCallback optimizations
*   **Caching:** Multi-layer caching with configurable TTL
*   **Monitoring:** Real-time performance tracking and optimization tools
*   **Data Fetching:** React Query with intelligent caching and background sync
*   **Editor:** ProseMirror
*   **Backend:** Supabase (Auth, PostgreSQL, Storage, Edge Functions)
*   **Testing:** Vitest (Unit), Playwright (E2E)
*   **Linting:** ESLint, Prettier 