# 2. Codebase Guide

This guide explains the structure of the frontend codebase located in the `/src` directory.

## Directory Structure

*   `src/components/`: Contains shared, reusable UI components (e.g., `Button`, `Dialog`, `Card`). This is our core component library.
*   `src/features/`: Home to large, self-contained product features (e.g., `marketplace`, `production`, `coverage`). Each feature folder contains its own components, hooks, and logic. This is a "feature-sliced" approach.
*   `src/pages/`: Contains components that map directly to application routes (e.g., `Dashboard.tsx`, `Settings.tsx`).
*   `src/hooks/`: Contains shared, reusable React hooks that are not specific to one feature.
    *   `useOptimizedQuery.ts`: React Query optimizations with caching strategies
    *   `useOptimizedStoryboards.ts`: Optimized storyboard data management
    *   `useOptimizedTeam.ts`: Team data with background sync
    *   `usePerformanceOptimization.ts`: Performance utilities (debouncing, throttling, etc.)
*   `src/contexts/`: Contains shared React Context providers (e.g., `AuthContext`).
*   `src/lib/`: A utility belt for the application. Contains wrappers for APIs, utility functions, and configurations.
    *   `lib/api/`: Functions for making server-side API calls.
    *   `lib/ai/`: Logic for interacting with AI models.
    *   `lib/utils.ts`: General-purpose helper functions.
    *   `lib/prosemirror/`: Optimized ProseMirror editor implementation
*   `src/types/`: Contains shared TypeScript type definitions, especially for API responses and database tables.
*   `src/integrations/`: Code for integrating with third-party services, like the Supabase client.

## Performance Architecture

### Component Optimization Patterns

**Memoization Strategy**:
```typescript
// Heavy components use React.memo
const ExpensiveComponent = memo(({ data }) => {
  // Memoize expensive calculations
  const processedData = useMemo(() =>
    heavyProcessing(data), [data]
  );

  // Stable event handlers
  const handleClick = useCallback(() => {
    // Handler logic
  }, [dependencies]);

  return <div>{/* Component JSX */}</div>;
});
```

**State Management Patterns**:
```typescript
// Complex state uses useReducer
const [state, dispatch] = useReducer(reducer, initialState);

// Simple state uses useState with memoization
const [value, setValue] = useState(initialValue);
const memoizedValue = useMemo(() => value, [value]);
```

### Data Fetching Architecture

**React Query Integration**:
```typescript
// Optimized queries with caching
const { data, isLoading } = useOptimizedQuery(
  ['resource', id],
  () => fetchResource(id),
  {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  }
);
```

**Caching Strategy**:
- **Real-time data**: 2-5 minute cache
- **User data**: 5-10 minute cache
- **Static data**: 30+ minute cache
- **Background sync**: For non-critical updates

### Performance Monitoring

**Development Tools**:
- `PerformanceMonitor`: Real-time component tracking
- `PerformanceTestSuite`: Comprehensive benchmarking
- Component render time tracking
- Memory usage monitoring

## Code Organization Principles

### Feature-Based Architecture

Each feature in `src/features/` follows this structure:
```
features/
├── marketplace/
│   ├── components/          # Feature-specific components
│   │   ├── ScreenplayCard.tsx (optimized with memo)
│   │   ├── BrowseTab.tsx (memoized sorting)
│   │   └── MyScreenplays.tsx (optimized list rendering)
│   ├── hooks/              # Feature-specific hooks
│   ├── types/              # Feature-specific types
│   └── utils/              # Feature-specific utilities
```

### Performance Best Practices

1. **Component Level**:
   - Use `React.memo` for expensive components
   - Memoize calculations with `useMemo`
   - Stabilize handlers with `useCallback`
   - Implement lazy loading for heavy components

2. **Data Level**:
   - Use React Query for server state
   - Implement proper caching strategies
   - Debounce user inputs
   - Use pagination for large datasets

3. **Bundle Level**:
   - Code split by routes and features
   - Lazy load non-critical components
   - Optimize images with lazy loading
   - Use dynamic imports for heavy libraries