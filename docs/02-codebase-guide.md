# 2. Codebase Guide

This guide explains the structure of the frontend codebase located in the `/src` directory.

## Directory Structure

*   `src/components/`: Contains shared, reusable UI components (e.g., `Button`, `Dialog`, `Card`). This is our core component library.
*   `src/features/`: Home to large, self-contained product features (e.g., `marketplace`, `production`, `coverage`). Each feature folder contains its own components, hooks, and logic. This is a "feature-sliced" approach.
*   `src/pages/`: Contains components that map directly to application routes (e.g., `Dashboard.tsx`, `Settings.tsx`).
*   `src/hooks/`: Contains shared, reusable React hooks that are not specific to one feature.
*   `src/contexts/`: Contains shared React Context providers (e.g., `AuthContext`).
*   `src/lib/`: A utility belt for the application. Contains wrappers for APIs, utility functions, and configurations.
    *   `lib/api/`: Functions for making server-side API calls.
    *   `lib/ai/`: Logic for interacting with AI models.
    *   `lib/utils.ts`: General-purpose helper functions.
*   `src/types/`: Contains shared TypeScript type definitions, especially for API responses and database tables.
*   `src/integrations/`: Code for integrating with third-party services, like the Supabase client. 