# 5. Deployment

This document describes the deployment process for ScriptGenius.

## Environments

*   **Staging:** Deploys automatically on every push to the `develop` branch. This environment is used for internal QA and testing.
*   **Production:** Deploys automatically when a pull request is merged into the `main` branch.

## CI/CD Pipeline (GitHub Actions)

Our pipeline is defined in `.github/workflows/ci.yml`. It performs the following steps on every push and pull request:

1.  **Lint:** Checks for code style issues.
2.  **Test:** Runs the complete unit and integration test suite.
3.  **Build:** Creates a production-ready build of the frontend application.
4.  **Deploy:** If the branch is `develop` or `main`, it deploys the relevant resources:
    *   **Frontend:** The contents of the `/dist` folder are deployed to Vercel/Netlify.
    *   **Supabase:** New database migrations and edge functions are deployed to the corresponding Supabase project.

## Environment Variables

Secrets and environment variables are managed through GitHub repository secrets and passed to the CI/CD pipeline. The frontend build consumes these at build time, while the Supabase deployment injects them into the function environment. 