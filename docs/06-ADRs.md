# 6. Architectural Decision Records (ADRs)

This directory contains records of significant architectural decisions made during the project's development.

## What is an ADR?

An ADR is a short document that captures a single architectural decision. It describes the context of the decision, the options considered, and the final decision made, along with its consequences.

## Existing ADRs

*   [ADR-001: Choice of Supabase as BaaS](./ADRs/001-use-supabase-as-baas.md)
*   [ADR-002: Using ProseMirror for the Script Editor](./ADRs/002-use-prosemirror-for-editor.md)

---

### **Template for a new ADR**

*   **Title:** A short, descriptive title (e.g., "ADR-003: Adopt Playwright for E2E Testing").
*   **Status:** Proposed | Accepted | Deprecated | Superseded.
*   **Context:** What is the problem or issue we are trying to solve?
*   **Decision Drivers:** What are the forces, constraints, and requirements influencing this decision?
*   **Considered Options:** A list of the options that were considered.
*   **Decision:** The option that was chosen and why.
*   **Consequences:** What are the positive, negative, and neutral consequences of this decision? 