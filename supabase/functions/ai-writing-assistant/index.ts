
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AIAssistantRequest {
  message: string;
  context?: {
    currentScene?: string;
    characters?: string[];
    genre?: string;
    contentAnalysis?: {
      visualLanguage: number;
      subtext: number;
      characterVoiceConsistency: number;
      pacing: 'too-fast' | 'good' | 'too-slow';
      strengths: string[];
      weaknesses: string[];
    };
    screenplayContext?: {
      currentElement: string;
      previousElements: string[];
      tonalContext: string;
      dialoguePattern?: string;
      location?: string;
      timeOfDay?: string;
    };
    currentContent?: string;
  };
}

// Enhanced system prompt with advanced screenplay analysis capabilities
const ENHANCED_WRITING_ASSISTANT_SYSTEM_PROMPT = `
You are an expert AI writing assistant specialized in professional screenwriting and creative writing. You have advanced capabilities in:

**Core Expertise:**
- **Dialogue Enhancement:** Sharpen character voice, improve subtext, suggest authenticity and emotional nuance
- **Character Development:** Deepen motivations, create meaningful arcs, add specific behavioral details
- **Scene Analysis:** Identify ways to increase dramatic tension, strengthen pacing, support cinematic language
- **Plot Structure:** Suggest improvements according to three-act paradigm and genre conventions
- **Visual Language:** Recommend "show, don't tell" techniques and enhance cinematic impact
- **Industry Standards:** All guidance follows professional screenplay formatting and industry practices

**Advanced Analysis:**
You can analyze content quality metrics including:
- Visual Language Score (how well the writing shows vs tells)
- Subtext Level (depth of implied meaning in dialogue)
- Character Voice Consistency
- Pacing Assessment (too-fast/good/too-slow)
- Tonal Context (dramatic, comedic, action, romantic, thriller)
- Current Element Type (scene heading, action, character, dialogue, etc.)

**Response Guidelines:**
- Provide specific, actionable suggestions with concrete examples
- Reference the context analysis when available (visual language scores, pacing, etc.)
- Adapt advice based on current element type and tonal context
- Offer 2-3 alternative approaches when relevant
- Structure feedback clearly with brief sections
- Be encouraging while providing professional-level improvements
- When content analysis is provided, reference specific metrics in your advice

**Industry Focus:**
- Prioritize practical, impactful edits over generic advice
- Consider genre-specific conventions when relevant
- Maintain professional screenplay format standards
- Focus on authentic, visual storytelling that translates well to screen

Remember: Be practical, supportive, and industry-focused. Offer clear improvements that make scripts more authentic, visual, and dramatically strong.
`;

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { message, context }: AIAssistantRequest = await req.json();

    // Build enhanced context-aware system prompt
    let contextualPrompt = ENHANCED_WRITING_ASSISTANT_SYSTEM_PROMPT;
    
    if (context) {
      contextualPrompt += "\n\n**Current Context:**";
      
      if (context.currentScene) {
        contextualPrompt += `\n- Current Scene: ${context.currentScene}`;
      }
      
      if (context.characters && context.characters.length > 0) {
        contextualPrompt += `\n- Characters: ${context.characters.join(', ')}`;
      }
      
      if (context.genre) {
        contextualPrompt += `\n- Genre: ${context.genre}`;
      }
      
      // Enhanced analysis context
      if (context.contentAnalysis) {
        contextualPrompt += `\n\n**Content Analysis:**`;
        contextualPrompt += `\n- Visual Language Score: ${context.contentAnalysis.visualLanguage}% (how well it shows vs tells)`;
        contextualPrompt += `\n- Subtext Level: ${context.contentAnalysis.subtext}% (depth of implied meaning)`;
        contextualPrompt += `\n- Character Voice Consistency: ${context.contentAnalysis.characterVoiceConsistency}%`;
        contextualPrompt += `\n- Pacing: ${context.contentAnalysis.pacing}`;
        
        if (context.contentAnalysis.strengths.length > 0) {
          contextualPrompt += `\n- Strengths: ${context.contentAnalysis.strengths.join(', ')}`;
        }
        
        if (context.contentAnalysis.weaknesses.length > 0) {
          contextualPrompt += `\n- Areas for improvement: ${context.contentAnalysis.weaknesses.join(', ')}`;
        }
      }
      
      if (context.screenplayContext) {
        contextualPrompt += `\n\n**Screenplay Context:**`;
        contextualPrompt += `\n- Current Element: ${context.screenplayContext.currentElement}`;
        contextualPrompt += `\n- Recent Elements: ${context.screenplayContext.previousElements.slice(-3).join(' → ')}`;
        contextualPrompt += `\n- Tonal Context: ${context.screenplayContext.tonalContext}`;
        
        if (context.screenplayContext.dialoguePattern) {
          contextualPrompt += `\n- Dialogue Pattern: ${context.screenplayContext.dialoguePattern}`;
        }
        
        if (context.screenplayContext.location) {
          contextualPrompt += `\n- Location: ${context.screenplayContext.location}`;
        }
        
        if (context.screenplayContext.timeOfDay) {
          contextualPrompt += `\n- Time: ${context.screenplayContext.timeOfDay}`;
        }
      }
      
      if (context.currentContent) {
        contextualPrompt += `\n\n**Recent Content Context:**\n${context.currentContent}`;
      }
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          { role: 'system', content: contextualPrompt },
          { role: 'user', content: message }
        ],
        max_tokens: 1200,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    const assistantResponse = data.choices[0].message.content;

    return new Response(JSON.stringify({ 
      response: assistantResponse,
      usage: data.usage 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in enhanced ai-writing-assistant function:', error);
    return new Response(JSON.stringify({ 
      error: 'Failed to get AI assistance',
      details: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
