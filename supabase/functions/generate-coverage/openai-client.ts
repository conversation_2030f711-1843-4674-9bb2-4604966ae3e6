
import { getSystemPrompt, getUserPrompt } from './prompts.ts';
import { parseAIResponse, type CoverageResponse } from './response-parser.ts';

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');

export async function generateCoverageWithAI(scene: any, fidelityLevel: string): Promise<CoverageResponse> {
  const systemPrompt = getSystemPrompt(fidelityLevel);
  const userPrompt = getUserPrompt(scene);

  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${openAIApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 3000, // Increased for Premium analysis
    }),
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status}`);
  }

  const data = await response.json();
  const content = data.choices[0].message.content;

  // Parse the structured response
  return parseAIResponse(content);
}
