
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

export interface SceneData {
  title: string;
  content: string;
  description: string;
}

export async function getSceneContent(sceneId: string): Promise<SceneData | null> {
  // Create Supabase client with service key for server-side operations
  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  // Get scene content
  const { data: scene, error: sceneError } = await supabase
    .from('scenes')
    .select('title, content, description')
    .eq('id', sceneId)
    .single();

  if (sceneError || !scene) {
    console.error('Error fetching scene:', sceneError);
    return null;
  }

  return scene;
}
