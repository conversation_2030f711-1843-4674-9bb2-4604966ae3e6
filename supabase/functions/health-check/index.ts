
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface HealthCheckResult {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  error?: string;
  metadata?: Record<string, any>;
}

interface SystemHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  environment: string;
  uptime: number;
  checks: HealthCheckResult[];
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const url = new URL(req.url);
  const path = url.pathname;

  try {
    if (path === '/health-check' || path === '/health-check/') {
      return await handleHealthCheck(req);
    } else if (path === '/health-check/database') {
      return await handleDatabaseCheck(req);
    } else if (path === '/health-check/quick') {
      return await handleQuickCheck(req);
    } else {
      return new Response(
        JSON.stringify({ error: 'Not found' }),
        { 
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }
  } catch (error) {
    console.error('Health check error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        message: error.message 
      }),
      { 
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

async function handleHealthCheck(req: Request): Promise<Response> {
  const startTime = Date.now();
  const checks: HealthCheckResult[] = [];

  // Database check
  checks.push(await checkDatabase());
  
  // Auth check
  checks.push(await checkAuth());
  
  // Storage check
  checks.push(await checkStorage());

  // Memory check (simulated)
  checks.push(await checkMemory());

  const summary = {
    total: checks.length,
    healthy: checks.filter(c => c.status === 'healthy').length,
    unhealthy: checks.filter(c => c.status === 'unhealthy').length,
    degraded: checks.filter(c => c.status === 'degraded').length
  };

  const overallStatus = summary.unhealthy > 0 ? 'unhealthy' : 
                       summary.degraded > 0 ? 'degraded' : 'healthy';

  const health: SystemHealth = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: Deno.env.get('FUNCTION_VERSION') || '1.0.0',
    environment: Deno.env.get('DENO_ENV') || 'production',
    uptime: Date.now() - startTime, // Simplified uptime
    checks,
    summary
  };

  const status = overallStatus === 'healthy' ? 200 : 
                 overallStatus === 'degraded' ? 200 : 503;

  return new Response(
    JSON.stringify(health, null, 2),
    { 
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  );
}

async function handleDatabaseCheck(req: Request): Promise<Response> {
  const result = await checkDatabase();
  const status = result.status === 'healthy' ? 200 : 503;
  
  return new Response(
    JSON.stringify(result, null, 2),
    { 
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  );
}

async function handleQuickCheck(req: Request): Promise<Response> {
  // Ultra-fast health check for load balancers
  return new Response(
    JSON.stringify({ 
      status: 'healthy',
      timestamp: new Date().toISOString()
    }),
    { 
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    }
  );
}

async function checkDatabase(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    // Import Supabase client
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2.50.0');
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Simple query to test database connectivity
    const { error } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    const responseTime = Date.now() - start;
    
    return {
      name: 'database',
      status: error ? 'unhealthy' : 'healthy',
      responseTime,
      error: error?.message,
      metadata: { 
        connection: 'supabase-postgres',
        query: 'SELECT id FROM profiles LIMIT 1'
      }
    };
  } catch (error) {
    return {
      name: 'database',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error.message || 'Database connection failed',
      metadata: { connection: 'supabase-postgres' }
    };
  }
}

async function checkAuth(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2.50.0');
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test auth service
    const { error } = await supabase.auth.getSession();
    
    const responseTime = Date.now() - start;
    
    return {
      name: 'auth',
      status: error ? 'unhealthy' : 'healthy',
      responseTime,
      error: error?.message,
      metadata: { provider: 'supabase-auth' }
    };
  } catch (error) {
    return {
      name: 'auth',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error.message || 'Auth service failed',
      metadata: { provider: 'supabase-auth' }
    };
  }
}

async function checkStorage(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2.50.0');
    
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')!;
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);
    
    // Test storage service
    const { data, error } = await supabase.storage.listBuckets();
    
    const responseTime = Date.now() - start;
    
    return {
      name: 'storage',
      status: error ? 'unhealthy' : 'healthy',
      responseTime,
      error: error?.message,
      metadata: { 
        provider: 'supabase-storage',
        buckets: data?.length || 0
      }
    };
  } catch (error) {
    return {
      name: 'storage',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error.message || 'Storage service failed',
      metadata: { provider: 'supabase-storage' }
    };
  }
}

async function checkMemory(): Promise<HealthCheckResult> {
  const start = Date.now();
  
  try {
    // Get memory usage info (Deno specific)
    const memInfo = Deno.memoryUsage();
    const usedMB = memInfo.heapUsed / (1024 * 1024);
    const totalMB = memInfo.heapTotal / (1024 * 1024);
    const usagePercent = (usedMB / totalMB) * 100;
    
    const status = usagePercent > 90 ? 'unhealthy' : 
                   usagePercent > 75 ? 'degraded' : 'healthy';
    
    return {
      name: 'memory',
      status,
      responseTime: Date.now() - start,
      metadata: {
        usedMB: Math.round(usedMB),
        totalMB: Math.round(totalMB),
        usagePercent: Math.round(usagePercent),
        external: Math.round(memInfo.external / (1024 * 1024))
      }
    };
  } catch (error) {
    return {
      name: 'memory',
      status: 'unhealthy',
      responseTime: Date.now() - start,
      error: error.message || 'Memory check failed'
    };
  }
}
