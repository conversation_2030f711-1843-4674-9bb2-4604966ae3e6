
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Authenticate user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Authentication failed');
    }

    const { screenplay_id, offer_id, amount, type } = await req.json();

    // Validate input
    if (!amount || amount <= 0) {
      throw new Error('Invalid amount');
    }

    if (!type || !['purchase', 'offer_payment'].includes(type)) {
      throw new Error('Invalid payment type');
    }

    if (type === 'purchase' && !screenplay_id) {
      throw new Error('Screenplay ID required for purchase');
    }

    if (type === 'offer_payment' && !offer_id) {
      throw new Error('Offer ID required for offer payment');
    }

    let sellerId: string;
    let productName: string;
    let sellerStripeAccountId: string;

    if (type === 'purchase') {
      // Get screenplay and seller information
      const { data: screenplay, error: screenplayError } = await supabase
        .from('screenplays')
        .select(`
          title,
          price,
          user_id,
          seller_accounts!inner(stripe_account_id, charges_enabled)
        `)
        .eq('id', screenplay_id)
        .single();

      if (screenplayError || !screenplay) {
        throw new Error('Screenplay not found');
      }

      if (!screenplay.seller_accounts.charges_enabled) {
        throw new Error('Seller account not ready for payments');
      }

      if (screenplay.price !== amount) {
        throw new Error('Price mismatch');
      }

      sellerId = screenplay.user_id;
      productName = screenplay.title;
      sellerStripeAccountId = screenplay.seller_accounts.stripe_account_id;
    } else {
      // Handle offer payment logic here
      const { data: offer, error: offerError } = await supabase
        .from('offers')
        .select(`
          amount,
          screenplay_id,
          screenplays!inner(title, user_id),
          seller_accounts!inner(stripe_account_id, charges_enabled)
        `)
        .eq('id', offer_id)
        .single();

      if (offerError || !offer) {
        throw new Error('Offer not found');
      }

      if (!offer.seller_accounts.charges_enabled) {
        throw new Error('Seller account not ready for payments');
      }

      if (offer.amount !== amount) {
        throw new Error('Offer amount mismatch');
      }

      sellerId = offer.screenplays.user_id;
      productName = `Offer for ${offer.screenplays.title}`;
      sellerStripeAccountId = offer.seller_accounts.stripe_account_id;
    }

    // Check if customer already exists
    const customers = await stripe.customers.list({ 
      email: user.email!,
      limit: 1 
    });

    let customerId: string;
    if (customers.data.length > 0) {
      customerId = customers.data[0].id;
    } else {
      const customer = await stripe.customers.create({
        email: user.email!,
        metadata: {
          user_id: user.id
        }
      });
      customerId = customer.id;
    }

    // Calculate platform fee (5%)
    const platformFeeRate = 0.05;
    const platformFee = Math.round(amount * platformFeeRate);

    // Create Checkout session with Connect integration
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price_data: {
            currency: 'usd',
            product_data: {
              name: productName,
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${req.headers.get('origin')}/payment-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get('origin')}/payment-canceled`,
      payment_intent_data: {
        application_fee_amount: platformFee,
        transfer_data: {
          destination: sellerStripeAccountId,
        },
        metadata: {
          type,
          screenplay_id: screenplay_id || '',
          offer_id: offer_id || '',
          buyer_id: user.id,
          seller_id: sellerId,
        }
      },
      metadata: {
        type,
        screenplay_id: screenplay_id || '',
        offer_id: offer_id || '',
        buyer_id: user.id,
        seller_id: sellerId,
      }
    });

    // Create transaction record
    const { error: transactionError } = await supabase
      .from('transactions')
      .insert({
        buyer_id: user.id,
        seller_id: sellerId,
        screenplay_id: type === 'purchase' ? screenplay_id : null,
        offer_id: type === 'offer_payment' ? offer_id : null,
        amount,
        platform_fee: platformFee,
        stripe_session_id: session.id,
        stripe_payment_intent_id: session.payment_intent as string,
        status: 'pending',
        type,
      });

    if (transactionError) {
      console.error('Error creating transaction record:', transactionError);
      // Don't throw here as the Stripe session is already created
    }

    console.log(`Created payment session for user ${user.id}: ${session.id}`);

    return new Response(JSON.stringify({ 
      sessionUrl: session.url 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error processing payment:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to process payment'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
