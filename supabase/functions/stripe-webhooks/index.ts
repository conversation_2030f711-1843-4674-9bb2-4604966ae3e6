
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import Stripe from 'https://esm.sh/stripe@14.21.0'
import { logWebhookEvent, withRetry } from './utils/logging.ts'
import { handleCheckoutCompleted } from './handlers/checkoutHandlers.ts'
import { handleAccountUpdated, handleCapabilityUpdated, handlePersonUpdated } from './handlers/accountHandlers.ts'
import { handlePaymentSucceeded, handlePaymentFailed } from './handlers/paymentHandlers.ts'

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
  apiVersion: '2023-10-16',
})

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  const signature = req.headers.get('stripe-signature');
  
  if (!signature) {
    console.error('No Stripe signature found');
    return new Response('No signature', { status: 400 });
  }

  try {
    const body = await req.text();
    const event = await stripe.webhooks.constructEventAsync(
      body,
      signature,
      Deno.env.get('STRIPE_WEBHOOK_SECRET') ?? ''
    );

    console.log(`Processing webhook: ${event.type} (${event.id})`);

    // Check for duplicate events
    const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { data: existingLog } = await supabase
      .from('webhook_logs')
      .select('id')
      .eq('event_id', event.id)
      .eq('status', 'success')
      .single();

    if (existingLog) {
      console.log(`Event ${event.id} already processed successfully`);
      return new Response('Event already processed', { status: 200 });
    }

    // Process the webhook with retry mechanism
    await withRetry(async () => {
      switch (event.type) {
        case 'checkout.session.completed':
          await handleCheckoutCompleted(event.data.object);
          break;
        
        case 'account.updated':
          await handleAccountUpdated(event.data.object);
          break;
          
        case 'capability.updated':
          await handleCapabilityUpdated(event.data.object);
          break;

        case 'person.updated':
          await handlePersonUpdated(event.data.object);
          break;

        case 'payment_intent.succeeded':
          await handlePaymentSucceeded(event.data.object);
          break;

        case 'payment_intent.payment_failed':
          await handlePaymentFailed(event.data.object);
          break;
          
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }
    });

    await logWebhookEvent(event, 'success');
    return new Response('Webhook processed successfully', { status: 200 });

  } catch (error) {
    console.error('Webhook error:', error);
    
    // Log the error for monitoring
    try {
      const body = await req.text();
      const event = JSON.parse(body);
      await logWebhookEvent(event, 'error', error);
    } catch (logError) {
      console.error('Failed to log webhook error:', logError);
    }

    // Return 500 to trigger Stripe retry
    return new Response('Webhook processing failed', { status: 500 });
  }
});
