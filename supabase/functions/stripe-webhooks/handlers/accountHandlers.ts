
export async function handleAccountUpdated(account: any) {
  console.log(`Processing account update for: ${account.id}`);
  
  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  const updateData = {
    account_status: getAccountStatus(account),
    onboarding_completed: account.details_submitted && account.charges_enabled,
    payouts_enabled: account.payouts_enabled,
    charges_enabled: account.charges_enabled,
    details_submitted: account.details_submitted,
    business_type: account.business_type,
    country: account.country,
    default_currency: account.default_currency,
    capabilities: account.capabilities,
    requirements: account.requirements,
    updated_at: new Date().toISOString(),
  };

  const { error: updateError } = await supabase
    .from('seller_accounts')
    .update(updateData)
    .eq('stripe_account_id', account.id);

  if (updateError) {
    console.error('Error updating seller account:', updateError);
    throw updateError;
  }

  // Mark onboarding sessions as completed if account is ready
  if (account.details_submitted && account.charges_enabled) {
    await supabase
      .from('seller_onboarding_sessions')
      .update({ 
        completed: true,
        updated_at: new Date().toISOString()
      })
      .eq('stripe_account_id', account.id)
      .eq('completed', false);
  }

  console.log(`Seller account updated: ${account.id}`);
}

export async function handleCapabilityUpdated(capability: any) {
  console.log(`Processing capability update for account: ${capability.account}`);
  
  const Stripe = await import('https://esm.sh/stripe@14.21.0');
  const stripe = new Stripe.default(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
    apiVersion: '2023-10-16',
  });

  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  try {
    const account = await stripe.accounts.retrieve(capability.account);
    
    const { error: updateError } = await supabase
      .from('seller_accounts')
      .update({
        capabilities: account.capabilities,
        charges_enabled: account.charges_enabled,
        payouts_enabled: account.payouts_enabled,
        requirements: account.requirements,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_account_id', capability.account);

    if (updateError) {
      console.error('Error updating seller account capabilities:', updateError);
      throw updateError;
    }

    console.log(`Seller account capabilities updated: ${capability.account}`);
  } catch (error) {
    console.error('Error retrieving account for capability update:', error);
    throw error;
  }
}

export async function handlePersonUpdated(person: any) {
  console.log(`Processing person update for account: ${person.account}`);
  
  const Stripe = await import('https://esm.sh/stripe@14.21.0');
  const stripe = new Stripe.default(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
    apiVersion: '2023-10-16',
  });

  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  try {
    const account = await stripe.accounts.retrieve(person.account);
    
    const { error: updateError } = await supabase
      .from('seller_accounts')
      .update({
        requirements: account.requirements,
        details_submitted: account.details_submitted,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_account_id', person.account);

    if (updateError) {
      console.error('Error updating seller account requirements:', updateError);
      throw updateError;
    }

    console.log(`Seller account requirements updated: ${person.account}`);
  } catch (error) {
    console.error('Error retrieving account for person update:', error);
    throw error;
  }
}

function getAccountStatus(account: any): string {
  if (!account.details_submitted) {
    return 'pending';
  }
  
  if (account.requirements?.currently_due?.length > 0 || 
      account.requirements?.past_due?.length > 0) {
    return 'restricted';
  }
  
  if (account.charges_enabled && account.payouts_enabled) {
    return 'active';
  }
  
  return 'pending';
}
