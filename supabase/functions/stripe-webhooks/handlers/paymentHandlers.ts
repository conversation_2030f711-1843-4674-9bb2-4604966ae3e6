
export async function handlePaymentSucceeded(paymentIntent: any) {
  console.log(`Processing successful payment: ${paymentIntent.id}`);
  
  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  const { error: updateError } = await supabase
    .from('transactions')
    .update({
      status: 'completed',
      completed_at: new Date().toISOString(),
      amount_received: paymentIntent.amount_received,
    })
    .eq('stripe_payment_intent_id', paymentIntent.id);

  if (updateError) {
    console.error('Error updating transaction for successful payment:', updateError);
    throw updateError;
  }
}

export async function handlePaymentFailed(paymentIntent: any) {
  console.log(`Processing failed payment: ${paymentIntent.id}`);
  
  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  const { error: updateError } = await supabase
    .from('transactions')
    .update({
      status: 'failed',
      error_message: paymentIntent.last_payment_error?.message || 'Payment failed',
      failed_at: new Date().toISOString(),
    })
    .eq('stripe_payment_intent_id', paymentIntent.id);

  if (updateError) {
    console.error('Error updating transaction for failed payment:', updateError);
    throw updateError;
  }
}
