
export async function handleCheckoutCompleted(session: any) {
  console.log(`Processing checkout completion for session: ${session.id}`);
  
  const { createClient } = await import('https://esm.sh/@supabase/supabase-js@2');
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Enhanced transaction update with validation
  const { error: updateError } = await supabase
    .from('transactions')
    .update({
      status: 'completed',
      completed_at: new Date().toISOString(),
      stripe_payment_intent_id: session.payment_intent,
      amount_received: session.amount_total,
      currency: session.currency,
      customer_email: session.customer_details?.email || null,
    })
    .eq('stripe_session_id', session.id);

  if (updateError) {
    console.error('Error updating transaction:', updateError);
    throw updateError;
  }

  // Update earnings summary for seller
  if (session.metadata?.seller_id) {
    await updateSellerEarnings(session.metadata.seller_id, session.amount_total, supabase);
  }

  console.log(`Transaction completed for session: ${session.id}`);
}

async function updateSellerEarnings(sellerId: string, amount: number, supabase: any) {
  // Calculate platform fee (e.g., 5%)
  const platformFeeRate = 0.05;
  const platformFee = Math.round(amount * platformFeeRate);
  const sellerEarning = amount - platformFee;

  const { error } = await supabase.rpc('update_seller_earnings', {
    seller_id: sellerId,
    earning_amount: sellerEarning,
    platform_fee: platformFee
  });

  if (error) {
    console.error('Error updating seller earnings:', error);
    throw error;
  }
}
