
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  // Create Supabase client
  const supabaseClient = createClient(
    Deno.env.get("SUPABASE_URL") ?? "",
    Deno.env.get("SUPABASE_ANON_KEY") ?? ""
  );

  try {
    // Retrieve authenticated user
    const authHeader = req.headers.get("Authorization")!;
    const token = authHeader.replace("Bearer ", "");
    const { data } = await supabaseClient.auth.getUser(token);
    const user = data.user;
    if (!user?.email) throw new Error("User not authenticated or email not available");

    // Get request body
    const { 
      planId, 
      planName, 
      priceAmount, 
      billingCycle = 'monthly',
      stripeProductId,
      promoCode,
      metadata = {} 
    } = await req.json();

    // Initialize Stripe
    const stripe = new Stripe(Deno.env.get("STRIPE_SECRET_KEY") || "", {
      apiVersion: "2023-10-16",
    });

    // Check if a Stripe customer record exists for this user
    const customers = await stripe.customers.list({ email: user.email, limit: 1 });
    let customerId;
    if (customers.data.length > 0) {
      customerId = customers.data[0].id;
    }

    // Prepare session parameters
    const sessionParams: any = {
      customer: customerId,
      customer_email: customerId ? undefined : user.email,
      line_items: [
        {
          price_data: {
            currency: "usd",
            product_data: { 
              name: `ScriptGenius ${planName}`,
              description: `${billingCycle === 'yearly' ? 'Annual' : 'Monthly'} subscription to ScriptGenius ${planName} plan`
            },
            unit_amount: priceAmount,
            recurring: { 
              interval: billingCycle === 'yearly' ? 'year' : 'month',
              interval_count: 1
            },
          },
          quantity: 1,
        },
      ],
      mode: "subscription",
      success_url: `${req.headers.get("origin")}/subscription-success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${req.headers.get("origin")}/pricing`,
      metadata: {
        plan_id: planId,
        billing_cycle: billingCycle,
        user_id: user.id,
        ...metadata
      },
      subscription_data: {
        metadata: {
          plan_id: planId,
          billing_cycle: billingCycle,
          user_id: user.id
        }
      }
    };

    // Add promotional code if provided
    if (promoCode) {
      try {
        const promoCodes = await stripe.promotionCodes.list({
          code: promoCode,
          active: true,
          limit: 1
        });
        
        if (promoCodes.data.length > 0) {
          sessionParams.discounts = [{
            promotion_code: promoCodes.data[0].id
          }];
        }
      } catch (promoError) {
        console.warn("Promo code validation failed:", promoError);
        // Continue without promo code rather than failing
      }
    }

    // If we have a specific Stripe product ID, use it
    if (stripeProductId) {
      sessionParams.line_items[0].price = stripeProductId;
      delete sessionParams.line_items[0].price_data;
    }

    // Create the checkout session
    const session = await stripe.checkout.sessions.create(sessionParams);

    return new Response(JSON.stringify({ url: session.url, sessionId: session.id }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });

  } catch (error) {
    console.error("Enhanced checkout error:", error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: error.stack 
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
