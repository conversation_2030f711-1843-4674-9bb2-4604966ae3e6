
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@14.21.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY') ?? '', {
  apiVersion: '2023-10-16',
})

const supabase = createClient(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
)

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Authenticate user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      throw new Error('Authentication failed');
    }

    // Check if seller account already exists
    const { data: existingAccount } = await supabase
      .from('seller_accounts')
      .select('stripe_account_id, account_status')
      .eq('user_id', user.id)
      .single();

    if (existingAccount) {
      // If account exists but isn't fully onboarded, create new account link
      if (existingAccount.account_status !== 'active') {
        const accountLink = await stripe.accountLinks.create({
          account: existingAccount.stripe_account_id,
          refresh_url: `${req.headers.get('origin')}/seller/setup?refresh=true`,
          return_url: `${req.headers.get('origin')}/seller/setup?success=true`,
          type: 'account_onboarding',
        });

        // Log the session
        await supabase.from('seller_onboarding_sessions').insert({
          user_id: user.id,
          stripe_account_id: existingAccount.stripe_account_id,
          stripe_account_link_id: accountLink.url,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        });

        return new Response(JSON.stringify({
          accountLink: accountLink.url,
          accountId: existingAccount.stripe_account_id
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        });
      } else {
        throw new Error('Seller account already exists and is active');
      }
    }

    // Get user profile for account creation
    const { data: profile } = await supabase
      .from('profiles')
      .select('full_name, username')
      .eq('id', user.id)
      .single();

    // Create new Stripe Connect account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // Default to US, can be made configurable
      email: user.email,
      business_profile: {
        name: profile?.full_name || profile?.username || user.email?.split('@')[0],
        product_description: 'Digital screenplay sales',
      },
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });

    // Create account link for onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${req.headers.get('origin')}/seller/setup?refresh=true`,
      return_url: `${req.headers.get('origin')}/seller/setup?success=true`,
      type: 'account_onboarding',
    });

    // Save seller account to database
    const { error: insertError } = await supabase
      .from('seller_accounts')
      .insert({
        user_id: user.id,
        stripe_account_id: account.id,
        account_status: 'pending',
        onboarding_completed: false,
        charges_enabled: false,
        payouts_enabled: false,
        details_submitted: false,
        business_type: account.business_type,
        country: account.country,
        default_currency: account.default_currency,
        capabilities: account.capabilities,
        requirements: account.requirements,
      });

    if (insertError) {
      // If database insert fails, try to delete the Stripe account
      try {
        await stripe.accounts.del(account.id);
      } catch (cleanupError) {
        console.error('Failed to cleanup Stripe account after database error:', cleanupError);
      }
      throw insertError;
    }

    // Log the onboarding session
    await supabase.from('seller_onboarding_sessions').insert({
      user_id: user.id,
      stripe_account_id: account.id,
      stripe_account_link_id: accountLink.url,
      expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
    });

    console.log(`Created seller account for user ${user.id}: ${account.id}`);

    return new Response(JSON.stringify({
      accountLink: accountLink.url,
      accountId: account.id
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });

  } catch (error) {
    console.error('Error creating seller account:', error);
    
    return new Response(JSON.stringify({ 
      error: error instanceof Error ? error.message : 'Failed to create seller account'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
});
