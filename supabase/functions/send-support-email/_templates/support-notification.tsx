
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
  Section,
  Hr,
} from 'npm:@react-email/components@0.0.22'
import * as React from 'npm:react@18.3.1'

interface SupportNotificationEmailProps {
  ticketId: string
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  category: string
  userAgent?: string
}

export const SupportNotificationEmail = ({
  ticketId,
  name,
  email,
  phone,
  subject,
  message,
  category,
  userAgent,
}: SupportNotificationEmailProps) => (
  <Html>
    <Head />
    <Preview>New Support Ticket #{ticketId} - {subject}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>New Support Ticket</Heading>
        
        <Section style={alertSection}>
          <Text style={alertText}>
            🎫 New support ticket received - requires attention
          </Text>
        </Section>
        
        <Section style={detailsSection}>
          <Text style={sectionHeader}>Ticket Information:</Text>
          <Text style={detail}><strong>Ticket ID:</strong> #{ticketId}</Text>
          <Text style={detail}><strong>Category:</strong> {category}</Text>
          <Text style={detail}><strong>Subject:</strong> {subject}</Text>
          <Text style={detail}><strong>Created:</strong> {new Date().toLocaleString()}</Text>
        </Section>
        
        <Section style={detailsSection}>
          <Text style={sectionHeader}>Contact Information:</Text>
          <Text style={detail}><strong>Name:</strong> {name}</Text>
          <Text style={detail}><strong>Email:</strong> <Link href={`mailto:${email}`} style={link}>{email}</Link></Text>
          {phone && <Text style={detail}><strong>Phone:</strong> {phone}</Text>}
        </Section>
        
        <Section style={messageSection}>
          <Text style={sectionHeader}>Message:</Text>
          <Text style={messageText}>{message}</Text>
        </Section>
        
        {userAgent && (
          <Section style={metaSection}>
            <Text style={metaHeader}>Technical Details:</Text>
            <Text style={metaText}><strong>User Agent:</strong> {userAgent}</Text>
          </Section>
        )}
        
        <Hr style={hr} />
        
        <Text style={actionText}>
          Please respond to this ticket promptly. Reply directly to this email or manage tickets in the admin dashboard.
        </Text>
        
        <Text style={footer}>
          ScriptGenius Support System
        </Text>
      </Container>
    </Body>
  </Html>
)

const main = {
  backgroundColor: '#ffffff',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '600px',
}

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0 20px',
  padding: '0',
}

const alertSection = {
  backgroundColor: '#fff3cd',
  border: '1px solid #ffeaa7',
  borderRadius: '8px',
  padding: '16px',
  margin: '20px 0',
}

const alertText = {
  color: '#856404',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0',
  textAlign: 'center' as const,
}

const detailsSection = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '20px',
  margin: '16px 0',
}

const messageSection = {
  backgroundColor: '#e3f2fd',
  border: '1px solid #bbdefb',
  borderRadius: '8px',
  padding: '20px',
  margin: '16px 0',
}

const metaSection = {
  backgroundColor: '#f5f5f5',
  border: '1px solid #e0e0e0',
  borderRadius: '8px',
  padding: '16px',
  margin: '16px 0',
}

const sectionHeader = {
  color: '#333',
  fontSize: '16px',
  fontWeight: 'bold',
  margin: '0 0 12px 0',
}

const metaHeader = {
  color: '#666',
  fontSize: '14px',
  fontWeight: 'bold',
  margin: '0 0 8px 0',
}

const detail = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '6px 0',
}

const messageText = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '22px',
  margin: '0',
  whiteSpace: 'pre-wrap' as const,
}

const metaText = {
  color: '#666',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '4px 0',
}

const hr = {
  borderColor: '#e9ecef',
  margin: '24px 0',
}

const link = {
  color: '#D4AF37',
  textDecoration: 'underline',
}

const actionText = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '16px 0',
  fontStyle: 'italic',
}

const footer = {
  color: '#898989',
  fontSize: '12px',
  lineHeight: '18px',
  marginTop: '32px',
  textAlign: 'center' as const,
}
