
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Link,
  Preview,
  Text,
  Section,
  Hr,
} from 'npm:@react-email/components@0.0.22'
import * as React from 'npm:react@18.3.1'

interface UserConfirmationEmailProps {
  name: string
  ticketId: string
  subject: string
  message: string
}

export const UserConfirmationEmail = ({
  name,
  ticketId,
  subject,
  message,
}: UserConfirmationEmailProps) => (
  <Html>
    <Head />
    <Preview>We've received your support request - Ticket #{ticketId}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Support Request Received</Heading>
        
        <Text style={text}>
          Hi {name},
        </Text>
        
        <Text style={text}>
          Thank you for contacting ScriptGenius support. We've received your message and will get back to you within 24 hours.
        </Text>
        
        <Section style={ticketSection}>
          <Text style={ticketHeader}>Ticket Details:</Text>
          <Text style={ticketDetail}><strong>Ticket ID:</strong> #{ticketId}</Text>
          <Text style={ticketDetail}><strong>Subject:</strong> {subject}</Text>
          <Hr style={hr} />
          <Text style={ticketDetail}><strong>Your Message:</strong></Text>
          <Text style={messageText}>{message}</Text>
        </Section>
        
        <Text style={text}>
          Our support team will review your request and respond as soon as possible. If you need immediate assistance, you can also reach us through our live chat feature in the app.
        </Text>
        
        <Text style={text}>
          Best regards,<br />
          The ScriptGenius Support Team
        </Text>
        
        <Hr style={hr} />
        
        <Text style={footer}>
          <Link href="https://scriptgenius.app" target="_blank" style={link}>
            ScriptGenius
          </Link>
          <br />
          Professional Screenwriting Tools
        </Text>
      </Container>
    </Body>
  </Html>
)

const main = {
  backgroundColor: '#ffffff',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
}

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
}

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: 'bold',
  margin: '40px 0 20px',
  padding: '0',
}

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '16px 0',
}

const ticketSection = {
  backgroundColor: '#f8f9fa',
  border: '1px solid #e9ecef',
  borderRadius: '8px',
  padding: '20px',
  margin: '24px 0',
}

const ticketHeader = {
  color: '#333',
  fontSize: '18px',
  fontWeight: 'bold',
  margin: '0 0 16px 0',
}

const ticketDetail = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
}

const messageText = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '8px 0',
  padding: '12px',
  backgroundColor: '#ffffff',
  border: '1px solid #dee2e6',
  borderRadius: '4px',
}

const hr = {
  borderColor: '#e9ecef',
  margin: '16px 0',
}

const link = {
  color: '#D4AF37',
  textDecoration: 'underline',
}

const footer = {
  color: '#898989',
  fontSize: '12px',
  lineHeight: '18px',
  marginTop: '32px',
  textAlign: 'center' as const,
}
