
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.50.0';
import { Resend } from 'npm:resend@4.0.0';
import { renderAsync } from 'npm:@react-email/components@0.0.22';
import React from 'npm:react@18.3.1';
import { UserConfirmationEmail } from './_templates/user-confirmation.tsx';
import { SupportNotificationEmail } from './_templates/support-notification.tsx';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface SupportRequest {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  category?: string;
  source?: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders } 
      }
    );
  }

  try {
    // Initialize clients
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const resendApiKey = Deno.env.get('RESEND_API_KEY')!;
    
    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY is not configured');
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    const resend = new Resend(resendApiKey);

    // Parse request
    const supportRequest: SupportRequest = await req.json();
    const { name, email, phone, subject, message, category = 'general', source = 'contact_form' } = supportRequest;

    // Validate required fields
    if (!name || !email || !subject || !message) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: name, email, subject, message' }),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json', ...corsHeaders } 
        }
      );
    }

    // Get user info if authenticated
    const authHeader = req.headers.get('authorization');
    let userId = null;
    if (authHeader) {
      const { data: { user } } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''));
      userId = user?.id || null;
    }

    // Get request metadata
    const userAgent = req.headers.get('user-agent');
    const forwarded = req.headers.get('x-forwarded-for');
    const realIp = req.headers.get('x-real-ip');
    const ipAddress = forwarded?.split(',')[0] || realIp || null;

    // Create support ticket
    const { data: ticket, error: ticketError } = await supabase
      .from('support_tickets')
      .insert({
        name,
        email,
        phone,
        subject,
        message,
        category,
        source,
        user_id: userId,
        user_agent: userAgent,
        ip_address: ipAddress,
      })
      .select('id')
      .single();

    if (ticketError) {
      console.error('Error creating support ticket:', ticketError);
      throw new Error('Failed to create support ticket');
    }

    const ticketId = ticket.id.split('-')[0].toUpperCase(); // Use first part of UUID as ticket ID

    console.log(`Creating support ticket ${ticketId} for ${email}`);

    // Render email templates
    const [userConfirmationHtml, supportNotificationHtml] = await Promise.all([
      renderAsync(
        React.createElement(UserConfirmationEmail, {
          name,
          ticketId,
          subject,
          message,
        })
      ),
      renderAsync(
        React.createElement(SupportNotificationEmail, {
          ticketId,
          name,
          email,
          phone,
          subject,
          message,
          category,
          userAgent,
        })
      ),
    ]);

    // Send emails
    const emailPromises = [
      // User confirmation email
      resend.emails.send({
        from: 'ScriptGenius Support <<EMAIL>>',
        to: [email],
        subject: `Support Request Received - Ticket #${ticketId}`,
        html: userConfirmationHtml,
      }),
      // Support team notification
      resend.emails.send({
        from: 'ScriptGenius Support System <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: `New Support Ticket #${ticketId} - ${subject}`,
        html: supportNotificationHtml,
        replyTo: email,
      }),
    ];

    const emailResults = await Promise.allSettled(emailPromises);
    
    // Log email results
    emailResults.forEach((result, index) => {
      const emailType = index === 0 ? 'user confirmation' : 'support notification';
      if (result.status === 'fulfilled') {
        console.log(`✅ ${emailType} email sent successfully:`, result.value);
      } else {
        console.error(`❌ Failed to send ${emailType} email:`, result.reason);
      }
    });

    return new Response(
      JSON.stringify({ 
        success: true, 
        ticketId,
        message: 'Support request submitted successfully' 
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Error in send-support-email function:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Failed to process support request',
        details: error.message 
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
};

serve(handler);
