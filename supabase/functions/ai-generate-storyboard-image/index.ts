
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const openAIApiKey = Deno.env.get('OPENAI_API_KEY');
const supabaseUrl = Deno.env.get("SUPABASE_URL");
const supabaseAnonKey = Deno.env.get("SUPABASE_ANON_KEY");

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const STYLE_MAP: Record<string, string> = {
  "Sketch": "hand-drawn storyboard sketch, rough pencil lines, cinematic, simple shading, black and white",
  "Illustrated": "detailed, digital illustration, vibrant colors, dynamic composition, high-quality",
  "Cinematic": "realistic film frame, cinematic lighting, 4k, dramatic angle, atmospheric",
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!openAIApiKey) throw new Error("OPENAI_API_KEY is not set");

    const authHeader = req.headers.get("authorization") || "";
    const jwt = authHeader.replace("Bearer ", "");
    if (!jwt) throw new Error("Missing JWT for user authentication");

    const { prompt, style = "Sketch", org_id } = await req.json();
    if (!prompt) {
      return new Response(JSON.stringify({ error: "Missing prompt" }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
    if (!org_id) {
      return new Response(JSON.stringify({ error: "Missing org_id" }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 1: Check and increment usage atomically
    const usageRes = await fetch(`${supabaseUrl}/rest/v1/rpc/get_or_increment_storyboard_usage`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "apikey": supabaseAnonKey,
        "Authorization": `Bearer ${jwt}`,
      },
      body: JSON.stringify({ target_org_id: org_id, increment: true }),
    });

    if (!usageRes.ok) {
      const err = await usageRes.text();
      return new Response(JSON.stringify({ error: `Usage check failed: ${err}` }), {
        status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const usage = await usageRes.json();
    const { remaining_generations, daily_limit } = usage[0] || {};
    if (remaining_generations < 0) {
      // Defensive, though our db function will already err before this
      return new Response(JSON.stringify({ error: `Quota exceeded: Only ${daily_limit} generations per day allowed.` }), {
        status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 2: Generate the image
    const styledPrompt = `[${STYLE_MAP[style] ?? STYLE_MAP["Sketch"]}] ${prompt}`.trim();
    const response = await fetch('https://api.openai.com/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: styledPrompt,
        model: "gpt-image-1",
        n: 1,
        size: "1024x1024"
      })
    });

    if (!response.ok) {
      const errorDetails = await response.text();
      throw new Error(`OpenAI API failed: ${errorDetails}`);
    }

    const result = await response.json();
    const imageB64 = result.data?.[0]?.b64_json;
    if (!imageB64) throw new Error("No image generated by OpenAI");

    return new Response(JSON.stringify({ 
      image: `data:image/png;base64,${imageB64}`,
      remaining_generations, 
      daily_limit 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error("Error in ai-generate-storyboard-image:", error);
    return new Response(
      JSON.stringify({ error: error.message ?? "Something went wrong" }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});

