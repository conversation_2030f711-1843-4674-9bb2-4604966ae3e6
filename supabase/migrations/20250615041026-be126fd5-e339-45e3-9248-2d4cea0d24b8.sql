
-- Create a table to track coverage generation usage with proper rate limiting
CREATE TABLE IF NOT EXISTS public.coverage_usage_tracking (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  org_id UUID NOT NULL,
  usage_date DATE NOT NULL DEFAULT CURRENT_DATE,
  generation_count INTEGER NOT NULL DEFAULT 0,
  last_generation_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Ensure one record per user per day
  UNIQUE(user_id, usage_date)
);

-- Enable RLS
ALTER TABLE public.coverage_usage_tracking ENABLE ROW LEVEL SECURITY;

-- RLS policies for coverage usage tracking
CREATE POLICY "Users can view their own usage tracking" 
  ON public.coverage_usage_tracking 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own usage tracking" 
  ON public.coverage_usage_tracking 
  FOR ALL 
  USING (auth.uid() = user_id);

-- Function to get or create daily usage tracking
CREATE OR REPLACE FUNCTION public.get_or_create_daily_usage(
  target_org_id UUID,
  target_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE (
  id UUID,
  generation_count INTEGER,
  daily_limit INTEGER,
  remaining_generations INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier TEXT;
  tier_limit INTEGER;
  current_count INTEGER := 0;
  usage_record_id UUID;
BEGIN
  -- Check organization membership
  IF NOT public.is_org_member(target_org_id) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- Get user's subscription tier
  SELECT o.plan INTO user_tier
  FROM public.organizations o
  WHERE o.id = target_org_id;

  -- Determine daily limit based on tier
  tier_limit := CASE user_tier
    WHEN 'pro-solo' THEN 5
    WHEN 'pro-team' THEN 15
    WHEN 'studio' THEN 50
    WHEN 'enterprise' THEN 200
    ELSE 0
  END;

  -- Get or create usage record for today
  INSERT INTO public.coverage_usage_tracking (user_id, org_id, usage_date, generation_count)
  VALUES (auth.uid(), target_org_id, target_date, 0)
  ON CONFLICT (user_id, usage_date) 
  DO UPDATE SET updated_at = now()
  RETURNING public.coverage_usage_tracking.id, public.coverage_usage_tracking.generation_count 
  INTO usage_record_id, current_count;

  RETURN QUERY
  SELECT 
    usage_record_id,
    current_count,
    tier_limit,
    GREATEST(0, tier_limit - current_count) as remaining_generations;
END;
$$;

-- Function to increment usage count atomically
CREATE OR REPLACE FUNCTION public.increment_coverage_usage(
  target_org_id UUID
)
RETURNS TABLE (
  success BOOLEAN,
  remaining_generations INTEGER,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier TEXT;
  tier_limit INTEGER;
  current_count INTEGER := 0;
  usage_record_id UUID;
BEGIN
  -- Check organization membership
  IF NOT public.is_org_member(target_org_id) THEN
    RETURN QUERY SELECT FALSE, 0, 'Access denied: User is not a member of this organization';
    RETURN;
  END IF;

  -- Get user's subscription tier
  SELECT o.plan INTO user_tier
  FROM public.organizations o
  WHERE o.id = target_org_id;

  -- Check if user has valid subscription
  IF user_tier IS NULL OR user_tier = 'starter' THEN
    RETURN QUERY SELECT FALSE, 0, 'Coverage Generator requires a Pro subscription or higher';
    RETURN;
  END IF;

  -- Determine daily limit based on tier
  tier_limit := CASE user_tier
    WHEN 'pro-solo' THEN 5
    WHEN 'pro-team' THEN 15
    WHEN 'studio' THEN 50
    WHEN 'enterprise' THEN 200
    ELSE 0
  END;

  -- Get or create usage record for today and increment atomically
  INSERT INTO public.coverage_usage_tracking (user_id, org_id, usage_date, generation_count)
  VALUES (auth.uid(), target_org_id, CURRENT_DATE, 1)
  ON CONFLICT (user_id, usage_date) 
  DO UPDATE SET 
    generation_count = coverage_usage_tracking.generation_count + 1,
    last_generation_at = now(),
    updated_at = now()
  RETURNING public.coverage_usage_tracking.id, public.coverage_usage_tracking.generation_count 
  INTO usage_record_id, current_count;

  -- Check if limit exceeded after increment
  IF current_count > tier_limit THEN
    -- Rollback the increment
    UPDATE public.coverage_usage_tracking 
    SET generation_count = generation_count - 1,
        updated_at = now()
    WHERE id = usage_record_id;
    
    RETURN QUERY SELECT FALSE, 0, 'Daily generation limit exceeded';
    RETURN;
  END IF;

  RETURN QUERY SELECT TRUE, tier_limit - current_count, NULL::TEXT;
END;
$$;

-- Add updated_at trigger for coverage_usage_tracking
CREATE TRIGGER update_coverage_usage_tracking_updated_at
  BEFORE UPDATE ON public.coverage_usage_tracking
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_coverage_usage_user_date 
  ON public.coverage_usage_tracking(user_id, usage_date);

CREATE INDEX IF NOT EXISTS idx_coverage_usage_org_date 
  ON public.coverage_usage_tracking(org_id, usage_date);
