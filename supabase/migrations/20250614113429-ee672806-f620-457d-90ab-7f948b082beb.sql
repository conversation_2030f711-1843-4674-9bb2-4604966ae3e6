
-- Create production scheduling tables
CREATE TABLE public.production_schedules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create production schedule items (individual shoots/events)
CREATE TABLE public.production_schedule_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  schedule_id UUID NOT NULL REFERENCES public.production_schedules(id) ON DELETE CASCADE,
  scene_id UUID REFERENCES public.scenes(id) ON DELETE SET NULL,
  location_id UUID REFERENCES public.locations(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  scheduled_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  estimated_duration INTEGER, -- in minutes
  status TEXT NOT NULL DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create crew assignments
CREATE TABLE public.crew_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  schedule_item_id UUID NOT NULL REFERENCES public.production_schedule_items(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  role TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'assigned' CHECK (status IN ('assigned', 'confirmed', 'declined', 'completed')),
  rate_per_day NUMERIC(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create production budgets
CREATE TABLE public.production_budgets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  total_budget NUMERIC(15,2) NOT NULL DEFAULT 0.00,
  currency TEXT NOT NULL DEFAULT 'USD',
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'approved', 'active', 'completed')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create budget line items
CREATE TABLE public.budget_line_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  budget_id UUID NOT NULL REFERENCES public.production_budgets(id) ON DELETE CASCADE,
  category TEXT NOT NULL,
  subcategory TEXT,
  description TEXT NOT NULL,
  estimated_cost NUMERIC(10,2) NOT NULL DEFAULT 0.00,
  actual_cost NUMERIC(10,2) DEFAULT 0.00,
  quantity INTEGER DEFAULT 1,
  unit_cost NUMERIC(10,2),
  vendor TEXT,
  status TEXT NOT NULL DEFAULT 'estimated' CHECK (status IN ('estimated', 'approved', 'ordered', 'received', 'paid')),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create resource management table
CREATE TABLE public.production_resources (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('equipment', 'location', 'talent', 'crew', 'vehicle', 'other')),
  description TEXT,
  availability_status TEXT NOT NULL DEFAULT 'available' CHECK (availability_status IN ('available', 'booked', 'maintenance', 'unavailable')),
  cost_per_day NUMERIC(10,2),
  contact_info JSONB,
  specifications JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create resource bookings
CREATE TABLE public.resource_bookings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  resource_id UUID NOT NULL REFERENCES public.production_resources(id) ON DELETE CASCADE,
  schedule_item_id UUID NOT NULL REFERENCES public.production_schedule_items(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'requested' CHECK (status IN ('requested', 'confirmed', 'cancelled', 'completed')),
  cost NUMERIC(10,2),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create production reports
CREATE TABLE public.production_reports (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  schedule_item_id UUID REFERENCES public.production_schedule_items(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  report_type TEXT NOT NULL CHECK (report_type IN ('daily', 'wrap', 'incident', 'progress')),
  content JSONB NOT NULL,
  date DATE NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved')),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.production_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_schedule_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_line_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resource_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_reports ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for production_schedules
CREATE POLICY "Users can view org schedules" 
  ON public.production_schedules 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create schedules in their org" 
  ON public.production_schedules 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update schedules in their org" 
  ON public.production_schedules 
  FOR UPDATE 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete schedules in their org" 
  ON public.production_schedules 
  FOR DELETE 
  USING (public.is_org_member(org_id));

-- Create RLS policies for production_schedule_items
CREATE POLICY "Users can view schedule items through org membership" 
  ON public.production_schedule_items 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedules ps 
      WHERE ps.id = schedule_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can create schedule items in their org" 
  ON public.production_schedule_items 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.production_schedules ps 
      WHERE ps.id = schedule_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can update schedule items in their org" 
  ON public.production_schedule_items 
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedules ps 
      WHERE ps.id = schedule_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can delete schedule items in their org" 
  ON public.production_schedule_items 
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedules ps 
      WHERE ps.id = schedule_id AND public.is_org_member(ps.org_id)
    )
  );

-- Create RLS policies for crew_assignments
CREATE POLICY "Users can view crew assignments through org membership" 
  ON public.crew_assignments 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedule_items psi 
      JOIN public.production_schedules ps ON ps.id = psi.schedule_id
      WHERE psi.id = schedule_item_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can create crew assignments in their org" 
  ON public.crew_assignments 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.production_schedule_items psi 
      JOIN public.production_schedules ps ON ps.id = psi.schedule_id
      WHERE psi.id = schedule_item_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can update crew assignments in their org" 
  ON public.crew_assignments 
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedule_items psi 
      JOIN public.production_schedules ps ON ps.id = psi.schedule_id
      WHERE psi.id = schedule_item_id AND public.is_org_member(ps.org_id)
    )
  );

CREATE POLICY "Users can delete crew assignments in their org" 
  ON public.crew_assignments 
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_schedule_items psi 
      JOIN public.production_schedules ps ON ps.id = psi.schedule_id
      WHERE psi.id = schedule_item_id AND public.is_org_member(ps.org_id)
    )
  );

-- Create RLS policies for production_budgets
CREATE POLICY "Users can view org budgets" 
  ON public.production_budgets 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create budgets in their org" 
  ON public.production_budgets 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update budgets in their org" 
  ON public.production_budgets 
  FOR UPDATE 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete budgets in their org" 
  ON public.production_budgets 
  FOR DELETE 
  USING (public.is_org_member(org_id));

-- Create RLS policies for budget_line_items
CREATE POLICY "Users can view budget line items through org membership" 
  ON public.budget_line_items 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_budgets pb 
      WHERE pb.id = budget_id AND public.is_org_member(pb.org_id)
    )
  );

CREATE POLICY "Users can create budget line items in their org" 
  ON public.budget_line_items 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.production_budgets pb 
      WHERE pb.id = budget_id AND public.is_org_member(pb.org_id)
    )
  );

CREATE POLICY "Users can update budget line items in their org" 
  ON public.budget_line_items 
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_budgets pb 
      WHERE pb.id = budget_id AND public.is_org_member(pb.org_id)
    )
  );

CREATE POLICY "Users can delete budget line items in their org" 
  ON public.budget_line_items 
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_budgets pb 
      WHERE pb.id = budget_id AND public.is_org_member(pb.org_id)
    )
  );

-- Create RLS policies for production_resources
CREATE POLICY "Users can view org resources" 
  ON public.production_resources 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create resources in their org" 
  ON public.production_resources 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update resources in their org" 
  ON public.production_resources 
  FOR UPDATE 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete resources in their org" 
  ON public.production_resources 
  FOR DELETE 
  USING (public.is_org_member(org_id));

-- Create RLS policies for resource_bookings
CREATE POLICY "Users can view resource bookings through org membership" 
  ON public.resource_bookings 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_resources pr 
      WHERE pr.id = resource_id AND public.is_org_member(pr.org_id)
    )
  );

CREATE POLICY "Users can create resource bookings in their org" 
  ON public.resource_bookings 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.production_resources pr 
      WHERE pr.id = resource_id AND public.is_org_member(pr.org_id)
    )
  );

CREATE POLICY "Users can update resource bookings in their org" 
  ON public.resource_bookings 
  FOR UPDATE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_resources pr 
      WHERE pr.id = resource_id AND public.is_org_member(pr.org_id)
    )
  );

CREATE POLICY "Users can delete resource bookings in their org" 
  ON public.resource_bookings 
  FOR DELETE 
  USING (
    EXISTS (
      SELECT 1 FROM public.production_resources pr 
      WHERE pr.id = resource_id AND public.is_org_member(pr.org_id)
    )
  );

-- Create RLS policies for production_reports
CREATE POLICY "Users can view org reports" 
  ON public.production_reports 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create reports in their org" 
  ON public.production_reports 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update reports in their org" 
  ON public.production_reports 
  FOR UPDATE 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete reports in their org" 
  ON public.production_reports 
  FOR DELETE 
  USING (public.is_org_member(org_id));

-- Create indexes for better performance
CREATE INDEX idx_production_schedules_org_id ON public.production_schedules(org_id);
CREATE INDEX idx_production_schedule_items_schedule_id ON public.production_schedule_items(schedule_id);
CREATE INDEX idx_production_schedule_items_date ON public.production_schedule_items(scheduled_date);
CREATE INDEX idx_crew_assignments_schedule_item_id ON public.crew_assignments(schedule_item_id);
CREATE INDEX idx_crew_assignments_user_id ON public.crew_assignments(user_id);
CREATE INDEX idx_production_budgets_org_id ON public.production_budgets(org_id);
CREATE INDEX idx_budget_line_items_budget_id ON public.budget_line_items(budget_id);
CREATE INDEX idx_production_resources_org_id ON public.production_resources(org_id);
CREATE INDEX idx_production_resources_type ON public.production_resources(type);
CREATE INDEX idx_resource_bookings_resource_id ON public.resource_bookings(resource_id);
CREATE INDEX idx_resource_bookings_schedule_item_id ON public.resource_bookings(schedule_item_id);
CREATE INDEX idx_production_reports_org_id ON public.production_reports(org_id);
CREATE INDEX idx_production_reports_date ON public.production_reports(date);
