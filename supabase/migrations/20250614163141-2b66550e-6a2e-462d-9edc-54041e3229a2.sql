
-- Add full-text search columns and indexes for production tables
ALTER TABLE production_schedules ADD COLUMN IF NOT EXISTS search_vector tsvector;
ALTER TABLE production_budgets ADD COLUMN IF NOT EXISTS search_vector tsvector;
ALTER TABLE production_resources ADD COLUMN IF NOT EXISTS search_vector tsvector;
ALTER TABLE production_reports ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Create search vector update functions
CREATE OR REPLACE FUNCTION update_schedule_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('english', 
    COALESCE(NEW.title, '') || ' ' || 
    COALESCE(NEW.description, '') || ' ' ||
    COALESCE(NEW.status, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_budget_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('english', 
    COALESCE(NEW.title, '') || ' ' || 
    COALESCE(NEW.description, '') || ' ' ||
    COALESCE(NEW.status, '') || ' ' ||
    COALESCE(NEW.currency, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_resource_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('english', 
    COALESCE(NEW.name, '') || ' ' || 
    COALESCE(NEW.description, '') || ' ' ||
    COALESCE(NEW.type, '') || ' ' ||
    COALESCE(NEW.availability_status, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION update_report_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('english', 
    COALESCE(NEW.title, '') || ' ' || 
    COALESCE(NEW.report_type, '') || ' ' ||
    COALESCE(NEW.status, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for search vector updates
DROP TRIGGER IF EXISTS update_schedule_search_trigger ON production_schedules;
CREATE TRIGGER update_schedule_search_trigger
  BEFORE INSERT OR UPDATE ON production_schedules
  FOR EACH ROW EXECUTE FUNCTION update_schedule_search_vector();

DROP TRIGGER IF EXISTS update_budget_search_trigger ON production_budgets;
CREATE TRIGGER update_budget_search_trigger
  BEFORE INSERT OR UPDATE ON production_budgets
  FOR EACH ROW EXECUTE FUNCTION update_budget_search_vector();

DROP TRIGGER IF EXISTS update_resource_search_trigger ON production_resources;
CREATE TRIGGER update_resource_search_trigger
  BEFORE INSERT OR UPDATE ON production_resources
  FOR EACH ROW EXECUTE FUNCTION update_resource_search_vector();

DROP TRIGGER IF EXISTS update_report_search_trigger ON production_reports;
CREATE TRIGGER update_report_search_trigger
  BEFORE INSERT OR UPDATE ON production_reports
  FOR EACH ROW EXECUTE FUNCTION update_report_search_vector();

-- Create GIN indexes for full-text search
CREATE INDEX IF NOT EXISTS idx_schedules_search ON production_schedules USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_budgets_search ON production_budgets USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_resources_search ON production_resources USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_reports_search ON production_reports USING GIN(search_vector);

-- Update existing records to populate search vectors
UPDATE production_schedules SET search_vector = to_tsvector('english', 
  COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(status, ''));
UPDATE production_budgets SET search_vector = to_tsvector('english', 
  COALESCE(title, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(status, '') || ' ' || COALESCE(currency, ''));
UPDATE production_resources SET search_vector = to_tsvector('english', 
  COALESCE(name, '') || ' ' || COALESCE(description, '') || ' ' || COALESCE(type, '') || ' ' || COALESCE(availability_status, ''));
UPDATE production_reports SET search_vector = to_tsvector('english', 
  COALESCE(title, '') || ' ' || COALESCE(report_type, '') || ' ' || COALESCE(status, ''));

-- Create production data backup/export functions
CREATE OR REPLACE FUNCTION export_production_data(org_id_param UUID)
RETURNS TABLE(
  data_type TEXT,
  export_data JSONB,
  record_count INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check organization access
  IF NOT public.is_org_member(org_id_param) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- Export schedules
  RETURN QUERY
  SELECT 
    'schedules'::TEXT,
    jsonb_agg(to_jsonb(s.*)) as export_data,
    COUNT(*)::INTEGER as record_count
  FROM production_schedules s
  WHERE s.org_id = org_id_param;

  -- Export budgets
  RETURN QUERY
  SELECT 
    'budgets'::TEXT,
    jsonb_agg(to_jsonb(b.*)) as export_data,
    COUNT(*)::INTEGER as record_count
  FROM production_budgets b
  WHERE b.org_id = org_id_param;

  -- Export resources
  RETURN QUERY
  SELECT 
    'resources'::TEXT,
    jsonb_agg(to_jsonb(r.*)) as export_data,
    COUNT(*)::INTEGER as record_count
  FROM production_resources r
  WHERE r.org_id = org_id_param;

  -- Export reports
  RETURN QUERY
  SELECT 
    'reports'::TEXT,
    jsonb_agg(to_jsonb(rep.*)) as export_data,
    COUNT(*)::INTEGER as record_count
  FROM production_reports rep
  WHERE rep.org_id = org_id_param;
END;
$$;

-- Create search function with pagination (fixed parameter naming conflict)
CREATE OR REPLACE FUNCTION search_production_data(
  org_id_param UUID,
  search_query TEXT DEFAULT '',
  entity_type_filter TEXT DEFAULT 'all',
  page_num INTEGER DEFAULT 1,
  page_size INTEGER DEFAULT 20
)
RETURNS TABLE(
  entity_type TEXT,
  entity_id UUID,
  title TEXT,
  description TEXT,
  status TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  relevance_rank REAL
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  offset_val INTEGER;
BEGIN
  -- Check organization access
  IF NOT public.is_org_member(org_id_param) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  offset_val := (page_num - 1) * page_size;

  -- Search across all entities or specific type
  IF entity_type_filter = 'all' OR entity_type_filter = 'schedules' THEN
    RETURN QUERY
    SELECT 
      'schedule'::TEXT as entity_type,
      s.id as entity_id,
      s.title,
      s.description,
      s.status,
      s.created_at,
      CASE 
        WHEN search_query = '' THEN 1.0
        ELSE ts_rank(s.search_vector, plainto_tsquery('english', search_query))
      END as relevance_rank
    FROM production_schedules s
    WHERE s.org_id = org_id_param
      AND (search_query = '' OR s.search_vector @@ plainto_tsquery('english', search_query))
    ORDER BY relevance_rank DESC, s.created_at DESC
    LIMIT page_size OFFSET offset_val;
  END IF;

  IF entity_type_filter = 'all' OR entity_type_filter = 'budgets' THEN
    RETURN QUERY
    SELECT 
      'budget'::TEXT as entity_type,
      b.id as entity_id,
      b.title,
      b.description,
      b.status,
      b.created_at,
      CASE 
        WHEN search_query = '' THEN 1.0
        ELSE ts_rank(b.search_vector, plainto_tsquery('english', search_query))
      END as relevance_rank
    FROM production_budgets b
    WHERE b.org_id = org_id_param
      AND (search_query = '' OR b.search_vector @@ plainto_tsquery('english', search_query))
    ORDER BY relevance_rank DESC, b.created_at DESC
    LIMIT page_size OFFSET offset_val;
  END IF;

  IF entity_type_filter = 'all' OR entity_type_filter = 'resources' THEN
    RETURN QUERY
    SELECT 
      'resource'::TEXT as entity_type,
      r.id as entity_id,
      r.name as title,
      r.description,
      r.availability_status as status,
      r.created_at,
      CASE 
        WHEN search_query = '' THEN 1.0
        ELSE ts_rank(r.search_vector, plainto_tsquery('english', search_query))
      END as relevance_rank
    FROM production_resources r
    WHERE r.org_id = org_id_param
      AND (search_query = '' OR r.search_vector @@ plainto_tsquery('english', search_query))
    ORDER BY relevance_rank DESC, r.created_at DESC
    LIMIT page_size OFFSET offset_val;
  END IF;

  IF entity_type_filter = 'all' OR entity_type_filter = 'reports' THEN
    RETURN QUERY
    SELECT 
      'report'::TEXT as entity_type,
      rep.id as entity_id,
      rep.title,
      rep.content::TEXT as description,
      rep.status,
      rep.created_at,
      CASE 
        WHEN search_query = '' THEN 1.0
        ELSE ts_rank(rep.search_vector, plainto_tsquery('english', search_query))
      END as relevance_rank
    FROM production_reports rep
    WHERE rep.org_id = org_id_param
      AND (search_query = '' OR rep.search_vector @@ plainto_tsquery('english', search_query))
    ORDER BY relevance_rank DESC, rep.created_at DESC
    LIMIT page_size OFFSET offset_val;
  END IF;
END;
$$;
