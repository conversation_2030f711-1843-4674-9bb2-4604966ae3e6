
-- Enhanced subscription system with organization-centric features
-- This migration maintains backward compatibility while adding new capabilities

-- Create organization subscription plans table
CREATE TABLE IF NOT EXISTS public.organization_subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  plan_id TEXT REFERENCES public.subscription_plans(plan_id),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'canceled', 'past_due', 'trialing')),
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  trial_start TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT false,
  stripe_subscription_id TEXT,
  billing_cycle TEXT DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'yearly')),
  seats_included INTEGER DEFAULT 1,
  seats_used INTEGER DEFAULT 0,
  addon_features JSONB DEFAULT '{}',
  custom_limits JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(org_id, plan_id)
);

-- Enable RLS on organization subscription plans
ALTER TABLE public.organization_subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create policy for organization members to view their org's subscription
CREATE POLICY "Organization members can view subscription" 
  ON public.organization_subscription_plans 
  FOR SELECT 
  USING (public.is_org_member(org_id));

-- Create policy for admins to manage subscription
CREATE POLICY "Organization admins can manage subscription" 
  ON public.organization_subscription_plans 
  FOR ALL 
  USING (public.is_org_admin(org_id));

-- Create subscription usage tracking table
CREATE TABLE IF NOT EXISTS public.subscription_usage_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  feature_key TEXT NOT NULL,
  usage_type TEXT DEFAULT 'count' CHECK (usage_type IN ('count', 'storage', 'duration')),
  usage_amount INTEGER DEFAULT 1,
  usage_date DATE DEFAULT CURRENT_DATE,
  reset_period TEXT DEFAULT 'monthly' CHECK (reset_period IN ('daily', 'weekly', 'monthly', 'yearly', 'never')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(org_id, user_id, feature_key, usage_date)
);

-- Enable RLS on usage tracking
ALTER TABLE public.subscription_usage_tracking ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own usage
CREATE POLICY "Users can view their own usage" 
  ON public.subscription_usage_tracking 
  FOR SELECT 
  USING (auth.uid() = user_id AND public.is_org_member(org_id));

-- Create policy for system to manage usage
CREATE POLICY "System can manage usage tracking" 
  ON public.subscription_usage_tracking 
  FOR ALL 
  USING (true);

-- Create subscription add-ons table
CREATE TABLE IF NOT EXISTS public.subscription_addons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  addon_key TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  price_monthly NUMERIC DEFAULT 0,
  price_yearly NUMERIC,
  features JSONB DEFAULT '{}',
  limits JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS on subscription add-ons
ALTER TABLE public.subscription_addons ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to active add-ons
CREATE POLICY "Public read access to active add-ons" 
  ON public.subscription_addons 
  FOR SELECT 
  USING (is_active = true);

-- Create organization add-ons table
CREATE TABLE IF NOT EXISTS public.organization_addons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  addon_key TEXT REFERENCES public.subscription_addons(addon_key),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'canceled')),
  stripe_subscription_id TEXT,
  activated_at TIMESTAMPTZ DEFAULT now(),
  canceled_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(org_id, addon_key)
);

-- Enable RLS on organization add-ons
ALTER TABLE public.organization_addons ENABLE ROW LEVEL SECURITY;

-- Create policy for organization members to view add-ons
CREATE POLICY "Organization members can view addons" 
  ON public.organization_addons 
  FOR SELECT 
  USING (public.is_org_member(org_id));

-- Create policy for admins to manage add-ons
CREATE POLICY "Organization admins can manage addons" 
  ON public.organization_addons 
  FOR ALL 
  USING (public.is_org_admin(org_id));

-- Update subscription plans with enhanced features (only if they exist)
UPDATE public.subscription_plans 
SET features = jsonb_set(
  features,
  '{organization_management}',
  'true'::jsonb
) WHERE plan_id IN ('pro-team', 'studio');

UPDATE public.subscription_plans 
SET features = jsonb_set(
  features,
  '{advanced_analytics}',
  'true'::jsonb
) WHERE plan_id IN ('studio');

UPDATE public.subscription_plans 
SET features = jsonb_set(
  features,
  '{white_label_branding}',
  'true'::jsonb
) WHERE plan_id = 'studio';

-- Insert default add-ons
INSERT INTO public.subscription_addons (addon_key, name, description, price_monthly, price_yearly, features, limits) VALUES
('extra_storage', 'Extra Storage', 'Additional cloud storage for projects', 5.00, 50.00, 
 '{"cloud_storage": true}', '{"storage_gb": 100}'),
('priority_rendering', 'Priority Rendering', 'Faster AI processing with priority queue', 10.00, 100.00,
 '{"priority_queue": true}', '{"render_priority": 1}'),
('advanced_collaboration', 'Advanced Collaboration', 'Enhanced team features and integrations', 15.00, 150.00,
 '{"advanced_permissions": true, "integrations": true}', '{"team_channels": 10}'),
('api_access', 'API Access', 'Developer API access with higher rate limits', 25.00, 250.00,
 '{"api_access": true}', '{"api_calls_per_month": 10000}')
ON CONFLICT (addon_key) DO NOTHING;

-- Create enhanced subscription function
CREATE OR REPLACE FUNCTION public.get_organization_subscription_with_features(target_org_id UUID)
RETURNS TABLE(
  has_subscription BOOLEAN,
  plan_id TEXT,
  plan_name TEXT,
  status TEXT,
  current_period_end TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN,
  features JSONB,
  limits JSONB,
  addons JSONB,
  usage_summary JSONB,
  seats_info JSONB
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  org_sub RECORD;
  addon_list JSONB;
  usage_data JSONB;
  seats_data JSONB;
BEGIN
  -- Initialize variables
  addon_list := '[]'::jsonb;
  usage_data := '{}'::jsonb;

  -- Check organization membership
  IF NOT public.is_org_member(target_org_id) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- Get organization subscription
  SELECT 
    osp.*,
    sp.display_name,
    sp.features as plan_features,
    sp.limits as plan_limits
  INTO org_sub
  FROM public.organization_subscription_plans osp
  LEFT JOIN public.subscription_plans sp ON sp.plan_id = osp.plan_id
  WHERE osp.org_id = target_org_id 
  AND osp.status = 'active'
  ORDER BY osp.created_at DESC
  LIMIT 1;

  -- If no org subscription, check individual subscription for backward compatibility
  IF org_sub IS NULL THEN
    SELECT 
      s.subscribed as has_sub,
      COALESCE(s.subscription_tier, 'free') as tier,
      COALESCE(sp.display_name, 'Free') as name,
      COALESCE(s.status, 'inactive') as sub_status,
      s.current_period_end,
      s.trial_end,
      COALESCE(s.cancel_at_period_end, false) as cancel_end,
      COALESCE(sp.features, '{}') as feats,
      COALESCE(sp.limits, '{}') as lims
    INTO org_sub
    FROM public.subscribers s
    LEFT JOIN public.subscription_plans sp ON sp.plan_id = s.subscription_tier
    WHERE s.user_id = auth.uid()
    LIMIT 1;

    -- Return individual subscription data
    RETURN QUERY SELECT 
      COALESCE(org_sub.has_sub, false),
      COALESCE(org_sub.tier, 'free'),
      COALESCE(org_sub.name, 'Free'),
      COALESCE(org_sub.sub_status, 'inactive'),
      org_sub.current_period_end,
      org_sub.trial_end,
      COALESCE(org_sub.cancel_end, false),
      COALESCE(org_sub.feats, '{}'),
      COALESCE(org_sub.lims, '{}'),
      '[]'::JSONB,
      '{}'::JSONB,
      '{"type": "individual", "total": 1, "used": 1}'::JSONB;
    RETURN;
  END IF;

  -- Get active add-ons
  SELECT jsonb_agg(
    jsonb_build_object(
      'addon_key', oa.addon_key,
      'name', sa.name,
      'features', sa.features,
      'limits', sa.limits
    )
  ) INTO addon_list
  FROM public.organization_addons oa
  JOIN public.subscription_addons sa ON sa.addon_key = oa.addon_key
  WHERE oa.org_id = target_org_id AND oa.status = 'active';

  -- Get usage summary for current month
  SELECT jsonb_object_agg(
    feature_key,
    usage_amount
  ) INTO usage_data
  FROM (
    SELECT 
      feature_key,
      SUM(usage_amount) as usage_amount
    FROM public.subscription_usage_tracking
    WHERE org_id = target_org_id 
    AND date_trunc('month', usage_date) = date_trunc('month', CURRENT_DATE)
    GROUP BY feature_key
  ) usage_summary;

  -- Prepare seats information
  seats_data := jsonb_build_object(
    'type', 'organization',
    'total', org_sub.seats_included,
    'used', org_sub.seats_used,
    'available', org_sub.seats_included - org_sub.seats_used
  );

  -- Return organization subscription data
  RETURN QUERY SELECT 
    true,
    org_sub.plan_id,
    org_sub.display_name,
    org_sub.status,
    org_sub.current_period_end,
    org_sub.trial_end,
    org_sub.cancel_at_period_end,
    org_sub.plan_features,
    org_sub.plan_limits,
    COALESCE(addon_list, '[]'::jsonb),
    COALESCE(usage_data, '{}'::jsonb),
    seats_data;
END;
$$;

-- Create function to track feature usage
CREATE OR REPLACE FUNCTION public.track_feature_usage(
  target_org_id UUID,
  feature_key TEXT,
  usage_amount INTEGER DEFAULT 1,
  usage_type TEXT DEFAULT 'count'
)
RETURNS TABLE(
  success BOOLEAN,
  remaining_usage INTEGER,
  limit_exceeded BOOLEAN,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_usage INTEGER := 0;
  feature_limit INTEGER;
  org_features JSONB;
  org_limits JSONB;
BEGIN
  -- Check organization membership
  IF NOT public.is_org_member(target_org_id) THEN
    RETURN QUERY SELECT false, 0, true, 'Access denied: User is not a member of this organization';
    RETURN;
  END IF;

  -- Get organization features and limits
  SELECT features, limits INTO org_features, org_limits
  FROM public.get_organization_subscription_with_features(target_org_id)
  LIMIT 1;

  -- Check if feature is available
  IF NOT (org_features ? feature_key) THEN
    RETURN QUERY SELECT false, 0, true, 'Feature not available in current subscription';
    RETURN;
  END IF;

  -- Get current usage for today
  SELECT COALESCE(SUM(usage_amount), 0) INTO current_usage
  FROM public.subscription_usage_tracking
  WHERE org_id = target_org_id 
  AND feature_key = track_feature_usage.feature_key
  AND usage_date = CURRENT_DATE;

  -- Get feature limit
  feature_limit := COALESCE((org_limits->feature_key)::INTEGER, -1);

  -- Check if adding usage would exceed limit
  IF feature_limit > 0 AND (current_usage + track_feature_usage.usage_amount) > feature_limit THEN
    RETURN QUERY SELECT false, GREATEST(0, feature_limit - current_usage), true, 'Daily usage limit exceeded';
    RETURN;
  END IF;

  -- Record usage
  INSERT INTO public.subscription_usage_tracking (
    org_id, user_id, feature_key, usage_type, usage_amount
  ) VALUES (
    target_org_id, auth.uid(), feature_key, usage_type, usage_amount
  ) ON CONFLICT (org_id, user_id, feature_key, usage_date)
  DO UPDATE SET 
    usage_amount = subscription_usage_tracking.usage_amount + track_feature_usage.usage_amount,
    created_at = now();

  -- Calculate remaining usage
  current_usage := current_usage + track_feature_usage.usage_amount;
  
  RETURN QUERY SELECT 
    true,
    CASE WHEN feature_limit > 0 THEN feature_limit - current_usage ELSE -1 END,
    false,
    NULL::TEXT;
END;
$$;

-- Create triggers for updated_at columns
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to new tables
CREATE TRIGGER update_organization_subscription_plans_updated_at
    BEFORE UPDATE ON public.organization_subscription_plans
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_subscription_addons_updated_at
    BEFORE UPDATE ON public.subscription_addons
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_org_subscription_plans_org_id ON public.organization_subscription_plans(org_id);
CREATE INDEX IF NOT EXISTS idx_org_subscription_plans_status ON public.organization_subscription_plans(status);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_org_date ON public.subscription_usage_tracking(org_id, usage_date);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_feature ON public.subscription_usage_tracking(feature_key);
