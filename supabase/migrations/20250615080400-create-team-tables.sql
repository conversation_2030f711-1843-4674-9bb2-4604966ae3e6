
-- Create team_activities table
CREATE TABLE IF NOT EXISTS public.team_activities (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id uuid NOT NULL,
  user_id uuid NOT NULL,
  activity_type text NOT NULL,
  entity_type text,
  entity_id uuid,
  description text NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  
  CONSTRAINT fk_team_activities_org FOREIGN KEY (org_id) REFERENCES public.organizations(id) ON DELETE CASCADE,
  CONSTRAINT fk_team_activities_user FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- Create team_discussions table
CREATE TABLE IF NOT EXISTS public.team_discussions (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id uuid NOT NULL,
  title text NOT NULL,
  content text NOT NULL,
  created_by uuid NOT NULL,
  discussion_type text NOT NULL DEFAULT 'general',
  is_announcement boolean NOT NULL DEFAULT false,
  is_pinned boolean NOT NULL DEFAULT false,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  
  CONSTRAINT fk_team_discussions_org FOREIGN KEY (org_id) REFERENCES public.organizations(id) ON DELETE CASCADE,
  CONSTRAINT fk_team_discussions_user FOREIGN KEY (created_by) REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- Create team_discussion_replies table
CREATE TABLE IF NOT EXISTS public.team_discussion_replies (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  discussion_id uuid NOT NULL,
  user_id uuid NOT NULL,
  content text NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  
  CONSTRAINT fk_team_discussion_replies_discussion FOREIGN KEY (discussion_id) REFERENCES public.team_discussions(id) ON DELETE CASCADE,
  CONSTRAINT fk_team_discussion_replies_user FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE
);

-- Enable RLS on all tables
ALTER TABLE public.team_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_discussions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_discussion_replies ENABLE ROW LEVEL SECURITY;

-- Add RLS policies
CREATE POLICY "Users can view activities for their organizations"
ON public.team_activities FOR SELECT
USING (public.is_org_member(org_id));

CREATE POLICY "Users can create activities for their organizations"
ON public.team_activities FOR INSERT
WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can view discussions for their organizations"
ON public.team_discussions FOR SELECT
USING (public.is_org_member(org_id));

CREATE POLICY "Users can create discussions for their organizations"
ON public.team_discussions FOR INSERT
WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can view replies for discussions in their organizations"
ON public.team_discussion_replies FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.team_discussions td
    WHERE td.id = team_discussion_replies.discussion_id
    AND public.is_org_member(td.org_id)
  )
);

CREATE POLICY "Users can create replies for discussions in their organizations"
ON public.team_discussion_replies FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.team_discussions td
    WHERE td.id = team_discussion_replies.discussion_id
    AND public.is_org_member(td.org_id)
  )
);

-- Add updated_at triggers
CREATE TRIGGER update_team_discussions_updated_at
  BEFORE UPDATE ON public.team_discussions
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_team_discussion_replies_updated_at
  BEFORE UPDATE ON public.team_discussion_replies
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
