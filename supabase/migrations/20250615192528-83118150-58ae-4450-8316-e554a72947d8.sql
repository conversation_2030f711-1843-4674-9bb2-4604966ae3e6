
-- Add RLS policies only for tables that don't have them yet
-- First, let's enable RLS on tables that don't have it enabled

-- Check and enable RLS on tables that need it
DO $$
BEGIN
    -- Enable RLS on tables that don't have it yet
    IF NOT EXISTS (
        SELECT 1 FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename = 'profiles' 
        AND rowsecurity = true
    ) THEN
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
    END IF;
    
    -- Add other tables as needed, but skip ones that already have RLS
END $$;

-- Add missing RLS policies (only if they don't exist)

-- Profiles policies (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'profiles' 
        AND policyname = 'Users can view own profile'
    ) THEN
        CREATE POLICY "Users can view own profile" ON public.profiles
          FOR SELECT USING (auth.uid() = id);
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'profiles' 
        AND policyname = 'Users can update own profile'
    ) THEN
        CREATE POLICY "Users can update own profile" ON public.profiles
          FOR UPDATE USING (auth.uid() = id);
    END IF;
END $$;

-- Characters policies (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'characters' 
        AND policyname = 'Users can view org characters'
    ) THEN
        CREATE POLICY "Users can view org characters" ON public.characters
          FOR SELECT USING (public.is_org_member(org_id));
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'characters' 
        AND policyname = 'Users can create characters in their orgs'
    ) THEN
        CREATE POLICY "Users can create characters in their orgs" ON public.characters
          FOR INSERT WITH CHECK (
            user_id = auth.uid() AND 
            public.is_org_member(org_id)
          );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'characters' 
        AND policyname = 'Users can update own characters'
    ) THEN
        CREATE POLICY "Users can update own characters" ON public.characters
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'characters' 
        AND policyname = 'Users can delete own characters'
    ) THEN
        CREATE POLICY "Users can delete own characters" ON public.characters
          FOR DELETE USING (user_id = auth.uid());
    END IF;
END $$;

-- Locations policies (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'locations' 
        AND policyname = 'Users can view org locations'
    ) THEN
        CREATE POLICY "Users can view org locations" ON public.locations
          FOR SELECT USING (public.is_org_member(org_id));
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'locations' 
        AND policyname = 'Users can create locations in their orgs'
    ) THEN
        CREATE POLICY "Users can create locations in their orgs" ON public.locations
          FOR INSERT WITH CHECK (
            user_id = auth.uid() AND 
            public.is_org_member(org_id)
          );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'locations' 
        AND policyname = 'Users can update own locations'
    ) THEN
        CREATE POLICY "Users can update own locations" ON public.locations
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'locations' 
        AND policyname = 'Users can delete own locations'
    ) THEN
        CREATE POLICY "Users can delete own locations" ON public.locations
          FOR DELETE USING (user_id = auth.uid());
    END IF;
END $$;

-- Scenes policies (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'scenes' 
        AND policyname = 'Users can view org scenes'
    ) THEN
        CREATE POLICY "Users can view org scenes" ON public.scenes
          FOR SELECT USING (public.is_org_member(org_id));
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'scenes' 
        AND policyname = 'Users can create scenes in their orgs'
    ) THEN
        CREATE POLICY "Users can create scenes in their orgs" ON public.scenes
          FOR INSERT WITH CHECK (
            user_id = auth.uid() AND 
            public.is_org_member(org_id)
          );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'scenes' 
        AND policyname = 'Users can update own scenes'
    ) THEN
        CREATE POLICY "Users can update own scenes" ON public.scenes
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'scenes' 
        AND policyname = 'Users can delete own scenes'
    ) THEN
        CREATE POLICY "Users can delete own scenes" ON public.scenes
          FOR DELETE USING (user_id = auth.uid());
    END IF;
END $$;

-- Comments policies (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'comments' 
        AND policyname = 'Users can view comments on org scenes'
    ) THEN
        CREATE POLICY "Users can view comments on org scenes" ON public.comments
          FOR SELECT USING (
            scene_id IN (
              SELECT s.id FROM public.scenes s 
              WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
            )
          );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'comments' 
        AND policyname = 'Users can create comments on org scenes'
    ) THEN
        CREATE POLICY "Users can create comments on org scenes" ON public.comments
          FOR INSERT WITH CHECK (
            user_id = auth.uid() AND 
            scene_id IN (
              SELECT s.id FROM public.scenes s 
              WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
            )
          );
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'comments' 
        AND policyname = 'Users can update own comments'
    ) THEN
        CREATE POLICY "Users can update own comments" ON public.comments
          FOR UPDATE USING (user_id = auth.uid());
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'comments' 
        AND policyname = 'Users can delete own comments'
    ) THEN
        CREATE POLICY "Users can delete own comments" ON public.comments
          FOR DELETE USING (user_id = auth.uid());
    END IF;
END $$;

-- Add essential indexes for performance (if not exists)
CREATE INDEX IF NOT EXISTS idx_characters_org_id ON public.characters(org_id);
CREATE INDEX IF NOT EXISTS idx_locations_org_id ON public.locations(org_id);
CREATE INDEX IF NOT EXISTS idx_scenes_org_id ON public.scenes(org_id);
CREATE INDEX IF NOT EXISTS idx_comments_scene_id ON public.comments(scene_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);
