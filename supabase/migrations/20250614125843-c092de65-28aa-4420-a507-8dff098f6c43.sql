
-- Database & API Enhancements for Production Tools
-- 1. Add proper foreign key relationships for all production tables
-- 2. Add RLS policies for security
-- 3. Add database triggers for automatic timestamps and validation
-- 4. Create composite indexes for better performance

-- First, add missing foreign key relationships
ALTER TABLE public.production_schedule_items 
ADD CONSTRAINT fk_schedule_items_schedules 
FOREIGN KEY (schedule_id) REFERENCES public.production_schedules(id) ON DELETE CASCADE;

ALTER TABLE public.production_schedule_items 
ADD CONSTRAINT fk_schedule_items_scenes 
FOREIGN KEY (scene_id) REFERENCES public.scenes(id) ON DELETE SET NULL;

ALTER TABLE public.production_schedule_items 
ADD CONSTRAINT fk_schedule_items_locations 
FOREIGN KEY (location_id) REFERENCES public.locations(id) ON DELETE SET NULL;

ALTER TABLE public.crew_assignments 
ADD CONSTRAINT fk_crew_assignments_schedule_items 
FOREIGN KEY (schedule_item_id) REFERENCES public.production_schedule_items(id) ON DELETE CASCADE;

ALTER TABLE public.budget_line_items 
ADD CONSTRAINT fk_budget_line_items_budgets 
FOREIGN KEY (budget_id) REFERENCES public.production_budgets(id) ON DELETE CASCADE;

ALTER TABLE public.resource_bookings 
ADD CONSTRAINT fk_resource_bookings_resources 
FOREIGN KEY (resource_id) REFERENCES public.production_resources(id) ON DELETE CASCADE;

ALTER TABLE public.resource_bookings 
ADD CONSTRAINT fk_resource_bookings_schedule_items 
FOREIGN KEY (schedule_item_id) REFERENCES public.production_schedule_items(id) ON DELETE CASCADE;

ALTER TABLE public.production_reports 
ADD CONSTRAINT fk_production_reports_schedule_items 
FOREIGN KEY (schedule_item_id) REFERENCES public.production_schedule_items(id) ON DELETE SET NULL;

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add update triggers for all production tables
CREATE TRIGGER trigger_update_production_schedules_updated_at
  BEFORE UPDATE ON public.production_schedules
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_production_schedule_items_updated_at
  BEFORE UPDATE ON public.production_schedule_items
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_crew_assignments_updated_at
  BEFORE UPDATE ON public.crew_assignments
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_production_budgets_updated_at
  BEFORE UPDATE ON public.production_budgets
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_budget_line_items_updated_at
  BEFORE UPDATE ON public.budget_line_items
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_production_resources_updated_at
  BEFORE UPDATE ON public.production_resources
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_resource_bookings_updated_at
  BEFORE UPDATE ON public.resource_bookings
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER trigger_update_production_reports_updated_at
  BEFORE UPDATE ON public.production_reports
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create validation triggers for data integrity
CREATE OR REPLACE FUNCTION public.validate_schedule_dates()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate that end_date is after start_date
  IF NEW.end_date <= NEW.start_date THEN
    RAISE EXCEPTION 'End date must be after start date';
  END IF;
  
  -- Validate that start_date is not in the past (for new records)
  IF TG_OP = 'INSERT' AND NEW.start_date < CURRENT_DATE THEN
    RAISE EXCEPTION 'Start date cannot be in the past';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_production_schedules
  BEFORE INSERT OR UPDATE ON public.production_schedules
  FOR EACH ROW EXECUTE FUNCTION public.validate_schedule_dates();

CREATE OR REPLACE FUNCTION public.validate_schedule_item_dates()
RETURNS TRIGGER AS $$
DECLARE
  schedule_start_date DATE;
  schedule_end_date DATE;
BEGIN
  -- Get the schedule's date range
  SELECT start_date, end_date INTO schedule_start_date, schedule_end_date
  FROM public.production_schedules
  WHERE id = NEW.schedule_id;
  
  -- Validate that the scheduled_date is within the schedule's date range
  IF NEW.scheduled_date < schedule_start_date OR NEW.scheduled_date > schedule_end_date THEN
    RAISE EXCEPTION 'Scheduled date must be within the schedule date range (% to %)', 
      schedule_start_date, schedule_end_date;
  END IF;
  
  -- Validate that end_time is after start_time if both are provided
  IF NEW.start_time IS NOT NULL AND NEW.end_time IS NOT NULL AND NEW.end_time <= NEW.start_time THEN
    RAISE EXCEPTION 'End time must be after start time';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_schedule_items
  BEFORE INSERT OR UPDATE ON public.production_schedule_items
  FOR EACH ROW EXECUTE FUNCTION public.validate_schedule_item_dates();

CREATE OR REPLACE FUNCTION public.validate_resource_booking_dates()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate that end_date is after start_date
  IF NEW.end_date <= NEW.start_date THEN
    RAISE EXCEPTION 'End date must be after start date for resource booking';
  END IF;
  
  -- Validate that start_date is not in the past (for new bookings)
  IF TG_OP = 'INSERT' AND NEW.start_date < CURRENT_DATE THEN
    RAISE EXCEPTION 'Booking start date cannot be in the past';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_resource_bookings
  BEFORE INSERT OR UPDATE ON public.resource_bookings
  FOR EACH ROW EXECUTE FUNCTION public.validate_resource_booking_dates();

CREATE OR REPLACE FUNCTION public.validate_budget_amounts()
RETURNS TRIGGER AS $$
BEGIN
  -- Validate that amounts are non-negative
  IF NEW.estimated_cost < 0 THEN
    RAISE EXCEPTION 'Estimated cost cannot be negative';
  END IF;
  
  IF NEW.actual_cost IS NOT NULL AND NEW.actual_cost < 0 THEN
    RAISE EXCEPTION 'Actual cost cannot be negative';
  END IF;
  
  IF NEW.unit_cost IS NOT NULL AND NEW.unit_cost < 0 THEN
    RAISE EXCEPTION 'Unit cost cannot be negative';
  END IF;
  
  IF NEW.quantity IS NOT NULL AND NEW.quantity <= 0 THEN
    RAISE EXCEPTION 'Quantity must be greater than zero';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_validate_budget_line_items
  BEFORE INSERT OR UPDATE ON public.budget_line_items
  FOR EACH ROW EXECUTE FUNCTION public.validate_budget_amounts();

-- Create composite indexes for better query performance
-- Indexes for production_schedules
CREATE INDEX idx_production_schedules_org_status ON public.production_schedules(org_id, status);
CREATE INDEX idx_production_schedules_dates ON public.production_schedules(start_date, end_date);
CREATE INDEX idx_production_schedules_user_created ON public.production_schedules(user_id, created_at DESC);

-- Indexes for production_schedule_items
CREATE INDEX idx_schedule_items_schedule_date ON public.production_schedule_items(schedule_id, scheduled_date);
CREATE INDEX idx_schedule_items_status_date ON public.production_schedule_items(status, scheduled_date);
CREATE INDEX idx_schedule_items_scene_location ON public.production_schedule_items(scene_id, location_id);

-- Indexes for crew_assignments
CREATE INDEX idx_crew_assignments_user_status ON public.crew_assignments(user_id, status);
CREATE INDEX idx_crew_assignments_schedule_role ON public.crew_assignments(schedule_item_id, role);

-- Indexes for production_budgets
CREATE INDEX idx_production_budgets_org_status ON public.production_budgets(org_id, status);
CREATE INDEX idx_production_budgets_user_created ON public.production_budgets(user_id, created_at DESC);

-- Indexes for budget_line_items
CREATE INDEX idx_budget_line_items_budget_category ON public.budget_line_items(budget_id, category);
CREATE INDEX idx_budget_line_items_status_cost ON public.budget_line_items(status, estimated_cost DESC);

-- Indexes for production_resources
CREATE INDEX idx_production_resources_org_type ON public.production_resources(org_id, type);
CREATE INDEX idx_production_resources_availability ON public.production_resources(availability_status, type);
CREATE INDEX idx_production_resources_cost ON public.production_resources(cost_per_day DESC) WHERE cost_per_day IS NOT NULL;

-- Indexes for resource_bookings
CREATE INDEX idx_resource_bookings_resource_dates ON public.resource_bookings(resource_id, start_date, end_date);
CREATE INDEX idx_resource_bookings_schedule_status ON public.resource_bookings(schedule_item_id, status);
CREATE INDEX idx_resource_bookings_dates_status ON public.resource_bookings(start_date, end_date, status);

-- Indexes for production_reports
CREATE INDEX idx_production_reports_org_type ON public.production_reports(org_id, report_type);
CREATE INDEX idx_production_reports_date_type ON public.production_reports(date DESC, report_type);
CREATE INDEX idx_production_reports_schedule_status ON public.production_reports(schedule_item_id, status);

-- Create function to automatically update budget totals
CREATE OR REPLACE FUNCTION public.update_budget_total()
RETURNS TRIGGER AS $$
DECLARE
  budget_id_to_update UUID;
  new_total NUMERIC;
BEGIN
  -- Determine which budget to update
  IF TG_OP = 'DELETE' THEN
    budget_id_to_update := OLD.budget_id;
  ELSE
    budget_id_to_update := NEW.budget_id;
  END IF;
  
  -- Calculate the new total
  SELECT COALESCE(SUM(estimated_cost), 0) INTO new_total
  FROM public.budget_line_items
  WHERE budget_id = budget_id_to_update;
  
  -- Update the budget total
  UPDATE public.production_budgets
  SET total_budget = new_total, updated_at = now()
  WHERE id = budget_id_to_update;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update budget totals
CREATE TRIGGER trigger_update_budget_total
  AFTER INSERT OR UPDATE OR DELETE ON public.budget_line_items
  FOR EACH ROW EXECUTE FUNCTION public.update_budget_total();
