
-- Create enum types for promo campaigns
CREATE TYPE campaign_status AS ENUM ('draft', 'active', 'paused', 'completed', 'cancelled');
CREATE TYPE discount_type AS ENUM ('percentage', 'fixed_amount', 'free_trial');
CREATE TYPE target_audience AS ENUM ('all_users', 'new_users', 'existing_users', 'specific_plans', 'custom_segment');

-- Create promo_campaigns table
CREATE TABLE public.promo_campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  campaign_code TEXT UNIQUE NOT NULL,
  discount_type discount_type NOT NULL,
  discount_value NUMERIC NOT NULL,
  target_audience target_audience NOT NULL DEFAULT 'all_users',
  target_plans TEXT[], -- Array of plan names if target_audience is 'specific_plans'
  custom_criteria JSONB, -- Custom segmentation criteria
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  usage_limit INTEGER, -- Max number of uses
  usage_limit_per_user INTEGER DEFAULT 1, -- Max uses per user
  is_active BOOLEAN NOT NULL DEFAULT true,
  status campaign_status NOT NULL DEFAULT 'draft',
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create promo_campaign_usage table to track usage
CREATE TABLE public.promo_campaign_usage (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID NOT NULL REFERENCES promo_campaigns(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  discount_amount NUMERIC NOT NULL,
  original_amount NUMERIC NOT NULL,
  final_amount NUMERIC NOT NULL,
  subscription_id TEXT, -- Stripe subscription ID if applicable
  payment_intent_id TEXT, -- Stripe payment intent ID if applicable
  metadata JSONB DEFAULT '{}'
);

-- Create promo_campaign_analytics table for tracking performance
CREATE TABLE public.promo_campaign_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  campaign_id UUID NOT NULL REFERENCES promo_campaigns(id) ON DELETE CASCADE,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  views INTEGER NOT NULL DEFAULT 0,
  applications INTEGER NOT NULL DEFAULT 0,
  conversions INTEGER NOT NULL DEFAULT 0,
  total_discount_given NUMERIC NOT NULL DEFAULT 0,
  total_revenue NUMERIC NOT NULL DEFAULT 0,
  unique_users INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(campaign_id, date)
);

-- Enable RLS on all tables
ALTER TABLE public.promo_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.promo_campaign_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.promo_campaign_analytics ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for promo_campaigns
CREATE POLICY "Super admins can manage all promo campaigns"
  ON public.promo_campaigns
  FOR ALL
  USING (public.is_super_admin());

CREATE POLICY "Users can view active campaigns"
  ON public.promo_campaigns
  FOR SELECT
  USING (is_active = true AND status = 'active' AND 
         (start_date <= now() AND (end_date IS NULL OR end_date >= now())));

-- Create RLS policies for promo_campaign_usage
CREATE POLICY "Super admins can view all campaign usage"
  ON public.promo_campaign_usage
  FOR SELECT
  USING (public.is_super_admin());

CREATE POLICY "Users can view their own usage"
  ON public.promo_campaign_usage
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "System can insert usage records"
  ON public.promo_campaign_usage
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for promo_campaign_analytics
CREATE POLICY "Super admins can manage all analytics"
  ON public.promo_campaign_analytics
  FOR ALL
  USING (public.is_super_admin());

-- Create indexes for better performance
CREATE INDEX idx_promo_campaigns_status ON promo_campaigns(status);
CREATE INDEX idx_promo_campaigns_active ON promo_campaigns(is_active);
CREATE INDEX idx_promo_campaigns_dates ON promo_campaigns(start_date, end_date);
CREATE INDEX idx_promo_campaigns_code ON promo_campaigns(campaign_code);
CREATE INDEX idx_promo_campaign_usage_campaign ON promo_campaign_usage(campaign_id);
CREATE INDEX idx_promo_campaign_usage_user ON promo_campaign_usage(user_id);
CREATE INDEX idx_promo_campaign_analytics_campaign_date ON promo_campaign_analytics(campaign_id, date);

-- Create function to validate promo code
CREATE OR REPLACE FUNCTION public.validate_promo_code(code TEXT, user_id_param UUID DEFAULT auth.uid())
RETURNS TABLE(
  valid BOOLEAN,
  campaign_id UUID,
  discount_type discount_type,
  discount_value NUMERIC,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  campaign_record promo_campaigns%ROWTYPE;
  usage_count INTEGER;
  user_usage_count INTEGER;
BEGIN
  -- Find the campaign
  SELECT * INTO campaign_record 
  FROM promo_campaigns 
  WHERE campaign_code = code AND is_active = true AND status = 'active';
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, NULL::UUID, NULL::discount_type, NULL::NUMERIC, 'Invalid or inactive promo code';
    RETURN;
  END IF;
  
  -- Check date validity
  IF campaign_record.start_date > now() THEN
    RETURN QUERY SELECT false, NULL::UUID, NULL::discount_type, NULL::NUMERIC, 'Promo code not yet active';
    RETURN;
  END IF;
  
  IF campaign_record.end_date IS NOT NULL AND campaign_record.end_date < now() THEN
    RETURN QUERY SELECT false, NULL::UUID, NULL::discount_type, NULL::NUMERIC, 'Promo code has expired';
    RETURN;
  END IF;
  
  -- Check usage limits
  IF campaign_record.usage_limit IS NOT NULL THEN
    SELECT COUNT(*) INTO usage_count 
    FROM promo_campaign_usage 
    WHERE campaign_id = campaign_record.id;
    
    IF usage_count >= campaign_record.usage_limit THEN
      RETURN QUERY SELECT false, NULL::UUID, NULL::discount_type, NULL::NUMERIC, 'Promo code usage limit exceeded';
      RETURN;
    END IF;
  END IF;
  
  -- Check per-user usage limit
  IF user_id_param IS NOT NULL AND campaign_record.usage_limit_per_user IS NOT NULL THEN
    SELECT COUNT(*) INTO user_usage_count 
    FROM promo_campaign_usage 
    WHERE campaign_id = campaign_record.id AND user_id = user_id_param;
    
    IF user_usage_count >= campaign_record.usage_limit_per_user THEN
      RETURN QUERY SELECT false, NULL::UUID, NULL::discount_type, NULL::NUMERIC, 'You have already used this promo code';
      RETURN;
    END IF;
  END IF;
  
  -- All validations passed
  RETURN QUERY SELECT 
    true, 
    campaign_record.id, 
    campaign_record.discount_type, 
    campaign_record.discount_value, 
    NULL::TEXT;
END;
$$;

-- Create function to apply promo code
CREATE OR REPLACE FUNCTION public.apply_promo_code(
  code TEXT,
  user_id_param UUID,
  original_amount_param NUMERIC,
  subscription_id_param TEXT DEFAULT NULL,
  payment_intent_id_param TEXT DEFAULT NULL
)
RETURNS TABLE(
  success BOOLEAN,
  discount_amount NUMERIC,
  final_amount NUMERIC,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  validation_result RECORD;
  calculated_discount NUMERIC;
  final_amount_calculated NUMERIC;
BEGIN
  -- Validate the promo code
  SELECT * INTO validation_result 
  FROM public.validate_promo_code(code, user_id_param);
  
  IF NOT validation_result.valid THEN
    RETURN QUERY SELECT false, 0::NUMERIC, original_amount_param, validation_result.error_message;
    RETURN;
  END IF;
  
  -- Calculate discount
  IF validation_result.discount_type = 'percentage' THEN
    calculated_discount := original_amount_param * (validation_result.discount_value / 100);
  ELSIF validation_result.discount_type = 'fixed_amount' THEN
    calculated_discount := LEAST(validation_result.discount_value, original_amount_param);
  ELSE
    calculated_discount := 0; -- Handle free_trial separately in application logic
  END IF;
  
  final_amount_calculated := GREATEST(0, original_amount_param - calculated_discount);
  
  -- Record the usage
  INSERT INTO promo_campaign_usage (
    campaign_id,
    user_id,
    discount_amount,
    original_amount,
    final_amount,
    subscription_id,
    payment_intent_id
  ) VALUES (
    validation_result.campaign_id,
    user_id_param,
    calculated_discount,
    original_amount_param,
    final_amount_calculated,
    subscription_id_param,
    payment_intent_id_param
  );
  
  -- Update analytics
  INSERT INTO promo_campaign_analytics (campaign_id, applications, conversions, total_discount_given, total_revenue)
  VALUES (validation_result.campaign_id, 1, 1, calculated_discount, final_amount_calculated)
  ON CONFLICT (campaign_id, date)
  DO UPDATE SET
    applications = promo_campaign_analytics.applications + 1,
    conversions = promo_campaign_analytics.conversions + 1,
    total_discount_given = promo_campaign_analytics.total_discount_given + calculated_discount,
    total_revenue = promo_campaign_analytics.total_revenue + final_amount_calculated,
    updated_at = now();
  
  RETURN QUERY SELECT true, calculated_discount, final_amount_calculated, NULL::TEXT;
END;
$$;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_promo_campaigns_updated_at 
  BEFORE UPDATE ON promo_campaigns 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_promo_campaign_analytics_updated_at 
  BEFORE UPDATE ON promo_campaign_analytics 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
