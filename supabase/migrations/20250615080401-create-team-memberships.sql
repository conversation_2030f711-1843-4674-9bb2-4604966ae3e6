
-- Create team_memberships table for project team management
CREATE TABLE IF NOT EXISTS public.team_memberships (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id uuid NOT NULL,
  user_id uuid NOT NULL,
  role text NOT NULL DEFAULT 'collaborator',
  permissions jsonb DEFAULT '{}',
  invited_by uuid NOT NULL,
  joined_at timestamp with time zone NOT NULL DEFAULT now(),
  status text NOT NULL DEFAULT 'active',
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  
  CONSTRAINT fk_team_memberships_user FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE,
  CONSTRAINT fk_team_memberships_invited_by <PERSON>OR<PERSON><PERSON><PERSON> KEY (invited_by) REFERENCES public.profiles(id) ON DELETE CASCADE,
  CONSTRAINT unique_project_user UNIQUE (project_id, user_id)
);

-- Enable RLS on team_memberships
ALTER TABLE public.team_memberships ENABLE ROW LEVEL SECURITY;

-- Add RLS policies
CREATE POLICY "Users can view memberships for their organizations"
ON public.team_memberships FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.projects p
    JOIN public.organization_members om ON p.org_id = om.org_id
    WHERE p.id = team_memberships.project_id
    AND om.user_id = auth.uid()
  )
);

CREATE POLICY "Users can create memberships for their organizations"
ON public.team_memberships FOR INSERT
WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.projects p
    JOIN public.organization_members om ON p.org_id = om.org_id
    WHERE p.id = team_memberships.project_id
    AND om.user_id = auth.uid()
    AND om.role IN ('admin', 'owner')
  )
);

CREATE POLICY "Users can update memberships for their organizations"
ON public.team_memberships FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM public.projects p
    JOIN public.organization_members om ON p.org_id = om.org_id
    WHERE p.id = team_memberships.project_id
    AND om.user_id = auth.uid()
    AND om.role IN ('admin', 'owner')
  )
);

CREATE POLICY "Users can delete memberships for their organizations"
ON public.team_memberships FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM public.projects p
    JOIN public.organization_members om ON p.org_id = om.org_id
    WHERE p.id = team_memberships.project_id
    AND om.user_id = auth.uid()
    AND om.role IN ('admin', 'owner')
  )
);

-- Add updated_at trigger
CREATE TRIGGER update_team_memberships_updated_at
  BEFORE UPDATE ON public.team_memberships
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
