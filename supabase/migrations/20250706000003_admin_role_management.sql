-- Admin role management functions for Super_Admin users

-- Function to assign roles (only <PERSON><PERSON><PERSON><PERSON> can use this)
CREATE OR REPLACE FUNCTION public.assign_user_role(
  target_user_id UUID,
  new_role TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is a super admin
  IF NOT public.is_super_admin() THEN
    RAISE EXCEPTION 'Access denied: Only Super_Admin can assign roles';
  END IF;
  
  -- Validate the role
  IF new_role NOT IN ('writer', 'producer', 'director', 'admin', 'super_admin') THEN
    RAISE EXCEPTION 'Invalid role: %', new_role;
  END IF;
  
  -- Update the user's role
  UPDATE public.profiles 
  SET role = new_role, updated_at = NOW()
  WHERE id = target_user_id;
  
  -- Check if the update was successful
  IF NOT FOUND THEN
    RAISE EXCEPTION 'User not found: %', target_user_id;
  END IF;
  
  -- Log the role change
  INSERT INTO public.beta_request_logs (
    beta_request_id,
    action,
    performed_by,
    details
  ) VALUES (
    NULL,
    'role_assignment',
    auth.uid(),
    jsonb_build_object(
      'target_user_id', target_user_id,
      'new_role', new_role,
      'timestamp', NOW()
    )
  );
  
  RETURN TRUE;
END;
$$;

-- Function to get user role information
CREATE OR REPLACE FUNCTION public.get_user_role_info(
  target_user_id UUID DEFAULT NULL
)
RETURNS TABLE(
  user_id UUID,
  email TEXT,
  full_name TEXT,
  role TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the current user is a super admin
  IF NOT public.is_super_admin() THEN
    RAISE EXCEPTION 'Access denied: Only Super_Admin can view role information';
  END IF;
  
  -- If no target user specified, return all users
  IF target_user_id IS NULL THEN
    RETURN QUERY
    SELECT 
      p.id,
      au.email,
      p.full_name,
      p.role,
      p.created_at,
      p.updated_at
    FROM public.profiles p
    JOIN auth.users au ON p.id = au.id
    ORDER BY p.role DESC, p.created_at DESC;
  ELSE
    -- Return specific user
    RETURN QUERY
    SELECT 
      p.id,
      au.email,
      p.full_name,
      p.role,
      p.created_at,
      p.updated_at
    FROM public.profiles p
    JOIN auth.users au ON p.id = au.id
    WHERE p.id = target_user_id;
  END IF;
END;
$$;

-- Function to promote first user to super_admin (one-time setup)
CREATE OR REPLACE FUNCTION public.setup_initial_super_admin(
  admin_email TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_count INTEGER;
  target_user_id UUID;
BEGIN
  -- Check if any super_admin already exists
  SELECT COUNT(*) INTO user_count
  FROM public.profiles
  WHERE role = 'super_admin';
  
  -- Only allow this if no super_admin exists yet
  IF user_count > 0 THEN
    RAISE EXCEPTION 'Super_Admin already exists. Use assign_user_role() instead.';
  END IF;
  
  -- Find the user by email
  SELECT au.id INTO target_user_id
  FROM auth.users au
  WHERE au.email = admin_email;
  
  IF target_user_id IS NULL THEN
    RAISE EXCEPTION 'User with email % not found', admin_email;
  END IF;
  
  -- Promote to super_admin
  UPDATE public.profiles 
  SET role = 'super_admin', updated_at = NOW()
  WHERE id = target_user_id;
  
  -- Log the initial setup
  INSERT INTO public.beta_request_logs (
    beta_request_id,
    action,
    performed_by,
    details
  ) VALUES (
    NULL,
    'initial_super_admin_setup',
    target_user_id,
    jsonb_build_object(
      'admin_email', admin_email,
      'timestamp', NOW()
    )
  );
  
  RETURN TRUE;
END;
$$;

-- Grant execute permissions to authenticated users (functions have their own security)
GRANT EXECUTE ON FUNCTION public.assign_user_role(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_role_info(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.setup_initial_super_admin(TEXT) TO authenticated;
