-- Create backup system tables for automated backups and disaster recovery

-- Create backup_metadata table to track backup information
CREATE TABLE IF NOT EXISTS public.backup_metadata (
  id TEXT PRIMARY KEY,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  size BIGINT NOT NULL DEFAULT 0,
  checksum TEXT NOT NULL,
  tables TEXT[] NOT NULL DEFAULT '{}',
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
  destination TEXT NOT NULL DEFAULT 'supabase',
  retention_date TIMESTAMPTZ NOT NULL,
  description TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create backup_data table to store actual backup data
CREATE TABLE IF NOT EXISTS public.backup_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_id TEXT NOT NULL REFERENCES public.backup_metadata(id) ON DELETE CASCADE,
  data JSONB NOT NULL,
  compressed BOOLEAN DEFAULT false,
  encrypted BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create backup_schedules table for automated backup scheduling
CREATE TABLE IF NOT EXISTS public.backup_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  schedule_expression TEXT NOT NULL, -- Cron expression
  enabled BOOLEAN NOT NULL DEFAULT true,
  tables TEXT[] NOT NULL DEFAULT '{}',
  retention_days INTEGER NOT NULL DEFAULT 30,
  compression_enabled BOOLEAN NOT NULL DEFAULT true,
  encryption_enabled BOOLEAN NOT NULL DEFAULT false,
  last_run_at TIMESTAMPTZ,
  next_run_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create backup_logs table for audit trail
CREATE TABLE IF NOT EXISTS public.backup_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_id TEXT REFERENCES public.backup_metadata(id),
  schedule_id UUID REFERENCES public.backup_schedules(id),
  action TEXT NOT NULL, -- 'create', 'restore', 'delete', 'verify'
  status TEXT NOT NULL CHECK (status IN ('started', 'completed', 'failed')),
  message TEXT,
  details JSONB,
  duration_ms INTEGER,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create disaster_recovery_plans table
CREATE TABLE IF NOT EXISTS public.disaster_recovery_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  priority INTEGER NOT NULL DEFAULT 1, -- 1 = highest priority
  recovery_time_objective INTEGER NOT NULL, -- RTO in minutes
  recovery_point_objective INTEGER NOT NULL, -- RPO in minutes
  procedures JSONB NOT NULL, -- Step-by-step recovery procedures
  contact_list JSONB, -- Emergency contacts
  dependencies TEXT[], -- System dependencies
  last_tested_at TIMESTAMPTZ,
  test_results JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_backup_metadata_timestamp ON public.backup_metadata(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_backup_metadata_status ON public.backup_metadata(status);
CREATE INDEX IF NOT EXISTS idx_backup_metadata_retention ON public.backup_metadata(retention_date);
CREATE INDEX IF NOT EXISTS idx_backup_data_backup_id ON public.backup_data(backup_id);
CREATE INDEX IF NOT EXISTS idx_backup_schedules_enabled ON public.backup_schedules(enabled) WHERE enabled = true;
CREATE INDEX IF NOT EXISTS idx_backup_schedules_next_run ON public.backup_schedules(next_run_at) WHERE enabled = true;
CREATE INDEX IF NOT EXISTS idx_backup_logs_backup_id ON public.backup_logs(backup_id);
CREATE INDEX IF NOT EXISTS idx_backup_logs_created_at ON public.backup_logs(created_at DESC);

-- Enable RLS on all backup tables
ALTER TABLE public.backup_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.disaster_recovery_plans ENABLE ROW LEVEL SECURITY;

-- RLS Policies - Only super admins can access backup system
CREATE POLICY "Super admins can manage backup metadata" ON public.backup_metadata
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can manage backup data" ON public.backup_data
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can manage backup schedules" ON public.backup_schedules
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can view backup logs" ON public.backup_logs
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "Super admins can manage disaster recovery plans" ON public.disaster_recovery_plans
  FOR ALL USING (public.is_super_admin());

-- Create functions for backup automation

-- Function to calculate next run time based on cron expression
CREATE OR REPLACE FUNCTION public.calculate_next_run_time(
  cron_expression TEXT,
  from_time TIMESTAMPTZ DEFAULT NOW()
) RETURNS TIMESTAMPTZ
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Simplified cron calculation - in production, use a proper cron library
  -- This is a basic implementation for daily backups
  IF cron_expression = '0 2 * * *' THEN
    -- Daily at 2 AM
    RETURN DATE_TRUNC('day', from_time) + INTERVAL '1 day' + INTERVAL '2 hours';
  ELSIF cron_expression = '0 0 * * 0' THEN
    -- Weekly on Sunday at midnight
    RETURN DATE_TRUNC('week', from_time) + INTERVAL '1 week';
  ELSIF cron_expression = '0 0 1 * *' THEN
    -- Monthly on the 1st at midnight
    RETURN DATE_TRUNC('month', from_time) + INTERVAL '1 month';
  ELSE
    -- Default to daily at 2 AM
    RETURN DATE_TRUNC('day', from_time) + INTERVAL '1 day' + INTERVAL '2 hours';
  END IF;
END;
$$;

-- Function to update backup schedule next run time
CREATE OR REPLACE FUNCTION public.update_backup_schedule_next_run()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  NEW.next_run_at := public.calculate_next_run_time(NEW.schedule_expression);
  NEW.updated_at := NOW();
  RETURN NEW;
END;
$$;

-- Trigger to automatically update next run time
CREATE TRIGGER trigger_update_backup_schedule_next_run
  BEFORE INSERT OR UPDATE ON public.backup_schedules
  FOR EACH ROW
  EXECUTE FUNCTION public.update_backup_schedule_next_run();

-- Function to log backup operations
CREATE OR REPLACE FUNCTION public.log_backup_operation(
  backup_id_param TEXT,
  schedule_id_param UUID,
  action_param TEXT,
  status_param TEXT,
  message_param TEXT DEFAULT NULL,
  details_param JSONB DEFAULT NULL,
  duration_ms_param INTEGER DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO public.backup_logs (
    backup_id,
    schedule_id,
    action,
    status,
    message,
    details,
    duration_ms
  ) VALUES (
    backup_id_param,
    schedule_id_param,
    action_param,
    status_param,
    message_param,
    details_param,
    duration_ms_param
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$;

-- Function to cleanup old backups
CREATE OR REPLACE FUNCTION public.cleanup_old_backups()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count INTEGER := 0;
  backup_record RECORD;
BEGIN
  -- Delete backups past their retention date
  FOR backup_record IN
    SELECT id FROM public.backup_metadata
    WHERE retention_date < NOW()
  LOOP
    -- Delete backup data first (due to foreign key)
    DELETE FROM public.backup_data WHERE backup_id = backup_record.id;
    
    -- Delete backup metadata
    DELETE FROM public.backup_metadata WHERE id = backup_record.id;
    
    deleted_count := deleted_count + 1;
  END LOOP;
  
  -- Log cleanup operation
  PERFORM public.log_backup_operation(
    NULL,
    NULL,
    'cleanup',
    'completed',
    format('Cleaned up %s old backups', deleted_count),
    jsonb_build_object('deleted_count', deleted_count)
  );
  
  RETURN deleted_count;
END;
$$;

-- Insert default backup schedule
INSERT INTO public.backup_schedules (
  name,
  schedule_expression,
  enabled,
  tables,
  retention_days,
  compression_enabled,
  encryption_enabled
) VALUES (
  'Daily Full Backup',
  '0 2 * * *', -- Daily at 2 AM
  true,
  ARRAY[
    'profiles',
    'organizations',
    'organization_members',
    'characters',
    'locations',
    'scenes',
    'posts',
    'comments',
    'revisions',
    'screenplays',
    'storyboards'
  ],
  30, -- 30 days retention
  true,
  true
) ON CONFLICT DO NOTHING;

-- Insert default disaster recovery plan
INSERT INTO public.disaster_recovery_plans (
  name,
  description,
  priority,
  recovery_time_objective,
  recovery_point_objective,
  procedures,
  contact_list,
  dependencies
) VALUES (
  'Database Failure Recovery',
  'Procedures for recovering from complete database failure',
  1,
  60, -- 1 hour RTO
  15, -- 15 minutes RPO
  jsonb_build_object(
    'steps', jsonb_build_array(
      'Assess the scope of the failure',
      'Notify stakeholders using contact list',
      'Identify the most recent valid backup',
      'Verify backup integrity',
      'Restore database from backup',
      'Verify data integrity post-restore',
      'Update DNS/routing if necessary',
      'Notify users of service restoration'
    ),
    'rollback_procedures', jsonb_build_array(
      'Create snapshot of current state',
      'Document issues encountered',
      'Restore from previous backup if needed'
    )
  ),
  jsonb_build_object(
    'primary_contact', '<EMAIL>',
    'technical_lead', '<EMAIL>',
    'management', '<EMAIL>'
  ),
  ARRAY['Supabase', 'DNS Provider', 'CDN', 'Monitoring Service']
) ON CONFLICT DO NOTHING;
