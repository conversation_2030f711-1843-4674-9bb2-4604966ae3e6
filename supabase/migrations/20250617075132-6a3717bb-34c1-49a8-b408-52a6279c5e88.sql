
-- Create database backup configuration table
CREATE TABLE IF NOT EXISTS public.database_backups (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  backup_type text NOT NULL CHECK (backup_type IN ('full', 'incremental', 'point_in_time')),
  backup_status text NOT NULL DEFAULT 'pending' CHECK (backup_status IN ('pending', 'running', 'completed', 'failed')),
  backup_location text NOT NULL,
  backup_size bigint,
  started_at timestamp with time zone NOT NULL DEFAULT now(),
  completed_at timestamp with time zone,
  retention_until timestamp with time zone NOT NULL,
  metadata jsonb DEFAULT '{}',
  checksum text,
  error_message text,
  created_by uuid,
  org_id uuid
);

-- Create migration history table for rollback tracking
CREATE TABLE IF NOT EXISTS public.migration_history (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  migration_name text NOT NULL,
  migration_version text NOT NULL,
  applied_at timestamp with time zone NOT NULL DEFAULT now(),
  rollback_sql text,
  migration_checksum text,
  applied_by uuid,
  rollback_available boolean NOT NULL DEFAULT false,
  rollback_tested boolean NOT NULL DEFAULT false,
  environment text NOT NULL DEFAULT 'production'
);

-- Create database performance monitoring table
CREATE TABLE IF NOT EXISTS public.database_performance_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp timestamp with time zone NOT NULL DEFAULT now(),
  connection_count integer NOT NULL,
  active_connections integer NOT NULL,
  idle_connections integer NOT NULL,
  max_connections integer NOT NULL,
  query_performance jsonb NOT NULL DEFAULT '{}',
  cache_hit_ratio numeric(5,4),
  index_usage jsonb DEFAULT '{}',
  slow_queries jsonb DEFAULT '[]',
  lock_waits integer DEFAULT 0,
  deadlocks integer DEFAULT 0,
  temp_files_created integer DEFAULT 0,
  temp_bytes bigint DEFAULT 0,
  database_size bigint,
  table_stats jsonb DEFAULT '{}',
  buffer_stats jsonb DEFAULT '{}'
);

-- Create connection pool monitoring table
CREATE TABLE IF NOT EXISTS public.connection_pool_metrics (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  timestamp timestamp with time zone NOT NULL DEFAULT now(),
  pool_name text NOT NULL,
  total_connections integer NOT NULL,
  active_connections integer NOT NULL,
  idle_connections integer NOT NULL,
  waiting_connections integer NOT NULL,
  max_connections integer NOT NULL,
  connection_errors integer DEFAULT 0,
  avg_connection_time numeric(10,3),
  pool_exhausted_count integer DEFAULT 0,
  metadata jsonb DEFAULT '{}'
);

-- Add RLS policies
ALTER TABLE public.database_backups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.migration_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.database_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.connection_pool_metrics ENABLE ROW LEVEL SECURITY;

-- Policies for database_backups
CREATE POLICY "Admin users can manage backups" ON public.database_backups
  FOR ALL USING (public.is_super_admin());

-- Policies for migration_history
CREATE POLICY "Admin users can view migration history" ON public.migration_history
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "Admin users can manage migrations" ON public.migration_history
  FOR ALL USING (public.is_super_admin());

-- Policies for performance metrics
CREATE POLICY "Admin users can view performance metrics" ON public.database_performance_metrics
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "System can insert performance metrics" ON public.database_performance_metrics
  FOR INSERT WITH CHECK (true);

-- Policies for connection pool metrics
CREATE POLICY "Admin users can view pool metrics" ON public.connection_pool_metrics
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "System can insert pool metrics" ON public.connection_pool_metrics
  FOR INSERT WITH CHECK (true);

-- Create function to initiate database backup
CREATE OR REPLACE FUNCTION public.initiate_database_backup(
  backup_type_param text DEFAULT 'full',
  retention_days integer DEFAULT 30
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  backup_id uuid;
  backup_location_path text;
BEGIN
  -- Check if user is super admin
  IF NOT public.is_super_admin() THEN
    RAISE EXCEPTION 'Access denied: Only super admins can initiate backups';
  END IF;

  backup_location_path := 'backups/' || backup_type_param || '/' || 
                         to_char(now(), 'YYYY/MM/DD') || '/' || 
                         gen_random_uuid()::text || '.backup';

  INSERT INTO public.database_backups (
    backup_type,
    backup_location,
    retention_until,
    created_by
  ) VALUES (
    backup_type_param,
    backup_location_path,
    now() + (retention_days || ' days')::interval,
    auth.uid()
  ) RETURNING id INTO backup_id;

  RETURN backup_id;
END;
$$;

-- Create function to record migration with rollback info
CREATE OR REPLACE FUNCTION public.record_migration(
  migration_name_param text,
  migration_version_param text,
  rollback_sql_param text DEFAULT NULL,
  migration_checksum_param text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  migration_id uuid;
BEGIN
  -- Check if user is super admin
  IF NOT public.is_super_admin() THEN
    RAISE EXCEPTION 'Access denied: Only super admins can record migrations';
  END IF;

  INSERT INTO public.migration_history (
    migration_name,
    migration_version,
    rollback_sql,
    migration_checksum,
    applied_by,
    rollback_available
  ) VALUES (
    migration_name_param,
    migration_version_param,
    rollback_sql_param,
    migration_checksum_param,
    auth.uid(),
    rollback_sql_param IS NOT NULL
  ) RETURNING id INTO migration_id;

  RETURN migration_id;
END;
$$;

-- Create function to collect database performance metrics
CREATE OR REPLACE FUNCTION public.collect_database_metrics()
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  metrics_id uuid;
  conn_stats record;
  cache_ratio numeric;
BEGIN
  -- Get connection statistics
  SELECT 
    count(*) as total_connections,
    count(*) FILTER (WHERE state = 'active') as active_connections,
    count(*) FILTER (WHERE state = 'idle') as idle_connections
  INTO conn_stats
  FROM pg_stat_activity
  WHERE datname = current_database();

  -- Get cache hit ratio
  SELECT 
    round(
      sum(blks_hit) * 100.0 / 
      NULLIF(sum(blks_hit) + sum(blks_read), 0), 4
    )
  INTO cache_ratio
  FROM pg_stat_database
  WHERE datname = current_database();

  INSERT INTO public.database_performance_metrics (
    connection_count,
    active_connections,
    idle_connections,
    max_connections,
    cache_hit_ratio,
    query_performance,
    database_size
  ) VALUES (
    conn_stats.total_connections,
    conn_stats.active_connections,
    conn_stats.idle_connections,
    (SELECT setting::integer FROM pg_settings WHERE name = 'max_connections'),
    cache_ratio,
    jsonb_build_object(
      'avg_query_time', (
        SELECT avg(mean_exec_time) 
        FROM pg_stat_statements 
        WHERE calls > 10
        LIMIT 1
      )
    ),
    pg_database_size(current_database())
  ) RETURNING id INTO metrics_id;

  RETURN metrics_id;
END;
$$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_database_backups_status_created 
  ON public.database_backups(backup_status, started_at);

CREATE INDEX IF NOT EXISTS idx_migration_history_applied_at 
  ON public.migration_history(applied_at DESC);

CREATE INDEX IF NOT EXISTS idx_performance_metrics_timestamp 
  ON public.database_performance_metrics(timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_connection_pool_metrics_timestamp 
  ON public.connection_pool_metrics(timestamp DESC, pool_name);
