
-- <PERSON><PERSON> offers table for the offer system
CREATE TABLE public.screenplay_offers (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id uuid NOT NULL,
  buyer_id uuid NOT NULL,
  seller_id uuid NOT NULL,
  offer_amount numeric NOT NULL,
  message text,
  status text NOT NULL DEFAULT 'pending',
  expires_at timestamp with time zone NOT NULL DEFAULT (now() + interval '7 days'),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  responded_at timestamp with time zone
);

-- Create screenplay files table for file management
CREATE TABLE public.screenplay_files (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id uuid NOT NULL,
  file_name text NOT NULL,
  file_type text NOT NULL,
  file_size bigint NOT NULL,
  storage_path text NOT NULL,
  is_preview boolean NOT NULL DEFAULT false,
  uploaded_at timestamp with time zone NOT NULL DEFAULT now(),
  uploaded_by uuid NOT NULL
);

-- Create marketplace analytics table
CREATE TABLE public.marketplace_analytics (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id uuid NOT NULL,
  event_type text NOT NULL,
  event_data jsonb,
  user_id uuid,
  ip_address inet,
  user_agent text,
  created_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Add RLS policies for screenplay_offers
ALTER TABLE public.screenplay_offers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view offers they made or received" 
  ON public.screenplay_offers 
  FOR SELECT 
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Users can create offers" 
  ON public.screenplay_offers 
  FOR INSERT 
  WITH CHECK (auth.uid() = buyer_id);

CREATE POLICY "Users can update offers they made or received" 
  ON public.screenplay_offers 
  FOR UPDATE 
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- Add RLS policies for screenplay_files
ALTER TABLE public.screenplay_files ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view files of published screenplays or their own" 
  ON public.screenplay_files 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.screenplays s 
      WHERE s.id = screenplay_id 
      AND (s.status = 'published' OR s.writer_id = auth.uid())
    )
  );

CREATE POLICY "Users can upload files to their own screenplays" 
  ON public.screenplay_files 
  FOR INSERT 
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.screenplays s 
      WHERE s.id = screenplay_id 
      AND s.writer_id = auth.uid()
    )
  );

-- Add RLS policies for marketplace_analytics  
ALTER TABLE public.marketplace_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Analytics are publicly readable" 
  ON public.marketplace_analytics 
  FOR SELECT 
  USING (true);

CREATE POLICY "Analytics can be inserted by anyone" 
  ON public.marketplace_analytics 
  FOR INSERT 
  WITH CHECK (true);

-- Create storage bucket for screenplay files
INSERT INTO storage.buckets (id, name, public) 
VALUES ('screenplay-files', 'screenplay-files', false);

-- Create storage policies for screenplay files
CREATE POLICY "Authenticated users can upload screenplay files"
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'screenplay-files' 
  AND auth.role() = 'authenticated'
);

CREATE POLICY "Users can view files they have access to"
ON storage.objects FOR SELECT
USING (
  bucket_id = 'screenplay-files' 
  AND (
    -- File owner can always access
    auth.uid()::text = (storage.foldername(name))[1]
    OR
    -- Users who purchased the screenplay can access
    EXISTS (
      SELECT 1 FROM public.screenplay_purchases sp
      JOIN public.screenplay_files sf ON sf.screenplay_id = sp.screenplay_id
      WHERE sp.buyer_id = auth.uid()
      AND sf.storage_path = name
    )
  )
);

-- Add indexes for performance
CREATE INDEX idx_screenplay_offers_screenplay_id ON public.screenplay_offers(screenplay_id);
CREATE INDEX idx_screenplay_offers_buyer_id ON public.screenplay_offers(buyer_id);
CREATE INDEX idx_screenplay_offers_seller_id ON public.screenplay_offers(seller_id);
CREATE INDEX idx_screenplay_offers_status ON public.screenplay_offers(status);

CREATE INDEX idx_screenplay_files_screenplay_id ON public.screenplay_files(screenplay_id);
CREATE INDEX idx_screenplay_files_is_preview ON public.screenplay_files(is_preview);

CREATE INDEX idx_marketplace_analytics_screenplay_id ON public.marketplace_analytics(screenplay_id);
CREATE INDEX idx_marketplace_analytics_event_type ON public.marketplace_analytics(event_type);
CREATE INDEX idx_marketplace_analytics_created_at ON public.marketplace_analytics(created_at);
