
-- Check what team-related tables already exist and create only what's missing

-- First, let's ensure team_activities table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_activities') THEN
        CREATE TABLE public.team_activities (
          id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          org_id uuid NOT NULL,
          user_id uuid NOT NULL,
          activity_type text NOT NULL,
          entity_type text,
          entity_id uuid,
          description text NOT NULL,
          metadata jsonb DEFAULT '{}',
          created_at timestamp with time zone NOT NULL DEFAULT now()
        );
        
        -- Add RLS policies for team_activities
        ALTER TABLE public.team_activities ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Users can view activities for their organizations"
        ON public.team_activities FOR SELECT
        USING (public.is_org_member(org_id));

        CREATE POLICY "Users can create activities for their organizations"
        ON public.team_activities FOR INSERT
        WITH CHECK (public.is_org_member(org_id) AND user_id = auth.uid());
    END IF;
END $$;

-- Create team_discussions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussions') THEN
        CREATE TABLE public.team_discussions (
          id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          org_id uuid NOT NULL,
          title text NOT NULL,
          content text NOT NULL,
          created_by uuid NOT NULL,
          discussion_type text NOT NULL DEFAULT 'general',
          is_announcement boolean DEFAULT false,
          is_pinned boolean DEFAULT false,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now()
        );
        
        -- Add RLS policies for team_discussions
        ALTER TABLE public.team_discussions ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Users can view discussions for their organizations"
        ON public.team_discussions FOR SELECT
        USING (public.is_org_member(org_id));

        CREATE POLICY "Users can create discussions for their organizations"
        ON public.team_discussions FOR INSERT
        WITH CHECK (public.is_org_member(org_id) AND created_by = auth.uid());

        CREATE POLICY "Discussion creators can update their discussions"
        ON public.team_discussions FOR UPDATE
        USING (created_by = auth.uid() AND public.is_org_member(org_id));
    END IF;
END $$;

-- Create team_discussion_replies table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussion_replies') THEN
        CREATE TABLE public.team_discussion_replies (
          id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          discussion_id uuid NOT NULL,
          user_id uuid NOT NULL,
          content text NOT NULL,
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now()
        );
        
        -- Add RLS policies for team_discussion_replies
        ALTER TABLE public.team_discussion_replies ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Users can view replies for accessible discussions"
        ON public.team_discussion_replies FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.team_discussions td
            WHERE td.id = team_discussion_replies.discussion_id
            AND public.is_org_member(td.org_id)
          )
        );

        CREATE POLICY "Users can create replies for accessible discussions"
        ON public.team_discussion_replies FOR INSERT
        WITH CHECK (
          user_id = auth.uid() AND
          EXISTS (
            SELECT 1 FROM public.team_discussions td
            WHERE td.id = team_discussion_replies.discussion_id
            AND public.is_org_member(td.org_id)
          )
        );
    END IF;
END $$;

-- Function to get user's team collaboration access
CREATE OR REPLACE FUNCTION public.get_user_team_access(target_org_id uuid)
RETURNS TABLE(
  can_manage_teams boolean,
  max_teams integer,
  max_members_per_team integer,
  can_access_production boolean,
  can_create_custom_roles boolean
)
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT 
    CASE 
      WHEN o.plan IN ('pro-team', 'studio', 'enterprise') THEN true
      ELSE false
    END as can_manage_teams,
    tl.max_teams,
    tl.max_members_per_team,
    CASE 
      WHEN o.plan IN ('studio', 'enterprise') THEN true
      ELSE false
    END as can_access_production,
    CASE 
      WHEN o.plan = 'enterprise' THEN true
      ELSE false
    END as can_create_custom_roles
  FROM public.organizations o
  JOIN public.tier_limits tl ON o.plan = tl.tier_name
  WHERE o.id = target_org_id
  AND public.is_org_member(target_org_id);
$$;

-- Function to track team activity
CREATE OR REPLACE FUNCTION public.track_team_activity(
  _org_id uuid,
  _activity_type text,
  _description text,
  _entity_type text DEFAULT NULL,
  _entity_id uuid DEFAULT NULL,
  _metadata jsonb DEFAULT '{}'
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  activity_id uuid;
BEGIN
  -- Check organization membership
  IF NOT public.is_org_member(_org_id) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  INSERT INTO public.team_activities (
    org_id,
    user_id,
    activity_type,
    entity_type,
    entity_id,
    description,
    metadata
  ) VALUES (
    _org_id,
    auth.uid(),
    _activity_type,
    _entity_type,
    _entity_id,
    _description,
    _metadata
  ) RETURNING id INTO activity_id;

  RETURN activity_id;
END;
$$;

-- Add triggers for automatic activity tracking if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'track_team_membership_changes'
    ) THEN
        CREATE OR REPLACE FUNCTION public.track_project_team_changes()
        RETURNS trigger
        LANGUAGE plpgsql
        AS $func$
        BEGIN
          IF TG_OP = 'INSERT' THEN
            PERFORM public.track_team_activity(
              (SELECT org_id FROM public.projects WHERE id = NEW.project_id),
              'member_added',
              'Added team member to project',
              'project',
              NEW.project_id,
              jsonb_build_object('user_id', NEW.user_id, 'role', NEW.role)
            );
          ELSIF TG_OP = 'UPDATE' THEN
            IF OLD.role != NEW.role THEN
              PERFORM public.track_team_activity(
                (SELECT org_id FROM public.projects WHERE id = NEW.project_id),
                'role_changed',
                'Changed team member role',
                'project',
                NEW.project_id,
                jsonb_build_object('user_id', NEW.user_id, 'old_role', OLD.role, 'new_role', NEW.role)
              );
            END IF;
          ELSIF TG_OP = 'DELETE' THEN
            PERFORM public.track_team_activity(
              (SELECT org_id FROM public.projects WHERE id = OLD.project_id),
              'member_removed',
              'Removed team member from project',
              'project',
              OLD.project_id,
              jsonb_build_object('user_id', OLD.user_id, 'role', OLD.role)
            );
          END IF;
          
          RETURN COALESCE(NEW, OLD);
        END;
        $func$;

        CREATE TRIGGER track_team_membership_changes
          AFTER INSERT OR UPDATE OR DELETE ON public.team_memberships
          FOR EACH ROW EXECUTE FUNCTION public.track_project_team_changes();
    END IF;
END $$;

-- Add updated_at triggers if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_team_discussions_updated_at'
    ) THEN
        CREATE TRIGGER update_team_discussions_updated_at
          BEFORE UPDATE ON public.team_discussions
          FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_team_discussion_replies_updated_at'
    ) THEN
        CREATE TRIGGER update_team_discussion_replies_updated_at
          BEFORE UPDATE ON public.team_discussion_replies
          FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
END $$;
