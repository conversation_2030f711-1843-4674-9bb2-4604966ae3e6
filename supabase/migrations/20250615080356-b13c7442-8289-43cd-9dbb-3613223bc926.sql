
-- First, let's check if team_memberships table exists and create it with proper structure
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_memberships') THEN
        CREATE TABLE public.team_memberships (
          id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
          project_id uuid NOT NULL,
          user_id uuid NOT NULL,
          role text NOT NULL DEFAULT 'collaborator',
          permissions jsonb DEFAULT '{}',
          invited_by uuid NOT NULL,
          joined_at timestamp with time zone DEFAULT now(),
          created_at timestamp with time zone NOT NULL DEFAULT now(),
          updated_at timestamp with time zone NOT NULL DEFAULT now()
        );
        
        -- Add RLS policies for team_memberships
        ALTER TABLE public.team_memberships ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Users can view memberships for their projects"
        ON public.team_memberships FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.projects p
            WHERE p.id = team_memberships.project_id
            AND public.is_org_member(p.org_id)
          )
        );

        CREATE POLICY "Project owners can manage memberships"
        ON public.team_memberships FOR ALL
        USING (
          EXISTS (
            SELECT 1 FROM public.projects p
            WHERE p.id = team_memberships.project_id
            AND p.user_id = auth.uid()
            AND public.is_org_member(p.org_id)
          )
        );
    END IF;
END $$;

-- Add missing columns to team_memberships if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_memberships' AND column_name = 'permissions') THEN
        ALTER TABLE public.team_memberships ADD COLUMN permissions jsonb DEFAULT '{}';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_memberships' AND column_name = 'created_at') THEN
        ALTER TABLE public.team_memberships ADD COLUMN created_at timestamp with time zone NOT NULL DEFAULT now();
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'team_memberships' AND column_name = 'updated_at') THEN
        ALTER TABLE public.team_memberships ADD COLUMN updated_at timestamp with time zone NOT NULL DEFAULT now();
    END IF;
END $$;

-- Add updated_at trigger for team_memberships if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.triggers 
        WHERE trigger_name = 'update_team_memberships_updated_at'
    ) THEN
        CREATE TRIGGER update_team_memberships_updated_at
          BEFORE UPDATE ON public.team_memberships
          FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
END $$;
