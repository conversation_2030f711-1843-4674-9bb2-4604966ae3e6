
-- Create prompt_library table
CREATE TABLE public.prompt_library (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tool_name TEXT NOT NULL,
  version TEXT NOT NULL DEFAULT 'v1.0',
  role TEXT NOT NULL,
  cot_enabled BOOLEAN NOT NULL DEFAULT true,
  output_format TEXT NOT NULL DEFAULT 'text',
  prompt_content TEXT,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_by UUID REFERENCES auth.users(id)
);

-- Add RLS to prompt_library table
ALTER TABLE public.prompt_library ENABLE ROW LEVEL SECURITY;

-- Create policy for super admins to have full access
CREATE POLICY "Super admins can manage prompt library" 
  ON public.prompt_library 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 
      FROM public.profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'super_admin'
    )
  );

-- Create index for better query performance
CREATE INDEX idx_prompt_library_tool_name ON public.prompt_library(tool_name);
CREATE INDEX idx_prompt_library_version ON public.prompt_library(version);
CREATE INDEX idx_prompt_library_last_updated ON public.prompt_library(last_updated);

-- Add constraint to ensure valid output formats
ALTER TABLE public.prompt_library 
ADD CONSTRAINT valid_output_format 
CHECK (output_format IN ('text', 'list', 'table', 'scorecard', 'json', 'conversation'));
