-- Create beta access request system for ScriptGenius
-- This migration creates tables and functions for managing beta access requests and promo codes

-- Create beta_requests table to track user requests for beta access
CREATE TABLE IF NOT EXISTS public.beta_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  company TEXT,
  use_case TEXT NOT NULL,
  referral_source TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'converted')),
  requested_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  approved_at TIMESTAMPTZ,
  approved_by UUID REFERENCES auth.users(id),
  rejected_at TIMESTAMPTZ,
  rejected_by UUID REFERENCES auth.users(id),
  rejection_reason TEXT,
  promo_code TEXT UNIQUE,
  promo_code_used_at TIMESTAMPTZ,
  conversion_tier TEXT CHECK (conversion_tier IN ('starter', 'pro')),
  conversion_amount DECIMAL(10,2),
  notes TEXT,
  auto_approved BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create promo_codes table for managing discount codes
CREATE TABLE IF NOT EXISTS public.promo_codes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code TEXT UNIQUE NOT NULL,
  discount_percentage INTEGER NOT NULL DEFAULT 90 CHECK (discount_percentage >= 0 AND discount_percentage <= 100),
  valid_tiers TEXT[] NOT NULL DEFAULT ARRAY['starter', 'pro'],
  max_uses INTEGER NOT NULL DEFAULT 1 CHECK (max_uses > 0),
  current_uses INTEGER NOT NULL DEFAULT 0 CHECK (current_uses >= 0),
  expires_at TIMESTAMPTZ,
  created_by UUID REFERENCES auth.users(id),
  beta_request_id UUID REFERENCES public.beta_requests(id),
  is_active BOOLEAN NOT NULL DEFAULT true,
  used_by UUID[] DEFAULT ARRAY[]::UUID[],
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create beta_request_logs table for audit trail
CREATE TABLE IF NOT EXISTS public.beta_request_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  beta_request_id UUID NOT NULL REFERENCES public.beta_requests(id) ON DELETE CASCADE,
  action TEXT NOT NULL, -- 'submitted', 'approved', 'rejected', 'converted', 'email_sent'
  performed_by UUID REFERENCES auth.users(id),
  details JSONB,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create beta_settings table for configuration
CREATE TABLE IF NOT EXISTS public.beta_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES auth.users(id),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_beta_requests_email ON public.beta_requests(email);
CREATE INDEX IF NOT EXISTS idx_beta_requests_status ON public.beta_requests(status);
CREATE INDEX IF NOT EXISTS idx_beta_requests_created_at ON public.beta_requests(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_beta_requests_promo_code ON public.beta_requests(promo_code) WHERE promo_code IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_promo_codes_code ON public.promo_codes(code);
CREATE INDEX IF NOT EXISTS idx_promo_codes_active ON public.promo_codes(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_promo_codes_expires_at ON public.promo_codes(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_promo_codes_beta_request ON public.promo_codes(beta_request_id);

CREATE INDEX IF NOT EXISTS idx_beta_request_logs_request_id ON public.beta_request_logs(beta_request_id);
CREATE INDEX IF NOT EXISTS idx_beta_request_logs_action ON public.beta_request_logs(action);
CREATE INDEX IF NOT EXISTS idx_beta_request_logs_created_at ON public.beta_request_logs(created_at DESC);

-- Enable RLS on all tables
ALTER TABLE public.beta_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.promo_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_request_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Beta requests: Public can insert, Super admins can manage
CREATE POLICY "Anyone can submit beta requests" ON public.beta_requests
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Super admins can view all beta requests" ON public.beta_requests
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "Super admins can update beta requests" ON public.beta_requests
  FOR UPDATE USING (public.is_super_admin());

CREATE POLICY "Super admins can delete beta requests" ON public.beta_requests
  FOR DELETE USING (public.is_super_admin());

-- Promo codes: Only Super admins can manage
CREATE POLICY "Super admins can manage promo codes" ON public.promo_codes
  FOR ALL USING (public.is_super_admin());

-- Users can validate their own promo codes during checkout
CREATE POLICY "Users can validate promo codes" ON public.promo_codes
  FOR SELECT USING (is_active = true AND (expires_at IS NULL OR expires_at > NOW()));

-- Beta request logs: Super admins can view, system can insert
CREATE POLICY "Super admins can view beta request logs" ON public.beta_request_logs
  FOR SELECT USING (public.is_super_admin());

CREATE POLICY "System can insert beta request logs" ON public.beta_request_logs
  FOR INSERT WITH CHECK (true);

-- Beta settings: Only Super admins can manage
CREATE POLICY "Super admins can manage beta settings" ON public.beta_settings
  FOR ALL USING (public.is_super_admin());

-- Functions for beta request management

-- Function to generate unique promo codes
CREATE OR REPLACE FUNCTION public.generate_promo_code()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  code TEXT;
  exists_check INTEGER;
BEGIN
  LOOP
    -- Generate code: BETA + 6 random uppercase letters/numbers
    code := 'BETA' || upper(substring(md5(random()::text) from 1 for 6));
    
    -- Check if code already exists
    SELECT COUNT(*) INTO exists_check
    FROM public.promo_codes
    WHERE promo_codes.code = code;
    
    -- Exit loop if code is unique
    EXIT WHEN exists_check = 0;
  END LOOP;
  
  RETURN code;
END;
$$;

-- Function to log beta request actions
CREATE OR REPLACE FUNCTION public.log_beta_request_action(
  request_id UUID,
  action_type TEXT,
  user_id UUID DEFAULT NULL,
  action_details JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO public.beta_request_logs (
    beta_request_id,
    action,
    performed_by,
    details
  ) VALUES (
    request_id,
    action_type,
    user_id,
    action_details
  ) RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$;

-- Function to approve beta request and create promo code
CREATE OR REPLACE FUNCTION public.approve_beta_request(
  request_id UUID,
  approver_id UUID,
  custom_discount INTEGER DEFAULT 90,
  custom_expiry_days INTEGER DEFAULT 30
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  promo_code TEXT;
  request_record RECORD;
BEGIN
  -- Get the beta request
  SELECT * INTO request_record
  FROM public.beta_requests
  WHERE id = request_id AND status = 'pending';
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Beta request not found or already processed';
  END IF;
  
  -- Generate unique promo code
  promo_code := public.generate_promo_code();
  
  -- Create promo code
  INSERT INTO public.promo_codes (
    code,
    discount_percentage,
    valid_tiers,
    max_uses,
    expires_at,
    created_by,
    beta_request_id
  ) VALUES (
    promo_code,
    custom_discount,
    ARRAY['starter', 'pro'],
    1,
    NOW() + (custom_expiry_days || ' days')::INTERVAL,
    approver_id,
    request_id
  );
  
  -- Update beta request
  UPDATE public.beta_requests
  SET 
    status = 'approved',
    approved_at = NOW(),
    approved_by = approver_id,
    promo_code = promo_code,
    updated_at = NOW()
  WHERE id = request_id;
  
  -- Log the action
  PERFORM public.log_beta_request_action(
    request_id,
    'approved',
    approver_id,
    jsonb_build_object(
      'promo_code', promo_code,
      'discount_percentage', custom_discount,
      'expires_at', NOW() + (custom_expiry_days || ' days')::INTERVAL
    )
  );
  
  RETURN promo_code;
END;
$$;

-- Function to reject beta request
CREATE OR REPLACE FUNCTION public.reject_beta_request(
  request_id UUID,
  rejector_id UUID,
  reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update beta request
  UPDATE public.beta_requests
  SET 
    status = 'rejected',
    rejected_at = NOW(),
    rejected_by = rejector_id,
    rejection_reason = reason,
    updated_at = NOW()
  WHERE id = request_id AND status = 'pending';
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Beta request not found or already processed';
  END IF;
  
  -- Log the action
  PERFORM public.log_beta_request_action(
    request_id,
    'rejected',
    rejector_id,
    jsonb_build_object('reason', reason)
  );
  
  RETURN true;
END;
$$;

-- Function to validate and use promo code
CREATE OR REPLACE FUNCTION public.validate_promo_code(
  code_input TEXT,
  tier_input TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  promo_record RECORD;
  result JSONB;
BEGIN
  -- Get promo code details
  SELECT * INTO promo_record
  FROM public.promo_codes
  WHERE code = code_input AND is_active = true;
  
  IF NOT FOUND THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Invalid promo code'
    );
  END IF;
  
  -- Check expiration
  IF promo_record.expires_at IS NOT NULL AND promo_record.expires_at < NOW() THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Promo code has expired'
    );
  END IF;
  
  -- Check usage limits
  IF promo_record.current_uses >= promo_record.max_uses THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Promo code has been fully used'
    );
  END IF;
  
  -- Check tier validity if specified
  IF tier_input IS NOT NULL AND NOT (tier_input = ANY(promo_record.valid_tiers)) THEN
    RETURN jsonb_build_object(
      'valid', false,
      'error', 'Promo code not valid for this tier'
    );
  END IF;
  
  -- Return valid result
  RETURN jsonb_build_object(
    'valid', true,
    'discount_percentage', promo_record.discount_percentage,
    'valid_tiers', promo_record.valid_tiers,
    'expires_at', promo_record.expires_at,
    'code', promo_record.code
  );
END;
$$;

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Apply update triggers
CREATE TRIGGER update_beta_requests_updated_at
  BEFORE UPDATE ON public.beta_requests
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_promo_codes_updated_at
  BEFORE UPDATE ON public.promo_codes
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_beta_settings_updated_at
  BEFORE UPDATE ON public.beta_settings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Insert default beta settings
INSERT INTO public.beta_settings (setting_key, setting_value, description) VALUES
  ('auto_approval_enabled', 'false', 'Enable automatic approval of beta requests based on criteria'),
  ('auto_approval_criteria', '{"min_use_case_length": 50, "require_company": false, "exclude_disposable_emails": true}', 'Criteria for automatic approval'),
  ('default_discount_percentage', '90', 'Default discount percentage for beta promo codes'),
  ('default_expiry_days', '30', 'Default expiry days for beta promo codes'),
  ('email_templates', '{"approval": {"subject": "Welcome to ScriptGenius Beta!", "template": "beta_approval"}, "rejection": {"subject": "ScriptGenius Beta Request Update", "template": "beta_rejection"}}', 'Email templates for beta request responses')
ON CONFLICT (setting_key) DO NOTHING;
