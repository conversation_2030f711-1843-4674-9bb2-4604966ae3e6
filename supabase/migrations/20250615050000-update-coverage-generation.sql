
-- Update the generate_script_coverage function to use the new rate limiting
CREATE OR REPLACE FUNCTION public.generate_script_coverage(
  scene_id_param UUID,
  fidelity_level_param TEXT,
  org_id_param UUID
)
RETURNS TABLE (
  id UUID,
  coverage_report TEXT,
  synopsis TEXT,
  strengths TEXT,
  weaknesses TEXT,
  verdict TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  coverage_data JSONB;
  new_report_id UUID;
BEGIN
  -- Check if user has access to the organization
  IF NOT public.is_org_member(org_id_param) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- Call the edge function to generate coverage
  SELECT net.http_post(
    url := 'https://dygcfpcndgivnuimdgqv.supabase.co/functions/v1/generate-coverage',
    headers := '{"Content-Type": "application/json", "Authorization": "Bearer ' || current_setting('request.jwt.claims', true)::json->>'sub' || '"}'::jsonb,
    body := json_build_object(
      'sceneId', scene_id_param,
      'fidelityLevel', fidelity_level_param,
      'orgId', org_id_param
    )::jsonb
  ) INTO coverage_data;

  -- Extract coverage data from the response
  coverage_data := coverage_data->'coverage';

  -- Insert the coverage report
  INSERT INTO public.coverage_reports (
    org_id,
    user_id,
    scene_id,
    fidelity_level,
    coverage_report,
    synopsis,
    strengths,
    weaknesses,
    verdict
  ) VALUES (
    org_id_param,
    auth.uid(),
    scene_id_param,
    fidelity_level_param,
    'AI-generated coverage report at ' || fidelity_level_param || ' fidelity level.',
    COALESCE(coverage_data->>'synopsis', 'AI analysis completed successfully.'),
    COALESCE(coverage_data->>'strengths', 'Positive elements identified.'),
    COALESCE(coverage_data->>'weaknesses', 'Areas for improvement noted.'),
    COALESCE(coverage_data->>'verdict', 'Professional analysis complete.')
  )
  RETURNING public.coverage_reports.id INTO new_report_id;

  -- Return the results
  RETURN QUERY
  SELECT 
    new_report_id,
    'AI-generated coverage report at ' || fidelity_level_param || ' fidelity level.',
    COALESCE(coverage_data->>'synopsis', 'AI analysis completed successfully.'),
    COALESCE(coverage_data->>'strengths', 'Positive elements identified.'),
    COALESCE(coverage_data->>'weaknesses', 'Areas for improvement noted.'),
    COALESCE(coverage_data->>'verdict', 'Professional analysis complete.');
END;
$$;
