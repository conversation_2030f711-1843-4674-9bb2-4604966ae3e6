
-- First, let's check what policies currently exist and drop all existing ones to start fresh
DO $$
DECLARE
    r RECORD;
BEGIN
    -- Drop all existing policies on all our tables
    FOR r IN (
        SELECT schemaname, tablename, policyname 
        FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename IN (
            'profiles', 'organizations', 'organization_members', 'characters', 
            'locations', 'scenes', 'posts', 'comments', 'revisions', 
            'scene_characters', 'scene_dependencies', 'invitations', 
            'organization_tools', 'tools'
        )
    ) LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || r.policyname || '" ON public.' || r.tablename;
    END LOOP;
END $$;

-- Enable RLS on all tables (this is safe to run even if already enabled)
ALTER TABLE public.characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revisions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scene_characters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scene_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scenes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tools ENABLE ROW LEVEL SECURITY;

-- Create security definer functions to avoid RLS recursion
CREATE OR REPLACE FUNCTION public.get_current_user_orgs()
RETURNS TABLE(org_id uuid)
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT om.org_id 
  FROM public.organization_members om 
  WHERE om.user_id = auth.uid();
$$;

CREATE OR REPLACE FUNCTION public.is_org_member(target_org_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.organization_members om 
    WHERE om.user_id = auth.uid() 
    AND om.org_id = target_org_id
  );
$$;

CREATE OR REPLACE FUNCTION public.is_org_admin(target_org_id uuid)
RETURNS boolean
LANGUAGE sql
SECURITY DEFINER
STABLE
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.organization_members om 
    WHERE om.user_id = auth.uid() 
    AND om.org_id = target_org_id 
    AND om.role = 'admin'
  );
$$;

-- Profiles table policies
CREATE POLICY "Users can view their own profile"
  ON public.profiles FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.profiles FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile"
  ON public.profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Organizations table policies
CREATE POLICY "Users can view their organizations"
  ON public.organizations FOR SELECT
  USING (id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Organization creators can update their organizations"
  ON public.organizations FOR UPDATE
  USING (created_by = auth.uid());

CREATE POLICY "Users can create organizations"
  ON public.organizations FOR INSERT
  WITH CHECK (created_by = auth.uid());

-- Organization members table policies
CREATE POLICY "Users can view their own memberships"
  ON public.organization_members FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Organization members can view org memberships"
  ON public.organization_members FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Organization admins can manage memberships"
  ON public.organization_members FOR ALL
  USING (public.is_org_admin(org_id));

CREATE POLICY "Users can join organizations via invitations"
  ON public.organization_members FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Characters table policies
CREATE POLICY "Users can view org characters"
  ON public.characters FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create characters in their orgs"
  ON public.characters FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Character creators can update their characters"
  ON public.characters FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Character creators can delete their characters"
  ON public.characters FOR DELETE
  USING (user_id = auth.uid());

-- Locations table policies
CREATE POLICY "Users can view org locations"
  ON public.locations FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create locations in their orgs"
  ON public.locations FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Location creators can update their locations"
  ON public.locations FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Location creators can delete their locations"
  ON public.locations FOR DELETE
  USING (user_id = auth.uid());

-- Scenes table policies
CREATE POLICY "Users can view org scenes"
  ON public.scenes FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create scenes in their orgs"
  ON public.scenes FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Scene creators can update their scenes"
  ON public.scenes FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Scene creators can delete their scenes"
  ON public.scenes FOR DELETE
  USING (user_id = auth.uid());

-- Posts table policies
CREATE POLICY "Users can view org posts"
  ON public.posts FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create posts in their orgs"
  ON public.posts FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Post creators can update their posts"
  ON public.posts FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Post creators can delete their posts"
  ON public.posts FOR DELETE
  USING (user_id = auth.uid());

-- Comments table policies
CREATE POLICY "Users can view scene comments in their orgs"
  ON public.comments FOR SELECT
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can create comments"
  ON public.comments FOR INSERT
  WITH CHECK (user_id = auth.uid());

CREATE POLICY "Comment creators can update their comments"
  ON public.comments FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Comment creators can delete their comments"
  ON public.comments FOR DELETE
  USING (user_id = auth.uid());

-- Revisions table policies
CREATE POLICY "Users can view scene revisions in their orgs"
  ON public.revisions FOR SELECT
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can create revisions"
  ON public.revisions FOR INSERT
  WITH CHECK (user_id = auth.uid());

-- Scene characters table policies
CREATE POLICY "Users can view scene characters in their orgs"
  ON public.scene_characters FOR SELECT
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage scene characters in their orgs"
  ON public.scene_characters FOR ALL
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Scene dependencies table policies
CREATE POLICY "Users can view scene dependencies in their orgs"
  ON public.scene_dependencies FOR SELECT
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage scene dependencies in their orgs"
  ON public.scene_dependencies FOR ALL
  USING (
    scene_id IN (
      SELECT s.id FROM public.scenes s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Invitations table policies
CREATE POLICY "Organization admins can manage invitations"
  ON public.invitations FOR ALL
  USING (public.is_org_admin(org_id));

CREATE POLICY "Users can view invitations sent to them"
  ON public.invitations FOR SELECT
  USING (email = (SELECT email FROM auth.users WHERE id = auth.uid()));

-- Organization tools table policies
CREATE POLICY "Organization members can view org tools"
  ON public.organization_tools FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Organization admins can manage org tools"
  ON public.organization_tools FOR ALL
  USING (public.is_org_admin(org_id));

-- Tools table policies (public read access)
CREATE POLICY "Anyone can view available tools"
  ON public.tools FOR SELECT
  USING (true);

CREATE POLICY "Authenticated users can create tools"
  ON public.tools FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');
