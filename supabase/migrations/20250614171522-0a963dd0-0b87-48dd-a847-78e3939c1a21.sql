
-- Create enum types for production workflows
CREATE TYPE approval_status AS ENUM ('pending', 'approved', 'rejected', 'revision_requested');
CREATE TYPE equipment_status AS ENUM ('available', 'checked_out', 'maintenance', 'retired');
CREATE TYPE crew_availability_status AS ENUM ('available', 'busy', 'unavailable', 'tentative');

-- Call sheets table
CREATE TABLE public.production_call_sheets (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  schedule_id UUID NOT NULL,
  title TEXT NOT NULL,
  call_date DATE NOT NULL,
  weather_info JSONB,
  general_notes TEXT,
  emergency_contacts JSONB,
  status TEXT NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Budget approval workflows
CREATE TABLE public.budget_approvals (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  budget_id UUID NOT NULL,
  org_id UUID NOT NULL,
  approver_id UUID NOT NULL,
  approval_level INTEGER NOT NULL DEFAULT 1,
  status approval_status NOT NULL DEFAULT 'pending',
  comments TEXT,
  approved_amount NUMERIC,
  approved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Equipment management
CREATE TABLE public.production_equipment (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  name TEXT NOT NULL,
  category TEXT NOT NULL,
  serial_number TEXT,
  purchase_date DATE,
  condition TEXT DEFAULT 'good',
  status equipment_status NOT NULL DEFAULT 'available',
  daily_rate NUMERIC,
  specifications JSONB,
  maintenance_notes TEXT,
  location TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Equipment check-in/check-out tracking
CREATE TABLE public.equipment_checkouts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  equipment_id UUID NOT NULL,
  org_id UUID NOT NULL,
  checked_out_by UUID NOT NULL,
  checked_out_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  expected_return_date DATE,
  checked_in_at TIMESTAMP WITH TIME ZONE,
  checked_in_by UUID,
  condition_out TEXT DEFAULT 'good',
  condition_in TEXT,
  notes TEXT,
  schedule_item_id UUID,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Location scouting
CREATE TABLE public.location_scouts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  location_name TEXT NOT NULL,
  address TEXT,
  coordinates POINT,
  description TEXT,
  accessibility_notes TEXT,
  parking_info TEXT,
  permits_required BOOLEAN DEFAULT false,
  cost_per_day NUMERIC,
  contact_person TEXT,
  contact_phone TEXT,
  contact_email TEXT,
  availability_notes TEXT,
  photos JSONB,
  status TEXT NOT NULL DEFAULT 'scouting',
  scouted_date DATE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Crew management
CREATE TABLE public.crew_members (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  union_status TEXT,
  daily_rate NUMERIC,
  overtime_rate NUMERIC,
  availability_status crew_availability_status NOT NULL DEFAULT 'available',
  skills JSONB,
  emergency_contact JSONB,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Crew scheduling
CREATE TABLE public.crew_schedules (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  crew_member_id UUID NOT NULL,
  schedule_item_id UUID NOT NULL,
  org_id UUID NOT NULL,
  role TEXT NOT NULL,
  call_time TIME,
  wrap_time TIME,
  rate_for_day NUMERIC,
  overtime_hours NUMERIC DEFAULT 0,
  status TEXT NOT NULL DEFAULT 'scheduled',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Call sheet items (scenes/activities for the day)
CREATE TABLE public.call_sheet_items (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  call_sheet_id UUID NOT NULL,
  schedule_item_id UUID,
  item_type TEXT NOT NULL DEFAULT 'scene',
  title TEXT NOT NULL,
  location TEXT,
  call_time TIME,
  estimated_duration INTEGER,
  description TEXT,
  special_requirements TEXT,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE public.production_call_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.equipment_checkouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_scouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.call_sheet_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for org-based access
CREATE POLICY "Organization members can manage call sheets"
  ON public.production_call_sheets
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage budget approvals"
  ON public.budget_approvals
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage equipment"
  ON public.production_equipment
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage equipment checkouts"
  ON public.equipment_checkouts
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage location scouts"
  ON public.location_scouts
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage crew members"
  ON public.crew_members
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage crew schedules"
  ON public.crew_schedules
  FOR ALL
  TO authenticated
  USING (public.is_org_member(org_id))
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Organization members can manage call sheet items"
  ON public.call_sheet_items
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.production_call_sheets cs
      WHERE cs.id = call_sheet_id AND public.is_org_member(cs.org_id)
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.production_call_sheets cs
      WHERE cs.id = call_sheet_id AND public.is_org_member(cs.org_id)
    )
  );

-- Add indexes for performance
CREATE INDEX idx_call_sheets_org_id ON public.production_call_sheets(org_id);
CREATE INDEX idx_call_sheets_schedule_id ON public.production_call_sheets(schedule_id);
CREATE INDEX idx_budget_approvals_budget_id ON public.budget_approvals(budget_id);
CREATE INDEX idx_equipment_org_status ON public.production_equipment(org_id, status);
CREATE INDEX idx_equipment_checkouts_equipment_id ON public.equipment_checkouts(equipment_id);
CREATE INDEX idx_location_scouts_org_status ON public.location_scouts(org_id, status);
CREATE INDEX idx_crew_members_org_availability ON public.crew_members(org_id, availability_status);
CREATE INDEX idx_crew_schedules_member_id ON public.crew_schedules(crew_member_id);
CREATE INDEX idx_crew_schedules_schedule_item_id ON public.crew_schedules(schedule_item_id);

-- Add triggers for updated_at timestamps
CREATE TRIGGER update_call_sheets_updated_at 
  BEFORE UPDATE ON public.production_call_sheets 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_budget_approvals_updated_at 
  BEFORE UPDATE ON public.budget_approvals 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_equipment_updated_at 
  BEFORE UPDATE ON public.production_equipment 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_equipment_checkouts_updated_at 
  BEFORE UPDATE ON public.equipment_checkouts 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_location_scouts_updated_at 
  BEFORE UPDATE ON public.location_scouts 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_crew_members_updated_at 
  BEFORE UPDATE ON public.crew_members 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_crew_schedules_updated_at 
  BEFORE UPDATE ON public.crew_schedules 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_call_sheet_items_updated_at 
  BEFORE UPDATE ON public.call_sheet_items 
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
