
-- Add comprehensive RLS policies for all production tables

-- Enable RLS on all production tables that don't have it yet
ALTER TABLE public.budget_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.call_sheet_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.crew_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.equipment_checkouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.location_scouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_call_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_schedule_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.resource_bookings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budget_line_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_audit_logs ENABLE ROW LEVEL SECURITY;

-- Production Schedules policies
CREATE POLICY "Users can view org production schedules"
  ON public.production_schedules FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create production schedules in their orgs"
  ON public.production_schedules FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Schedule creators can update their schedules"
  ON public.production_schedules FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Schedule creators can delete their schedules"
  ON public.production_schedules FOR DELETE
  USING (user_id = auth.uid());

-- Production Schedule Items policies
CREATE POLICY "Users can view org schedule items"
  ON public.production_schedule_items FOR SELECT
  USING (
    schedule_id IN (
      SELECT s.id FROM public.production_schedules s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage schedule items in their orgs"
  ON public.production_schedule_items FOR ALL
  USING (
    schedule_id IN (
      SELECT s.id FROM public.production_schedules s 
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Production Budgets policies
CREATE POLICY "Users can view org production budgets"
  ON public.production_budgets FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create production budgets in their orgs"
  ON public.production_budgets FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Budget creators can update their budgets"
  ON public.production_budgets FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Budget creators can delete their budgets"
  ON public.production_budgets FOR DELETE
  USING (user_id = auth.uid());

-- Budget Line Items policies
CREATE POLICY "Users can view budget line items in their orgs"
  ON public.budget_line_items FOR SELECT
  USING (
    budget_id IN (
      SELECT b.id FROM public.production_budgets b 
      WHERE b.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage budget line items in their orgs"
  ON public.budget_line_items FOR ALL
  USING (
    budget_id IN (
      SELECT b.id FROM public.production_budgets b 
      WHERE b.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Production Resources policies
CREATE POLICY "Users can view org production resources"
  ON public.production_resources FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create production resources in their orgs"
  ON public.production_resources FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Resource creators can update their resources"
  ON public.production_resources FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Resource creators can delete their resources"
  ON public.production_resources FOR DELETE
  USING (user_id = auth.uid());

-- Production Reports policies
CREATE POLICY "Users can view org production reports"
  ON public.production_reports FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create production reports in their orgs"
  ON public.production_reports FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Report creators can update their reports"
  ON public.production_reports FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Report creators can delete their reports"
  ON public.production_reports FOR DELETE
  USING (user_id = auth.uid());

-- Crew Members policies
CREATE POLICY "Users can view org crew members"
  ON public.crew_members FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create crew members in their orgs"
  ON public.crew_members FOR INSERT
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update crew members in their orgs"
  ON public.crew_members FOR UPDATE
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete crew members in their orgs"
  ON public.crew_members FOR DELETE
  USING (public.is_org_member(org_id));

-- Location Scouts policies
CREATE POLICY "Users can view org location scouts"
  ON public.location_scouts FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create location scouts in their orgs"
  ON public.location_scouts FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Location scout creators can update their scouts"
  ON public.location_scouts FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Location scout creators can delete their scouts"
  ON public.location_scouts FOR DELETE
  USING (user_id = auth.uid());

-- Budget Approvals policies
CREATE POLICY "Users can view org budget approvals"
  ON public.budget_approvals FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create budget approvals in their orgs"
  ON public.budget_approvals FOR INSERT
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update budget approvals in their orgs"
  ON public.budget_approvals FOR UPDATE
  USING (public.is_org_member(org_id));

-- Call Sheets policies
CREATE POLICY "Users can view org call sheets"
  ON public.production_call_sheets FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create call sheets in their orgs"
  ON public.production_call_sheets FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Call sheet creators can update their sheets"
  ON public.production_call_sheets FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Call sheet creators can delete their sheets"
  ON public.production_call_sheets FOR DELETE
  USING (user_id = auth.uid());

-- Call Sheet Items policies
CREATE POLICY "Users can view call sheet items in their orgs"
  ON public.call_sheet_items FOR SELECT
  USING (
    call_sheet_id IN (
      SELECT cs.id FROM public.production_call_sheets cs 
      WHERE cs.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage call sheet items in their orgs"
  ON public.call_sheet_items FOR ALL
  USING (
    call_sheet_id IN (
      SELECT cs.id FROM public.production_call_sheets cs 
      WHERE cs.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Equipment policies
CREATE POLICY "Users can view org equipment"
  ON public.production_equipment FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create equipment in their orgs"
  ON public.production_equipment FOR INSERT
  WITH CHECK (public.is_org_member(org_id));

CREATE POLICY "Users can update equipment in their orgs"
  ON public.production_equipment FOR UPDATE
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can delete equipment in their orgs"
  ON public.production_equipment FOR DELETE
  USING (public.is_org_member(org_id));

-- Equipment Checkouts policies
CREATE POLICY "Users can view org equipment checkouts"
  ON public.equipment_checkouts FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create equipment checkouts in their orgs"
  ON public.equipment_checkouts FOR INSERT
  WITH CHECK (
    checked_out_by = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Users can update equipment checkouts in their orgs"
  ON public.equipment_checkouts FOR UPDATE
  USING (public.is_org_member(org_id));

-- Crew Schedules policies
CREATE POLICY "Users can view org crew schedules"
  ON public.crew_schedules FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can manage crew schedules in their orgs"
  ON public.crew_schedules FOR ALL
  USING (public.is_org_member(org_id));

-- Crew Assignments policies
CREATE POLICY "Users can view org crew assignments"
  ON public.crew_assignments FOR SELECT
  USING (
    schedule_item_id IN (
      SELECT si.id FROM public.production_schedule_items si 
      JOIN public.production_schedules s ON si.schedule_id = s.id
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage crew assignments in their orgs"
  ON public.crew_assignments FOR ALL
  USING (
    schedule_item_id IN (
      SELECT si.id FROM public.production_schedule_items si 
      JOIN public.production_schedules s ON si.schedule_id = s.id
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Resource Bookings policies
CREATE POLICY "Users can view org resource bookings"
  ON public.resource_bookings FOR SELECT
  USING (
    schedule_item_id IN (
      SELECT si.id FROM public.production_schedule_items si 
      JOIN public.production_schedules s ON si.schedule_id = s.id
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

CREATE POLICY "Users can manage resource bookings in their orgs"
  ON public.resource_bookings FOR ALL
  USING (
    schedule_item_id IN (
      SELECT si.id FROM public.production_schedule_items si 
      JOIN public.production_schedules s ON si.schedule_id = s.id
      WHERE s.org_id IN (SELECT org_id FROM public.get_current_user_orgs())
    )
  );

-- Production User Roles policies
CREATE POLICY "Users can view org production roles"
  ON public.production_user_roles FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Organization admins can manage production roles"
  ON public.production_user_roles FOR ALL
  USING (public.is_org_admin(org_id));

-- Production Templates policies
CREATE POLICY "Users can view org production templates"
  ON public.production_templates FOR SELECT
  USING (public.is_org_member(org_id) OR is_public = true);

CREATE POLICY "Users can create production templates in their orgs"
  ON public.production_templates FOR INSERT
  WITH CHECK (
    created_by = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Template creators can update their templates"
  ON public.production_templates FOR UPDATE
  USING (created_by = auth.uid());

CREATE POLICY "Template creators can delete their templates"
  ON public.production_templates FOR DELETE
  USING (created_by = auth.uid());

-- Production File Attachments policies
CREATE POLICY "Users can view org file attachments"
  ON public.production_file_attachments FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create file attachments in their orgs"
  ON public.production_file_attachments FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "File attachment creators can update their files"
  ON public.production_file_attachments FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "File attachment creators can delete their files"
  ON public.production_file_attachments FOR DELETE
  USING (user_id = auth.uid());

-- Production Integrations policies
CREATE POLICY "Users can view org production integrations"
  ON public.production_integrations FOR SELECT
  USING (public.is_org_member(org_id));

CREATE POLICY "Organization admins can manage production integrations"
  ON public.production_integrations FOR ALL
  USING (public.is_org_admin(org_id));

-- Production Audit Logs policies (read-only for members, admins can see all)
CREATE POLICY "Users can view org audit logs"
  ON public.production_audit_logs FOR SELECT
  USING (public.is_org_member(org_id));
