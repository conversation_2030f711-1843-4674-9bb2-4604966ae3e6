
-- 1. Create table to track storyboard usage per user/org/day
CREATE TABLE public.storyboard_usage_tracking (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id uuid NOT NULL,
  org_id uuid NOT NULL,
  usage_date date NOT NULL DEFAULT CURRENT_DATE,
  generation_count integer NOT NULL DEFAULT 0,
  last_generation_at timestamptz NOT NULL DEFAULT now(),
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE (user_id, org_id, usage_date)
);

-- 2. Enable RLS and add policies so users only see/update their own records
ALTER TABLE public.storyboard_usage_tracking ENABLE ROW LEVEL SECURITY;

-- Users can view their usage records if they are the owner
CREATE POLICY "Users can view their own storyboard usage" 
  ON public.storyboard_usage_tracking
  FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert only for themselves
CREATE POLICY "Users can insert their own storyboard usage" 
  ON public.storyboard_usage_tracking
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Users can update only their own records
CREATE POLICY "Users can update their storyboard usage"
  ON public.storyboard_usage_tracking
  FOR UPDATE
  USING (auth.uid() = user_id);

-- 3. Helper functions (plpgsql) for API/Edge use

-- Returns the daily storyboard gen limit for a subscription tier
CREATE OR REPLACE FUNCTION public.storyboard_tier_limit(tier_name text)
RETURNS integer AS $$
BEGIN
  RETURN CASE tier_name
    WHEN 'pro-team' THEN 3
    WHEN 'studio' THEN 5
    WHEN 'enterprise' THEN 10
    ELSE 0 -- treat others (starter, pro-solo, unknown) as not allowed
  END;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Atomically get or create today's usage row and (optionally) increment
CREATE OR REPLACE FUNCTION public.get_or_increment_storyboard_usage(
  target_org_id uuid,
  increment boolean DEFAULT false
)
RETURNS TABLE(
  usage_record_id uuid,
  generation_count integer,
  daily_limit integer,
  remaining_generations integer,
  tier_name text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier text;
  daily_limit integer;
  current_count integer := 0;
  record_id uuid;
BEGIN
  -- Only allow access for org members
  IF NOT public.is_org_member(target_org_id) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- Get user tier for org
  SELECT plan INTO user_tier FROM public.organizations WHERE id = target_org_id;

  daily_limit := public.storyboard_tier_limit(user_tier);

  IF user_tier IS NULL OR daily_limit <= 0 THEN
    RAISE EXCEPTION 'Your subscription does not include storyboard AI generation for this organization.';
  END IF;

  -- Upsert: get or create today's row; increment if needed
  LOOP
    -- Try to insert or update atomically
    BEGIN
      IF increment THEN
        INSERT INTO public.storyboard_usage_tracking(user_id, org_id, usage_date, generation_count, last_generation_at)
          VALUES (auth.uid(), target_org_id, CURRENT_DATE, 1, now())
        ON CONFLICT (user_id, org_id, usage_date) 
          DO UPDATE SET 
            generation_count = storyboard_usage_tracking.generation_count + 1,
            last_generation_at = now(),
            updated_at = now()
        RETURNING id, generation_count 
        INTO record_id, current_count;
      ELSE
        INSERT INTO public.storyboard_usage_tracking(user_id, org_id, usage_date)
          VALUES (auth.uid(), target_org_id, CURRENT_DATE)
        ON CONFLICT (user_id, org_id, usage_date) DO UPDATE 
          SET updated_at = now()
        RETURNING id, generation_count
        INTO record_id, current_count;
      END IF;
      EXIT;
    EXCEPTION
      WHEN unique_violation THEN
        -- Retry in case of race condition
        NULL;
    END;
  END LOOP;

  -- Check if over limit after increment
  IF increment AND current_count > daily_limit THEN
    -- Roll back one increment
    UPDATE public.storyboard_usage_tracking
      SET generation_count = generation_count - 1, updated_at = now()
      WHERE id = record_id;
    RAISE EXCEPTION 'You have reached the daily storyboard generation limit (%/day) for your subscription.', daily_limit;
  END IF;

  RETURN QUERY
  SELECT 
    record_id,
    current_count,
    daily_limit,
    GREATEST(0, daily_limit - current_count) as remaining_generations,
    user_tier;

END;
$$;

