
-- Create enum for project status
CREATE TYPE project_status AS ENUM ('active', 'draft', 'completed', 'archived');

-- Create projects table to track user projects
CREATE TABLE public.projects (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status project_status NOT NULL DEFAULT 'draft',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  screenplay_id UUID REFERENCES public.screenplays(id) ON DELETE SET NULL,
  collaborator_count INTEGER NOT NULL DEFAULT 0,
  is_archived BOOLEAN NOT NULL DEFAULT false
);

-- Create team_memberships table for tracking team members
CREATE TABLE public.team_memberships (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  role TEXT NOT NULL DEFAULT 'member',
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  invited_by UUID,
  status TEXT NOT NULL DEFAULT 'active'
);

-- Create user_analytics table for tracking tool usage
CREATE TABLE public.user_analytics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  org_id UUID NOT NULL,
  event_type TEXT NOT NULL,
  event_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  tool_name TEXT,
  session_id TEXT
);

-- Create user_favorites table for favorited writers and screenplays
CREATE TABLE public.user_favorites (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  favorite_type TEXT NOT NULL, -- 'writer' or 'screenplay'
  favorite_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(user_id, favorite_type, favorite_id)
);

-- Create tier_limits table to define limits per subscription tier
CREATE TABLE public.tier_limits (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tier_name TEXT NOT NULL UNIQUE,
  max_active_projects INTEGER NOT NULL DEFAULT 0,
  max_teams INTEGER NOT NULL DEFAULT 0,
  max_members_per_team INTEGER NOT NULL DEFAULT 0,
  max_collaborators_per_project INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Insert default tier limits
INSERT INTO public.tier_limits (tier_name, max_active_projects, max_teams, max_members_per_team, max_collaborators_per_project) VALUES
('starter', 3, 0, 0, 0),
('pro-solo', 10, 0, 0, 0),
('pro-team', 10, 5, 4, 0),
('studio', 15, 0, 0, 7),
('enterprise', 50, 10, 10, 15);

-- Enable RLS on all new tables
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.team_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tier_limits ENABLE ROW LEVEL SECURITY;

-- RLS policies for projects
CREATE POLICY "Users can view projects in their organization" 
  ON public.projects 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create projects in their organization" 
  ON public.projects 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id) AND user_id = auth.uid());

CREATE POLICY "Users can update their own projects" 
  ON public.projects 
  FOR UPDATE 
  USING (user_id = auth.uid() AND public.is_org_member(org_id));

CREATE POLICY "Users can delete their own projects" 
  ON public.projects 
  FOR DELETE 
  USING (user_id = auth.uid() AND public.is_org_member(org_id));

-- RLS policies for team_memberships
CREATE POLICY "Users can view team memberships for projects they have access to" 
  ON public.team_memberships 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p 
      WHERE p.id = project_id AND public.is_org_member(p.org_id)
    )
  );

CREATE POLICY "Project owners can manage team memberships" 
  ON public.team_memberships 
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.projects p 
      WHERE p.id = project_id AND p.user_id = auth.uid()
    )
  );

-- RLS policies for user_analytics
CREATE POLICY "Users can view their own analytics" 
  ON public.user_analytics 
  FOR SELECT 
  USING (user_id = auth.uid());

CREATE POLICY "Users can create their own analytics" 
  ON public.user_analytics 
  FOR INSERT 
  WITH CHECK (user_id = auth.uid());

-- RLS policies for user_favorites
CREATE POLICY "Users can manage their own favorites" 
  ON public.user_favorites 
  FOR ALL
  USING (user_id = auth.uid());

-- RLS policies for tier_limits (read-only for all authenticated users)
CREATE POLICY "All authenticated users can view tier limits" 
  ON public.tier_limits 
  FOR SELECT 
  TO authenticated
  USING (true);

-- Create function to get user's current tier limits
CREATE OR REPLACE FUNCTION public.get_user_tier_limits(target_org_id uuid)
RETURNS TABLE(
  tier_name text,
  max_active_projects integer,
  max_teams integer,
  max_members_per_team integer,
  max_collaborators_per_project integer
)
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT 
    tl.tier_name,
    tl.max_active_projects,
    tl.max_teams,
    tl.max_members_per_team,
    tl.max_collaborators_per_project
  FROM public.tier_limits tl
  JOIN public.organizations o ON o.plan = tl.tier_name
  WHERE o.id = target_org_id
  AND public.is_org_member(target_org_id);
$$;

-- Create function to check if user can create new project
CREATE OR REPLACE FUNCTION public.can_create_project(target_org_id uuid)
RETURNS boolean
LANGUAGE plpgsql
STABLE SECURITY DEFINER
AS $$
DECLARE
  current_active_count integer;
  tier_limit integer;
BEGIN
  -- Check organization membership
  IF NOT public.is_org_member(target_org_id) THEN
    RETURN false;
  END IF;

  -- Get current active project count
  SELECT COUNT(*) INTO current_active_count
  FROM public.projects 
  WHERE org_id = target_org_id 
  AND user_id = auth.uid() 
  AND status = 'active'
  AND NOT is_archived;

  -- Get tier limit
  SELECT tl.max_active_projects INTO tier_limit
  FROM public.tier_limits tl
  JOIN public.organizations o ON o.plan = tl.tier_name
  WHERE o.id = target_org_id;

  RETURN current_active_count < tier_limit;
END;
$$;

-- Create function to get user's current usage stats
CREATE OR REPLACE FUNCTION public.get_user_usage_stats(target_org_id uuid)
RETURNS TABLE(
  active_projects integer,
  total_projects integer,
  team_count integer,
  total_collaborators integer,
  tier_name text,
  max_active_projects integer,
  max_teams integer,
  max_collaborators_per_project integer
)
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT 
    (SELECT COUNT(*)::integer FROM public.projects 
     WHERE org_id = target_org_id AND user_id = auth.uid() 
     AND status = 'active' AND NOT is_archived) as active_projects,
    (SELECT COUNT(*)::integer FROM public.projects 
     WHERE org_id = target_org_id AND user_id = auth.uid()) as total_projects,
    (SELECT COUNT(DISTINCT p.id)::integer FROM public.projects p
     JOIN public.team_memberships tm ON tm.project_id = p.id
     WHERE p.org_id = target_org_id AND p.user_id = auth.uid()) as team_count,
    (SELECT COUNT(*)::integer FROM public.team_memberships tm
     JOIN public.projects p ON p.id = tm.project_id
     WHERE p.org_id = target_org_id AND p.user_id = auth.uid()) as total_collaborators,
    tl.tier_name,
    tl.max_active_projects,
    tl.max_teams,
    tl.max_collaborators_per_project
  FROM public.tier_limits tl
  JOIN public.organizations o ON o.plan = tl.tier_name
  WHERE o.id = target_org_id
  AND public.is_org_member(target_org_id);
$$;

-- Add indexes for better performance
CREATE INDEX idx_projects_org_user_status ON public.projects(org_id, user_id, status);
CREATE INDEX idx_team_memberships_project_user ON public.team_memberships(project_id, user_id);
CREATE INDEX idx_user_analytics_user_date ON public.user_analytics(user_id, created_at);
CREATE INDEX idx_user_favorites_user_type ON public.user_favorites(user_id, favorite_type);

-- Add updated_at trigger for projects
CREATE TRIGGER update_projects_updated_at
  BEFORE UPDATE ON public.projects
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- Add updated_at trigger for tier_limits
CREATE TRIGGER update_tier_limits_updated_at
  BEFORE UPDATE ON public.tier_limits
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
