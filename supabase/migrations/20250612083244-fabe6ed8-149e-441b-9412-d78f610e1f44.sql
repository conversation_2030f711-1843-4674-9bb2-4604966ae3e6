
-- Create characters table for SmartType autocomplete
CREATE TABLE public.characters (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  org_id UUID REFERENCES public.organizations,
  name TEXT NOT NULL,
  description TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create locations table for SmartType autocomplete
CREATE TABLE public.locations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  org_id UUID REFERENCES public.organizations,
  name TEXT NOT NULL,
  description TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create scenes table for script organization
CREATE TABLE public.scenes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  org_id UUID REFERENCES public.organizations,
  title TEXT NOT NULL,
  description TEXT,
  content TEXT,
  act INTEGER CHECK (act IN (1, 2, 3)),
  order_index INTEGER NOT NULL DEFAULT 0,
  location_id UUID REFERENCES public.locations,
  duration_minutes INTEGER DEFAULT 3,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create scene_characters junction table
CREATE TABLE public.scene_characters (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  scene_id UUID REFERENCES public.scenes ON DELETE CASCADE,
  character_id UUID REFERENCES public.characters ON DELETE CASCADE,
  is_main_character BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(scene_id, character_id)
);

-- Create scene_dependencies table for dependency graph
CREATE TABLE public.scene_dependencies (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  scene_id UUID REFERENCES public.scenes ON DELETE CASCADE,
  depends_on_scene_id UUID REFERENCES public.scenes ON DELETE CASCADE,
  dependency_type TEXT CHECK (dependency_type IN ('plot', 'character', 'location', 'timeline')) DEFAULT 'plot',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(scene_id, depends_on_scene_id)
);

-- Create comments table
CREATE TABLE public.comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  scene_id UUID REFERENCES public.scenes ON DELETE CASCADE,
  content TEXT NOT NULL,
  line_number INTEGER,
  resolved BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create revisions table for basic revision tracking
CREATE TABLE public.revisions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users NOT NULL,
  scene_id UUID REFERENCES public.scenes ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  content TEXT NOT NULL,
  change_summary TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add RLS policies for characters
ALTER TABLE public.characters ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own characters" 
  ON public.characters 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own characters" 
  ON public.characters 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own characters" 
  ON public.characters 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own characters" 
  ON public.characters 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add RLS policies for locations
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own locations" 
  ON public.locations 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own locations" 
  ON public.locations 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own locations" 
  ON public.locations 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own locations" 
  ON public.locations 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add RLS policies for scenes
ALTER TABLE public.scenes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own scenes" 
  ON public.scenes 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own scenes" 
  ON public.scenes 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scenes" 
  ON public.scenes 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scenes" 
  ON public.scenes 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add RLS policies for scene_characters
ALTER TABLE public.scene_characters ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view scene_characters through scenes" 
  ON public.scene_characters 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_characters.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can create scene_characters for their scenes" 
  ON public.scene_characters 
  FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_characters.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can update scene_characters for their scenes" 
  ON public.scene_characters 
  FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_characters.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete scene_characters for their scenes" 
  ON public.scene_characters 
  FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_characters.scene_id 
    AND scenes.user_id = auth.uid()
  ));

-- Add RLS policies for scene_dependencies
ALTER TABLE public.scene_dependencies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view scene_dependencies through scenes" 
  ON public.scene_dependencies 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_dependencies.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can create scene_dependencies for their scenes" 
  ON public.scene_dependencies 
  FOR INSERT 
  WITH CHECK (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_dependencies.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can update scene_dependencies for their scenes" 
  ON public.scene_dependencies 
  FOR UPDATE 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_dependencies.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can delete scene_dependencies for their scenes" 
  ON public.scene_dependencies 
  FOR DELETE 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = scene_dependencies.scene_id 
    AND scenes.user_id = auth.uid()
  ));

-- Add RLS policies for comments
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view comments on their scenes" 
  ON public.comments 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = comments.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can create comments" 
  ON public.comments 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" 
  ON public.comments 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" 
  ON public.comments 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add RLS policies for revisions
ALTER TABLE public.revisions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view revisions for their scenes" 
  ON public.revisions 
  FOR SELECT 
  USING (EXISTS (
    SELECT 1 FROM public.scenes 
    WHERE scenes.id = revisions.scene_id 
    AND scenes.user_id = auth.uid()
  ));

CREATE POLICY "Users can create revisions" 
  ON public.revisions 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX idx_characters_user_id ON public.characters(user_id);
CREATE INDEX idx_locations_user_id ON public.locations(user_id);
CREATE INDEX idx_scenes_user_id ON public.scenes(user_id);
CREATE INDEX idx_scenes_act_order ON public.scenes(act, order_index);
CREATE INDEX idx_comments_scene_id ON public.comments(scene_id);
CREATE INDEX idx_revisions_scene_id ON public.revisions(scene_id, version_number);
CREATE INDEX idx_scene_dependencies_scene_id ON public.scene_dependencies(scene_id);
