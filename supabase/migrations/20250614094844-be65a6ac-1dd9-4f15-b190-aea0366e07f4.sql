
-- Create transactions table to track all marketplace transactions
CREATE TABLE public.transactions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  buyer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  seller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  seller_earnings DECIMAL(10,2) NOT NULL,
  stripe_payment_intent_id TEXT,
  stripe_transfer_id TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  transaction_type TEXT NOT NULL DEFAULT 'purchase' CHECK (transaction_type IN ('purchase', 'offer_payment')),
  offer_id UUID REFERENCES public.screenplay_offers(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create seller_accounts table to track Stripe Connect accounts
CREATE TABLE public.seller_accounts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  stripe_account_id TEXT NOT NULL UNIQUE,
  account_status TEXT NOT NULL DEFAULT 'pending' CHECK (account_status IN ('pending', 'active', 'restricted', 'rejected')),
  onboarding_completed BOOLEAN NOT NULL DEFAULT false,
  payouts_enabled BOOLEAN NOT NULL DEFAULT false,
  charges_enabled BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create earnings_summary table for quick dashboard queries
CREATE TABLE public.earnings_summary (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  total_earnings DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  pending_earnings DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  total_sales INTEGER NOT NULL DEFAULT 0,
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.seller_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.earnings_summary ENABLE ROW LEVEL SECURITY;

-- RLS Policies for transactions
CREATE POLICY "Users can view their transactions as buyer or seller" 
  ON public.transactions 
  FOR SELECT 
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Service can insert transactions" 
  ON public.transactions 
  FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Service can update transactions" 
  ON public.transactions 
  FOR UPDATE 
  USING (true);

-- RLS Policies for seller_accounts
CREATE POLICY "Users can view their own seller account" 
  ON public.seller_accounts 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service can manage seller accounts" 
  ON public.seller_accounts 
  FOR ALL 
  USING (true);

-- RLS Policies for earnings_summary
CREATE POLICY "Users can view their own earnings summary" 
  ON public.earnings_summary 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service can manage earnings summary" 
  ON public.earnings_summary 
  FOR ALL 
  USING (true);

-- Create indexes for performance
CREATE INDEX idx_transactions_buyer_id ON public.transactions(buyer_id);
CREATE INDEX idx_transactions_seller_id ON public.transactions(seller_id);
CREATE INDEX idx_transactions_screenplay_id ON public.transactions(screenplay_id);
CREATE INDEX idx_transactions_status ON public.transactions(status);
CREATE INDEX idx_seller_accounts_user_id ON public.seller_accounts(user_id);
CREATE INDEX idx_seller_accounts_stripe_account_id ON public.seller_accounts(stripe_account_id);

-- Function to update earnings summary when transactions change
CREATE OR REPLACE FUNCTION public.update_earnings_summary()
RETURNS TRIGGER AS $$
BEGIN
  -- Update earnings summary for the seller
  INSERT INTO public.earnings_summary (user_id, total_earnings, pending_earnings, total_sales, last_updated)
  VALUES (
    NEW.seller_id,
    CASE WHEN NEW.status = 'completed' THEN NEW.seller_earnings ELSE 0 END,
    CASE WHEN NEW.status = 'pending' THEN NEW.seller_earnings ELSE 0 END,
    CASE WHEN NEW.status = 'completed' THEN 1 ELSE 0 END,
    now()
  )
  ON CONFLICT (user_id) DO UPDATE SET
    total_earnings = (
      SELECT COALESCE(SUM(seller_earnings), 0)
      FROM public.transactions 
      WHERE seller_id = NEW.seller_id AND status = 'completed'
    ),
    pending_earnings = (
      SELECT COALESCE(SUM(seller_earnings), 0)
      FROM public.transactions 
      WHERE seller_id = NEW.seller_id AND status = 'pending'
    ),
    total_sales = (
      SELECT COUNT(*)
      FROM public.transactions 
      WHERE seller_id = NEW.seller_id AND status = 'completed'
    ),
    last_updated = now();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update earnings summary
CREATE TRIGGER update_earnings_summary_trigger
  AFTER INSERT OR UPDATE ON public.transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_earnings_summary();
