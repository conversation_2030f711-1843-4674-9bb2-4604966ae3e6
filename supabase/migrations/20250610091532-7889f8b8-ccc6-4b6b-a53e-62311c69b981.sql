
-- Extend profiles table with role information
ALTER TABLE public.profiles 
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'super_admin'));

-- Create organizations table
CREATE TABLE public.organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  plan TEXT DEFAULT 'starter' CHECK (plan IN ('starter', 'pro_solo', 'pro_team', 'studio', 'enterprise')),
  user_limit INTEGER DEFAULT 1, -- starter/pro_solo: 1, others: 5
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create organization_members table
CREATE TABLE public.organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT CHECK (role IN ('admin', 'member')) NOT NULL,
  joined_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (org_id, user_id)
);

-- Create tools table
CREATE TABLE public.tools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  slug TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create organization_tools table
CREATE TABLE public.organization_tools (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  tool_id UUID REFERENCES public.tools(id) ON DELETE CASCADE,
  enabled BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE (org_id, tool_id)
);

-- Create invitations table
CREATE TABLE public.invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  invited_by UUID REFERENCES auth.users(id),
  status TEXT CHECK (status IN ('pending', 'accepted', 'declined')) DEFAULT 'pending',
  role TEXT CHECK (role IN ('admin', 'member')) DEFAULT 'member',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days')
);

-- Insert default tools
INSERT INTO public.tools (name, description, slug) VALUES
('Script Editor', 'Advanced screenplay writing tool', 'script-editor'),
('Storyboard Creator', 'Visual storyboarding tool', 'storyboard-creator'),
('AI Assistant', 'AI-powered writing assistance', 'ai-assistant'),
('Team Collaboration', 'Real-time collaborative editing', 'team-collaboration'),
('Analytics Dashboard', 'Project analytics and insights', 'analytics-dashboard');

-- Enable RLS on all new tables
ALTER TABLE public.organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.organization_tools ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invitations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for organizations
CREATE POLICY "Users can view organizations they are members of"
  ON public.organizations FOR SELECT
  USING (
    created_by = auth.uid() OR
    id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create organizations"
  ON public.organizations FOR INSERT
  WITH CHECK (created_by = auth.uid());

CREATE POLICY "Organization admins can update their organizations"
  ON public.organizations FOR UPDATE
  USING (
    created_by = auth.uid() OR
    id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for organization_members
CREATE POLICY "Users can view their own memberships"
  ON public.organization_members FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Organization admins can view all memberships"
  ON public.organization_members FOR SELECT
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

CREATE POLICY "Organization admins can manage memberships"
  ON public.organization_members FOR ALL
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for tools (public read access)
CREATE POLICY "Anyone can view tools"
  ON public.tools FOR SELECT
  USING (true);

-- RLS Policies for organization_tools
CREATE POLICY "Organization members can view their tools"
  ON public.organization_tools FOR SELECT
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization admins can manage tools"
  ON public.organization_tools FOR ALL
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- RLS Policies for invitations
CREATE POLICY "Users can view invitations sent to their email"
  ON public.invitations FOR SELECT
  USING (email = (SELECT email FROM auth.users WHERE id = auth.uid()));

CREATE POLICY "Organization admins can manage invitations"
  ON public.invitations FOR ALL
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );

-- Function to check user limits before adding members
CREATE OR REPLACE FUNCTION check_organization_user_limit()
RETURNS TRIGGER AS $$
DECLARE
  org_plan TEXT;
  org_user_limit INTEGER;
  current_member_count INTEGER;
BEGIN
  -- Get organization plan and user limit
  SELECT plan, user_limit INTO org_plan, org_user_limit
  FROM public.organizations
  WHERE id = NEW.org_id;

  -- Count current members
  SELECT COUNT(*) INTO current_member_count
  FROM public.organization_members
  WHERE org_id = NEW.org_id;

  -- Check if adding this member would exceed the limit
  IF current_member_count >= org_user_limit THEN
    RAISE EXCEPTION 'Organization has reached its user limit of %. Upgrade your plan or remove inactive members.', org_user_limit;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce user limits
CREATE TRIGGER enforce_organization_user_limit
  BEFORE INSERT ON public.organization_members
  FOR EACH ROW
  EXECUTE FUNCTION check_organization_user_limit();

-- Function to automatically create organization for new users
CREATE OR REPLACE FUNCTION create_default_organization()
RETURNS TRIGGER AS $$
BEGIN
  -- Create a default personal organization for new users
  INSERT INTO public.organizations (name, plan, user_limit, created_by)
  VALUES (
    COALESCE(NEW.full_name, NEW.username, 'Personal') || '''s Organization',
    'starter',
    1,
    NEW.id
  );
  
  -- Add the user as admin of their organization
  INSERT INTO public.organization_members (org_id, user_id, role)
  VALUES (
    (SELECT id FROM public.organizations WHERE created_by = NEW.id ORDER BY created_at DESC LIMIT 1),
    NEW.id,
    'admin'
  );

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Update the existing trigger to also create organizations
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, username)
  VALUES (
    new.id,
    new.raw_user_meta_data ->> 'full_name',
    new.raw_user_meta_data ->> 'username'
  );
  
  -- Create default organization
  INSERT INTO public.organizations (name, plan, user_limit, created_by)
  VALUES (
    COALESCE(new.raw_user_meta_data ->> 'full_name', new.raw_user_meta_data ->> 'username', 'Personal') || '''s Organization',
    'starter',
    1,
    new.id
  );
  
  -- Add user as admin of their organization
  INSERT INTO public.organization_members (org_id, user_id, role)
  VALUES (
    (SELECT id FROM public.organizations WHERE created_by = new.id ORDER BY created_at DESC LIMIT 1),
    new.id,
    'admin'
  );

  RETURN new;
END;
$$;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Update posts table to link to organizations
ALTER TABLE public.posts 
ADD COLUMN IF NOT EXISTS org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE;

-- Update posts RLS policies
DROP POLICY IF EXISTS "Posts are viewable by everyone" ON public.posts;
DROP POLICY IF EXISTS "Users can create their own posts" ON public.posts;
DROP POLICY IF EXISTS "Users can update their own posts" ON public.posts;
DROP POLICY IF EXISTS "Users can delete their own posts" ON public.posts;

CREATE POLICY "Organization members can view posts"
  ON public.posts FOR SELECT
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization members can create posts"
  ON public.posts FOR INSERT
  WITH CHECK (
    auth.uid() = user_id AND
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own posts"
  ON public.posts FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own posts"
  ON public.posts FOR DELETE
  USING (auth.uid() = user_id);
