
-- Add <PERSON>e Connect account management fields to seller_accounts table
ALTER TABLE public.seller_accounts 
ADD COLUMN IF NOT EXISTS details_submitted BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN IF NOT EXISTS requirements JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS capabilities JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS business_type TEXT,
ADD COLUMN IF NOT EXISTS country TEXT DEFAULT 'US',
ADD COLUMN IF NOT EXISTS default_currency TEXT DEFAULT 'usd';

-- Create a table to track onboarding sessions
CREATE TABLE IF NOT EXISTS public.seller_onboarding_sessions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  seller_account_id UUID NOT NULL REFERENCES public.seller_accounts(id) ON DELETE CASCADE,
  stripe_account_link_id TEXT NOT NULL,
  return_url TEXT NOT NULL,
  refresh_url TEXT NOT NULL,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  completed BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on the new table
ALTER TABLE public.seller_onboarding_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for onboarding sessions
CREATE POLICY "Users can view their own onboarding sessions" 
  ON public.seller_onboarding_sessions 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Service can manage onboarding sessions" 
  ON public.seller_onboarding_sessions 
  FOR ALL 
  USING (true);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_seller_onboarding_sessions_user_id ON public.seller_onboarding_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_seller_onboarding_sessions_seller_account_id ON public.seller_onboarding_sessions(seller_account_id);

-- Update the earnings summary trigger to handle Connect accounts
CREATE OR REPLACE FUNCTION public.update_seller_account_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Update seller account capabilities when transactions are completed
  IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
    UPDATE public.seller_accounts 
    SET updated_at = now()
    WHERE user_id = NEW.seller_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for seller account updates
DROP TRIGGER IF EXISTS update_seller_account_status_trigger ON public.transactions;
CREATE TRIGGER update_seller_account_status_trigger
  AFTER UPDATE ON public.transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_seller_account_status();
