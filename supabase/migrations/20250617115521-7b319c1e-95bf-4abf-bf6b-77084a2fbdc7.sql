
-- Check which foreign key constraints already exist and add only the missing ones

-- Add foreign key for posts -> profiles (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'posts_user_id_fkey' 
        AND table_name = 'posts'
    ) THEN
        ALTER TABLE public.posts 
        ADD CONSTRAINT posts_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add foreign key for team_activities -> profiles (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'team_activities_user_id_fkey' 
        AND table_name = 'team_activities'
    ) THEN
        ALTER TABLE public.team_activities 
        ADD CONSTRAINT team_activities_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add foreign key for team_discussions -> profiles (created_by field) (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'team_discussions_created_by_fkey' 
        AND table_name = 'team_discussions'
    ) THEN
        ALTER TABLE public.team_discussions 
        ADD CONSTRAINT team_discussions_created_by_fkey 
        FOREIGN KEY (created_by) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Add foreign key for team_discussion_replies -> profiles (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'team_discussion_replies_user_id_fkey' 
        AND table_name = 'team_discussion_replies'
    ) THEN
        ALTER TABLE public.team_discussion_replies 
        ADD CONSTRAINT team_discussion_replies_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;
