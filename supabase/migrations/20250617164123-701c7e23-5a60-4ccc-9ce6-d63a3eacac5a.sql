
-- Update subscribers table with enhanced fields for better subscription management
ALTER TABLE public.subscribers 
ADD COLUMN IF NOT EXISTS plan_name TEXT,
ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'inactive',
ADD COLUMN IF NOT EXISTS current_period_start TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS current_period_end TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS trial_start TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS trial_end TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS cancel_at_period_end BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Create subscription_plans table for plan configuration
CREATE TABLE IF NOT EXISTS public.subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_id TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  price_monthly NUMERIC NOT NULL DEFAULT 0,
  price_yearly NUMERIC,
  currency TEXT DEFAULT 'usd',
  stripe_price_id_monthly TEXT,
  stripe_price_id_yearly TEXT,
  features JSONB DEFAULT '{}',
  limits JSONB DEFAULT '{}',
  is_popular BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS on subscription_plans
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;

-- Create policy for public read access to active plans
CREATE POLICY "Public read access to active subscription plans" 
  ON public.subscription_plans 
  FOR SELECT 
  USING (is_active = true);

-- Create policy for admin management of plans
CREATE POLICY "Admins can manage subscription plans" 
  ON public.subscription_plans 
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'super_admin')
    )
  );

-- Insert default subscription plans
INSERT INTO public.subscription_plans (plan_id, name, display_name, description, price_monthly, price_yearly, features, limits, is_popular, sort_order) VALUES
('starter', 'starter', 'Starter', 'Perfect for aspiring screenwriters', 29.00, 290.00, 
 '{"screenplay_editor": true, "basic_ai_tools": true, "pdf_export": true, "email_support": true}',
 '{"active_projects": 3, "ai_generations_per_day": 5}', false, 1),
('pro-solo', 'pro-solo', 'Pro Solo', 'For serious independent writers', 49.00, 490.00,
 '{"screenplay_editor": true, "advanced_ai_tools": true, "marketplace_submission": true, "all_export_formats": true, "priority_support": true, "version_history": true}',
 '{"active_projects": 10, "ai_generations_per_day": 25}', true, 2),
('pro-team', 'pro-team', 'Pro Team', 'Collaborative writing teams', 79.00, 790.00,
 '{"screenplay_editor": true, "advanced_ai_tools": true, "team_collaboration": true, "scene_planning": true, "storyboarding": true, "real_time_editing": true, "admin_controls": true}',
 '{"active_projects": 10, "team_members": 5, "ai_generations_per_day": 50}', false, 3),
('studio', 'studio', 'Studio', 'Production companies & studios', 129.00, 1290.00,
 '{"screenplay_editor": true, "advanced_ai_tools": true, "production_tools": true, "marketplace_access": true, "script_discovery": true, "advanced_analytics": true, "custom_integrations": true}',
 '{"active_projects": -1, "team_members": 20, "ai_generations_per_day": 100}', false, 4);

-- Create subscription_feature_access table for granular feature control
CREATE TABLE IF NOT EXISTS public.subscription_feature_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  feature_key TEXT NOT NULL,
  access_level TEXT DEFAULT 'full', -- 'full', 'limited', 'none'
  usage_count INTEGER DEFAULT 0,
  usage_limit INTEGER,
  reset_period TEXT DEFAULT 'daily', -- 'daily', 'monthly', 'never'
  last_reset TIMESTAMPTZ DEFAULT now(),
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(user_id, feature_key)
);

-- Enable RLS on subscription_feature_access
ALTER TABLE public.subscription_feature_access ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own feature access
CREATE POLICY "Users can view their own feature access" 
  ON public.subscription_feature_access 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- Create policy for system to manage feature access
CREATE POLICY "System can manage feature access" 
  ON public.subscription_feature_access 
  FOR ALL 
  USING (true);

-- Create function to get user's subscription tier with enhanced data
CREATE OR REPLACE FUNCTION public.get_user_subscription_with_features(target_user_id UUID DEFAULT auth.uid())
RETURNS TABLE(
  subscribed BOOLEAN,
  plan_id TEXT,
  plan_name TEXT,
  status TEXT,
  current_period_end TIMESTAMPTZ,
  trial_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN,
  features JSONB,
  limits JSONB
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.subscribed,
    COALESCE(s.subscription_tier, 'free') as plan_id,
    COALESCE(sp.display_name, 'Free') as plan_name,
    COALESCE(s.status, 'inactive') as status,
    s.current_period_end,
    s.trial_end,
    COALESCE(s.cancel_at_period_end, false) as cancel_at_period_end,
    COALESCE(sp.features, '{}') as features,
    COALESCE(sp.limits, '{}') as limits
  FROM public.subscribers s
  LEFT JOIN public.subscription_plans sp ON sp.plan_id = s.subscription_tier
  WHERE s.user_id = target_user_id;
END;
$$;

-- Create function to check feature access
CREATE OR REPLACE FUNCTION public.check_feature_access(
  target_user_id UUID,
  feature_key TEXT
)
RETURNS TABLE(
  has_access BOOLEAN,
  access_level TEXT,
  usage_count INTEGER,
  usage_limit INTEGER,
  remaining_usage INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_subscription RECORD;
  feature_access RECORD;
BEGIN
  -- Get user subscription info
  SELECT * INTO user_subscription
  FROM public.get_user_subscription_with_features(target_user_id)
  LIMIT 1;
  
  -- Check if feature is included in subscription
  IF user_subscription.features ? feature_key THEN
    -- Get specific feature access record
    SELECT * INTO feature_access
    FROM public.subscription_feature_access
    WHERE user_id = target_user_id AND feature_key = check_feature_access.feature_key;
    
    IF feature_access IS NULL THEN
      -- Create default access record
      INSERT INTO public.subscription_feature_access (user_id, feature_key, access_level)
      VALUES (target_user_id, feature_key, 'full')
      RETURNING * INTO feature_access;
    END IF;
    
    RETURN QUERY SELECT 
      true as has_access,
      feature_access.access_level,
      feature_access.usage_count,
      feature_access.usage_limit,
      CASE 
        WHEN feature_access.usage_limit IS NULL THEN -1
        ELSE GREATEST(0, feature_access.usage_limit - feature_access.usage_count)
      END as remaining_usage;
  ELSE
    RETURN QUERY SELECT 
      false as has_access,
      'none'::TEXT as access_level,
      0 as usage_count,
      0 as usage_limit,
      0 as remaining_usage;
  END IF;
END;
$$;

-- Add trigger to update updated_at on subscription_plans
CREATE OR REPLACE FUNCTION public.update_subscription_plans_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscription_plans_updated_at
  BEFORE UPDATE ON public.subscription_plans
  FOR EACH ROW
  EXECUTE FUNCTION public.update_subscription_plans_updated_at();
