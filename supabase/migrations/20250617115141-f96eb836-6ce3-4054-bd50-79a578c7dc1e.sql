
-- Create team_activities table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.team_activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  activity_type TEXT NOT NULL,
  entity_type TEXT,
  entity_id UUID,
  description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_discussions table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.team_discussions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE NOT NULL,
  created_by UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  content TEXT,
  discussion_type TEXT DEFAULT 'general',
  is_announcement BOOLEAN DEFAULT FALSE,
  is_pinned BOOLEAN DEFAULT FALSE,
  reply_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create team_discussion_replies table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS public.team_discussion_replies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  discussion_id UUID REFERENCES public.team_discussions(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on new tables (only if they were just created)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_activities' AND table_schema = 'public') THEN
    ALTER TABLE public.team_activities ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussions' AND table_schema = 'public') THEN
    ALTER TABLE public.team_discussions ENABLE ROW LEVEL SECURITY;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussion_replies' AND table_schema = 'public') THEN
    ALTER TABLE public.team_discussion_replies ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- Create RLS policies for team_activities (only if table exists and policies don't exist)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_activities' AND table_schema = 'public') THEN
    -- Check if policy doesn't exist before creating
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_activities' AND policyname = 'Users can view team activities for their organizations') THEN
      CREATE POLICY "Users can view team activities for their organizations"
        ON public.team_activities FOR SELECT
        USING (public.is_org_member(org_id));
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_activities' AND policyname = 'Users can create team activities for their organizations') THEN
      CREATE POLICY "Users can create team activities for their organizations"
        ON public.team_activities FOR INSERT
        WITH CHECK (public.is_org_member(org_id) AND user_id = auth.uid());
    END IF;
  END IF;
END $$;

-- Create RLS policies for team_discussions (only if table exists and policies don't exist)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussions' AND table_schema = 'public') THEN
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_discussions' AND policyname = 'Users can view team discussions for their organizations') THEN
      CREATE POLICY "Users can view team discussions for their organizations"
        ON public.team_discussions FOR SELECT
        USING (public.is_org_member(org_id));
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_discussions' AND policyname = 'Users can create team discussions for their organizations') THEN
      CREATE POLICY "Users can create team discussions for their organizations"
        ON public.team_discussions FOR INSERT
        WITH CHECK (public.is_org_member(org_id) AND created_by = auth.uid());
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_discussions' AND policyname = 'Users can update their own team discussions') THEN
      CREATE POLICY "Users can update their own team discussions"
        ON public.team_discussions FOR UPDATE
        USING (created_by = auth.uid() AND public.is_org_member(org_id));
    END IF;
  END IF;
END $$;

-- Create RLS policies for team_discussion_replies (only if table exists and policies don't exist)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussion_replies' AND table_schema = 'public') THEN
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_discussion_replies' AND policyname = 'Users can view discussion replies for their organizations') THEN
      CREATE POLICY "Users can view discussion replies for their organizations"
        ON public.team_discussion_replies FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.team_discussions td 
            WHERE td.id = team_discussion_replies.discussion_id 
            AND public.is_org_member(td.org_id)
          )
        );
    END IF;
    
    IF NOT EXISTS (SELECT FROM pg_policies WHERE tablename = 'team_discussion_replies' AND policyname = 'Users can create discussion replies for their organizations') THEN
      CREATE POLICY "Users can create discussion replies for their organizations"
        ON public.team_discussion_replies FOR INSERT
        WITH CHECK (
          user_id = auth.uid() AND
          EXISTS (
            SELECT 1 FROM public.team_discussions td 
            WHERE td.id = team_discussion_replies.discussion_id 
            AND public.is_org_member(td.org_id)
          )
        );
    END IF;
  END IF;
END $$;

-- Add updated_at triggers (only if they don't exist)
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussions' AND table_schema = 'public') THEN
    IF NOT EXISTS (SELECT FROM information_schema.triggers WHERE trigger_name = 'update_team_discussions_updated_at') THEN
      CREATE TRIGGER update_team_discussions_updated_at 
        BEFORE UPDATE ON public.team_discussions
        FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();
    END IF;
  END IF;
  
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'team_discussion_replies' AND table_schema = 'public') THEN
    IF NOT EXISTS (SELECT FROM information_schema.triggers WHERE trigger_name = 'update_team_discussion_replies_updated_at') THEN
      CREATE TRIGGER update_team_discussion_replies_updated_at 
        BEFORE UPDATE ON public.team_discussion_replies
        FOR EACH ROW EXECUTE PROCEDURE public.update_updated_at_column();
    END IF;
  END IF;
END $$;
