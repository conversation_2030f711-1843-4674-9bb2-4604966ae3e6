
-- Add foreign key constraint between organization_members and profiles
ALTER TABLE public.organization_members 
ADD CONSTRAINT fk_organization_members_profiles 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

-- Update the RLS policies to properly handle the profiles relation
DROP POLICY IF EXISTS "Users can view their own memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Organization admins can view all memberships" ON public.organization_members;
DROP POLICY IF EXISTS "Organization admins can manage memberships" ON public.organization_members;

-- Create better RLS policies for organization_members
CREATE POLICY "Users can view their own memberships"
  ON public.organization_members FOR SELECT
  USING (user_id = auth.uid());

CREATE POLICY "Organization members can view org memberships"
  ON public.organization_members FOR SELECT
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Organization admins can manage memberships"
  ON public.organization_members FOR ALL
  USING (
    org_id IN (
      SELECT org_id FROM public.organization_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
