
-- Create monitoring and error tracking tables
CREATE TABLE IF NOT EXISTS public.system_health_checks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  check_name TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('healthy', 'degraded', 'unhealthy')),
  response_time_ms INTEGER,
  details JSONB DEFAULT '{}',
  error_message TEXT,
  checked_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.error_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  error_level TEXT NOT NULL CHECK (error_level IN ('error', 'warning', 'info', 'debug')),
  error_message TEXT NOT NULL,
  error_stack TEXT,
  user_id UUID REFERENCES auth.users(id),
  org_id UUID,
  component TEXT,
  url TEXT,
  user_agent TEXT,
  session_id TEXT,
  metadata JSONB DEFAULT '{}',
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name TEXT NOT NULL,
  metric_value NUMERIC NOT NULL,
  metric_unit TEXT DEFAULT 'ms',
  tags JSONB DEFAULT '{}',
  user_id UUID REFERENCES auth.users(id),
  org_id UUID,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.system_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  alert_type TEXT NOT NULL,
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  source TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  acknowledged BOOLEAN DEFAULT false,
  acknowledged_by UUID REFERENCES auth.users(id),
  acknowledged_at TIMESTAMP WITH TIME ZONE,
  resolved BOOLEAN DEFAULT false,
  resolved_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.backup_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  backup_type TEXT NOT NULL CHECK (backup_type IN ('full', 'incremental', 'differential')),
  schedule_expression TEXT NOT NULL, -- cron expression
  retention_days INTEGER NOT NULL DEFAULT 30,
  is_active BOOLEAN DEFAULT true,
  last_run_at TIMESTAMP WITH TIME ZONE,
  next_run_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE TABLE IF NOT EXISTS public.disaster_recovery_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_name TEXT NOT NULL,
  plan_type TEXT NOT NULL,
  recovery_steps JSONB NOT NULL,
  estimated_rto_minutes INTEGER, -- Recovery Time Objective
  estimated_rpo_minutes INTEGER, -- Recovery Point Objective
  is_active BOOLEAN DEFAULT true,
  last_tested_at TIMESTAMP WITH TIME ZONE,
  test_results JSONB,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_system_health_checks_checked_at ON public.system_health_checks(checked_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON public.error_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON public.error_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_org_id ON public.error_logs(org_id);
CREATE INDEX IF NOT EXISTS idx_performance_metrics_recorded_at ON public.performance_metrics(recorded_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_created_at ON public.system_alerts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_alerts_severity ON public.system_alerts(severity);

-- Add RLS policies
ALTER TABLE public.system_health_checks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.backup_schedules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.disaster_recovery_plans ENABLE ROW LEVEL SECURITY;

-- Super admin can access all monitoring data
CREATE POLICY "Super admins can access all health checks" ON public.system_health_checks
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can access all error logs" ON public.error_logs
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can access all performance metrics" ON public.performance_metrics
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can access all alerts" ON public.system_alerts
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can access backup schedules" ON public.backup_schedules
  FOR ALL USING (public.is_super_admin());

CREATE POLICY "Super admins can access disaster recovery plans" ON public.disaster_recovery_plans
  FOR ALL USING (public.is_super_admin());

-- Users can view their own error logs and metrics
CREATE POLICY "Users can view their own error logs" ON public.error_logs
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can view their own performance metrics" ON public.performance_metrics
  FOR SELECT USING (auth.uid() = user_id);

-- Function to record system health check
CREATE OR REPLACE FUNCTION public.record_health_check(
  check_name_param TEXT,
  status_param TEXT,
  response_time_param INTEGER DEFAULT NULL,
  details_param JSONB DEFAULT '{}',
  error_message_param TEXT DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  health_check_id UUID;
BEGIN
  INSERT INTO public.system_health_checks (
    check_name,
    status,
    response_time_ms,
    details,
    error_message
  ) VALUES (
    check_name_param,
    status_param,
    response_time_param,
    details_param,
    error_message_param
  ) RETURNING id INTO health_check_id;

  RETURN health_check_id;
END;
$$;

-- Function to log errors
CREATE OR REPLACE FUNCTION public.log_error(
  error_level_param TEXT,
  error_message_param TEXT,
  error_stack_param TEXT DEFAULT NULL,
  component_param TEXT DEFAULT NULL,
  url_param TEXT DEFAULT NULL,
  metadata_param JSONB DEFAULT '{}'
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  error_log_id UUID;
BEGIN
  INSERT INTO public.error_logs (
    error_level,
    error_message,
    error_stack,
    user_id,
    component,
    url,
    metadata
  ) VALUES (
    error_level_param,
    error_message_param,
    error_stack_param,
    auth.uid(),
    component_param,
    url_param,
    metadata_param
  ) RETURNING id INTO error_log_id;

  RETURN error_log_id;
END;
$$;

-- Function to record performance metrics
CREATE OR REPLACE FUNCTION public.record_performance_metric(
  metric_name_param TEXT,
  metric_value_param NUMERIC,
  metric_unit_param TEXT DEFAULT 'ms',
  tags_param JSONB DEFAULT '{}'
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  metric_id UUID;
BEGIN
  INSERT INTO public.performance_metrics (
    metric_name,
    metric_value,
    metric_unit,
    tags,
    user_id
  ) VALUES (
    metric_name_param,
    metric_value_param,
    metric_unit_param,
    tags_param,
    auth.uid()
  ) RETURNING id INTO metric_id;

  RETURN metric_id;
END;
$$;

-- Function to create system alert
CREATE OR REPLACE FUNCTION public.create_system_alert(
  alert_type_param TEXT,
  severity_param TEXT,
  title_param TEXT,
  message_param TEXT,
  source_param TEXT,
  metadata_param JSONB DEFAULT '{}'
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  alert_id UUID;
BEGIN
  INSERT INTO public.system_alerts (
    alert_type,
    severity,
    title,
    message,
    source,
    metadata
  ) VALUES (
    alert_type_param,
    severity_param,
    title_param,
    message_param,
    source_param,
    metadata_param
  ) RETURNING id INTO alert_id;

  RETURN alert_id;
END;
$$;
