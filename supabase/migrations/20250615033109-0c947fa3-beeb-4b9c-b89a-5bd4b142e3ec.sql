
-- Add indexes for subscription lookups and performance optimization
CREATE INDEX IF NOT EXISTS idx_subscribers_user_id ON public.subscribers(user_id);
CREATE INDEX IF NOT EXISTS idx_subscribers_email ON public.subscribers(email);
CREATE INDEX IF NOT EXISTS idx_subscribers_stripe_customer_id ON public.subscribers(stripe_customer_id) WHERE stripe_customer_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscribers_subscription_tier ON public.subscribers(subscription_tier) WHERE subscription_tier IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subscribers_updated_at ON public.subscribers(updated_at DESC);

-- Add indexes for production data queries
CREATE INDEX IF NOT EXISTS idx_production_schedules_org_id_status ON public.production_schedules(org_id, status);
CREATE INDEX IF NOT EXISTS idx_production_schedules_search ON public.production_schedules USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_production_schedules_dates ON public.production_schedules(start_date, end_date);

CREATE INDEX IF NOT EXISTS idx_production_budgets_org_id_status ON public.production_budgets(org_id, status);
CREATE INDEX IF NOT EXISTS idx_production_budgets_search ON public.production_budgets USING gin(search_vector);

CREATE INDEX IF NOT EXISTS idx_production_resources_org_id_status ON public.production_resources(org_id, availability_status);
CREATE INDEX IF NOT EXISTS idx_production_resources_search ON public.production_resources USING gin(search_vector);

CREATE INDEX IF NOT EXISTS idx_production_reports_org_id_status ON public.production_reports(org_id, status);
CREATE INDEX IF NOT EXISTS idx_production_reports_search ON public.production_reports USING gin(search_vector);
CREATE INDEX IF NOT EXISTS idx_production_reports_date ON public.production_reports(date DESC);

-- Add indexes for organization and user queries
CREATE INDEX IF NOT EXISTS idx_organization_members_user_id ON public.organization_members(user_id);
CREATE INDEX IF NOT EXISTS idx_organization_members_org_id_role ON public.organization_members(org_id, role);

-- Add indexes for marketplace queries
CREATE INDEX IF NOT EXISTS idx_screenplays_status_created_at ON public.screenplays(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_screenplays_genre_status ON public.screenplays(genre, status);
CREATE INDEX IF NOT EXISTS idx_screenplays_writer_id ON public.screenplays(writer_id);

-- Add indexes for audit logs
CREATE INDEX IF NOT EXISTS idx_production_audit_logs_org_id_created_at ON public.production_audit_logs(org_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_production_audit_logs_entity ON public.production_audit_logs(entity_type, entity_id);

-- Create materialized view for subscription analytics
CREATE MATERIALIZED VIEW IF NOT EXISTS subscription_analytics AS
SELECT 
  subscription_tier,
  COUNT(*) as subscriber_count,
  COUNT(*) FILTER (WHERE subscribed = true) as active_subscribers,
  AVG(EXTRACT(EPOCH FROM (now() - created_at))/86400) as avg_days_subscribed,
  DATE_TRUNC('month', created_at) as signup_month
FROM public.subscribers
WHERE subscription_tier IS NOT NULL
GROUP BY subscription_tier, DATE_TRUNC('month', created_at)
ORDER BY signup_month DESC, subscription_tier;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_subscription_analytics_unique 
ON subscription_analytics(subscription_tier, signup_month);

-- Create materialized view for production dashboard analytics with qualified column references
CREATE MATERIALIZED VIEW IF NOT EXISTS production_dashboard_analytics AS
SELECT 
  o.id as org_id,
  COUNT(DISTINCT ps.id) as total_schedules,
  COUNT(DISTINCT pb.id) as total_budgets,
  COUNT(DISTINCT pr.id) as total_resources,
  COUNT(DISTINCT prep.id) as total_reports,
  COUNT(DISTINCT ps.id) FILTER (WHERE ps.status = 'active') as active_schedules,
  COUNT(DISTINCT pb.id) FILTER (WHERE pb.status = 'approved') as approved_budgets,
  COUNT(DISTINCT pr.id) FILTER (WHERE pr.availability_status = 'available') as available_resources,
  COALESCE(SUM(pb.total_budget), 0) as total_budget_amount,
  MAX(GREATEST(
    COALESCE(ps.updated_at, o.updated_at), 
    COALESCE(pb.updated_at, o.updated_at), 
    COALESCE(pr.updated_at, o.updated_at), 
    COALESCE(prep.updated_at, o.updated_at)
  )) as last_activity
FROM public.organizations o
LEFT JOIN public.production_schedules ps ON o.id = ps.org_id
LEFT JOIN public.production_budgets pb ON o.id = pb.org_id
LEFT JOIN public.production_resources pr ON o.id = pr.org_id
LEFT JOIN public.production_reports prep ON o.id = prep.org_id
GROUP BY o.id;

-- Create unique index on production analytics materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_production_dashboard_analytics_org_id 
ON production_dashboard_analytics(org_id);

-- Refresh materialized views function
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY production_dashboard_analytics;
END;
$$;

-- Create optimized RLS helper functions
CREATE OR REPLACE FUNCTION public.get_user_org_ids()
RETURNS TABLE(org_id uuid)
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT om.org_id 
  FROM public.organization_members om 
  WHERE om.user_id = auth.uid();
$$;

-- Optimize existing RLS policies for better performance
DROP POLICY IF EXISTS "Users can view own subscription data" ON public.subscribers;
CREATE POLICY "Users can view own subscription data" ON public.subscribers
FOR SELECT
USING (user_id = auth.uid() OR email = auth.email());

-- Add RLS policies for production tables with optimized queries
DROP POLICY IF EXISTS "Organization members can view schedules" ON public.production_schedules;
CREATE POLICY "Organization members can view schedules" ON public.production_schedules
FOR SELECT
USING (org_id IN (SELECT public.get_user_org_ids()));

DROP POLICY IF EXISTS "Organization members can view budgets" ON public.production_budgets;
CREATE POLICY "Organization members can view budgets" ON public.production_budgets
FOR SELECT
USING (org_id IN (SELECT public.get_user_org_ids()));

DROP POLICY IF EXISTS "Organization members can view resources" ON public.production_resources;
CREATE POLICY "Organization members can view resources" ON public.production_resources
FOR SELECT
USING (org_id IN (SELECT public.get_user_org_ids()));

DROP POLICY IF EXISTS "Organization members can view reports" ON public.production_reports;
CREATE POLICY "Organization members can view reports" ON public.production_reports
FOR SELECT
USING (org_id IN (SELECT public.get_user_org_ids()));
