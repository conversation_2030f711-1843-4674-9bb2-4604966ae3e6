
-- Create favorites table (using a different name to avoid conflicts)
CREATE TABLE IF NOT EXISTS public.favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(user_id, screenplay_id)
);

-- Create collections table
CREATE TABLE IF NOT EXISTS public.collections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create collection_items table
CREATE TABLE IF NOT EXISTS public.collection_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  collection_id UUID NOT NULL REFERENCES public.collections(id) ON DELETE CASCADE,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  added_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(collection_id, screenplay_id)
);

-- Enable RLS on all tables
ALTER TABLE public.favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.collection_items ENABLE ROW LEVEL SECURITY;

-- RLS policies for favorites
CREATE POLICY "Users can manage their own favorites" 
  ON public.favorites 
  FOR ALL 
  USING (auth.uid() = user_id);

-- RLS policies for collections
CREATE POLICY "Users can manage their own collections" 
  ON public.collections 
  FOR ALL 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can view public collections" 
  ON public.collections 
  FOR SELECT 
  USING (is_public = true OR auth.uid() = user_id);

-- RLS policies for collection_items
CREATE POLICY "Users can manage their collection items" 
  ON public.collection_items 
  FOR ALL 
  USING (
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can view collection items" 
  ON public.collection_items 
  FOR SELECT 
  USING (
    collection_id IN (
      SELECT id FROM public.collections 
      WHERE is_public = true OR user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_favorites_user_id ON public.favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_favorites_screenplay_id ON public.favorites(screenplay_id);
CREATE INDEX IF NOT EXISTS idx_collections_user_id ON public.collections(user_id);
CREATE INDEX IF NOT EXISTS idx_collection_items_collection_id ON public.collection_items(collection_id);
CREATE INDEX IF NOT EXISTS idx_collection_items_screenplay_id ON public.collection_items(screenplay_id);

-- Create trigger for updated_at on collections
CREATE OR REPLACE FUNCTION public.update_collections_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_collections_updated_at
    BEFORE UPDATE ON public.collections
    FOR EACH ROW
    EXECUTE FUNCTION public.update_collections_updated_at();
