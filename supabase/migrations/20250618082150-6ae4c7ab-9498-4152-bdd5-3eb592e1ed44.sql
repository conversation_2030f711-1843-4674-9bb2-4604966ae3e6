
-- Add comments table for storyboard panels
CREATE TABLE IF NOT EXISTS public.storyboard_panel_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  panel_id UUID NOT NULL REFERENCES public.storyboard_panels(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  org_id UUID NOT NULL,
  content TEXT NOT NULL,
  timestamp_position NUMERIC DEFAULT NULL, -- For timeline-based comments
  resolved BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for storyboard panel comments
ALTER TABLE public.storyboard_panel_comments ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view comments in their org" 
  ON public.storyboard_panel_comments 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can create comments in their org" 
  ON public.storyboard_panel_comments 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id) AND auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" 
  ON public.storyboard_panel_comments 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" 
  ON public.storyboard_panel_comments 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add storyboard templates table
CREATE TABLE IF NOT EXISTS public.storyboard_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  template_data JSONB NOT NULL DEFAULT '{}',
  thumbnail_url TEXT,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies for storyboard templates
ALTER TABLE public.storyboard_templates ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view templates in their org or public templates" 
  ON public.storyboard_templates 
  FOR SELECT 
  USING (public.is_org_member(org_id) OR is_public = TRUE);

CREATE POLICY "Users can create templates in their org" 
  ON public.storyboard_templates 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id) AND auth.uid() = user_id);

CREATE POLICY "Users can update their own templates" 
  ON public.storyboard_templates 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own templates" 
  ON public.storyboard_templates 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Add enhanced search vector to storyboard_panels
ALTER TABLE public.storyboard_panels 
ADD COLUMN IF NOT EXISTS search_vector tsvector;

-- Create search vector update function for storyboard panels
CREATE OR REPLACE FUNCTION update_storyboard_panel_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector := to_tsvector('english', 
    COALESCE(NEW.dialogue, '') || ' ' || 
    COALESCE(NEW.feedback, '')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for search vector updates
DROP TRIGGER IF EXISTS update_storyboard_panel_search_trigger ON public.storyboard_panels;
CREATE TRIGGER update_storyboard_panel_search_trigger
  BEFORE INSERT OR UPDATE ON public.storyboard_panels
  FOR EACH ROW EXECUTE FUNCTION update_storyboard_panel_search_vector();

-- Update existing panels to have search vectors
UPDATE public.storyboard_panels 
SET search_vector = to_tsvector('english', 
  COALESCE(dialogue, '') || ' ' || 
  COALESCE(feedback, '')
) WHERE search_vector IS NULL;

-- Add export metadata to storyboards
ALTER TABLE public.storyboards 
ADD COLUMN IF NOT EXISTS export_settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS last_exported_at TIMESTAMP WITH TIME ZONE;
