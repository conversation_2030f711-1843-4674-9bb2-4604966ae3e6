
-- ScriptGenius Extension Schema Update: Storyboard Studio & Coverage Generator
-- Add two new tools to the existing ScriptGenius application

-- 1. Feature Flags Table for project-level tool enablement
CREATE TABLE IF NOT EXISTS public.project_feature_flags (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  storyboard_enabled BOOLEAN DEFAULT false,
  coverage_enabled BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- 2. Storyboards Table
CREATE TABLE IF NOT EXISTS public.storyboards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  fidelity TEXT DEFAULT 'Sketch',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- 3. Storyboard Panels Table
CREATE TABLE IF NOT EXISTS public.storyboard_panels (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  storyboard_id UUID REFERENCES public.storyboards(id) ON DELETE CASCADE,
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  image_url TEXT,
  dialogue TEXT,
  order_index INTEGER DEFAULT 0,
  feedback TEXT,
  scene_id UUID REFERENCES public.scenes(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- 4. Coverage Reports Table
CREATE TABLE IF NOT EXISTS public.coverage_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  org_id UUID REFERENCES public.organizations(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  scene_id UUID REFERENCES public.scenes(id) ON DELETE CASCADE,
  fidelity_level TEXT DEFAULT 'Standard',
  coverage_report TEXT,
  synopsis TEXT,
  strengths TEXT,
  weaknesses TEXT,
  verdict TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- 5. Storyboard Prompt Styles Table (for different visual styles)
CREATE TABLE IF NOT EXISTS public.storyboard_prompt_styles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  label TEXT UNIQUE NOT NULL,
  description TEXT,
  prompt_suffix TEXT,
  cost_multiplier NUMERIC DEFAULT 1.0,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Insert default prompt styles
INSERT INTO public.storyboard_prompt_styles (label, description, prompt_suffix, cost_multiplier) VALUES
('Sketch', 'Rough pencil-style frames for shot planning', 'rough pencil sketch, no color, no shading, storyboard frame, line art', 1.0),
('Illustrated', 'Flat color illustrations for pitches and reference', 'hand-drawn illustration, flat color, subtle shadows, clear characters and backgrounds', 1.5),
('Cinematic', 'High-fidelity visual boards with atmospheric effects', 'cinematic concept art, dramatic lighting, lens blur, atmospheric detail, high contrast', 2.0)
ON CONFLICT (label) DO NOTHING;

-- Enable RLS on new tables
ALTER TABLE public.project_feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storyboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storyboard_panels ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.coverage_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storyboard_prompt_styles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for project_feature_flags
CREATE POLICY "Organization members can view feature flags"
  ON public.project_feature_flags FOR SELECT
  USING (public.is_org_member(project_id));

CREATE POLICY "Organization admins can manage feature flags"
  ON public.project_feature_flags FOR ALL
  USING (public.is_org_admin(project_id));

-- RLS Policies for storyboards
CREATE POLICY "Users can view org storyboards"
  ON public.storyboards FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create storyboards in their orgs"
  ON public.storyboards FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Storyboard creators can update their storyboards"
  ON public.storyboards FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Storyboard creators can delete their storyboards"
  ON public.storyboards FOR DELETE
  USING (user_id = auth.uid());

-- RLS Policies for storyboard_panels
CREATE POLICY "Users can view org storyboard panels"
  ON public.storyboard_panels FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create panels in their org storyboards"
  ON public.storyboard_panels FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Panel creators can update their panels"
  ON public.storyboard_panels FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Panel creators can delete their panels"
  ON public.storyboard_panels FOR DELETE
  USING (user_id = auth.uid());

-- RLS Policies for coverage_reports
CREATE POLICY "Users can view org coverage reports"
  ON public.coverage_reports FOR SELECT
  USING (org_id IN (SELECT org_id FROM public.get_current_user_orgs()));

CREATE POLICY "Users can create coverage reports in their orgs"
  ON public.coverage_reports FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND 
    public.is_org_member(org_id)
  );

CREATE POLICY "Coverage creators can update their reports"
  ON public.coverage_reports FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Coverage creators can delete their reports"
  ON public.coverage_reports FOR DELETE
  USING (user_id = auth.uid());

-- RLS Policies for storyboard_prompt_styles (public read access)
CREATE POLICY "Authenticated users can view prompt styles"
  ON public.storyboard_prompt_styles FOR SELECT
  USING (auth.role() = 'authenticated');

-- Create safe public view for storyboard prompt styles
CREATE OR REPLACE VIEW public.public_storyboard_prompt_styles AS
SELECT
  id,
  label,
  description,
  cost_multiplier
FROM public.storyboard_prompt_styles;

-- RPC Function to Generate Coverage (placeholder for AI integration)
CREATE OR REPLACE FUNCTION public.generate_script_coverage(
  scene_id_param UUID,
  fidelity_level_param TEXT,
  org_id_param UUID
)
RETURNS TABLE (
  id UUID,
  coverage_report TEXT,
  synopsis TEXT,
  strengths TEXT,
  weaknesses TEXT,
  verdict TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if user has access to the organization
  IF NOT public.is_org_member(org_id_param) THEN
    RAISE EXCEPTION 'Access denied: User is not a member of this organization';
  END IF;

  -- This is a placeholder function that would integrate with AI services
  -- In production, this would call OpenAI API or similar for actual coverage generation
  RETURN QUERY
  INSERT INTO public.coverage_reports (
    org_id,
    user_id,
    scene_id,
    fidelity_level,
    coverage_report,
    synopsis,
    strengths,
    weaknesses,
    verdict
  ) VALUES (
    org_id_param,
    auth.uid(),
    scene_id_param,
    fidelity_level_param,
    'Generated coverage report for scene at ' || fidelity_level_param || ' fidelity.',
    'This is a placeholder synopsis that would be generated by AI.',
    'Placeholder strengths analysis.',
    'Placeholder weaknesses analysis.',
    'Placeholder verdict.'
  )
  RETURNING 
    public.coverage_reports.id,
    public.coverage_reports.coverage_report,
    public.coverage_reports.synopsis,
    public.coverage_reports.strengths,
    public.coverage_reports.weaknesses,
    public.coverage_reports.verdict;
END;
$$;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_storyboards_org_id ON public.storyboards(org_id);
CREATE INDEX IF NOT EXISTS idx_storyboard_panels_storyboard_id ON public.storyboard_panels(storyboard_id);
CREATE INDEX IF NOT EXISTS idx_storyboard_panels_org_id ON public.storyboard_panels(org_id);
CREATE INDEX IF NOT EXISTS idx_coverage_reports_org_id ON public.coverage_reports(org_id);
CREATE INDEX IF NOT EXISTS idx_coverage_reports_scene_id ON public.coverage_reports(scene_id);
