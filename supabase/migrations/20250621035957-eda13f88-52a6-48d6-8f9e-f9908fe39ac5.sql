
-- Create beta testing infrastructure tables
CREATE TABLE public.beta_invitations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT NOT NULL UNIQUE,
  invited_by UUID REFERENCES auth.users,
  org_id UUID REFERENCES public.organizations,
  invitation_code TEXT NOT NULL UNIQUE,
  status TEXT NOT NULL DEFAULT 'pending', -- pending, accepted, expired
  phase INTEGER NOT NULL DEFAULT 1, -- 1: Small Beta (50), 2: Expanded Beta (150), 3: Full Beta (250)
  invited_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  accepted_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT (now() + interval '7 days'),
  metadata JSONB DEFAULT '{}'::jsonb
);

-- Create beta feedback collection table
CREATE TABLE public.beta_feedback (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users,
  org_id UUID REFERENCES public.organizations,
  feedback_type TEXT NOT NULL, -- bug_report, feature_request, general_feedback, usability_issue
  category TEXT NOT NULL, -- ui_ux, performance, functionality, content, mobile
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  severity TEXT NOT NULL DEFAULT 'medium', -- low, medium, high, critical
  status TEXT NOT NULL DEFAULT 'open', -- open, in_progress, resolved, closed
  page_url TEXT,
  user_agent TEXT,
  browser_info JSONB,
  screenshot_url TEXT,
  steps_to_reproduce TEXT,
  expected_behavior TEXT,
  actual_behavior TEXT,
  priority INTEGER DEFAULT 3, -- 1-5 scale
  assigned_to UUID REFERENCES auth.users,
  tags TEXT[] DEFAULT '{}',
  votes INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  resolved_at TIMESTAMP WITH TIME ZONE
);

-- Create beta analytics tracking table
CREATE TABLE public.beta_analytics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users,
  org_id UUID REFERENCES public.organizations,
  session_id TEXT NOT NULL,
  event_type TEXT NOT NULL, -- page_view, feature_usage, error, conversion
  event_name TEXT NOT NULL,
  page_path TEXT,
  feature_name TEXT,
  duration_ms INTEGER,
  metadata JSONB DEFAULT '{}'::jsonb,
  user_agent TEXT,
  device_info JSONB,
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create beta user cohorts table
CREATE TABLE public.beta_cohorts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users,
  cohort_name TEXT NOT NULL, -- phase_1_beta, phase_2_beta, phase_3_beta
  joined_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  is_active BOOLEAN DEFAULT true,
  onboarding_completed BOOLEAN DEFAULT false,
  last_activity_at TIMESTAMP WITH TIME ZONE,
  feature_flags JSONB DEFAULT '{}'::jsonb,
  notes TEXT
);

-- Create beta metrics summary table
CREATE TABLE public.beta_metrics_summary (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  phase INTEGER NOT NULL,
  total_invitations INTEGER DEFAULT 0,
  accepted_invitations INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0,
  daily_active_users INTEGER DEFAULT 0,
  feature_usage JSONB DEFAULT '{}'::jsonb,
  feedback_count INTEGER DEFAULT 0,
  bug_reports INTEGER DEFAULT 0,
  conversion_rate DECIMAL(5,2),
  retention_rate DECIMAL(5,2),
  nps_score DECIMAL(3,1),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(date, phase)
);

-- Add Row Level Security
ALTER TABLE public.beta_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_cohorts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.beta_metrics_summary ENABLE ROW LEVEL SECURITY;

-- RLS Policies for beta_invitations
CREATE POLICY "Admins can manage beta invitations" 
  ON public.beta_invitations 
  FOR ALL 
  USING (public.is_super_admin());

-- RLS Policies for beta_feedback
CREATE POLICY "Users can view and create their own feedback" 
  ON public.beta_feedback 
  FOR ALL 
  USING (auth.uid() = user_id OR public.is_super_admin());

CREATE POLICY "Org admins can view org feedback" 
  ON public.beta_feedback 
  FOR SELECT 
  USING (public.is_org_admin(org_id));

-- RLS Policies for beta_analytics
CREATE POLICY "Users can create their own analytics" 
  ON public.beta_analytics 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all analytics" 
  ON public.beta_analytics 
  FOR SELECT 
  USING (public.is_super_admin());

-- RLS Policies for beta_cohorts
CREATE POLICY "Users can view their own cohort info" 
  ON public.beta_cohorts 
  FOR SELECT 
  USING (auth.uid() = user_id OR public.is_super_admin());

CREATE POLICY "Admins can manage cohorts" 
  ON public.beta_cohorts 
  FOR ALL 
  USING (public.is_super_admin());

-- RLS Policies for beta_metrics_summary
CREATE POLICY "Admins can manage beta metrics" 
  ON public.beta_metrics_summary 
  FOR ALL 
  USING (public.is_super_admin());

-- Create functions for beta management
CREATE OR REPLACE FUNCTION public.send_beta_invitation(
  email_param TEXT,
  phase_param INTEGER DEFAULT 1,
  org_id_param UUID DEFAULT NULL
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation_id UUID;
  invitation_code TEXT;
BEGIN
  -- Check if user is super admin
  IF NOT public.is_super_admin() THEN
    RAISE EXCEPTION 'Access denied: Only super admins can send beta invitations';
  END IF;

  -- Generate unique invitation code
  invitation_code := 'BETA-' || upper(substring(gen_random_uuid()::text, 1, 8));

  INSERT INTO public.beta_invitations (
    email,
    invited_by,
    org_id,
    invitation_code,
    phase
  ) VALUES (
    email_param,
    auth.uid(),
    org_id_param,
    invitation_code,
    phase_param
  ) RETURNING id INTO invitation_id;

  RETURN invitation_id;
END;
$$;

CREATE OR REPLACE FUNCTION public.accept_beta_invitation(
  invitation_code_param TEXT
) RETURNS TABLE(success BOOLEAN, message TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation_record public.beta_invitations%ROWTYPE;
  user_email TEXT;
BEGIN
  -- Get user email
  SELECT email INTO user_email FROM auth.users WHERE id = auth.uid();
  
  -- Find the invitation
  SELECT * INTO invitation_record
  FROM public.beta_invitations
  WHERE invitation_code = invitation_code_param
  AND email = user_email
  AND status = 'pending'
  AND expires_at > now();

  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 'Invalid or expired invitation code';
    RETURN;
  END IF;

  -- Update invitation status
  UPDATE public.beta_invitations
  SET status = 'accepted', accepted_at = now()
  WHERE id = invitation_record.id;

  -- Add user to beta cohort
  INSERT INTO public.beta_cohorts (user_id, cohort_name)
  VALUES (auth.uid(), 'phase_' || invitation_record.phase || '_beta')
  ON CONFLICT (user_id) DO UPDATE SET
    cohort_name = 'phase_' || invitation_record.phase || '_beta',
    is_active = true;

  RETURN QUERY SELECT true, 'Beta invitation accepted successfully';
END;
$$;

CREATE OR REPLACE FUNCTION public.track_beta_event(
  event_type_param TEXT,
  event_name_param TEXT,
  metadata_param JSONB DEFAULT '{}'::jsonb
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  analytics_id UUID;
  session_id_val TEXT;
BEGIN
  -- Generate or get session ID
  session_id_val := COALESCE(
    (metadata_param->>'session_id')::TEXT,
    'session_' || gen_random_uuid()::text
  );

  INSERT INTO public.beta_analytics (
    user_id,
    session_id,
    event_type,
    event_name,
    metadata,
    user_agent
  ) VALUES (
    auth.uid(),
    session_id_val,
    event_type_param,
    event_name_param,
    metadata_param,
    COALESCE((metadata_param->>'user_agent')::TEXT, 'unknown')
  ) RETURNING id INTO analytics_id;

  -- Update last activity for cohort
  UPDATE public.beta_cohorts
  SET last_activity_at = now()
  WHERE user_id = auth.uid();

  RETURN analytics_id;
END;
$$;

-- Create indexes for performance
CREATE INDEX idx_beta_invitations_email ON public.beta_invitations(email);
CREATE INDEX idx_beta_invitations_code ON public.beta_invitations(invitation_code);
CREATE INDEX idx_beta_feedback_user_id ON public.beta_feedback(user_id);
CREATE INDEX idx_beta_feedback_status ON public.beta_feedback(status);
CREATE INDEX idx_beta_analytics_user_id ON public.beta_analytics(user_id);
CREATE INDEX idx_beta_analytics_timestamp ON public.beta_analytics(timestamp);
CREATE INDEX idx_beta_cohorts_user_id ON public.beta_cohorts(user_id);

-- Create triggers for updating timestamps
CREATE OR REPLACE FUNCTION public.update_beta_feedback_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_beta_feedback_timestamp
  BEFORE UPDATE ON public.beta_feedback
  FOR EACH ROW EXECUTE FUNCTION public.update_beta_feedback_timestamp();
