
-- First, let's create profiles for any users referenced in organization_members but missing from profiles
INSERT INTO public.profiles (id, full_name, role)
SELECT DISTINCT om.user_id, 'Unknown User', 'writer'
FROM public.organization_members om
LEFT JOIN public.profiles p ON om.user_id = p.id
WHERE p.id IS NULL;

-- Now add the foreign key relationship between organization_members and profiles
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'organization_members_user_id_fkey' 
        AND table_name = 'organization_members'
    ) THEN
        ALTER TABLE public.organization_members 
        ADD CONSTRAINT organization_members_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;
    END IF;
END $$;
