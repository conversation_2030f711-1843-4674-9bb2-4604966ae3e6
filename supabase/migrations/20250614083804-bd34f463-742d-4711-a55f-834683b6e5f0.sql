
-- Create screenplays table for the marketplace
CREATE TABLE public.screenplays (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  logline TEXT,
  synopsis TEXT,
  genre TEXT NOT NULL,
  page_count INTEGER,
  price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  script_content TEXT NOT NULL,
  cover_image_url TEXT,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'published')),
  rejection_reason TEXT,
  writer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  org_id UUID REFERENCES public.organizations(id),
  reviewed_by UUID REFERENCES auth.users(id),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create screenplay_purchases table for tracking sales
CREATE TABLE public.screenplay_purchases (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  buyer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  purchase_price DECIMAL(10,2) NOT NULL,
  payment_intent_id TEXT,
  purchased_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(screenplay_id, buyer_id)
);

-- Create screenplay_reviews table for buyer feedback
CREATE TABLE public.screenplay_reviews (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  purchase_id UUID NOT NULL REFERENCES public.screenplay_purchases(id) ON DELETE CASCADE,
  rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
  review_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(screenplay_id, reviewer_id)
);

-- Enable RLS on all tables
ALTER TABLE public.screenplays ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.screenplay_purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.screenplay_reviews ENABLE ROW LEVEL SECURITY;

-- Create security definer function to check super admin role
CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS BOOLEAN
LANGUAGE SQL
STABLE SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.profiles 
    WHERE id = auth.uid() 
    AND role = 'super_admin'
  );
$$;

-- RLS Policies for screenplays table
-- Writers can view and manage their own screenplays
CREATE POLICY "Writers can view their own screenplays" 
  ON public.screenplays 
  FOR SELECT 
  USING (auth.uid() = writer_id);

CREATE POLICY "Writers can create screenplays" 
  ON public.screenplays 
  FOR INSERT 
  WITH CHECK (auth.uid() = writer_id);

CREATE POLICY "Writers can update their own pending screenplays" 
  ON public.screenplays 
  FOR UPDATE 
  USING (auth.uid() = writer_id AND status = 'pending');

-- Users can view published screenplays
CREATE POLICY "Users can view published screenplays" 
  ON public.screenplays 
  FOR SELECT 
  USING (status = 'published');

-- Super admins can view and manage all screenplays
CREATE POLICY "Super admins can view all screenplays" 
  ON public.screenplays 
  FOR SELECT 
  USING (public.is_super_admin());

CREATE POLICY "Super admins can update all screenplays" 
  ON public.screenplays 
  FOR UPDATE 
  USING (public.is_super_admin());

-- RLS Policies for screenplay_purchases table
CREATE POLICY "Users can view their own purchases" 
  ON public.screenplay_purchases 
  FOR SELECT 
  USING (auth.uid() = buyer_id);

CREATE POLICY "Writers can view purchases of their screenplays" 
  ON public.screenplay_purchases 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.screenplays 
      WHERE id = screenplay_id AND writer_id = auth.uid()
    )
  );

CREATE POLICY "Users can create purchases" 
  ON public.screenplay_purchases 
  FOR INSERT 
  WITH CHECK (auth.uid() = buyer_id);

-- RLS Policies for screenplay_reviews table
CREATE POLICY "Users can view reviews for published screenplays" 
  ON public.screenplay_reviews 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.screenplays 
      WHERE id = screenplay_id AND status = 'published'
    )
  );

CREATE POLICY "Buyers can create reviews for purchased screenplays" 
  ON public.screenplay_reviews 
  FOR INSERT 
  WITH CHECK (
    auth.uid() = reviewer_id AND
    EXISTS (
      SELECT 1 FROM public.screenplay_purchases 
      WHERE screenplay_id = screenplay_reviews.screenplay_id 
      AND buyer_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_screenplays_status ON public.screenplays(status);
CREATE INDEX idx_screenplays_genre ON public.screenplays(genre);
CREATE INDEX idx_screenplays_writer_id ON public.screenplays(writer_id);
CREATE INDEX idx_screenplay_purchases_buyer_id ON public.screenplay_purchases(buyer_id);
CREATE INDEX idx_screenplay_purchases_screenplay_id ON public.screenplay_purchases(screenplay_id);
CREATE INDEX idx_screenplay_reviews_screenplay_id ON public.screenplay_reviews(screenplay_id);
