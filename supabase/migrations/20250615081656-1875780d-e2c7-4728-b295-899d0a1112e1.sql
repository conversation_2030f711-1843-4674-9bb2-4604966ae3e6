
-- Add foreign key constraints to link team tables with profiles table
ALTER TABLE public.team_activities 
ADD CONSTRAINT fk_team_activities_user_profiles 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.team_discussions 
ADD CONSTRAINT fk_team_discussions_created_by_profiles 
FOREIGN KEY (created_by) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.team_discussion_replies 
ADD CONSTRAINT fk_team_discussion_replies_user_profiles 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.team_memberships 
ADD CONSTRAINT fk_team_memberships_user_profiles 
FOREIGN KEY (user_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE public.team_memberships 
ADD CONSTRAINT fk_team_memberships_invited_by_profiles 
FOR<PERSON><PERSON><PERSON> KEY (invited_by) REFERENCES public.profiles(id) ON DELETE CASCADE;
