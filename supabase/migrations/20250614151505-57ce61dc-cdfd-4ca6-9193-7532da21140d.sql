
-- File attachments system
CREATE TABLE public.production_file_attachments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  entity_type TEXT NOT NULL, -- 'report', 'budget', 'schedule', 'resource'
  entity_id UUID NOT NULL,
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size BIGINT NOT NULL,
  storage_path TEXT NOT NULL,
  description TEXT,
  uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- User roles for production access
CREATE TYPE public.production_role AS ENUM ('producer', 'coordinator', 'crew', 'viewer');

CREATE TABLE public.production_user_roles (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  role production_role NOT NULL DEFAULT 'viewer',
  permissions JSONB NOT NULL DEFAULT '{}',
  assigned_by UUID NOT NULL,
  assigned_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(org_id, user_id)
);

-- Production templates
CREATE TABLE public.production_templates (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  created_by UUID NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  template_type TEXT NOT NULL, -- 'schedule', 'budget', 'complete'
  template_data JSONB NOT NULL DEFAULT '{}',
  is_public BOOLEAN NOT NULL DEFAULT false,
  usage_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Integration webhooks
CREATE TABLE public.production_integrations (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  integration_type TEXT NOT NULL, -- 'calendar', 'accounting', 'custom'
  name TEXT NOT NULL,
  webhook_url TEXT,
  api_credentials JSONB, -- encrypted storage for API keys
  settings JSONB NOT NULL DEFAULT '{}',
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Audit logging
CREATE TABLE public.production_audit_logs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  org_id UUID NOT NULL,
  user_id UUID NOT NULL,
  entity_type TEXT NOT NULL, -- 'schedule', 'budget', 'resource', 'report'
  entity_id UUID NOT NULL,
  action TEXT NOT NULL, -- 'create', 'update', 'delete', 'view'
  old_values JSONB,
  new_values JSONB,
  changes_summary TEXT,
  ip_address INET,
  user_agent TEXT,
  session_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE public.production_file_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.production_audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for file attachments
CREATE POLICY "Users can view attachments in their org" 
  ON public.production_file_attachments 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Users can upload attachments to their org" 
  ON public.production_file_attachments 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id) AND auth.uid() = user_id);

-- RLS policies for user roles
CREATE POLICY "Org members can view production roles" 
  ON public.production_user_roles 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Org admins can manage production roles" 
  ON public.production_user_roles 
  FOR ALL 
  USING (public.is_org_admin(org_id));

-- RLS policies for templates
CREATE POLICY "Users can view public templates and org templates" 
  ON public.production_templates 
  FOR SELECT 
  USING (is_public = true OR public.is_org_member(org_id));

CREATE POLICY "Users can create templates in their org" 
  ON public.production_templates 
  FOR INSERT 
  WITH CHECK (public.is_org_member(org_id) AND auth.uid() = created_by);

CREATE POLICY "Users can update their own templates" 
  ON public.production_templates 
  FOR UPDATE 
  USING (public.is_org_member(org_id) AND auth.uid() = created_by);

-- RLS policies for integrations
CREATE POLICY "Org members can view integrations" 
  ON public.production_integrations 
  FOR SELECT 
  USING (public.is_org_member(org_id));

CREATE POLICY "Org admins can manage integrations" 
  ON public.production_integrations 
  FOR ALL 
  USING (public.is_org_admin(org_id));

-- RLS policies for audit logs
CREATE POLICY "Org members can view audit logs" 
  ON public.production_audit_logs 
  FOR SELECT 
  USING (public.is_org_member(org_id));

-- Functions for role checking
CREATE OR REPLACE FUNCTION public.has_production_role(_org_id UUID, _role production_role)
RETURNS BOOLEAN
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT EXISTS (
    SELECT 1 
    FROM public.production_user_roles 
    WHERE org_id = _org_id 
    AND user_id = auth.uid() 
    AND role = _role
  );
$$;

CREATE OR REPLACE FUNCTION public.get_user_production_role(_org_id UUID)
RETURNS production_role
LANGUAGE sql
STABLE SECURITY DEFINER
AS $$
  SELECT role 
  FROM public.production_user_roles 
  WHERE org_id = _org_id 
  AND user_id = auth.uid()
  LIMIT 1;
$$;

-- Trigger for audit logging
CREATE OR REPLACE FUNCTION public.log_production_changes()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
DECLARE
  entity_type_name TEXT;
  action_name TEXT;
BEGIN
  -- Determine entity type from table name
  entity_type_name := CASE TG_TABLE_NAME
    WHEN 'production_schedules' THEN 'schedule'
    WHEN 'production_budgets' THEN 'budget'
    WHEN 'production_resources' THEN 'resource'
    WHEN 'production_reports' THEN 'report'
    ELSE TG_TABLE_NAME
  END;
  
  -- Determine action
  action_name := CASE TG_OP
    WHEN 'INSERT' THEN 'create'
    WHEN 'UPDATE' THEN 'update'
    WHEN 'DELETE' THEN 'delete'
  END;
  
  -- Insert audit log
  INSERT INTO public.production_audit_logs (
    org_id,
    user_id,
    entity_type,
    entity_id,
    action,
    old_values,
    new_values,
    changes_summary
  ) VALUES (
    COALESCE(NEW.org_id, OLD.org_id),
    auth.uid(),
    entity_type_name,
    COALESCE(NEW.id, OLD.id),
    action_name,
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW) ELSE NULL END,
    CASE 
      WHEN TG_OP = 'INSERT' THEN 'Created new ' || entity_type_name
      WHEN TG_OP = 'UPDATE' THEN 'Updated ' || entity_type_name
      WHEN TG_OP = 'DELETE' THEN 'Deleted ' || entity_type_name
    END
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Add audit triggers to production tables
CREATE TRIGGER production_schedules_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.production_schedules
  FOR EACH ROW EXECUTE FUNCTION public.log_production_changes();

CREATE TRIGGER production_budgets_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.production_budgets
  FOR EACH ROW EXECUTE FUNCTION public.log_production_changes();

CREATE TRIGGER production_resources_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.production_resources
  FOR EACH ROW EXECUTE FUNCTION public.log_production_changes();

CREATE TRIGGER production_reports_audit_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.production_reports
  FOR EACH ROW EXECUTE FUNCTION public.log_production_changes();

-- Add updated_at triggers
CREATE TRIGGER production_templates_updated_at_trigger
  BEFORE UPDATE ON public.production_templates
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER production_integrations_updated_at_trigger
  BEFORE UPDATE ON public.production_integrations
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
