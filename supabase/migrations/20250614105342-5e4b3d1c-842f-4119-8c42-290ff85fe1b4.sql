
-- Create AI assessment table for screenplay evaluations
CREATE TABLE public.screenplay_assessments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  assessment_type TEXT NOT NULL DEFAULT 'full' CHECK (assessment_type IN ('quick', 'standard', 'full')),
  overall_score INTEGER CHECK (overall_score >= 0 AND overall_score <= 100),
  structure_score INTEGER CHECK (structure_score >= 0 AND structure_score <= 100),
  character_score INTEGER CHECK (character_score >= 0 AND character_score <= 100),
  dialogue_score INTEGER CHECK (dialogue_score >= 0 AND dialogue_score <= 100),
  marketability_score INTEGER CHECK (marketability_score >= 0 AND marketability_score <= 100),
  detailed_feedback JSONB,
  strengths TEXT[],
  weaknesses TEXT[],
  recommendations TEXT[],
  market_analysis JSONB,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  assessed_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  completed_at TIMESTAMP WITH TIME ZONE
);

-- Create notifications table
CREATE TABLE public.notifications (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('offer_received', 'offer_accepted', 'offer_rejected', 'purchase_made', 'assessment_complete', 'contract_signed', 'payment_received')),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB,
  read BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create contracts table
CREATE TABLE public.screenplay_contracts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  buyer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  seller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  offer_id UUID REFERENCES public.screenplay_offers(id),
  contract_type TEXT NOT NULL DEFAULT 'purchase' CHECK (contract_type IN ('purchase', 'option', 'licensing')),
  terms JSONB NOT NULL,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'pending_signatures', 'signed', 'executed', 'terminated')),
  buyer_signed_at TIMESTAMP WITH TIME ZONE,
  seller_signed_at TIMESTAMP WITH TIME ZONE,
  executed_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Create enhanced analytics views
CREATE TABLE public.screenplay_analytics_summary (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  screenplay_id UUID NOT NULL REFERENCES public.screenplays(id) ON DELETE CASCADE,
  total_views INTEGER NOT NULL DEFAULT 0,
  unique_viewers INTEGER NOT NULL DEFAULT 0,
  total_offers INTEGER NOT NULL DEFAULT 0,
  avg_offer_amount DECIMAL(10,2),
  total_purchases INTEGER NOT NULL DEFAULT 0,
  total_revenue DECIMAL(10,2) NOT NULL DEFAULT 0,
  conversion_rate DECIMAL(5,2),
  last_updated TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS on all new tables
ALTER TABLE public.screenplay_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.screenplay_contracts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.screenplay_analytics_summary ENABLE ROW LEVEL SECURITY;

-- RLS Policies for screenplay_assessments
CREATE POLICY "Users can view assessments for their screenplays or purchased ones" 
  ON public.screenplay_assessments 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.screenplays s 
      WHERE s.id = screenplay_id 
      AND (s.writer_id = auth.uid() OR EXISTS (
        SELECT 1 FROM public.screenplay_purchases sp 
        WHERE sp.screenplay_id = s.id AND sp.buyer_id = auth.uid()
      ))
    )
  );

CREATE POLICY "Super admins can manage all assessments" 
  ON public.screenplay_assessments 
  FOR ALL 
  USING (public.is_super_admin());

-- RLS Policies for notifications
CREATE POLICY "Users can view their own notifications" 
  ON public.notifications 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "System can insert notifications" 
  ON public.notifications 
  FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Users can update their own notifications" 
  ON public.notifications 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- RLS Policies for screenplay_contracts
CREATE POLICY "Users can view contracts they're involved in" 
  ON public.screenplay_contracts 
  FOR SELECT 
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Users can create contracts" 
  ON public.screenplay_contracts 
  FOR INSERT 
  WITH CHECK (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "Users can update contracts they're involved in" 
  ON public.screenplay_contracts 
  FOR UPDATE 
  USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- RLS Policies for screenplay_analytics_summary
CREATE POLICY "Writers can view analytics for their screenplays" 
  ON public.screenplay_analytics_summary 
  FOR SELECT 
  USING (
    EXISTS (
      SELECT 1 FROM public.screenplays s 
      WHERE s.id = screenplay_id AND s.writer_id = auth.uid()
    )
  );

CREATE POLICY "Super admins can view all analytics" 
  ON public.screenplay_analytics_summary 
  FOR ALL 
  USING (public.is_super_admin());

-- Create indexes for performance
CREATE INDEX idx_screenplay_assessments_screenplay_id ON public.screenplay_assessments(screenplay_id);
CREATE INDEX idx_screenplay_assessments_status ON public.screenplay_assessments(status);
CREATE INDEX idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX idx_notifications_type ON public.notifications(type);
CREATE INDEX idx_notifications_read ON public.notifications(read);
CREATE INDEX idx_screenplay_contracts_screenplay_id ON public.screenplay_contracts(screenplay_id);
CREATE INDEX idx_screenplay_contracts_buyer_id ON public.screenplay_contracts(buyer_id);
CREATE INDEX idx_screenplay_contracts_seller_id ON public.screenplay_contracts(seller_id);
CREATE INDEX idx_screenplay_contracts_status ON public.screenplay_contracts(status);
CREATE INDEX idx_screenplay_analytics_summary_screenplay_id ON public.screenplay_analytics_summary(screenplay_id);

-- Function to update analytics summary
CREATE OR REPLACE FUNCTION public.update_screenplay_analytics_summary()
RETURNS TRIGGER AS $$
BEGIN
  -- Update analytics summary when events occur
  INSERT INTO public.screenplay_analytics_summary (screenplay_id, last_updated)
  VALUES (
    COALESCE(NEW.screenplay_id, OLD.screenplay_id),
    now()
  )
  ON CONFLICT (screenplay_id) DO UPDATE SET
    total_views = (
      SELECT COUNT(*) FROM public.marketplace_analytics 
      WHERE screenplay_id = COALESCE(NEW.screenplay_id, OLD.screenplay_id) 
      AND event_type = 'view'
    ),
    total_offers = (
      SELECT COUNT(*) FROM public.screenplay_offers 
      WHERE screenplay_id = COALESCE(NEW.screenplay_id, OLD.screenplay_id)
    ),
    avg_offer_amount = (
      SELECT AVG(offer_amount) FROM public.screenplay_offers 
      WHERE screenplay_id = COALESCE(NEW.screenplay_id, OLD.screenplay_id)
    ),
    total_purchases = (
      SELECT COUNT(*) FROM public.screenplay_purchases 
      WHERE screenplay_id = COALESCE(NEW.screenplay_id, OLD.screenplay_id)
    ),
    total_revenue = (
      SELECT COALESCE(SUM(purchase_price), 0) FROM public.screenplay_purchases 
      WHERE screenplay_id = COALESCE(NEW.screenplay_id, OLD.screenplay_id)
    ),
    last_updated = now();
    
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update analytics
CREATE TRIGGER update_analytics_on_view_trigger
  AFTER INSERT ON public.marketplace_analytics
  FOR EACH ROW
  EXECUTE FUNCTION public.update_screenplay_analytics_summary();

CREATE TRIGGER update_analytics_on_offer_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.screenplay_offers
  FOR EACH ROW
  EXECUTE FUNCTION public.update_screenplay_analytics_summary();

CREATE TRIGGER update_analytics_on_purchase_trigger
  AFTER INSERT ON public.screenplay_purchases
  FOR EACH ROW
  EXECUTE FUNCTION public.update_screenplay_analytics_summary();
