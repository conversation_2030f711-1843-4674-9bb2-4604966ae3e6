import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'inter': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
				'playfair': ['Playfair Display', 'Georgia', 'serif'],
				'mono': ['JetBrains Mono', 'Monaco', 'Consolas', 'monospace'],
				'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
				'serif': ['Playfair Display', 'Georgia', 'serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// Enhanced ScriptGenius design system
				cinema: {
					50: 'hsl(210, 40%, 98%)',
					100: 'hsl(210, 40%, 95%)',
					200: 'hsl(215, 25%, 85%)',
					300: 'hsl(215, 20%, 75%)',
					400: 'hsl(215, 15%, 60%)',
					500: 'hsl(220, 15%, 45%)',
					600: 'hsl(220, 20%, 35%)',
					700: 'hsl(220, 25%, 25%)',
					800: 'hsl(220, 27%, 15%)',
					900: 'hsl(220, 30%, 8%)',
					950: 'hsl(220, 35%, 5%)',
				},
				gold: {
					50: 'hsl(50, 90%, 95%)',
					100: 'hsl(48, 85%, 88%)',
					200: 'hsl(46, 80%, 78%)',
					300: 'hsl(44, 85%, 68%)',
					400: 'hsl(42, 85%, 58%)',
					500: 'hsl(40, 85%, 55%)',
					600: 'hsl(38, 80%, 50%)',
					700: 'hsl(36, 75%, 45%)',
					800: 'hsl(34, 70%, 40%)',
					900: 'hsl(32, 65%, 35%)',
				},
				story: {
					50: 'hsl(240, 50%, 98%)',
					100: 'hsl(240, 45%, 95%)',
					200: 'hsl(240, 40%, 88%)',
					300: 'hsl(240, 35%, 78%)',
					400: 'hsl(240, 30%, 68%)',
					500: 'hsl(240, 25%, 58%)',
					600: 'hsl(240, 30%, 48%)',
					700: 'hsl(240, 35%, 38%)',
					800: 'hsl(240, 40%, 28%)',
					900: 'hsl(240, 45%, 18%)',
				},
			},
			typography: {
				DEFAULT: {
					css: {
						'max-width': 'none',
						'color': 'hsl(var(--foreground))',
						'font-family': 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
						'h1': {
							'font-family': 'Playfair Display, Georgia, serif',
							'color': 'hsl(var(--foreground))',
						},
						'h2': {
							'font-family': 'Playfair Display, Georgia, serif',
							'color': 'hsl(var(--foreground))',
						},
						'h3': {
							'font-family': 'Playfair Display, Georgia, serif',
							'color': 'hsl(var(--foreground))',
						},
						'h4': {
							'color': 'hsl(var(--foreground))',
						},
						'p': {
							'color': 'hsl(var(--foreground))',
						},
						'strong': {
							'color': 'hsl(var(--foreground))',
						},
						'a': {
							'color': 'hsl(var(--primary))',
						},
					},
				},
			},
			spacing: {
				'18': '4.5rem',
				'88': '22rem',
				'112': '28rem',
				'128': '32rem',
				'144': '36rem',
				'160': '40rem',
				'176': '44rem',
				'192': '48rem',
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)',
				'xl': '1rem',
				'2xl': '1.5rem',
				'3xl': '2rem',
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' }
				},
				'parallax': {
					'0%': { transform: 'translateY(0px)' },
					'100%': { transform: 'translateY(-50px)' }
				},
				'slide-in-left': {
					'0%': { transform: 'translateX(-100%)', opacity: '0' },
					'100%': { transform: 'translateX(0)', opacity: '1' }
				},
				'slide-out-left': {
					'0%': { transform: 'translateX(0)', opacity: '1' },
					'100%': { transform: 'translateX(-100%)', opacity: '0' }
				},
				'slide-in-right': {
					'0%': { transform: 'translateX(100%)', opacity: '0' },
					'100%': { transform: 'translateX(0)', opacity: '1' }
				},
				'slide-out-right': {
					'0%': { transform: 'translateX(0)', opacity: '1' },
					'100%': { transform: 'translateX(100%)', opacity: '0' }
				},
				'fade-in-up': {
					'0%': { 
						opacity: '0',
						transform: 'translateY(20px)'
					},
					'100%': { 
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'fade-in-down': {
					'0%': { 
						opacity: '0',
						transform: 'translateY(-20px)'
					},
					'100%': { 
						opacity: '1',
						transform: 'translateY(0)'
					}
				},
				'fade-in-scale': {
					'0%': { 
						opacity: '0',
						transform: 'scale(0.95)'
					},
					'100%': { 
						opacity: '1',
						transform: 'scale(1)'
					}
				},
				'pulse-glow': {
					'0%, 100%': { 
						boxShadow: '0 0 20px rgba(251, 191, 36, 0.3)',
						transform: 'scale(1)'
					},
					'50%': { 
						boxShadow: '0 0 40px rgba(251, 191, 36, 0.6)',
						transform: 'scale(1.02)'
					}
				},
				'float': {
					'0%, 100%': { transform: 'translateY(0px)' },
					'50%': { transform: 'translateY(-10px)' }
				},
				'typewriter': {
					'0%': { width: '0ch' },
					'100%': { width: '100ch' }
				},
				'blink': {
					'0%, 50%': { opacity: '1' },
					'51%, 100%': { opacity: '0' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'parallax': 'parallax 20s ease-in-out infinite alternate',
				'slide-in-left': 'slide-in-left 0.3s ease-out',
				'slide-out-left': 'slide-out-left 0.3s ease-out',
				'slide-in-right': 'slide-in-right 0.3s ease-out',
				'slide-out-right': 'slide-out-right 0.3s ease-out',
				'fade-in-up': 'fade-in-up 0.6s ease-out',
				'fade-in-down': 'fade-in-down 0.6s ease-out',
				'fade-in-scale': 'fade-in-scale 0.6s ease-out',
				'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
				'float': 'float 6s ease-in-out infinite',
				'typewriter': 'typewriter 3s steps(40, end)',
				'blink': 'blink 1s infinite',
				'glow': 'pulse-glow 2s ease-in-out infinite',
			},
			backdropBlur: {
				'xs': '2px',
			},
			boxShadow: {
				'cinema': '0 20px 40px rgba(0, 0, 0, 0.3)',
				'glow': '0 0 20px rgba(251, 191, 36, 0.3)',
				'glow-lg': '0 0 40px rgba(251, 191, 36, 0.6)',
				'story': '0 10px 30px rgba(139, 92, 246, 0.2)',
				'soft': '0 4px 20px rgba(0, 0, 0, 0.1)',
			}
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		require("@tailwindcss/typography")
	],
} satisfies Config;
