
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    watch: {
      ignored: [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/coverage/**',
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts',
        '**/*.spec.tsx'
      ]
    }
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    dedupe: ['react', 'react-dom']
  },
  define: {
    'process.env': {}
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Mobile-optimized chunking - smaller, focused chunks
          'vendor-core': ['react', 'react-dom'],
          'vendor-router': ['react-router-dom'],
          'vendor-query': ['@tanstack/react-query'],
          'vendor-supabase': ['@supabase/supabase-js'],
          
          // UI chunks - split for better caching
          'ui-core': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
          ],
          'ui-forms': [
            '@radix-ui/react-tabs',
            '@radix-ui/react-toast'
          ],
          
          // Feature-specific chunks for code splitting
          'mobile-utils': [
            './src/hooks/useMobileNetwork.ts',
            './src/components/mobile/MobileErrorBoundary.tsx'
          ]
        }
      }
    },
    sourcemap: false, // Disable sourcemaps for mobile builds
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : []
      },
      mangle: {
        safari10: true // Better mobile Safari compatibility
      }
    },
    cssCodeSplit: true,
    chunkSizeWarningLimit: 500, // Smaller chunks for mobile
    assetsInlineLimit: 2048 // Inline smaller assets
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query'
    ],
    exclude: [
      'prosemirror-state',
      'prosemirror-view',
      'recharts' // Heavy chart library
    ],
    force: true,
    esbuildOptions: {
      target: 'es2020'
    }
  },
  // Mobile-specific performance optimizations
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  },
  cacheDir: 'node_modules/.vite'
}));
